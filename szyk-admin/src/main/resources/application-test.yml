#数据源配置
spring:
  redis:
    ##redis 单机环境配置
    host: r-2zewurygcqi5jmbiv6pd.redis.rds.aliyuncs.com
    port: 6379
    password: RoFJc&sdI2S
    database: 23
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    # MySql
    url: ********************************************************************************************************************************************************************************************************************************************************************************
    username: szykdev
    password: 2SSFJ<PERSON>&sGw@
    # PostgreSQL
    #url: ******************************************
    #username: postgres
    #password: 123456
    # Oracle
    #url: *************************************
    #username: SZYK_BOOT
    #password: SZYK_BOOT
    # SqlServer
    #url: ******************************************************
    #username: szyk_boot
    #password: szyk_boot
  kafka:
    custom:
      enable: false
    bootstrap-servers: 127.0.0.1:9092
    producer:
      # 发生错误后，消息重发的次数。
      retries: 0
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
      batch-size: 16384
      # 设置生产者内存缓冲区的大小。
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      # acks: 1
    consumer:
      # 指定默认消费者group id
      group-id: GROUP1
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      # 自动提交的时间间隔 在spring boot 2.X 版本中这里采用的是值的类型为Duration 需要符合特定的格式，如1S,1M,2H,5D
      # auto-commit-interval: 1S
      enable-auto-commit: false
      # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
      # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
      # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
      auto-offset-reset: latest
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        session.timeout.ms: 60000
    listener:
      log-container-config: false
      # 在侦听器容器中运行的线程数。
      concurrency: 2
      #listner负责ack，每调用一次，就立即commit
      ack-mode: manual_immediate
      # missing-topics-fatal: false
  elasticsearch:
    rest:
      url: *************
      uris: *************:9200
      connection-timeout: 1000
      read-timeout: 3000
      username: elastic
      password: TeY%Ks82S^Mg


#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#szyk配置
szyk:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: true
    ##redis服务地址
    address: redis://r-2zewurygcqi5jmbiv6pd.redis.rds.aliyuncs.com:6379
    password: RoFJc&sdI2S
    database: 4
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html

#oss默认配置
oss:
  enabled: true
  name: minio
  tenant-mode: false
  endpoint: http://*************:9000
  access-key: minioadmin
  secret-key: MisX*2jwsDF
  bucket-name: sn-mc
#泛微OA配置
oa:
  url: http://************:80
  appid: snxxjsgkxtcs
  token-timeout: 1800

# 统一认证平台单点登录,组织用户同步参数配置
oauth:
  #组织用户同步
  app:
    app-id: 4c4ee6d8a12745fe
    app-key: 1041e4fab830471e9d4bf30da6c99595
  #单点登录
  client:
    client-id: 59fddbc80fc5e64e1788
    client-secret: bf0434160636704cc929b739a065ffa6a3b0
    authorize-request-url: https://iamtest.ykjt.cc/esc-sso/oauth2.0/authorize
    token-request-url: https://iamtest.ykjt.cc/esc-sso/oauth2.0/accessToken
    userinfo-request-url: https://iamtest.ykjt.cc/esc-sso/oauth2.0/profile
    #回调地址
    call-back-url: http://*************/api/oauth/callBack
    #认证通过后跳转的地址
    app-jump-url: http://*************/#/sso
  #同步人员信息默认组织和岗位
  default:
    #同步人员信息默认角色id
    role-id: 1123598816738675202
    #同步人员信息默认岗位id
    post-id: 1123598817738675208
dingtalk:
  appkey: ding3nhvq3a1it10xxxj
  appsecret: k4hMWy--HoC036mgrTVqa9twgkl9Xh_H5f-8JK5n4VMD_iY3ZDHCr8_-u0QN7KUo
  agentId: 2569138485
  #山能页面地址前缀 测试环境
  snAddressPrrfix: http://*************:32529/#
task:
  tempPath: /usr/local/temp/
#数据湖相关配置
do:
  client-id: b27724d55b3a4ce5ba5601b56158a467
  client-secret: "!j78KOd!"
  token-url : http://************:8310/oauth/token
  data-url: http://************:8310/service/api/520794dc38e642feb14507f7b8920748/data-api/service/api/queryInfo

nginx:
  #文件路径
  file-url: http://*************:9000
  #代理路径
  file-proxy: http://*************:32529/file
system:
  website-url: http://*************:32529

xxl:
  job:
    admin:
      addresses: http://*************:30039/xxl-job-admin
    accessToken: SZYKxm_token_00000000
    executor:
      appname: sn-mc-job
      address:
      ip:
      port: 9999
      logpath: /home/<USER>/xjob/log
      logretentiondays: 30
    groupid: 2
    username: SZYKxm
    password: 2Ghlmcl@szyk
