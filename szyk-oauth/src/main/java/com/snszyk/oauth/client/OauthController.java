package com.snszyk.oauth.client;

import cn.hutool.core.collection.CollectionUtil;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.oauth.Constant.SsoConstant;
import com.snszyk.oauth.adapter.OauthAdapter;
import com.snszyk.oauth.config.AppClient;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/2/20 10:51
 **/
@Slf4j
@Controller
@AllArgsConstructor
@RequestMapping("oauth")
public class OauthController {


	private final AppClient appClient;
	private final OauthAdapter oauthAdapter;
	private final SzykRedis szykRedis;


	@SneakyThrows
	@GetMapping
	public String redirectToSso(RedirectAttributes attr) {
		attr.addAttribute("client_id", appClient.getClientId());
		attr.addAttribute("response_type", "code");
		attr.addAttribute("redirect_uri", appClient.getCallBackUrl());
		return "redirect:".concat(appClient.getAuthorizeRequestUrl());
	}

	@GetMapping("/callBack")
	public String callBack(@RequestParam(value = "code", required = true) String code,
						   @RequestParam(value = "state", required = false) String state,
						   RedirectAttributes attr) throws Exception {
		//业务范围id
		String deptScopeId = "";
		//获取用户账号
		String accountNo = oauthAdapter.validateUser(code, appClient);
		if (StringUtils.isNotBlank(accountNo)) {
			//用户认证通过后作废
			szykRedis.setEx(SsoConstant.REDIS_USER + accountNo, accountNo, SsoConstant.DEFAULT_TIME_OUT);
		}
		//获取链接中的业务范围参数值
		if (StringUtils.isBlank(state) || "null".equals(state)) {
			state = "";
		}
		if (StringUtil.isNoneBlank(state)) {
			Map<String, String> params = getUrlPramNameAndValue(state);
			if (CollectionUtil.isNotEmpty(params) && StringUtil.isNoneBlank(params.get("deptScopeId"))) {
				deptScopeId = params.get("deptScopeId");
			}
		}
		return "redirect:".concat(appClient.getAppJumpUrl() + "?username=" + accountNo + "&deptScopeId=" + deptScopeId + "&url=" + state);
	}

	@SneakyThrows
	@GetMapping("/target")
	public String redirectToSsoTarget(@RequestParam(value = "state", required = false) String state, RedirectAttributes attr) {
		attr.addAttribute("client_id", appClient.getClientId());
		attr.addAttribute("response_type", "code");
		attr.addAttribute("redirect_uri", appClient.getCallBackUrl());
		attr.addAttribute("state", state);
		return "redirect:".concat(appClient.getAuthorizeRequestUrl());
	}

	/**
	 * 获取URL中的参数名和参数值的Map集合
	 *
	 * @param url
	 * @return
	 */
	private Map<String, String> getUrlPramNameAndValue(String url) {
		String regEx = "(\\?|&+)(.+?)=([^&]*)";//匹配参数名和参数值的正则表达式
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(url);
		// LinkedHashMap是有序的Map集合，遍历时会按照加入的顺序遍历输出
		Map<String, String> paramMap = new LinkedHashMap<String, String>();
		while (m.find()) {
			String paramName = m.group(2);//获取参数名
			String paramVal = m.group(3);//获取参数值
			paramMap.put(paramName, paramVal);
		}
		return paramMap;
	}


}
