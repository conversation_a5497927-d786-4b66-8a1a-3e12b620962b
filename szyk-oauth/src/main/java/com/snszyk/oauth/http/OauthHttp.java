package com.snszyk.oauth.http;

import com.snszyk.core.http.util.HttpUtil;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.oauth.config.AppClient;
import com.snszyk.oauth.param.TokenRes;
import com.snszyk.oauth.param.UserInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/2/20 15:58
 **/
@Slf4j
public class OauthHttp {


    /**
     * 获取access_token
     * @param code
     * @param config
     * @return
     * @throws Exception
     */
    public static String requestToken(String code, AppClient config) throws Exception {
        Map<String,Object> map = new HashMap<>();
        map.putIfAbsent("client_id",config.getClientId());
        map.putIfAbsent("client_secret",config.getClientSecret());
        map.putIfAbsent("grant_type","authorization_code");
        map.putIfAbsent("redirect_uri", config.getCallBackUrl());
        map.putIfAbsent("code",code);
        try {
            String s = HttpUtil.post(config.getTokenRequestUrl(), map);
            log.info("token reponse =={}",s);
			TokenRes tokenRes = JsonUtil.parse(s, TokenRes.class);
			return tokenRes.getAccess_token();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("请求token异常====={}",e.getMessage());
            throw new Exception(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     * @param accessToken
     * @param config
     * @return
     * @throws Exception
     */
    public static UserInfo userInfo(String accessToken, AppClient config) throws Exception {
        Map<String,Object> map = new HashMap<>();
        map.putIfAbsent("access_token",accessToken);
        try {
            String s = HttpUtil.get(config.getUserinfoRequestUrl(), map);
			return JsonUtil.parse(s, UserInfo.class);
        } catch (Exception e) {
            log.error("request userInfo Exception====={}",e.getMessage());
            throw new Exception(e.getMessage());
        }
    }
}
