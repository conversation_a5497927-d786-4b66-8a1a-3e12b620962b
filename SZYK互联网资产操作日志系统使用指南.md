# SZYK互联网资产操作日志系统使用指南

## 1. 系统概述

SZYK互联网资产操作日志系统是一个通用的、跨模块的操作记录追踪系统，位于`szyk-system/system-rest`模块中，可供所有业务模块使用。本系统专门为互联网资产台账管理提供完整的操作审计功能。

### 1.1 核心特性

- **跨模块通用设计**：位于`szyk-common`模块，支持所有业务模块
- **注解驱动**：通过`@OperationLog`注解自动记录操作
- **字段级变更追踪**：详细记录字段变更前后值
- **异步处理**：支持异步记录，不影响业务性能
- **SpEL表达式支持**：灵活的业务ID和名称提取
- **完整审计信息**：记录操作人、时间、IP、请求信息等

### 1.2 架构设计

```
szyk-common/
├── operationlog/
│   ├── annotation/          # 注解定义
│   ├── aspect/             # AOP切面
│   ├── controller/         # 控制器
│   ├── dto/               # 数据传输对象
│   ├── entity/            # 实体类
│   ├── enums/             # 枚举定义
│   ├── mapper/            # MyBatis映射
│   ├── service/           # 服务接口及实现
│   └── vo/                # 视图对象
└── resources/
    ├── mapper/            # MyBatis XML映射
    └── sql/               # 数据库脚本
```

## 2. 数据库设计

### 2.1 主表：sys_operation_log

```sql
CREATE TABLE `sys_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_module` varchar(50) NOT NULL COMMENT '业务模块',
  `business_id` bigint DEFAULT NULL COMMENT '业务ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务名称',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
  `operation_description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operation_result` varchar(20) NOT NULL COMMENT '操作结果',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(200) DEFAULT NULL COMMENT '操作人部门名称',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_uri` varchar(500) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `execution_time` bigint DEFAULT NULL COMMENT '执行时间(毫秒)',
  `old_data` longtext COMMENT '变更前数据',
  `new_data` longtext COMMENT '变更后数据',
  `change_fields` varchar(1000) DEFAULT NULL COMMENT '变更字段',
  `exception_info` text COMMENT '异常信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_module`,`business_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

### 2.2 详情表：sys_operation_log_detail

```sql
CREATE TABLE `sys_operation_log_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_id` bigint NOT NULL COMMENT '日志ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_display_name` varchar(200) DEFAULT NULL COMMENT '字段显示名称',
  `old_value` text COMMENT '旧值',
  `new_value` text COMMENT '新值',
  `old_display_value` text COMMENT '旧值显示',
  `new_display_value` text COMMENT '新值显示',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_id` (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志详情表';
```

## 3. 核心组件

### 3.1 业务模块枚举

```java
public enum BusinessModuleEnum {
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产"),
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产"),
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统");
    
    private final String code;
    private final String name;
}
```

### 3.2 操作类型枚举

```java
public enum OperationTypeEnum {
    CREATE("CREATE", "新增"),
    UPDATE("UPDATE", "更新"),
    DELETE("DELETE", "删除"),
    QUERY("QUERY", "查询"),
    IMPORT("IMPORT", "导入"),
    EXPORT("EXPORT", "导出"),
    AUDIT("AUDIT", "审核"),
    ENABLE("ENABLE", "启用"),
    DISABLE("DISABLE", "禁用"),
    LOCK("LOCK", "锁定"),
    UNLOCK("UNLOCK", "解锁");
}
```

### 3.3 操作日志注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {
    BusinessModuleEnum businessModule();
    OperationTypeEnum operationType();
    String description() default "";
    String businessIdExpression() default "";
    String businessNameExpression() default "";
    boolean logParams() default true;
    boolean logResult() default false;
    boolean logException() default true;
}
```

## 4. 使用方法

### 4.1 在控制器中使用注解

```java
@RestController
@RequestMapping("/zbusiness/internet")
public class RsInternetController {

    @PostMapping("/save")
    @OperationLog(
        businessModule = BusinessModuleEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.CREATE,
        description = "保存互联网资产台账",
        businessIdExpression = "#result.data.id",
        businessNameExpression = "#result.data.systemName"
    )
    public R<RsInternetDto> saveOrUpdate(@RequestBody RsInternetVo v) {
        RsInternetDto save = rsInternetLogicService.saveOrUpdate(v);
        return R.data(save);
    }

    @PostMapping("/delete")
    @OperationLog(
        businessModule = BusinessModuleEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.DELETE,
        description = "删除互联网资产台账"
    )
    public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsEquipmentDeleteVo vo) {
        List<RsSoftwareDeleteDto> result = rsInternetLogicService.delete(vo);
        return R.data(result);
    }

    @PostMapping("/updateResourceStatus")
    @OperationLog(
        businessModule = BusinessModuleEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.UPDATE,
        description = "更改互联网资产状态",
        businessIdExpression = "#vo.id"
    )
    public R<Boolean> updateResourceStatus(@RequestBody RsInternetStatusVo vo) {
        Boolean result = rsInternetLogicService.updateResourceStatus(vo);
        return R.data(result);
    }
}
```

### 4.2 SpEL表达式说明

- `#result.data.id`：从返回结果中提取业务ID
- `#result.data.systemName`：从返回结果中提取业务名称
- `#vo.id`：从方法参数中提取业务ID
- `#param1.name`：从第一个参数中提取属性值

### 4.3 查询操作日志

```java
@RestController
@RequestMapping("/common/operationlog")
public class OperationLogController {

    // 分页查询
    @PostMapping("/page")
    public R<IPage<OperationLogDto>> page(@RequestBody OperationLogPageVo vo) {
        IPage<OperationLogDto> pages = operationLogService.pageList(vo);
        return R.data(pages);
    }

    // 根据业务ID查询
    @GetMapping("/listByBusiness")
    public R<List<OperationLogDto>> listByBusiness(
        @RequestParam String businessModule,
        @RequestParam Long businessId) {
        List<OperationLogDto> list = operationLogService.listByBusinessId(businessModule, businessId);
        return R.data(list);
    }

    // 查询详情
    @GetMapping("/detail/{id}")
    public R<OperationLogDto> detail(@PathVariable Long id) {
        OperationLogDto detail = operationLogService.getDetailWithChanges(id);
        return R.data(detail);
    }
}
```

## 5. 配置说明

### 5.1 启用异步处理

在Spring Boot配置类中启用异步：

```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("operation-log-");
        executor.initialize();
        return executor;
    }
}
```

### 5.2 MyBatis配置

确保MyBatis扫描到mapper包：

```yaml
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.snszyk.common.operationlog.entity
```

## 6. 扩展其他业务模块

### 6.1 添加新的业务模块

1. 在`BusinessModuleEnum`中添加新的业务模块：

```java
public enum BusinessModuleEnum {
    // 现有模块...
    PROJECT_MANAGEMENT("PROJECT_MANAGEMENT", "项目管理"),
    PERSONNEL_MANAGEMENT("PERSONNEL_MANAGEMENT", "人员管理");
}
```

2. 在对应的控制器中添加注解：

```java
@PostMapping("/save")
@OperationLog(
    businessModule = BusinessModuleEnum.PROJECT_MANAGEMENT,
    operationType = OperationTypeEnum.CREATE,
    description = "保存项目信息",
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.projectName"
)
public R<ProjectDto> save(@RequestBody ProjectVo vo) {
    // 业务逻辑
}
```

### 6.2 自定义字段显示名称

在`OperationLogServiceImpl`中的`getFieldDisplayName`方法中添加字段映射：

```java
private String getFieldDisplayName(String fieldName) {
    switch (fieldName) {
        case "systemName": return "系统名称";
        case "internetAddress": return "互联网地址";
        case "projectName": return "项目名称";
        case "projectCode": return "项目编码";
        // 添加更多字段映射...
        default: return fieldName;
    }
}
```

## 7. 最佳实践

### 7.1 注解使用建议

1. **必须设置的属性**：`businessModule`、`operationType`、`description`
2. **建议设置的属性**：`businessIdExpression`、`businessNameExpression`
3. **性能考虑**：对于查询操作，可以设置`logParams = false`减少日志量

### 7.2 SpEL表达式最佳实践

1. **返回结果提取**：使用`#result.data.xxx`格式
2. **参数提取**：使用`#参数名.属性名`格式
3. **复杂表达式**：可以使用`#result.data != null ? #result.data.id : null`

### 7.3 性能优化建议

1. **异步记录**：使用`@Async`注解进行异步处理
2. **批量处理**：对于大量操作，考虑批量记录日志
3. **定期清理**：定期清理过期的操作日志

## 8. 故障排查

### 8.1 常见问题

1. **日志未记录**：检查AOP切面是否生效，确认方法是否被Spring管理
2. **SpEL表达式错误**：检查表达式语法，确认参数和返回值结构
3. **异步失效**：确认`@EnableAsync`已配置，方法调用是通过Spring代理

### 8.2 调试方法

1. **启用DEBUG日志**：
```yaml
logging:
  level:
    com.snszyk.common.operationlog: DEBUG
```

2. **检查切面执行**：在`OperationLogAspect`中添加日志输出

## 9. API接口文档

### 9.1 查询接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/common/operationlog/page` | POST | 分页查询操作日志 |
| `/common/operationlog/detail/{id}` | GET | 查询日志详情 |
| `/common/operationlog/listByBusiness` | GET | 根据业务ID查询 |
| `/common/operationlog/countByModule` | GET | 统计模块日志数量 |
| `/common/operationlog/countByType` | GET | 统计操作类型数量 |

### 9.2 管理接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/common/operationlog/deleteByBusiness` | DELETE | 删除业务相关日志 |
| `/common/operationlog/cleanExpired` | POST | 清理过期日志 |

## 10. 总结

SZYK互联网资产操作日志系统提供了完整的操作审计功能，通过注解驱动的方式简化了使用复杂度，同时保持了高度的灵活性和扩展性。系统设计遵循了SZYK项目的代码规范，可以无缝集成到现有的业务模块中。

通过本系统，可以实现：
- 完整的操作追踪和审计
- 详细的字段级变更记录
- 灵活的查询和统计功能
- 良好的性能和扩展性

建议在实际使用中根据具体业务需求进行适当的配置和扩展。
