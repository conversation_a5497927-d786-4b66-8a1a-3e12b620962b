-- 互联网资产操作日志系统数据库更新脚本
-- 执行日期: 2024-07-04

-- 检查sys_operation_log表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_module` varchar(50) DEFAULT NULL COMMENT '业务模块',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务ID',
  `business_name` varchar(255) DEFAULT NULL COMMENT '业务名称',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `operation_description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operation_result` varchar(50) DEFAULT NULL COMMENT '操作结果',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint(20) DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(255) DEFAULT NULL COMMENT '操作人部门名称',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_uri` varchar(500) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(20) DEFAULT NULL COMMENT '请求方法',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间(毫秒)',
  `old_data` longtext COMMENT '变更前数据',
  `new_data` longtext COMMENT '变更后数据',
  `change_fields` text COMMENT '变更字段',
  `exception_info` text COMMENT '异常信息',
  `org_id` bigint(20) DEFAULT NULL COMMENT '主管单位ID',
  `org_name` varchar(255) DEFAULT NULL COMMENT '主管单位名称',
  `business_fields` text COMMENT '业务扩展字段(JSON格式)',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_business_module` (`business_module`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 如果表已存在，检查并添加缺失的字段
-- 添加业务相关字段
ALTER TABLE `sys_operation_log` 
ADD COLUMN IF NOT EXISTS `business_id` bigint(20) DEFAULT NULL COMMENT '业务ID' AFTER `business_module`,
ADD COLUMN IF NOT EXISTS `business_name` varchar(255) DEFAULT NULL COMMENT '业务名称' AFTER `business_id`,
ADD COLUMN IF NOT EXISTS `org_id` bigint(20) DEFAULT NULL COMMENT '主管单位ID' AFTER `exception_info`,
ADD COLUMN IF NOT EXISTS `org_name` varchar(255) DEFAULT NULL COMMENT '主管单位名称' AFTER `org_id`,
ADD COLUMN IF NOT EXISTS `business_fields` text COMMENT '业务扩展字段(JSON格式)' AFTER `org_name`;

-- 添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS `idx_business_module` ON `sys_operation_log` (`business_module`);
CREATE INDEX IF NOT EXISTS `idx_business_id` ON `sys_operation_log` (`business_id`);
CREATE INDEX IF NOT EXISTS `idx_operation_type` ON `sys_operation_log` (`operation_type`);
CREATE INDEX IF NOT EXISTS `idx_operation_time` ON `sys_operation_log` (`operation_time`);
CREATE INDEX IF NOT EXISTS `idx_operator_id` ON `sys_operation_log` (`operator_id`);
CREATE INDEX IF NOT EXISTS `idx_org_id` ON `sys_operation_log` (`org_id`);
CREATE INDEX IF NOT EXISTS `idx_create_time` ON `sys_operation_log` (`create_time`);

-- 插入测试数据（可选）
INSERT IGNORE INTO `sys_operation_log` (
  `business_module`, `business_id`, `business_name`, `operation_type`, 
  `operation_description`, `operation_time`, `operator_id`, `operator_name`,
  `operator_dept_id`, `operator_dept_name`, `org_id`, `org_name`,
  `old_data`, `new_data`, `change_fields`, `create_time`
) VALUES (
  'INTERNET_ASSET', 1, '测试互联网资产', 'CREATE', 
  '创建互联网资产：测试互联网资产', NOW(), 1, '系统管理员',
  1, '系统管理部', 1, '测试组织',
  NULL, '{"id":1,"systemName":"测试互联网资产","internetType":"WEB"}', 
  'systemName,internetType', NOW()
);

-- 验证表结构
SELECT 
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_operation_log'
ORDER BY ORDINAL_POSITION;
