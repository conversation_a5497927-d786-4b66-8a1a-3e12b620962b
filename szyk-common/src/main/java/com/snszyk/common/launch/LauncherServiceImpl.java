/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.common.launch;

import com.snszyk.common.constant.LauncherConstant;
import com.snszyk.core.auto.service.AutoService;
import com.snszyk.core.launch.service.LauncherService;
import com.snszyk.core.launch.utils.PropsUtil;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 *
 * <AUTHOR>
 */
@AutoService(LauncherService.class)
public class LauncherServiceImpl implements LauncherService {

	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
		Properties props = System.getProperties();
		PropsUtil.setProperty(props, "spring.cloud.sentinel.transport.dashboard", LauncherConstant.sentinelAddr(profile));
		PropsUtil.setProperty(props, "spring.datasource.dynamic.enabled", "false");
		// 开启elk日志
		//PropsUtil.setProperty(props, "szyk.log.elk.destination", LauncherConstant.elkAddr(profile));
	}

}
