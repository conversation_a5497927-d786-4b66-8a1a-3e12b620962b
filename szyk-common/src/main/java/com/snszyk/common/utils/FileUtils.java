package com.snszyk.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

@Slf4j
public class FileUtils {
	/**
	 * 下载ZIP压缩包(会对下载后的压缩包进行删除)
	 *
	 * @param file     zip压缩包文件
	 * @param response 响应
	 */
	public static void downloadZip(File file, HttpServletResponse response) {
		OutputStream toClient = null;
		try {
			// 以流的形式下载文件。
			BufferedInputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
			byte[] buffer = new byte[fis.available()];
			fis.read(buffer);
			fis.close();
			// 清空response
			response.reset();
			toClient = new BufferedOutputStream(response.getOutputStream());
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());
			toClient.write(buffer);
			toClient.flush();
		} catch (Exception e) {
			log.error("下载zip压缩包过程发生异常:", e);
		} finally {
			if (toClient != null) {
				try {
					toClient.close();
				} catch (IOException e) {
					log.error("zip包下载关流失败:", e);
				}
			}
			//删除改临时zip包(此zip包任何时候都不需要保留,因为源文件随时可以再次进行压缩生成zip包)
			file.delete();
		}
	}


}
