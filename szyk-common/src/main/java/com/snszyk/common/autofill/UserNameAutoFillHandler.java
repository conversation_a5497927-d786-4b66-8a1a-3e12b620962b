package com.snszyk.common.autofill;

import com.snszyk.core.crud.handler.AutoFillHandler;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 全局获取用户id
 */
@Component
public class UserNameAutoFillHandler implements AutoFillHandler<String> {

    @Override
    public String getVal(Object object, Class<?> clazz, Field field) {

		SzykUser user = AuthUtil.getUser();
		if (user != null) {
			return user.getUserName();
		}
		return null;
    }
}
