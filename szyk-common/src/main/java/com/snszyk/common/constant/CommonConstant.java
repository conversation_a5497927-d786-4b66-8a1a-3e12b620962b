/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.common.constant;

import com.snszyk.core.launch.constant.AppConstant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * app name
	 */
	String APPLICATION_NAME = AppConstant.APPLICATION_NAME_PREFIX + "api";

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Long TOP_PARENT_ID = 0L;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";

	/**
	 * 未封存状态值
	 */
	Integer NOT_SEALED_ID = 0;

	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "JQ1tol@sd";

	/**
	 * 默认密码参数值
	 */
	String DEFAULT_PARAM_PASSWORD = "account.initPassword";

	/**
	 * 默认排序字段
	 */
	String SORT_FIELD = "sort";

	/**
	 * 数据权限类型
	 */
	Integer DATA_SCOPE_CATEGORY = 1;

	/**
	 * 接口权限类型
	 */
	Integer API_SCOPE_CATEGORY = 2;

	Integer ZERO=0;
	Integer ONE=1;
	Integer TWO=2;
	Integer THREE=3;
	String ONE_STR="1";
	String ZERO_STR="0";
	Long NULL_DEPT_PARENT_ID = 999999999l;


	/**
	 * 项目提报的数量
	 */
	String PROJECT_INDEX_REPORT_COUNT = "PROJECT_INDEX_REPORT_COUNT";
	/**
	 * 项目退回的数量
	 */
	String PROJECT_INDEX_RETURN_COUNT = "PROJECT_INDEX_RETURN_COUNT";
	/**
	 * 项目撤回的数量
	 */
	String PROJECT_INDEX_CANCEL_COUNT = "PROJECT_INDEX_CANCEL_COUNT";

	String SHOW_ENV_CODE = "show";
}
