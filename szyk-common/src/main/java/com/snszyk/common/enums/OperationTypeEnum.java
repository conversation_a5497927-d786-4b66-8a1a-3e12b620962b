package com.snszyk.common.enums;

import com.snszyk.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum OperationTypeEnum {
	/**
	 * 新增
	 */
	SAVE,
	/**
	 * 修改
	 */
	UPDATE,
	/**
	 * 查询
	 */
	SELECT,
	/**
	 * 删除
	 */
	REMOVE;

	/**
	 * 通过有无id来判断此次操作类型
	 *
	 * @param id
	 * @return
	 */
	public static OperationTypeEnum getOperationType(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			return SAVE;
		}
		return UPDATE;
	}
}
