package com.snszyk.common.operationlog.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志分页查询对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作日志分页查询VO", description = "操作日志分页查询对象")
public class OperationLogPageVo extends BaseCrudVo {

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作开始时间
     */
    @ApiModelProperty(value = "操作开始时间")
    private LocalDateTime startOperationTime;

    /**
     * 操作结束时间
     */
    @ApiModelProperty(value = "操作结束时间")
    private LocalDateTime endOperationTime;

    /**
     * 组织ID（用于数据权限控制）
     */
    @ApiModelProperty(value = "组织ID")
    private Long orgId;
}
