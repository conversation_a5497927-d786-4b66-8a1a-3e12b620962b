package com.snszyk.common.operationlog.aspect;

import com.alibaba.fastjson.JSON;
import com.snszyk.common.operationlog.annotation.OperationLog;
import com.snszyk.common.operationlog.enums.BusinessModuleEnum;
import com.snszyk.common.operationlog.enums.OperationTypeEnum;
import com.snszyk.common.operationlog.service.IOperationLogService;
import com.snszyk.common.operationlog.vo.OperationLogVo;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.WebUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Aspect
@Component
@Order(1)
@AllArgsConstructor
public class OperationLogAspect {

    private final IOperationLogService operationLogService;
    private final ExpressionParser parser = new SpelExpressionParser();

    @Pointcut("@annotation(com.snszyk.common.operationlog.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            try {
                // 记录操作日志
                recordOperationLog(joinPoint, result, exception, startTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, Object result, 
                                   Throwable exception, long startTime) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog annotation = method.getAnnotation(OperationLog.class);

            if (annotation == null) {
                return;
            }

            // 构建操作日志
            OperationLogVo logVo = buildOperationLog(joinPoint, annotation, result, exception, startTime);
            
            // 异步保存日志
            operationLogService.saveAsync(logVo);
            
        } catch (Exception e) {
            log.error("构建操作日志失败", e);
        }
    }

    /**
     * 构建操作日志对象
     */
    private OperationLogVo buildOperationLog(ProceedingJoinPoint joinPoint, OperationLog annotation,
                                           Object result, Throwable exception, long startTime) {
        OperationLogVo logVo = new OperationLogVo();

        // 基础信息
        BusinessModuleEnum businessModule = annotation.businessModule();
        OperationTypeEnum operationType = annotation.operationType();
        
        logVo.setBusinessModule(businessModule.getCode());
        logVo.setOperationType(operationType.getCode());
        logVo.setOperationDescription(annotation.description());
        logVo.setOperationTime(LocalDateTime.now());

        // 执行时间
        long executionTime = System.currentTimeMillis() - startTime;
        logVo.setExecutionTime(executionTime);

        // 操作结果
        if (exception != null) {
            logVo.setOperationResult("FAILURE");
            if (annotation.logException()) {
                logVo.setExceptionInfo(getExceptionInfo(exception));
            }
        } else {
            logVo.setOperationResult("SUCCESS");
        }

        // 操作人信息
        setOperatorInfo(logVo);

        // 请求信息
        setRequestInfo(logVo);

        // 业务信息
        setBusinessInfo(logVo, joinPoint, annotation, result);

        // 参数和结果
        setParamsAndResult(logVo, joinPoint, annotation, result);

        return logVo;
    }

    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(OperationLogVo logVo) {
        try {
            if (AuthUtil.getUser() != null) {
                logVo.setOperatorId(AuthUtil.getUserId());
                logVo.setOperatorName(AuthUtil.getUserName());
                logVo.setOperatorDeptId(Long.valueOf(AuthUtil.getDeptId()));
                logVo.setOperatorDeptName(AuthUtil.getDeptName());
            }
        } catch (Exception e) {
            log.warn("获取操作人信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(OperationLogVo logVo) {
        try {
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                logVo.setClientIp(WebUtil.getIP(request));
                logVo.setUserAgent(request.getHeader("User-Agent"));
                logVo.setRequestUri(request.getRequestURI());
                logVo.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
    }

    /**
     * 设置业务信息
     */
    private void setBusinessInfo(OperationLogVo logVo, ProceedingJoinPoint joinPoint, 
                               OperationLog annotation, Object result) {
        try {
            // 解析业务ID
            if (!annotation.businessIdExpression().isEmpty()) {
                Object businessId = parseSpelExpression(annotation.businessIdExpression(), 
                    joinPoint, result);
                if (businessId instanceof Number) {
                    logVo.setBusinessId(((Number) businessId).longValue());
                }
            }

            // 解析业务名称
            if (!annotation.businessNameExpression().isEmpty()) {
                Object businessName = parseSpelExpression(annotation.businessNameExpression(), 
                    joinPoint, result);
                if (businessName != null) {
                    logVo.setBusinessName(businessName.toString());
                }
            }
        } catch (Exception e) {
            log.warn("解析业务信息失败", e);
        }
    }

    /**
     * 设置参数和结果
     */
    private void setParamsAndResult(OperationLogVo logVo, ProceedingJoinPoint joinPoint, 
                                  OperationLog annotation, Object result) {
        try {
            // 记录请求参数
            if (annotation.logParams()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    Map<String, Object> params = new HashMap<>();
                    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                    String[] paramNames = signature.getParameterNames();
                    
                    for (int i = 0; i < args.length && i < paramNames.length; i++) {
                        if (args[i] != null && !isIgnoredParam(args[i])) {
                            params.put(paramNames[i], args[i]);
                        }
                    }
                    
                    if (!params.isEmpty()) {
                        logVo.setNewData(JSON.toJSONString(params));
                    }
                }
            }

            // 记录返回结果
            if (annotation.logResult() && result != null) {
                // 这里可以根据需要记录返回结果
                // logVo.setOperationResult(JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.warn("记录参数和结果失败", e);
        }
    }

    /**
     * 解析SpEL表达式
     */
    private Object parseSpelExpression(String expression, ProceedingJoinPoint joinPoint, Object result) {
        try {
            EvaluationContext context = new StandardEvaluationContext();
            
            // 设置方法参数
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();
            
            for (int i = 0; i < paramNames.length && i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
            
            // 设置返回结果
            if (result != null) {
                context.setVariable("result", result);
            }
            
            Expression exp = parser.parseExpression(expression);
            return exp.getValue(context);
        } catch (Exception e) {
            log.warn("解析SpEL表达式失败: {}", expression, e);
            return null;
        }
    }

    /**
     * 获取异常信息
     */
    private String getExceptionInfo(Throwable exception) {
        if (exception == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(exception.getClass().getSimpleName()).append(": ");
        sb.append(exception.getMessage());
        
        // 限制异常信息长度
        String exceptionInfo = sb.toString();
        if (exceptionInfo.length() > 2000) {
            exceptionInfo = exceptionInfo.substring(0, 2000) + "...";
        }
        
        return exceptionInfo;
    }

    /**
     * 判断是否为需要忽略的参数类型
     */
    private boolean isIgnoredParam(Object param) {
        if (param == null) {
            return true;
        }
        
        String className = param.getClass().getName();
        
        // 忽略Servlet相关对象
        return className.startsWith("javax.servlet") ||
               className.startsWith("org.springframework.web") ||
               className.startsWith("org.springframework.ui");
    }
}
