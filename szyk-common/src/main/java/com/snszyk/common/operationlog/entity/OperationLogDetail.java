package com.snszyk.common.operationlog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作日志详情实体
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("sys_operation_log_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作日志详情", description = "操作日志详情实体")
public class OperationLogDetail extends BaseCrudEntity {

    /**
     * 操作日志ID
     */
    @ApiModelProperty(value = "操作日志ID")
    private Long logId;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段标签
     */
    @ApiModelProperty(value = "字段标签")
    private String fieldLabel;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值")
    private String oldValue;

    /**
     * 新值
     */
    @ApiModelProperty(value = "新值")
    private String newValue;

    /**
     * 旧值显示
     */
    @ApiModelProperty(value = "旧值显示")
    private String oldValueDisplay;

    /**
     * 新值显示
     */
    @ApiModelProperty(value = "新值显示")
    private String newValueDisplay;
}
