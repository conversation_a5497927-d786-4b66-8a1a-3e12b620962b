package com.snszyk.common.operationlog.annotation;

import com.snszyk.common.operationlog.enums.BusinessModuleEnum;
import com.snszyk.common.operationlog.enums.OperationTypeEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 业务模块
     */
    BusinessModuleEnum businessModule();

    /**
     * 操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default false;

    /**
     * 是否记录异常信息
     */
    boolean logException() default true;

    /**
     * 业务ID的SpEL表达式
     * 例如: "#id" 或 "#dto.id"
     */
    String businessIdExpression() default "";

    /**
     * 业务名称的SpEL表达式
     * 例如: "#dto.systemName"
     */
    String businessNameExpression() default "";

    /**
     * 需要比较的字段列表
     * 用于记录字段变更详情
     */
    String[] compareFields() default {};
}
