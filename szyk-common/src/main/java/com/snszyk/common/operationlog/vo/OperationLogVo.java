package com.snszyk.common.operationlog.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志查询对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作日志VO", description = "操作日志查询对象")
public class OperationLogVo extends BaseCrudVo {

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDescription;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作人部门ID
     */
    @ApiModelProperty(value = "操作人部门ID")
    private Long operatorDeptId;

    /**
     * 操作人部门名称
     */
    @ApiModelProperty(value = "操作人部门名称")
    private String operatorDeptName;

    /**
     * 变更前数据
     */
    @ApiModelProperty(value = "变更前数据")
    private String oldData;

    /**
     * 变更后数据
     */
    @ApiModelProperty(value = "变更后数据")
    private String newData;

    /**
     * 变更字段
     */
    @ApiModelProperty(value = "变更字段")
    private String changeFields;

    /**
     * 客户端IP
     */
    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    /**
     * 请求URI
     */
    @ApiModelProperty(value = "请求URI")
    private String requestUri;

    /**
     * 请求方法
     */
    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    /**
     * 执行时间(毫秒)
     */
    @ApiModelProperty(value = "执行时间(毫秒)")
    private Long executionTime;

    /**
     * 操作结果
     */
    @ApiModelProperty(value = "操作结果")
    private String operationResult;

    /**
     * 异常信息
     */
    @ApiModelProperty(value = "异常信息")
    private String exceptionInfo;
}
