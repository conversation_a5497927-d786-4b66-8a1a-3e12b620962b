package com.snszyk.common.operationlog.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务模块枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum BusinessModuleEnum {

    /**
     * 互联网资产台账
     */
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产台账"),

    /**
     * 设备台账
     */
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备台账"),

    /**
     * 信息系统
     */
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessModuleEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BusinessModuleEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举，如果不存在则抛出异常
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessModuleEnum fromCode(String code) {
        BusinessModuleEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("未知的业务模块编码: " + code);
        }
        return result;
    }
}
