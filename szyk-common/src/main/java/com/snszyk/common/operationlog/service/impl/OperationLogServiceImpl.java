package com.snszyk.common.operationlog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.operationlog.dto.OperationLogDetailDto;
import com.snszyk.common.operationlog.dto.OperationLogDto;
import com.snszyk.common.operationlog.entity.OperationLog;
import com.snszyk.common.operationlog.entity.OperationLogDetail;
import com.snszyk.common.operationlog.mapper.OperationLogDetailMapper;
import com.snszyk.common.operationlog.mapper.OperationLogMapper;
import com.snszyk.common.operationlog.service.IOperationLogService;
import com.snszyk.common.operationlog.vo.OperationLogPageVo;
import com.snszyk.common.operationlog.vo.OperationLogVo;
import com.snszyk.core.tool.utils.BeanUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 操作日志服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

    private final OperationLogMapper operationLogMapper;
    private final OperationLogDetailMapper operationLogDetailMapper;

    @Override
    @Async
    public void saveAsync(OperationLogVo logVo) {
        try {
            save(logVo);
        } catch (Exception e) {
            log.error("异步保存操作日志失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(OperationLogVo logVo) {
        try {
            // 转换并保存主记录
            OperationLog operationLog = BeanUtil.copy(logVo, OperationLog.class);
            boolean saved = save(operationLog);
            
            if (saved && logVo.getDetailList() != null && !logVo.getDetailList().isEmpty()) {
                // 保存详情记录
                List<OperationLogDetail> detailList = new ArrayList<>();
                for (var detailVo : logVo.getDetailList()) {
                    OperationLogDetail detail = BeanUtil.copy(detailVo, OperationLogDetail.class);
                    detail.setLogId(operationLog.getId());
                    detailList.add(detail);
                }
                operationLogDetailMapper.batchInsert(detailList);
            }
            
            return saved;
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
            return false;
        }
    }

    @Override
    public IPage<OperationLogDto> pageList(OperationLogPageVo vo) {
        Page<OperationLogDto> page = new Page<>(vo.getCurrent(), vo.getSize());
        return operationLogMapper.pageList(page, vo);
    }

    @Override
    public List<OperationLogDto> listByBusinessId(String businessModule, Long businessId) {
        return operationLogMapper.listByBusinessId(businessModule, businessId);
    }

    @Override
    public OperationLogDto getDetailWithChanges(Long logId) {
        OperationLogDto logDto = operationLogMapper.getDetailWithChanges(logId);
        if (logDto != null) {
            List<OperationLogDetailDto> detailList = operationLogDetailMapper.listByLogId(logId);
            logDto.setDetailList(detailList);
        }
        return logDto;
    }

    @Override
    public List<String> compareObjectChanges(Object oldObj, Object newObj, String[] compareFields) {
        List<String> changedFields = new ArrayList<>();
        
        if (oldObj == null || newObj == null) {
            return changedFields;
        }
        
        try {
            Class<?> clazz = oldObj.getClass();
            
            for (String fieldName : compareFields) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    
                    Object oldValue = field.get(oldObj);
                    Object newValue = field.get(newObj);
                    
                    if (!Objects.equals(oldValue, newValue)) {
                        changedFields.add(fieldName);
                    }
                } catch (NoSuchFieldException e) {
                    log.warn("字段不存在: {}", fieldName);
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段: {}", fieldName);
                }
            }
        } catch (Exception e) {
            log.error("比较对象变更失败", e);
        }
        
        return changedFields;
    }

    @Override
    public List<OperationLogDetailDto> buildChangeDetails(Object oldObj, Object newObj, String[] compareFields) {
        List<OperationLogDetailDto> detailList = new ArrayList<>();
        
        if (oldObj == null || newObj == null) {
            return detailList;
        }
        
        try {
            Class<?> clazz = oldObj.getClass();
            
            for (String fieldName : compareFields) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    
                    Object oldValue = field.get(oldObj);
                    Object newValue = field.get(newObj);
                    
                    if (!Objects.equals(oldValue, newValue)) {
                        OperationLogDetailDto detail = new OperationLogDetailDto();
                        detail.setFieldName(fieldName);
                        detail.setFieldDisplayName(getFieldDisplayName(fieldName));
                        detail.setOldValue(oldValue != null ? oldValue.toString() : null);
                        detail.setNewValue(newValue != null ? newValue.toString() : null);
                        detail.setOldDisplayValue(formatDisplayValue(oldValue));
                        detail.setNewDisplayValue(formatDisplayValue(newValue));
                        
                        detailList.add(detail);
                    }
                } catch (NoSuchFieldException e) {
                    log.warn("字段不存在: {}", fieldName);
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段: {}", fieldName);
                }
            }
        } catch (Exception e) {
            log.error("构建变更详情失败", e);
        }
        
        return detailList;
    }

    @Override
    public boolean deleteByBusinessId(String businessModule, Long businessId) {
        try {
            // 删除详情记录
            List<OperationLogDto> logList = listByBusinessId(businessModule, businessId);
            for (OperationLogDto log : logList) {
                operationLogDetailMapper.selectByMap(
                    java.util.Map.of("log_id", log.getId())
                ).forEach(detail -> operationLogDetailMapper.deleteById(detail.getId()));
            }
            
            // 删除主记录
            return removeByMap(java.util.Map.of(
                "business_module", businessModule,
                "business_id", businessId
            ));
        } catch (Exception e) {
            log.error("删除业务操作日志失败", e);
            return false;
        }
    }

    @Override
    public boolean cleanExpiredLogs(int retentionDays) {
        try {
            // 这里可以实现清理过期日志的逻辑
            // 例如删除超过指定天数的日志记录
            log.info("清理{}天前的操作日志", retentionDays);
            return true;
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return false;
        }
    }

    @Override
    public long countByBusinessModule(String businessModule) {
        return count(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<OperationLog>()
            .eq(OperationLog::getBusinessModule, businessModule));
    }

    @Override
    public long countByOperationType(String operationType) {
        return count(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<OperationLog>()
            .eq(OperationLog::getOperationType, operationType));
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        // 这里可以根据字段名称返回对应的中文显示名称
        // 可以从配置文件或数据库中获取
        switch (fieldName) {
            case "systemName": return "系统名称";
            case "internetAddress": return "互联网地址";
            case "networkAddress": return "网络地址";
            case "domainName": return "域名";
            case "internetType": return "互联网类型";
            case "applicationType": return "应用类型";
            case "resourceStatus": return "资源状态";
            case "securityLevel": return "安全等级";
            case "contactPerson": return "联系人";
            case "contactPhone": return "联系电话";
            default: return fieldName;
        }
    }

    /**
     * 格式化显示值
     */
    private String formatDisplayValue(Object value) {
        if (value == null) {
            return null;
        }
        
        // 这里可以根据值的类型进行格式化
        // 例如日期格式化、枚举值转换等
        return value.toString();
    }
}
