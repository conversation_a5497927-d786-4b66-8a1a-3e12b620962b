package com.snszyk.common.operationlog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.operationlog.dto.OperationLogDto;
import com.snszyk.common.operationlog.service.IOperationLogService;
import com.snszyk.common.operationlog.vo.OperationLogPageVo;
import com.snszyk.core.tool.api.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common/operationlog")
@Api(value = "操作日志管理", tags = "操作日志管理接口")
public class OperationLogController {

    private final IOperationLogService operationLogService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询操作日志", notes = "传入查询条件")
    @ApiOperationSupport(order = 1)
    public R<IPage<OperationLogDto>> page(@RequestBody OperationLogPageVo vo) {
        IPage<OperationLogDto> pages = operationLogService.pageList(vo);
        return R.data(pages);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "查询操作日志详情", notes = "传入日志ID")
    @ApiOperationSupport(order = 2)
    public R<OperationLogDto> detail(@ApiParam(value = "日志ID", required = true) @PathVariable Long id) {
        OperationLogDto detail = operationLogService.getDetailWithChanges(id);
        return R.data(detail);
    }

    @GetMapping("/listByBusiness")
    @ApiOperation(value = "根据业务ID查询操作日志", notes = "传入业务模块和业务ID")
    @ApiOperationSupport(order = 3)
    public R<List<OperationLogDto>> listByBusiness(
        @ApiParam(value = "业务模块", required = true) @RequestParam String businessModule,
        @ApiParam(value = "业务ID", required = true) @RequestParam Long businessId) {
        List<OperationLogDto> list = operationLogService.listByBusinessId(businessModule, businessId);
        return R.data(list);
    }

    @GetMapping("/countByModule")
    @ApiOperation(value = "统计业务模块操作日志数量", notes = "传入业务模块")
    @ApiOperationSupport(order = 4)
    public R<Long> countByModule(@ApiParam(value = "业务模块", required = true) @RequestParam String businessModule) {
        long count = operationLogService.countByBusinessModule(businessModule);
        return R.data(count);
    }

    @GetMapping("/countByType")
    @ApiOperation(value = "统计操作类型日志数量", notes = "传入操作类型")
    @ApiOperationSupport(order = 5)
    public R<Long> countByType(@ApiParam(value = "操作类型", required = true) @RequestParam String operationType) {
        long count = operationLogService.countByOperationType(operationType);
        return R.data(count);
    }

    @DeleteMapping("/deleteByBusiness")
    @ApiOperation(value = "删除业务相关操作日志", notes = "传入业务模块和业务ID")
    @ApiOperationSupport(order = 6)
    public R<Boolean> deleteByBusiness(
        @ApiParam(value = "业务模块", required = true) @RequestParam String businessModule,
        @ApiParam(value = "业务ID", required = true) @RequestParam Long businessId) {
        boolean result = operationLogService.deleteByBusinessId(businessModule, businessId);
        return R.data(result);
    }

    @PostMapping("/cleanExpired")
    @ApiOperation(value = "清理过期日志", notes = "传入保留天数")
    @ApiOperationSupport(order = 7)
    public R<Boolean> cleanExpired(@ApiParam(value = "保留天数", required = true) @RequestParam int retentionDays) {
        boolean result = operationLogService.cleanExpiredLogs(retentionDays);
        return R.data(result);
    }
}
