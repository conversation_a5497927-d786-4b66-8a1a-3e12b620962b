package com.snszyk.common.operationlog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.common.operationlog.dto.OperationLogDetailDto;
import com.snszyk.common.operationlog.entity.OperationLogDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志详情Mapper接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Repository
public interface OperationLogDetailMapper extends BaseMapper<OperationLogDetail> {

    /**
     * 根据日志ID查询详情列表
     *
     * @param logId 日志ID
     * @return 详情列表
     */
    List<OperationLogDetailDto> listByLogId(@Param("logId") Long logId);

    /**
     * 批量保存详情
     *
     * @param detailList 详情列表
     * @return 影响行数
     */
    int batchInsert(@Param("detailList") List<OperationLogDetail> detailList);
}
