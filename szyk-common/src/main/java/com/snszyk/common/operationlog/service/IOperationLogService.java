package com.snszyk.common.operationlog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.operationlog.dto.OperationLogDto;
import com.snszyk.common.operationlog.entity.OperationLog;
import com.snszyk.common.operationlog.enums.BusinessModuleEnum;
import com.snszyk.common.operationlog.vo.OperationLogPageVo;
import com.snszyk.common.operationlog.vo.OperationLogVo;
import com.snszyk.core.crud.service.IBaseCrudService;

import java.util.List;

/**
 * 操作日志服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface IOperationLogService extends IBaseCrudService<OperationLog, OperationLogDto, OperationLogVo> {

    /**
     * 分页查询操作日志
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<OperationLogDto> pageList(OperationLogPageVo vo);

    /**
     * 异步保存操作日志
     *
     * @param vo 操作日志VO
     */
    void saveAsync(OperationLogVo vo);

    /**
     * 获取操作日志详情（包含变更详情）
     *
     * @param logId 日志ID
     * @return 操作日志详情
     */
    OperationLogDto getDetailWithChanges(Long logId);

    /**
     * 根据业务ID查询操作日志
     *
     * @param businessModule 业务模块
     * @param businessId     业务ID
     * @return 操作日志列表
     */
    List<OperationLogDto> listByBusinessId(BusinessModuleEnum businessModule, Long businessId);

    /**
     * 获取业务数据
     *
     * @param businessModule 业务模块
     * @param businessId     业务ID
     * @return 业务数据
     */
    Object getBusinessData(BusinessModuleEnum businessModule, Long businessId);

    /**
     * 比较对象变更
     *
     * @param oldObject    旧对象
     * @param newObject    新对象
     * @param compareFields 比较字段
     * @return 变更字段列表
     */
    List<String> compareObjectChanges(Object oldObject, Object newObject, String[] compareFields);

    /**
     * 记录操作日志
     *
     * @param businessModule      业务模块
     * @param operationType       操作类型
     * @param businessId          业务ID
     * @param businessName        业务名称
     * @param operationDescription 操作描述
     * @param oldData             变更前数据
     * @param newData             变更后数据
     * @param changeFields        变更字段
     */
    void recordOperation(BusinessModuleEnum businessModule, String operationType, Long businessId,
                        String businessName, String operationDescription, Object oldData,
                        Object newData, String[] changeFields);
}
