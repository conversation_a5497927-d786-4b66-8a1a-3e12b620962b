package com.snszyk.common.config;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * 描述: 通用ObjectMapper配置 </br>
 * 时间: 2023-02-02 9:13  </br>
 * 作者：
 */
@Configuration
public class CommonObjectMapperConfig {

	//时间格式数组
	private static final List<String> formarts = new ArrayList<>(4);
	//年月格式
	private static final String YYYY_MM = "yyyy-MM";
	//年月日格式
	private static final String YYYY_MM_DD = "yyyy-MM-dd";
	//年月日时分格式
	private static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
	//年月日时分秒格式
	private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	//初始加载
	static{
		formarts.add(YYYY_MM);
		formarts.add(YYYY_MM_DD);
		formarts.add(YYYY_MM_DD_HH_MM);
		formarts.add(YYYY_MM_DD_HH_MM_SS);
	}

	//ymal配置，不配置就走默认值
	@Value("${spring.jackson.date-format:yyyy-MM-dd HH:mm:ss}")
	private String pattern;
	@Value("${spring.jackson.time-zone:GMT+8}")
	private String timeZone;


	@Bean
	public ObjectMapper objectMapper() {
		ObjectMapper objectMapper = new ObjectMapper();
		//设置时区
		objectMapper.setTimeZone(TimeZone.getTimeZone(timeZone));
		objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		//关闭日期序列化为时间戳的功能
		objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
		//关闭序列化的时候没有为属性找到getter方法,报错
		objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		//序列化的时候序列对象的所有属性
		objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
		//空值不序列化
		//objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		//序列化空对象不抛异常
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		//关闭反序列化的时候，没有找到属性的setter报错
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
		//反序列化未知属性不抛出异常
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		//自定义序列化：key-序列化字段类型,value-序列化所用对象,支持自定义及Jackson自带序列化器
		SimpleModule module = new SimpleModule();

		/**
		 * 序列换成json时,将所有的long变成string
		 * 因为js中得数字类型不能包含所有的java long值
		 */
		/*SimpleModule simpleModule = new SimpleModule();
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
		simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);*/


		String[] patternArr = pattern.split(" ");
		module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(pattern)));
		module.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(patternArr[0])));
		module.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(patternArr[1])));
		module.addSerializer(Date.class, new DateSerializer(false,new SimpleDateFormat(pattern)));
		//自定义反序列化：key-序列化字段类型,value-序列化所用对象,支持自定义及Jackson自带序列化器
		module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(pattern)));
		module.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(patternArr[0])));
		module.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(patternArr[1])));
		//设置全局反序列化，string -> date
		module.addDeserializer(Date.class, new DateDeserializers.DateDeserializer(){
			@SneakyThrows
			@Override
			public Date deserialize(JsonParser jsonParser, DeserializationContext ctxt){
				if (ObjectUtils.isEmpty(jsonParser.getText())){
					return null;
				}
				//获取序列文本
				String text = jsonParser.getText().trim();
				String timeFormat = getTimeFormat(text);
				//进行时间格式化
				SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
				return sdf.parse(text);
			}
		});
		objectMapper.registerModule(module);
		return objectMapper;
	}


	/**
	 * 正则配置获取时间格式 formart
	 * @param source 时间文本字符串
	 * @return String - 格式化格式
	 */
	public String getTimeFormat(String source) {
		if (StringUtils.isBlank(source)) {
			return null;
		}
		if(source.matches("^\\d{4}-\\d{1,2}$")){
			return formarts.get(0);
		}else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2}$")){
			return formarts.get(1);
		}else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}$")){
			return  formarts.get(2);
		}else if(source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}:\\d{1,2}$")){
			return formarts.get(3);
		}else {
			throw new IllegalArgumentException("Invalid false value '" + source + "'");
		}
	}

}

