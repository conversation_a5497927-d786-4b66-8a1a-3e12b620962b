package com.snszyk.common.config;

import com.snszyk.common.filter.PreviewFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 演示配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(value = "szyk.preview.enabled", havingValue = "true")
public class SzykPreviewConfiguration {

	/**
	 * 演示模式配置
	 */
	@Bean
	public PreviewFilter previewFilter() {
		return new PreviewFilter();
	}


}
