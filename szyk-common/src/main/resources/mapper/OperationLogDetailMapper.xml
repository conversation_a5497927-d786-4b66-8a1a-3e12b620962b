<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.common.operationlog.mapper.OperationLogDetailMapper">

    <!-- 结果映射 -->
    <resultMap id="OperationLogDetailDtoMap" type="com.snszyk.common.operationlog.dto.OperationLogDetailDto">
        <id column="id" property="id"/>
        <result column="log_id" property="logId"/>
        <result column="field_name" property="fieldName"/>
        <result column="field_display_name" property="fieldDisplayName"/>
        <result column="old_value" property="oldValue"/>
        <result column="new_value" property="newValue"/>
        <result column="old_display_value" property="oldDisplayValue"/>
        <result column="new_display_value" property="newDisplayValue"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据日志ID查询详情列表 -->
    <select id="listByLogId" resultMap="OperationLogDetailDtoMap">
        SELECT 
            id, log_id, field_name, field_display_name,
            old_value, new_value, old_display_value, new_display_value,
            create_time, update_time
        FROM sys_operation_log_detail
        WHERE log_id = #{logId}
        ORDER BY id ASC
    </select>

    <!-- 批量插入详情 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_operation_log_detail (
            log_id, field_name, field_display_name,
            old_value, new_value, old_display_value, new_display_value,
            create_time, update_time
        ) VALUES
        <foreach collection="detailList" item="item" separator=",">
            (
                #{item.logId}, #{item.fieldName}, #{item.fieldDisplayName},
                #{item.oldValue}, #{item.newValue}, #{item.oldDisplayValue}, #{item.newDisplayValue},
                NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper>
