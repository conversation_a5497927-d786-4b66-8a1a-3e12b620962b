-- 操作日志相关表结构
-- 适用于SZYK项目的通用操作日志系统

-- 1. 操作日志主表
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_module` varchar(50) NOT NULL COMMENT '业务模块',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务数据ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务数据名称',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
  `operation_description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint(20) DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(100) DEFAULT NULL COMMENT '操作人部门名称',
  `old_data` longtext COMMENT '变更前数据(JSON格式)',
  `new_data` longtext COMMENT '变更后数据(JSON格式)',
  `change_fields` varchar(1000) DEFAULT NULL COMMENT '变更字段(逗号分隔)',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_uri` varchar(200) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间(毫秒)',
  `operation_result` varchar(20) DEFAULT 'SUCCESS' COMMENT '操作结果(SUCCESS/FAILURE)',
  `exception_info` text COMMENT '异常信息',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_business_module` (`business_module`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operator_dept_id` (`operator_dept_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 2. 操作日志详情表
DROP TABLE IF EXISTS `sys_operation_log_detail`;
CREATE TABLE `sys_operation_log_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_id` bigint(20) NOT NULL COMMENT '操作日志ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(100) DEFAULT NULL COMMENT '字段标签',
  `field_type` varchar(20) DEFAULT 'STRING' COMMENT '字段类型',
  `old_value` text COMMENT '旧值',
  `new_value` text COMMENT '新值',
  `old_value_display` text COMMENT '旧值显示',
  `new_value_display` text COMMENT '新值显示',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_log_id` (`log_id`),
  KEY `idx_field_name` (`field_name`),
  CONSTRAINT `fk_operation_log_detail_log_id` FOREIGN KEY (`log_id`) REFERENCES `sys_operation_log` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志详情表';

-- 初始化数据：互联网资产台账字段映射配置
INSERT INTO `sys_operation_log_detail` (`id`, `log_id`, `field_name`, `field_label`, `field_type`, `old_value`, `new_value`, `old_value_display`, `new_value_display`) VALUES
(1, 0, 'systemName', '系统名称', 'STRING', NULL, NULL, NULL, NULL),
(2, 0, 'internetAddress', '互联网地址', 'STRING', NULL, NULL, NULL, NULL),
(3, 0, 'systemType', '系统类型', 'DICT', NULL, NULL, NULL, NULL),
(4, 0, 'importance', '重要程度', 'DICT', NULL, NULL, NULL, NULL),
(5, 0, 'status', '状态', 'DICT', NULL, NULL, NULL, NULL),
(6, 0, 'description', '描述', 'TEXT', NULL, NULL, NULL, NULL),
(7, 0, 'responsiblePerson', '负责人', 'STRING', NULL, NULL, NULL, NULL),
(8, 0, 'responsibleDept', '负责部门', 'STRING', NULL, NULL, NULL, NULL),
(9, 0, 'maintainPerson', '维护人', 'STRING', NULL, NULL, NULL, NULL),
(10, 0, 'maintainDept', '维护部门', 'STRING', NULL, NULL, NULL, NULL);

-- 删除初始化数据（这些数据仅用于字段映射配置参考）
DELETE FROM `sys_operation_log_detail` WHERE `log_id` = 0;

-- 创建索引优化查询性能
CREATE INDEX `idx_business_module_id` ON `sys_operation_log` (`business_module`, `business_id`);
CREATE INDEX `idx_operation_time_desc` ON `sys_operation_log` (`operation_time` DESC);
CREATE INDEX `idx_operator_time` ON `sys_operation_log` (`operator_id`, `operation_time`);

-- 添加表注释
ALTER TABLE `sys_operation_log` COMMENT = '系统操作日志表 - 记录各业务模块的操作行为';
ALTER TABLE `sys_operation_log_detail` COMMENT = '系统操作日志详情表 - 记录字段级别的变更详情';
