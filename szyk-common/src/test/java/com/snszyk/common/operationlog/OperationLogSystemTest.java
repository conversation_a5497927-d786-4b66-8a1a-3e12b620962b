package com.snszyk.common.operationlog;

import com.snszyk.common.operationlog.enums.BusinessModuleEnum;
import com.snszyk.common.operationlog.enums.OperationTypeEnum;
import com.snszyk.common.operationlog.service.IOperationLogService;
import com.snszyk.common.operationlog.vo.OperationLogVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

/**
 * 操作日志系统测试
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@SpringBootTest
public class OperationLogSystemTest {

    @Autowired
    private IOperationLogService operationLogService;

    @Test
    public void testSaveOperationLog() {
        // 创建测试操作日志
        OperationLogVo logVo = new OperationLogVo();
        logVo.setBusinessModule(BusinessModuleEnum.INTERNET_ASSET.getCode());
        logVo.setBusinessId(1L);
        logVo.setBusinessName("测试互联网资产");
        logVo.setOperationType(OperationTypeEnum.CREATE.getCode());
        logVo.setOperationDescription("测试创建互联网资产");
        logVo.setOperationTime(LocalDateTime.now());
        logVo.setOperationResult("SUCCESS");
        logVo.setOperatorId(1L);
        logVo.setOperatorName("测试用户");
        logVo.setOperatorDeptId(1L);
        logVo.setOperatorDeptName("测试部门");
        logVo.setClientIp("127.0.0.1");
        logVo.setRequestUri("/zbusiness/internet/save");
        logVo.setRequestMethod("POST");
        logVo.setExecutionTime(100L);

        // 保存日志
        boolean result = operationLogService.save(logVo);
        log.info("保存操作日志结果: {}", result);
        
        assert result : "操作日志保存失败";
    }

    @Test
    public void testCompareObjectChanges() {
        // 创建测试对象
        TestObject oldObj = new TestObject();
        oldObj.setName("旧名称");
        oldObj.setStatus(1);
        oldObj.setDescription("旧描述");

        TestObject newObj = new TestObject();
        newObj.setName("新名称");
        newObj.setStatus(2);
        newObj.setDescription("旧描述"); // 未变更

        // 比较变更
        String[] compareFields = {"name", "status", "description"};
        var changedFields = operationLogService.compareObjectChanges(oldObj, newObj, compareFields);
        
        log.info("变更字段: {}", changedFields);
        
        assert changedFields.size() == 2 : "应该有2个字段发生变更";
        assert changedFields.contains("name") : "name字段应该发生变更";
        assert changedFields.contains("status") : "status字段应该发生变更";
        assert !changedFields.contains("description") : "description字段不应该发生变更";
    }

    /**
     * 测试对象
     */
    public static class TestObject {
        private String name;
        private Integer status;
        private String description;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
