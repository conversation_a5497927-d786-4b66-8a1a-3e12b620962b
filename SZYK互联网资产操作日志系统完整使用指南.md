# SZYK互联网资产操作日志系统完整使用指南

## 1. 系统概述

SZYK互联网资产操作日志系统是一个专门为互联网资产管理模块设计的操作记录系统，能够自动记录用户对互联网资产的增删改操作，并提供详细的变更记录查询功能。

### 1.1 主要功能

- **自动记录操作日志**：通过AOP切面自动捕获互联网资产的操作
- **字段级变更追踪**：记录具体哪些字段发生了变更，包括变更前后的值
- **灵活查询功能**：支持按时间、操作人、组织等多维度查询
- **扩展性设计**：可轻松扩展到其他业务模块

### 1.2 技术架构

- **Spring AOP**：实现操作日志的自动记录
- **MyBatis-Plus**：数据持久化
- **JSON序列化**：数据变更记录
- **自定义注解**：标记需要记录日志的方法

## 2. 系统部署

### 2.1 数据库初始化

执行以下SQL脚本初始化数据库表：

```sql
-- 执行 database/operation_log_update.sql 脚本
source database/operation_log_update.sql;
```

### 2.2 依赖配置

确保项目中包含以下依赖：

```xml
<!-- Spring AOP -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>

<!-- Alibaba FastJSON -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
</dependency>

<!-- MyBatis-Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
</dependency>
```

### 2.3 配置启用AOP

在Spring Boot主配置类上添加注解：

```java
@SpringBootApplication
@EnableAspectJAutoProxy
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 3. 核心组件说明

### 3.1 注解定义

**@InternetAssetOperationLog**：用于标记需要记录操作日志的方法

```java
@InternetAssetOperationLog(
    operationType = "CREATE",           // 操作类型
    description = "创建互联网资产",      // 操作描述
    businessIdField = "id",             // 业务ID字段名
    businessNameField = "systemName",   // 业务名称字段名
    orgIdField = "orgId",              // 组织ID字段名
    orgNameField = "orgName",          // 组织名称字段名
    recordDetails = true               // 是否记录详细变更
)
```

### 3.2 枚举定义

**BusinessModuleEnum**：业务模块枚举
- INTERNET_ASSET：互联网资产台账
- EQUIPMENT_ASSET：设备台账
- INFORMATION_SYSTEM：信息系统

**OperationTypeEnum**：操作类型枚举
- CREATE：创建
- UPDATE：更新
- DELETE：删除
- QUERY：查询

### 3.3 核心服务

**IInternetAssetOperationLogService**：操作日志服务接口

主要方法：
- `page(vo)`：分页查询操作日志
- `getById(id)`：根据ID查询操作日志详情
- `getChangeDetails(id)`：获取变更详情
- `createOperationLog(...)`：创建操作日志

## 4. 使用方法

### 4.1 在业务方法上添加注解

#### 4.1.1 保存/更新操作

```java
@InternetAssetOperationLog(
    operationType = "UPDATE",
    description = "保存或更新互联网资产",
    businessIdField = "id",
    businessNameField = "systemName",
    orgIdField = "orgId",
    orgNameField = "orgName"
)
public R<RsInternetDto> saveOrUpdate(@RequestBody RsInternetVo vo) {
    // 业务逻辑
    return R.data(result);
}
```

#### 4.1.2 删除操作

```java
@InternetAssetOperationLog(
    operationType = "DELETE",
    description = "删除互联网资产",
    businessIdField = "id",
    businessNameField = "systemName",
    recordDetails = false  // 删除操作通常不需要记录详细变更
)
public List<RsSoftwareDeleteDto> delete(RsEquipmentDeleteVo vo) {
    // 删除逻辑
    return result;
}
```

### 4.2 查询操作日志

#### 4.2.1 分页查询

```java
@PostMapping("/operation-log/page")
public R<IPage<InternetAssetOperationLogDto>> pageOperationLog(
    @RequestBody InternetAssetOperationLogVo vo) {
    IPage<InternetAssetOperationLogDto> page = operationLogService.page(vo);
    return R.data(page);
}
```

#### 4.2.2 查询详情

```java
@GetMapping("/operation-log/detail/{id}")
public R<InternetAssetOperationLogDto> getOperationLogDetail(@PathVariable Long id) {
    InternetAssetOperationLogDto detail = operationLogService.getById(id);
    return R.data(detail);
}
```

#### 4.2.3 查询变更详情

```java
@GetMapping("/operation-log/change-details/{id}")
public R<List<FieldChangeDetailDto>> getChangeDetails(@PathVariable Long id) {
    List<FieldChangeDetailDto> details = operationLogService.getChangeDetails(id);
    return R.data(details);
}
```

### 4.3 查询参数说明

**InternetAssetOperationLogVo** 查询参数：

```java
{
    "current": 1,                    // 当前页
    "size": 10,                      // 页大小
    "systemName": "测试系统",         // 系统名称（模糊查询）
    "operationType": "UPDATE",       // 操作类型
    "operatorId": 1,                // 操作人ID
    "orgId": 1,                     // 组织ID
    "orgName": "测试组织",           // 组织名称
    "startTime": "2024-07-01",      // 开始时间
    "endTime": "2024-07-31"         // 结束时间
}
```

## 5. 字段映射配置

系统内置了互联网资产字段的中文映射，可在 `InternetAssetOperationLogServiceImpl` 中的 `FIELD_LABEL_MAP` 中配置：

```java
private static final Map<String, String> FIELD_LABEL_MAP = new HashMap<>();

static {
    FIELD_LABEL_MAP.put("systemName", "系统名称");
    FIELD_LABEL_MAP.put("internetType", "资源类型");
    FIELD_LABEL_MAP.put("applicationType", "应用类型");
    FIELD_LABEL_MAP.put("domainName", "域名或URL");
    FIELD_LABEL_MAP.put("internetAddress", "互联网地址");
    FIELD_LABEL_MAP.put("networkAddress", "内网地址");
    FIELD_LABEL_MAP.put("contactPerson", "联系人");
    FIELD_LABEL_MAP.put("contactPhone", "电话号码");
    FIELD_LABEL_MAP.put("resourceStatus", "资源状态");
    FIELD_LABEL_MAP.put("securityLevel", "等保级别");
    FIELD_LABEL_MAP.put("isPublicCloud", "是否公有云部署");
    FIELD_LABEL_MAP.put("publicCloudSupplier", "公有云供应商");
    FIELD_LABEL_MAP.put("remark", "备注");
}
```

## 6. API接口文档

### 6.1 分页查询操作日志

**接口地址**：`POST /zbusiness/internet-asset-operation-log/page`

**请求参数**：
```json
{
    "current": 1,
    "size": 10,
    "systemName": "测试系统",
    "operationType": "UPDATE",
    "startTime": "2024-07-01",
    "endTime": "2024-07-31"
}
```

**响应结果**：
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "businessModule": "INTERNET_ASSET",
                "businessModuleName": "互联网资产台账",
                "businessId": 1,
                "businessName": "测试系统",
                "operationType": "UPDATE",
                "operationTypeName": "修改",
                "operationDescription": "更新互联网资产",
                "operationTime": "2024-07-04 10:30:00",
                "operatorId": 1,
                "operatorName": "张三",
                "operatorDeptName": "信息部",
                "orgName": "测试组织"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    }
}
```

### 6.2 查询操作日志详情

**接口地址**：`GET /zbusiness/internet-asset-operation-log/detail/{id}`

**响应结果**：
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "businessModule": "INTERNET_ASSET",
        "businessId": 1,
        "businessName": "测试系统",
        "operationType": "UPDATE",
        "operationDescription": "更新互联网资产",
        "operationTime": "2024-07-04 10:30:00",
        "operatorName": "张三",
        "changeDetails": [
            {
                "fieldName": "systemName",
                "fieldLabel": "系统名称",
                "oldValue": "旧系统名称",
                "newValue": "新系统名称",
                "oldDisplayValue": "旧系统名称",
                "newDisplayValue": "新系统名称"
            }
        ]
    }
}
```

### 6.3 查询变更详情

**接口地址**：`GET /zbusiness/internet-asset-operation-log/change-details/{id}`

**响应结果**：
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "fieldName": "systemName",
            "fieldLabel": "系统名称",
            "oldValue": "旧系统名称",
            "newValue": "新系统名称",
            "oldDisplayValue": "旧系统名称",
            "newDisplayValue": "新系统名称"
        },
        {
            "fieldName": "internetType",
            "fieldLabel": "资源类型",
            "oldValue": "WEB",
            "newValue": "API",
            "oldDisplayValue": "网站",
            "newDisplayValue": "接口"
        }
    ]
}
```

## 7. 测试验证

### 7.1 单元测试

运行测试类验证功能：

```bash
mvn test -Dtest=InternetAssetOperationLogServiceTest
```

### 7.2 接口测试

使用测试控制器验证功能：

```bash
# 测试创建日志
curl -X POST http://localhost:8080/test/internet-asset-log/test-create \
  -H "Content-Type: application/json" \
  -d '{"id":1,"systemName":"测试系统","orgId":1,"orgName":"测试组织"}'

# 测试更新日志
curl -X POST http://localhost:8080/test/internet-asset-log/test-update \
  -H "Content-Type: application/json" \
  -d '{"id":1,"systemName":"测试系统-修改","orgId":1,"orgName":"测试组织"}'

# 测试删除日志
curl -X DELETE http://localhost:8080/test/internet-asset-log/test-delete/1
```

## 8. 扩展其他业务模块

### 8.1 添加新的业务模块

1. 在 `BusinessModuleEnum` 中添加新的业务模块：

```java
/**
 * 项目管理
 */
PROJECT_MANAGEMENT("PROJECT_MANAGEMENT", "项目管理");
```

2. 创建对应的操作日志服务：

```java
@Service
public class ProjectOperationLogServiceImpl implements IProjectOperationLogService {
    // 实现具体业务逻辑
}
```

3. 在业务方法上添加注解：

```java
@InternetAssetOperationLog(
    operationType = "CREATE",
    description = "创建项目",
    businessIdField = "id",
    businessNameField = "projectName"
)
public R<ProjectDto> saveProject(@RequestBody ProjectVo vo) {
    // 业务逻辑
}
```

### 8.2 自定义字段映射

为新业务模块配置字段映射：

```java
private static final Map<String, String> PROJECT_FIELD_LABEL_MAP = new HashMap<>();

static {
    PROJECT_FIELD_LABEL_MAP.put("projectName", "项目名称");
    PROJECT_FIELD_LABEL_MAP.put("projectCode", "项目编码");
    PROJECT_FIELD_LABEL_MAP.put("projectStatus", "项目状态");
    // 更多字段映射...
}
```

## 9. 注意事项

### 9.1 性能考虑

- 操作日志记录是异步执行的，不会影响主业务流程
- 大量数据变更时，建议定期清理历史日志
- 对于频繁查询的字段，已建立相应索引

### 9.2 数据安全

- 敏感字段不会记录在操作日志中
- 支持配置哪些字段需要记录变更详情
- 操作日志支持逻辑删除，不会物理删除数据

### 9.3 故障排查

- 如果操作日志未生成，检查AOP配置是否正确
- 如果字段变更未记录，检查字段映射配置
- 查看应用日志中的错误信息进行排查

## 10. 常见问题

### Q1: 为什么操作日志没有自动生成？

A: 请检查以下几点：
1. 确保在方法上添加了 `@InternetAssetOperationLog` 注解
2. 确保Spring AOP配置正确
3. 确保方法是通过Spring容器调用的（不是直接调用）

### Q2: 如何自定义字段显示名称？

A: 在 `FIELD_LABEL_MAP` 中添加字段映射：
```java
FIELD_LABEL_MAP.put("fieldName", "字段显示名称");
```

### Q3: 如何扩展到其他业务模块？

A: 参考第8章节的扩展指南，主要步骤：
1. 添加业务模块枚举
2. 创建对应的服务实现
3. 在业务方法上添加注解
4. 配置字段映射

## 11. 更新日志

- **v1.0.0** (2024-07-04)
  - 初始版本发布
  - 支持互联网资产操作日志记录
  - 支持字段级变更追踪
  - 提供完整的查询API

---

**技术支持**：如有问题请联系开发团队
**文档版本**：v1.0.0
**最后更新**：2024-07-04
