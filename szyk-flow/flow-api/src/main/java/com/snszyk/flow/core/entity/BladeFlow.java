package com.snszyk.flow.core.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 工作流通用实体类
 *
 * <AUTHOR>
 */
@Data
public class   BladeFlow implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 任务编号
	 */
	@ApiModelProperty("任务编号")
	private String taskId;
	/**
	 * 任务名称
	 */
	@ApiModelProperty("任务名称")
	private String taskName;
	/**
	 * 任务定义Key
	 */
	@ApiModelProperty("任务定义key")
	private String taskDefinitionKey;
	/**
	 * 任务执行人编号
	 */
	@ApiModelProperty("任务执行人编号")
	private String assignee;
	/**
	 * 任务执行人名称
	 */
	@ApiModelProperty("任务执行人名称")
	private String assigneeName;
	/**
	 * 流程分类
	 */
	@ApiModelProperty("任务分类id")
	private String category;
	/**
	 * 流程分类名
	 */
	@ApiModelProperty("任务分类")
	private String categoryName;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty("结束时间")
	private Date endTime;
	/**
	 * 签收时间
	 */
	@ApiModelProperty("签收时间")
	private Date claimTime;
	/**
	 * 历史任务结束时间
	 */
	@ApiModelProperty("历史任务结束时间")
	private Date historyTaskEndTime;
	/**
	 * 执行ID
	 */
	@ApiModelProperty("执行id")
	private String executionId;
	/**
	 * 流程实例ID
	 */
	@ApiModelProperty("流程实例id")
	private String processInstanceId;
	/**
	 * 流程ID
	 */
	@ApiModelProperty("流程id")
	private String processDefinitionId;
	/**
	 * 流程标识
	 */
	@ApiModelProperty("流程标识")
	private String processDefinitionKey;
	/**
	 * 流程名
	 */
	@ApiModelProperty("流程名")
	private String processDefinitionName;
	/**
	 * 流程版本
	 */
	@ApiModelProperty("流程版本")
	private int processDefinitionVersion;
	/**
	 * 流程说明
	 */
	@ApiModelProperty("流程说明")
	private String processDefinitionDesc;
	/**
	 * 流程简图名
	 */
	@ApiModelProperty("流程简图名")
	private String processDefinitionDiagramResName;
	/**
	 * 流程重命名
	 */
	@ApiModelProperty("流程重命名")
	private String processDefinitionResName;
	/**
	 * 历史任务流程实例ID 查看流程图会用到
	 */
	@ApiModelProperty("历史任务流程实例id 查看流程图会用到")
	private String historyProcessInstanceId;
	/**
	 * 流程实例是否结束
	 */
	@ApiModelProperty("流程实例是否结束")
	private String processIsFinished;
	/**
	 * 历史活动流程
	 */
	@ApiModelProperty("历史活动流程")
	private String historyActivityName;
	/**
	 * 历史活动耗时
	 */
	@ApiModelProperty("历史活动耗时")
	private String historyActivityDurationTime;
	/**
	 * 业务绑定Table
	 */
	@ApiModelProperty("业务绑定Table")
	private String businessTable;
	/**
	 * 业务绑定ID
	 */
	@ApiModelProperty("业务绑定Id")
	private String businessId;
	/**
	 * 任务状态
	 */
	@ApiModelProperty("任务状态")
	private String status;
	/**
	 * 任务意见
	 */
	@ApiModelProperty("任务意见")
	private String comment;
	/**
	 * 是否通过
	 */
	@ApiModelProperty("是否通过")
	private boolean isPass;
	/**
	 * 是否通过代号
	 */
	@ApiModelProperty("是否通过代号")
	private String flag;
	/**
	 * 开始查询日期
	 */
	@ApiModelProperty("开始查询日期")
	private Date beginDate;
	/**
	 * 结束查询日期
	 */
	@ApiModelProperty("结束时间查询")
	private Date endDate;
	/**
	 * 流程参数
	 */
	@ApiModelProperty("流程参数")
	private Map<String, Object> variables;

	/**
	 * 任务状态名
	 */
	@ApiModelProperty("任务状态名")
	private String statusName;

	/**
	 * 发起人name
	 */
	@ApiModelProperty("流程发起人Name")
	private String createUserName;

	/**
	 * 审批名称 表单value 模糊查询
	 */
	@ApiModelProperty("查询用")
	private String queryStr;

	/**
	 * 发起人头像
	 */
	@ApiModelProperty("流程发起人头像")
	private String createUserPhoto;

	/**
	 * 发起人部门
	 */
	@ApiModelProperty("流程发起人部门")
	private String createUserDept;

	/**
	 * 0  需我审批
	 * 1  我已审批
	 * 2  我发起的
	 * 3  抄送我的
	 */
	@ApiModelProperty("app查询列表类型标识")
	private String queryFlag;

	@ApiModelProperty("表单id")
	private Long formId;

	@ApiModelProperty("表单KV")
	private String formKv;

	@ApiModelProperty(value = "表单json系统key")
	private String sysKey;

	@ApiModelProperty(value = "表单json自定义key")
	private String customKey;
	@ApiModelProperty("发起人orgId")
	private Long orgId;

	@Getter
	@AllArgsConstructor
	public enum StatusEnum {
		//企业状态
		A(0,"审批中"),
		B(2,"审批通过"),
		C(3,"审批失败"),
		;
		final Integer code;
		final String desc;

		public static StatusEnum from(Integer code){
			for (StatusEnum statusEnum: StatusEnum.values()) {
				if(Objects.equals(statusEnum.code,code)){
					return statusEnum;
				}
			}
			return null;
		}
	}

}
