package com.snszyk.flow.core.feign;

import com.snszyk.core.tool.api.R;
import com.snszyk.flow.core.entity.BladeFlow;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 流程远程调用失败处理类
 *
 * <AUTHOR>
 */
@Component
public class IFlow<PERSON>lient<PERSON>allback implements IFlowClient {

	@Override
	public R<BladeFlow> startProcessInstanceById(String processDefinitionId, String businessKey, Map<String, Object> variables) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<BladeFlow> startProcessInstanceByKey(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
		return R.fail("远程调用失败");
	}

	@Override
	public R completeTask(String taskId, String processInstanceId, String comment, Map<String, Object> variables) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<Object> taskVariable(String taskId, String variableName) {
		return R.fail("远程调用失败");
	}

	@Override
	public R<Map<String, Object>> taskVariables(String taskId) {
		return R.fail("远程调用失败");
	}

}
