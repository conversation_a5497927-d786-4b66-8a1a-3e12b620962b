package com.snszyk.flow.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.snszyk.core.tool.utils.Func;
/**
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum ProcessEnum {

	/**
	 * 待审批
	 */
    stay("待审批", 4),

	/**
	 *审批中
	 */
    ING("审批中", 0),
	/**
	 *已通过
	 */
    PASS("已通过", 1),
	/**
	 *已拒绝
	 */
    NOT_PASS("已拒绝", 2),
	/**
	 *已撤回
	 */
    RECALL("已撤回", 3)
    ;

    final String name;
    final int mode;

    public static ProcessEnum form(int mode) {
        for (ProcessEnum processEnum : ProcessEnum.values()) {
            if (Func.equals(mode, processEnum.mode)) {
                return processEnum;
            }
        }
        return ING;
    }

}
