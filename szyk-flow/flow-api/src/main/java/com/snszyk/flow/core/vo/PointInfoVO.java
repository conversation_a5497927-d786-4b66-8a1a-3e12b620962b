package com.snszyk.flow.core.vo;/**
 * <AUTHOR>
 * @since 2020/10/15 14:09
 */

import com.snszyk.flow.vo.AuditJobVo;
import lombok.Data;


import java.util.List;

/**
 * <AUTHOR>
 *类描述:
 *创建人:86175
 *创建时间:2020/10/15 14:09
 *Version 1.0
 */
@Data
public class PointInfoVO {
    private String nodeName;
    private String nodeId;
    private String multiInstanceType;
    private String personScope;
    private List<AuditUserVO> auditUser;
    private AuditJobVo auditJob;

}
