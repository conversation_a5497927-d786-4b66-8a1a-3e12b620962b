package com.snszyk.flow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.snszyk.flow.core.entity.BladeFlow;
import com.snszyk.flow.core.entity.ProcFormRelation;

import java.util.List;
/**
 * <AUTHOR>
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProcFormRelationVO对象", description = "流程表单关联表")
public class ProcFromDetailVO extends ProcFormRelation {
    @ApiModelProperty(value = "操作记录")
    private List<BladeFlow> historyList;

    @ApiModelProperty(value = "抄送人")
    private List<FlowUserVO> informUsers;

    @ApiModelProperty(value = "状态str")
    private String statusStr;
}
