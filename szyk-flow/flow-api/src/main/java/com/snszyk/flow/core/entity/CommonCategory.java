package com.snszyk.flow.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/9/27 9:34
 * 分类表
 */
@ApiModel(value = "org-attila-flow-engine-entity-CommonCategory")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "attila_common_category")
public class CommonCategory extends BaseEntity {
	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.INPUT)
	@ApiModelProperty(value = "主键")
	private Long id;

	/**
	 * 模块名称
	 */
	@TableField(value = "module_name")
	@ApiModelProperty(value = "模块名称")
	private String moduleName;

	/**
	 * 一级类别名称
	 */
	@TableField(value = "level_first_name")
	@ApiModelProperty(value = "一级类别名称")
	private String levelFirstName;

	/**
	 * 二级类别名称
	 */
	@TableField(value = "level_second_name")
	@ApiModelProperty(value = "二级类别名称")
	private String levelSecondName;

	/**
	 * 0是系统1是自定义
	 */
	@TableField(value = "level")
	@ApiModelProperty(value = "0是系统1是自定义")
	private Integer level;


	@TableField(value = "first_id")
	@ApiModelProperty(value = "0是系统1是自定义")
	private Integer firstId;
}
