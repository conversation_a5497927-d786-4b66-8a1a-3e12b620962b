package com.snszyk.flow.process.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import com.snszyk.core.tool.utils.Func;

import java.io.Serializable;

/**
 * <AUTHOR> 审批列表我发起的列表查询条件
 */
@Data
public class MyLaunchPageQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发起开始时间2021-10-18 00:00")
    private String startTime;

    @ApiModelProperty("发起结束时间2021-10-18 00:00")
    private String endTime;

    @ApiModelProperty("完成开始时间2021-10-18 00:00")
    private String cStartTime;

    @ApiModelProperty("完成结束时间2021-10-18 00:00")
    private String cEndTime;

    @ApiModelProperty("1时间倒叙，2时间正序")
    private Integer seq;


    @ApiModelProperty("标题、正文检索条件")
    private String processName;

    @ApiModelProperty("审批分组ID")
    private Long groupId;

    @ApiModelProperty("审批定义id")
    private String definationId;

    @ApiModelProperty("审批状态")
    private Integer status;

    @ApiModelProperty("发起人")
    private Long createUser;

    @ApiModelProperty("组织机构ID")
    private Long orgId;

    @ApiModelProperty("查询端--QueryClientEnum")
    private Integer queryClient;

    public String getStartTime() {
        if(Func.isNotEmpty(startTime)){
            return startTime+":00";
        }
        return startTime;
    }

    public String getEndTime() {
        if(Func.isNotEmpty(endTime)){
            return endTime+":00";
        }
        return endTime;
    }

    public String getCStartTime() {
        if(Func.isNotEmpty(cStartTime)){
            return cStartTime+":00";
        }
        return cStartTime;
    }

    public String getCEndTime() {
        if(Func.isNotEmpty(cEndTime)){
            return cEndTime+":00";
        }
        return cEndTime;
    }
//    public Integer getSeq(){
//        if(Func.isNotEmpty(seq)){
//            return seq;
//        }
//        return 1;
//    }

    @Getter
    @AllArgsConstructor
    public enum QueryClientEnum {
		/**
		 * app查询
		 */
		APP(1,"app查询"),
		/**
		 *web查询
		 */
        WEB(2,"web查询");
        final Integer code;
        final String name;
    }
}
