package com.snszyk.flow.process.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 类名称:ProcessTurnTaskDTO
 * 类描述: 转交
 * 创建人:杨小琪
 * 创建时间:2021/3/2 10:32
 * Version 1.0
 */
@Data
public class ProcessTurnTaskDTO {
    @ApiModelProperty(value="flowable任务id")
    private String flowableTaskId;

    /**
     * 意见
     */
    @ApiModelProperty(value="意见")
    private String opinion;

    /**
     * 附件
     */
    @ApiModelProperty(value="附件")
    private String file;

    /**
     * 1,同意/2,拒绝 processInstanceStatus
     */
    private Integer status;


   /**
   *
    *  转交人员
    *
    *  **/
    private String userId;
}
