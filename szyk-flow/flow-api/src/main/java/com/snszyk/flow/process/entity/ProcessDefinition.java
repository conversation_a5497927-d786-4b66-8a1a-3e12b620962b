package com.snszyk.flow.process.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR> 流程定义表
 */
@ApiModel(value = "org-attila-flow-process-entity-ProcessDefinition")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "blade_process_definition")
public class ProcessDefinition extends TenantEntity {
	/**
	 * 数据id
	 */
	@TableField(value = "data_id")
	@ApiModelProperty(value = "数据id")
	private Long dataId;

	/**
	 * 流程部署id
	 */
	@TableField(value = "flowable_deploy_id")
	@ApiModelProperty(value = "流程部署id")
	private String flowableDeployId;

	/**
	 * 排序
	 */
	@TableField(value = "ranked")
	@ApiModelProperty(value = "排序")
	private Integer ranked;

	/**
	 * 图标
	 */
	@TableField(value = "icon")
	@ApiModelProperty(value = "图标")
	private String icon;

	/**
	 * 审批名称
	 */
	@TableField(value = "process_name")
	@ApiModelProperty(value = "审批名称")
	private String processName;

	/**
	 * 说明
	 */
	@TableField(value = "remark")
	@ApiModelProperty(value = "说明")
	private String remark;

	/**
	 * 分组id
	 */
	@TableField(value = "group_id")
	@ApiModelProperty(value = "分组id")
	private Long groupId;

	/**
	 * 发起人范围类型
	 * //1是指定全员
	 * //2是职务
	 * //3是部门
	 * //4是岗位
	 * //5是全员
	 */
	@TableField(value = "start_scope_type")
	@ApiModelProperty(value = "发起人范围类型")
	private Integer startScopeType;

	/**
	 * 发起人数据id
	 */
	@TableField(value = "start_scope_ids")
	@ApiModelProperty(value = "发起人数据id")
	private String startScopeIds;

	/**
	 * 发起人数据汉字
	 */
	@TableField(value = "start_detail_names")
	@ApiModelProperty(value = "发起人数据id")
	private String startDetailNames;


	/**
	 * 审批管理员
	 */
	@TableField(value = "admin_user_ids")
	@ApiModelProperty(value = "审批管理员")
	private String adminUserIds;

	/**
	 * 表单样式
	 */
	@TableField(value = "form_style_json")
	@ApiModelProperty(value = "表单样式")
	private String formStyleJson;

	/**
	 * 表单包含的控件组类型(6个控件组。可以为空)
	 */
	@TableField(value = "form_business_type")
	@ApiModelProperty(value = "表单包含的控件组类型")
	private Integer formBusinessType;

	/**
	 * 流程json 解析生成bpmnModel进行部署
	 */
	@TableField(value = "process_json")
	@ApiModelProperty(value = "流程json 解析生成bpmnModel进行部署")
	private String processJson;

	/**
	 * 流程是否包含条件分支,0是不包含，1包含条件
	 */
	@TableField(value = "has_condition")
	@ApiModelProperty(value = "流程是否包含条件分支")
	private Integer hasCondition;


	/**
	 * 抄送人返回样式
	 */
	@TableField(value = "persons_all")
	@ApiModelProperty(value = "抄送人返回样式")
	private String personsAll;

	/**
	 * 抄送人ids
	 */
	@TableField(value = "inform_user_ids")
	@ApiModelProperty(value = "抄送人ids")
	private String informUserIds;

	/**
	 * 抄送部门ids
	 */
	@TableField(value = "inform_dept_ids")
	@ApiModelProperty(value = "抄送部门ids")
	private String informDeptIds;

	/**
	 * 抄送岗位ids
	 */
	@TableField(value = "inform_post_ids")
	@ApiModelProperty(value = "抄送岗位ids")
	private String informPostIds;

	/**
	 * 抄送职务ids
	 */
	@TableField(value = "inform_job_ids")
	@ApiModelProperty(value = "抄送职务ids")
	private String informJobIds;

	/**
	 * 自定义逻辑删除标志,0是使用，1是删除
	 */
	@TableField(value = "flag")
	@ApiModelProperty(value = "逻辑删除")
	private int flag;
	/**
	 * 自定义逻辑删除标志,0是使用，1是删除
	 */
	@TableField(value = "info_message")
	@ApiModelProperty(value = "抄送人json（优化）")
	private String infoMessage;
	/**
	 * 自定义逻辑删除标志,0是使用，1是删除
	 */
	@TableField(value = "node_new_json")
	@ApiModelProperty(value = "流程定义样式（前端使用）")
	private String nodeNewJson;

	/**
	 * 是否使用表单 0否，1是
	 */
	@TableField(value = "is_form")
	@ApiModelProperty(value = "是否使用表单 0否，1是")
	private Integer isForm;

	@Getter
	@AllArgsConstructor
	public enum StatusEnum {
		/**
		 * 禁用
		 */
		STOP(0, "禁用"),
		/**
		 * 启用
		 */
		START(1, "启用");
		final Integer code;
		final String desc;

		public static ProcessDefinition.StatusEnum from(Integer code) {
			for (ProcessDefinition.StatusEnum statusEnum : ProcessDefinition.StatusEnum.values()) {
				if (Objects.equals(statusEnum.getCode(), code)) {
					return statusEnum;
				}
			}
			return null;
		}
	}

	@Getter
	@AllArgsConstructor
	public enum formBusinessTypeEnum {
		/**
		 *
		 */
		LEAVE(1, "leave"),
		/**
		 *
		 */
		CARD(2, "card"),
		/**
		 *
		 */
		OVERTIME(3, "overtime"),
		/**
		 *
		 */
		GO_OUT(4, "go_out"),
		/**
		 *
		 */
		BUSINESS_TRIP(5, "business_trip"),
		/**
		 *
		 */
		REGULAR(6, "regular"),
		/**
		 *
		 */
		DIMISSION(7, "dimission"),
		/**
		 *
		 */
		RESUME_LEAVE(8, "resume_leave"),
		//上会申请
		YD_SHSQ(11, "yd-shsq"),
		//盘点管理
		YD_PDGL(12, "yd-pdgl"),
		//合同
		YD_HT(13, "yd-ht"),
		//业务决策
		YD_YWJC(14, "yd-ywjc"),
		//授信
		YD_SX(15, "yd-sx"),
		//收款
		YD_SK(16, "yd-sk"),
		//付款
		YD_FK(17, "yd-fk"),
		//发票
		YD_FP(18, "yd-fp"),
		//非标准合同
		YD_FBZHT(19, "yd-fbzht"),
		//交易挂牌
		TRADE_CARD(30, "trade_card"),
		//交易报名
		TRADE_APPLY(31, "trade_apply"),
		//交易合同
		TRADE_CONTRACT(32, "trade_contract"),
		//竞卖-挂牌审批
		TRADE_SALE(33, "trade_sale"),
		//竞买-挂牌审批
		TRADE_BUY(34, "trade_buy"),
		//竞卖报名
		TRADE_SALE_SIGN(35, "trade_sale_sign"),
		//竞买报名
		TRADE_BUY_SIGN(36, "trade_buy_sign");


		public final Integer code;
		public final String desc;

		public static ProcessDefinition.formBusinessTypeEnum from(Integer code) {
			for (ProcessDefinition.formBusinessTypeEnum statusEnum : ProcessDefinition.formBusinessTypeEnum.values()) {
				if (Objects.equals(statusEnum.getCode(), code)) {
					return statusEnum;
				}
			}
			return null;
		}
	}

}
