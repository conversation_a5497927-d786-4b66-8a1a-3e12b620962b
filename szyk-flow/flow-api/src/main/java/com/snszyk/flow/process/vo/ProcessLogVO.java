package com.snszyk.flow.process.vo;

import com.snszyk.flow.process.entity.ProcessLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 审批过程编辑日志表视图实体类
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProcessLogVO对象", description = "审批过程编辑日志表")
public class ProcessLogVO extends ProcessLog {
	private static final long serialVersionUID = 1L;

}
