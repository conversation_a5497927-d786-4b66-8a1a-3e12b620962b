/*
 * Copyright (c) 2019. 九五数字科技(青岛)有限公司.  All rights reserved.
 */
package com.snszyk.flow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;
import java.util.Objects;

/**
 * @author: wangxin
 * date: 2020/9/29
 * desc:
 */
@Data
@ApiModel(value = "AuditNodeVo对象", description = "审核节点对象")
public class AuditNodeVo {

	@ApiModelProperty(value = "执行模式 1:单人 2:会签 3:或签")
	private String multiInstanceType;

	@ApiModelProperty(value = "审批范围 1:人 2:职务")
	private String personScope;

	@ApiModelProperty(value = "节点id")
	private String nodeId;

	@ApiModelProperty(value = "节点名称")
	private String nodeName;

	@ApiModelProperty(value = "审核人")
	private List<AuditUserVo> auditUser;

	@ApiModelProperty(value = "审核职务")
	private AuditJobVo auditJob;

	/**
	 * 审批范围 1:人 2:职务
	 */
	@Getter
	@AllArgsConstructor
	public enum PersonScopeEnum {
		/**
		 * 人
		 */
		PERSON("1", "人"),
		/**
		 * 职务
		 */
		JOB("2", "职务"),
		;

		final String code;

		final String desc;

		public static PersonScopeEnum from(String code) {
			for (PersonScopeEnum personScopeEnum : PersonScopeEnum.values()) {
				if (Objects.equals(personScopeEnum.code, code)) {
					return personScopeEnum;
				}
			}
			return null;
		}
	}

	/**
	 * 执行模式 1:单人 2:会签 3:或签
	 */
	@Getter
	@AllArgsConstructor
	public enum MultiInstanceTypeEnum {
		/**
		 * 单人
		 */
		SINGLE("1", "单人"),
		/**
		 * 会签
		 */
		COUNTER_SIGN("2", "会签"),
		/**
		 * 或签
		 */
		OR_SIGN("3", "或签"),
		;

		final String code;

		final String desc;

		public static MultiInstanceTypeEnum from(String code) {
			for (MultiInstanceTypeEnum multiInstanceTypeEnum : MultiInstanceTypeEnum.values()) {
				if (Objects.equals(multiInstanceTypeEnum.code, code)) {
					return multiInstanceTypeEnum;
				}
			}
			return null;
		}
	}
}
