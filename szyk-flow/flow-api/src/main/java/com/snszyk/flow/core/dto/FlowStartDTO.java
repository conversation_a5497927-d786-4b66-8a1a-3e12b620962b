package com.snszyk.flow.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工作流通用实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel("启动流程DTO")
public class FlowStartDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流程审核节点json
	 */
	@ApiModelProperty(value = "流程审核节点json")
	private String nodeInfo;

	@ApiModelProperty(value = "抄送人ids")
	private String informUserIds;

	/**
	 * 流程ID
	 */
	@ApiModelProperty("流程id")
	private String processDefinitionId;

	@ApiModelProperty("表单id")
	private Long formId;

	@ApiModelProperty("表单K-V")
	private String formKv;

	@ApiModelProperty(value = "业务数据ID")
	private String businessId;


//	@ApiModelProperty(value = "淄矿展示业务企业")
//	private LinkedList<String> procName;





}
