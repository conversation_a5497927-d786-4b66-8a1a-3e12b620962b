package com.snszyk.flow.process.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
@Data
@ApiModel(value = "MyTaskVO", description = "MyTaskVO")
public class MyTaskVO implements Serializable {

    @ApiModelProperty(value = "taskID")
    private String  id;

    @ApiModelProperty(value = "flowable processInstanceId")
    private String processInstanceId;

    @ApiModelProperty(value = "bladeInsId")
    private Long bladeInsId;

}
