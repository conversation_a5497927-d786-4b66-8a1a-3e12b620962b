package com.snszyk.flow.process.vo;

import com.snszyk.flow.process.entity.TestPluginForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
/**
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
public class ProcessInstanceAppDetailVO extends ProcessInstanceDetailVO{

    private Integer isTaskIdActive;

    private String flowableTaskId;
	/**
	 * 企业名称
	 */
    private String orgName;

    private Map<String,String> fieldStatus;


    private TestPluginForm testPluginForm;
}
