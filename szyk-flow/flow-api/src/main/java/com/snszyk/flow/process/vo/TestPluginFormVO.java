package com.snszyk.flow.process.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
@Data
public class TestPluginFormVO {

	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "流程id")
	private Long instanceId;

	@ApiModelProperty(value = "项目名称")
	private String proName;

	@ApiModelProperty(value = "开始时间")
	private Date startTime;

	@ApiModelProperty(value = "结束时间")
	private Date endTime;

	@ApiModelProperty(value = "备注")
	private String remark;

}
