package com.snszyk.flow.process.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR> 流程实例抄送关系表
 */
@ApiModel(value = "org-attila-flow-process-entity-ProcessInstanceInform")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "blade_process_instance_inform")
public class ProcessInstanceInform extends TenantEntity {
	/**
	 * 实例id
	 */
	@TableField(value = "instance_id")
	@ApiModelProperty(value = "实例id")
	private Long instanceId;

	/**
	 * 用户id
	 */
	@TableField(value = "user_id")
	@ApiModelProperty(value = "用户id")
	private Long userId;

	@Getter
	@AllArgsConstructor
	public enum StatusEnum {
		/**
		 * 未生效
		 */
		INVALID(0, "未生效"),
		/**
		 * 已读
		 */
		READ(1, "已读"),
		/**
		 * 未读
		 */
		UNREAD(2, "未读");

		final Integer code;
		final String desc;

		public static ProcessInstanceInform.StatusEnum from(Integer code) {
			for (ProcessInstanceInform.StatusEnum statusEnum : ProcessInstanceInform.StatusEnum.values()) {
				if (Objects.equals(statusEnum.getCode(), code)) {
					return statusEnum;
				}
			}
			return null;
		}
	}
}
