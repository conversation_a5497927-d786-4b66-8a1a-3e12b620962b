package com.snszyk.flow.process.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> 测试
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "test_plugin_form")
public class TestPluginForm extends BaseEntity {


	@TableField(value = "instance_id")
	@ApiModelProperty(value = "流程id")
	private Long instanceId;

	@TableField(value = "pro_name")
	@ApiModelProperty(value = "项目名称")
	private String proName;

	@TableField(value = "start_time")
	@ApiModelProperty(value = "开始时间")
	private Date startTime;

	@TableField(value = "end_time")
	@ApiModelProperty(value = "结束时间")
	private Date endTime;

	@TableField(value = "remark")
	@ApiModelProperty(value = "备注")
	private String remark;

}
