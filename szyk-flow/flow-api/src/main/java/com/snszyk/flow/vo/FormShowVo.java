/*
 * Copyright (c) 2019. 九五数字科技(青岛)有限公司.  All rights reserved.
 */
package com.snszyk.flow.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @author: wangxin
 * date: 2020/9/24
 * desc:
 */
@Data
@ApiModel(value = "FormShowVo对象", description = "表单回显")
public class FormShowVo {

//    @ApiModelProperty(value = "表单id")
//    @JsonSerialize(
//            using = ToStringSerializer.class
//    )
//    private Long formId;
//
//    @ApiModelProperty(value = "表单json系统key")
//    private String sysKey;
//
//    @ApiModelProperty(value = "表单json自定义key")
//    private String customKey;
//
//    @ApiModelProperty(value = "流程id")
//    private String procDefId;
//
//    @ApiModelProperty(value = "实例id")
//    private String procInstId;
//
//    @ApiModelProperty(value = "历史流转列表")
//    private List<BladeFlow> historyList;
//
//    /**
//     * 表单kv
//     */
//    @ApiModelProperty(value = "表单KV")
//    private String formKv;
//
//    /**
//     * 抄送人
//     */
//    @ApiModelProperty("抄送人列表")
//    private List<User> informUsers;
//
//    @ApiModelProperty("表单名")
//    private String formName;
//
//    @ApiModelProperty("状态")
//    private String status;
//
//    @ApiModelProperty("状态")
//    private String statusStr;
//
//    /**
//     * 发起人name
//     */
//    @ApiModelProperty("流程发起人Name")
//    private String createUserName;
//
//    @ApiModelProperty("部门名")
//    private String deptName;
//
//    @ApiModelProperty("创建人")
//    private Long createUser;
//
//    @ApiModelProperty("任务Id")
//    private String taskId;

}
