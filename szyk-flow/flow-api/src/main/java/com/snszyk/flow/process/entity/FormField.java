package com.snszyk.flow.process.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.snszyk.core.tenant.mp.TenantEntity;

import java.util.Objects;

/**
 * 审批流程表单字段可读属性表实体类
 *
 * <AUTHOR>
 * @since 2021-10-09
 */
@Data
@TableName("blade_form_field")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FormField对象", description = "审批流程表单字段可读属性表")
public class FormField extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 节点ID
	*/
		@ApiModelProperty(value = "节点ID")
		private String nodeId;
	/**
	* 流程定义ID
	*/
		@ApiModelProperty(value = "流程定义ID")
		private Long processDefinitionId;

		/**
		 * 流程实例ID
		 */
		@ApiModelProperty(value = "流程实例ID")
		private Long processInstanceId;



	/**
	 * dataID
	 */
	@ApiModelProperty(value = "dataId")
		private Long dataId;

	/**
	* 表单字段可读状态json
	*/
		@ApiModelProperty(value = "表单字段可读状态json")
		private String fieldJson;


	@Getter
	@AllArgsConstructor
	public enum FieldStatusEnum {

		/**
		 * 可编辑
		 */
		edit("0","可编辑"),
		/**
		 *只读
		 */
		read("1","只读"),
		/**
		 *隐藏
		 */
		hide("2","隐藏");
		final String code;
		final String name;

		public static FieldStatusEnum from(String code){
			for (FieldStatusEnum vo: FieldStatusEnum.values()) {
				if(Objects.equals(vo.code,code)){
					return vo;
				}
			}
			return null;
		}
	}
}
