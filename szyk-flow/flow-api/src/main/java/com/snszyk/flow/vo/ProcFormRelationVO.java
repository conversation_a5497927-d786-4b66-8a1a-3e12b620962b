package com.snszyk.flow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.snszyk.flow.core.entity.BladeFlow;
import com.snszyk.flow.core.entity.ProcFormRelation;

import java.util.List;

/**
 * 流程表单关联表视图实体类
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProcFormRelationVO对象", description = "流程表单关联表")
public class ProcFormRelationVO extends ProcFormRelation {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "操作记录")
	private List<BladeFlow> historyList;

	@ApiModelProperty(value = "抄送人")
	private List<FlowUserVO> informUsers;

	@ApiModelProperty(value = "状态str")
	private String statusStr;

	@ApiModelProperty(value = "发起人name")
	private String createUserName;

	@ApiModelProperty(value = "部门name")
	private String deptName;

	@ApiModelProperty(value = "任务id(app用)")
	private String taskId;

	@ApiModelProperty(value = "流程名称")
	private String processDefinitionName;

	@ApiModelProperty("modelId")
	private String modelId;

	/**
	 * 0 不需要展示按钮 1 需要
	 */
	@ApiModelProperty("是否展示需我审批按钮")
	private Integer approvalType;

	@ApiModelProperty("业务类型")
	private Long categoryId;


	@ApiModelProperty("发起人orgId")
	private Long orgId;


}
