package com.snszyk.flow.process.vo;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.process.enums.CommEnum;
import com.snszyk.flow.process.enums.CommNumberEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class FormKvUtil {

	/**
	 * 表单正文分隔符
	 */
	public static final String SPLIT_STR = "Φ";

	public static final String OLD_SPLIT_STR = "<br /> &nbsp;&nbsp;&nbsp;";


	/**
	 * 使用 styleFrom  dataJson 转换打印数据
	 *
	 * @param styleFrom
	 * @param dataJson
	 * @return
	 */
	public static List<FormKv.FromKv> exec(String styleFrom, String dataJson) {
		List<VueFormModel> vueFormModel = JSON.parseArray(styleFrom, VueFormModel.class);
		JSONObject object = JSON.parseObject(dataJson);
		if (object == null) {
			object = new JSONObject();
		}
		List<FormKv.FromKv> all = new ArrayList<>();
		for (VueFormModel formModel : vueFormModel) {
			List<FormKv.FromKv> tt = dealModel(formModel, object);
			all.addAll(tt);
		}
		return all;
	}


	private static List<FormKv.FromKv> dealModel(VueFormModel model, JSONObject valueObject) {
		List<FormKv.FromKv> all = new ArrayList<>();
		try {
			FormKv.FromKv fromKv;
			if (Func.equals(model.getType(), CommEnum.TEST.getDesc())) {
				//文本
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.INPUT.getDesc())) {
				// 文本
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.TEXTAREA.getDesc())) {
				//多行文本
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.TIPS.getDesc())) {
				//说明文字
				boolean noApply = model.getValueJson().getBooleanValue("noApply");
				if (!noApply) {
					fromKv = new FormKv.FromKv();
					fromKv.setType(model.getType());
					fromKv.setLable(model.getValueJson().getString("content"));
					String link = model.getValueJson().getString("link");
					if (Func.isNotBlank(link)) {
						fromKv.setValue(link);
					}

					FormKv.TipsExt ext = new FormKv.TipsExt();
					boolean fontColor = model.getValueJson().getBooleanValue("fontColor");
					ext.setFontColor(fontColor);
					fromKv.setExt(ext);
					all.add(fromKv);
				}

			} else if (Func.equals(model.getType(), CommEnum.INPUT_NUMBER)) {
				//数字输入框
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				if (Func.isNotBlank(value)) {
					String unit = model.getValueJson().getString("unit");
					if (Func.isNotBlank(unit)) {
						fromKv.setValue(fromKv.getValue().concat(" ").concat(unit));
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.INPUT_MONEY.getDesc())) {
				//金额
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONObject object = valueObject.getJSONObject(model.getId());
				if (Func.isNotEmpty(object)) {
					boolean capital = model.getValueJson().getBooleanValue("capital");
					String value = object.getString("value");
					String text = object.getString("text");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value.concat(" 元"));
						if (capital) {
							fromKv.setValue(fromKv.getValue().concat("<br /> &nbsp;&nbsp;&nbsp;").concat(text));
						}
					}

					if (Func.isNotBlank(value)) {
						FormKv.InputMoneyExt ext = new FormKv.InputMoneyExt();
						ext.setOldValue(value);
						if (capital) {
							ext.setWords(text);
						}
						fromKv.setExt(ext);
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.COMPUTED.getDesc())) {
				//计算公式
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.CHECK_BOX.getDesc())) {
				//多选框
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					List<String> array = JSON.parseArray(jsonArray.toJSONString(), String.class);
					StringBuffer buffer = new StringBuffer();
					for (String str : array) {
						buffer.append(str).append(",");
					}
					String value = buffer.toString();
					if (Func.isNotBlank(value)) {
						fromKv.setValue(StrUtil.removeSuffix(value, ","));
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.RADIO.getDesc())) {
				//单选框
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (CommEnum.REST1.getDesc().equals(model.getId())) {
					value = valueObject.getString("rest6");
				}
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.DATE.getDesc())) {
				//日期
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.DATERANGE.getDesc())) {
				JSONObject object = valueObject.getJSONObject(model.getId());
				//日期-时间段
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("nameOne"));
				if (Func.isNotEmpty(object)) {
					String value = object.getString("startTime");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value);
					}
				}
				all.add(fromKv);
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("nameTwo"));
				if (Func.isNotEmpty(object)) {
					String value = object.getString("endTime");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value);
					}
				}
				all.add(fromKv);

				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("nameThree"));
				if (Func.isNotEmpty(object)) {
					String value = object.getString("interval");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value);
					}
				}
				all.add(fromKv);

			} else if (Func.equals(model.getType(), CommEnum.IMAGE.getDesc())) {
				//图片
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					List<String> array = JSON.parseArray(jsonArray.toJSONString(), String.class);

					StringBuffer buffer = new StringBuffer();
					for (String str : array) {
						buffer.append(FileUtil.getName(str)).append("<br /> &nbsp;&nbsp;&nbsp;");
					}
					String value = buffer.toString();
					if (Func.isNotBlank(value)) {
						fromKv.setValue(StrUtil.removeSuffix(value, "<br /> &nbsp;&nbsp;&nbsp;"));
					}


					//列表值处理
					List<FormKv.FileExt.WorkFile> values = new ArrayList<>();
					for (String str : array) {
						FormKv.FileExt.WorkFile workFile = new FormKv.FileExt.WorkFile();
						workFile.setFileName(FileUtil.getName(str));
						workFile.setFileUrl(str);
						workFile.setFileType(FileUtil.extName(str));
						values.add(workFile);
					}
					if (Func.isNotEmpty(values)) {
						FormKv.FileExt ext = new FormKv.FileExt();
						ext.setArray(values);
						fromKv.setExt(ext);
					}
				}

				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.FILE.getDesc())) {
				//附件
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					//  原值处理
					StringBuffer buffer = new StringBuffer();
					for (int i = 0; i < jsonArray.size(); i++) {
						JSONObject job = jsonArray.getJSONObject(i);
						String fileUrl = job.getString("fileUrl");
						buffer.append(FileUtil.getName(fileUrl)).append("<br /> &nbsp;&nbsp;&nbsp;");
					}
					String value = buffer.toString();
					if (Func.isNotBlank(value)) {
						fromKv.setValue(StrUtil.removeSuffix(value, "<br /> &nbsp;&nbsp;&nbsp;"));
					}

					//列表值处理
					List<FormKv.FileExt.WorkFile> values = new ArrayList<>();
					for (int i = 0; i < jsonArray.size(); i++) {
						FormKv.FileExt.WorkFile workFile = new FormKv.FileExt.WorkFile();
						JSONObject job = jsonArray.getJSONObject(i);
						String fileUrl = job.getString("fileUrl");
						workFile.setFileName(job.getString("fileName"));
						workFile.setFileUrl(fileUrl);
						workFile.setFileType(FileUtil.extName(fileUrl));
						values.add(workFile);
					}

					if (Func.isNotEmpty(values)) {
						FormKv.FileExt ext = new FormKv.FileExt();
						ext.setArray(values);
						fromKv.setExt(ext);
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.ID_CARD.getDesc())) {
				//身份证
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.PHONE.getDesc())) {
				//电话
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				String value = valueObject.getString(model.getId());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.PEOPLE.getDesc())) {
				//联系人
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					//列表值处理
					List<FormKv.PeopleExt.People> values = new ArrayList<>();
					StringBuffer buffer = new StringBuffer();
					for (int i = 0; i < jsonArray.size(); i++) {
						JSONObject job = jsonArray.getJSONObject(i);
						buffer.append(job.getString("name")).append(",");

						FormKv.PeopleExt.People people = new FormKv.PeopleExt.People();
						people.setId(job.getString("id"));
						people.setName(job.getString("name"));
						people.setAvatar(job.getString("avatar"));
						values.add(people);
					}
					String value = buffer.toString();
					if (Func.isNotBlank(value)) {
						fromKv.setValue(StrUtil.removeSuffix(value, ","));
						FormKv.PeopleExt ext = new FormKv.PeopleExt();
						ext.setPeopleArray(values);
						fromKv.setExt(ext);
					}

				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.DEPT.getDesc())) {
				//部门
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					StringBuffer buffer = new StringBuffer();
					for (int i = 0; i < jsonArray.size(); i++) {
						JSONObject job = jsonArray.getJSONObject(i);
						buffer.append(job.getString("name")).append(",");
					}
					String value = buffer.toString();
					if (Func.isNotBlank(value)) {
						fromKv.setValue(StrUtil.removeSuffix(value, ","));
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.AREA.getDesc())) {
				//地点
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONObject object = valueObject.getJSONObject(model.getId());
				if (Func.isNotEmpty(object)) {
					String value = object.getString("address");
					String valueApp = object.getString("laddress");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value);
					}
					if (Func.isNotBlank(valueApp)) {
						fromKv.setValue(valueApp);
					}
				}
				all.add(fromKv);
			} else if (Func.equals(model.getType(), CommEnum.REGION.getDesc())) {
				//省市区
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				Integer provinceType = model.getValueJson().getInteger("provinceType");
				JSONObject object = valueObject.getJSONObject(model.getId());
				if (Func.isNotEmpty(object)) {
					StringBuffer buffer = new StringBuffer();
					String provinceName = object.getString("provinceName");
					if (Func.isNotBlank(provinceName)) {
						buffer.append(provinceName).append("-");
					}

					String cityName = object.getString("cityName");
					if (Func.isNotBlank(cityName)) {
						buffer.append(cityName).append("-");
					}

					String countyName = object.getString("countyName");
					if (Func.isNotBlank(countyName)) {
						buffer.append(countyName).append("-");
					}


					if (buffer.length() > 0) {
						String value = buffer.toString();
						value = StrUtil.removeSuffix(value, "-");
						fromKv.setValue(value);
					}


				}

				all.add(fromKv);

				if (provinceType == CommNumberEnum.NUMBER_2.getCode()) {
					fromKv = new FormKv.FromKv();
					fromKv.setType(model.getType());
					fromKv.setLable("街道");
					if (Func.isNotEmpty(object)) {
						String street = object.getString("street");
						if (Func.isNotBlank(street)) {
							fromKv.setValue(street);
						}
					}
					all.add(fromKv);
				}

			} else if (Func.equals(model.getType(), CommEnum.RELATION_APPLY.getDesc())) {
				//关联审批单
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				JSONObject object = valueObject.getJSONObject(model.getId());
				if (Func.isNotEmpty(object)) {
					String value = object.getString("name");
					String id = object.getString("id");
					if (Func.isNotBlank(value)) {
						fromKv.setValue(value);
						FormKv.RelationApplyExt ext = new FormKv.RelationApplyExt();
						ext.setRelationId(id);
						fromKv.setExt(ext);
					}
				}
				all.add(fromKv);

			} else if (Func.equals(model.getType(), CommEnum.FORM.getDesc())) {
				//明细/表格
				fromKv = new FormKv.FromKv();
				fromKv.setType(model.getType());
				fromKv.setLable(model.getValueJson().getString("name"));
				all.add(fromKv);

				JSONArray jsonArray = valueObject.getJSONArray(model.getId());
				if (Func.isNotEmpty(jsonArray)) {
					List<List<FormKv.FromKv>> lineValues = new ArrayList<>();
					for (int i = 0; i < jsonArray.size(); i++) {
						JSONObject object = jsonArray.getJSONObject(i);
//                        if (Func.isNotEmpty(model.getChildren()) && Func.isNotEmpty(object)) {
						if (Func.isNotEmpty(model.getChildren())) {
							List<FormKv.FromKv> lineFromKv = new ArrayList<>();
							for (VueFormModel child : model.getChildren()) {
								List<FormKv.FromKv> records = dealModel(child, object);
								lineFromKv.addAll(records);
							}
							if (Func.isNotEmpty(lineFromKv)) {
								lineValues.add(lineFromKv);
							}
						}
					}
					if (Func.isNotEmpty(lineValues)) {
						fromKv.setChild(lineValues);
					}
				}

			} else if (Func.equals(model.getType(), CommEnum.REST.getDesc())) {
				//请假/调休套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.REPLACECARD.getDesc())) {
				//补卡套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.WORK.getDesc())) {
				//加班套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.OUT.getDesc())) {
				//外出套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.TRIP.getDesc())) {
				//出差套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.TURN_FORMAL.getDesc())) {
				//转正套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else if (Func.equals(model.getType(), CommEnum.LEAVE.getDesc())) {
				//离职套件
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			} else {
				//通用 套件处理
				if (Func.isNotEmpty(model.getChildren())) {
					for (VueFormModel child : model.getChildren()) {
						List<FormKv.FromKv> records = dealModel(child, valueObject);
						all.addAll(records);
					}
				}
			}
		} catch (Exception e) {
			log.error("解析表单样式错误 " + JSON.toJSONString(model) + "  " + ExceptionUtil.getMessage(e));
			return all;
		}
		return all;
	}


	public static List<FormKv.FromKv> execOld(String formStyleJson, String dataJson) {
		List<VueFormOldModel> vueFormOldModel = JSON.parseArray(formStyleJson, VueFormOldModel.class);
		JSONObject object = JSON.parseObject(dataJson);
		if (object == null) {
			object = new JSONObject();
		}
		List<FormKv.FromKv> all = new ArrayList<>();
		for (VueFormOldModel formModel : vueFormOldModel) {
			List<FormKv.FromKv> tt = dealOldModel(formModel, object);
			all.addAll(tt);
		}
		return all;
	}

	private static List<FormKv.FromKv> dealOldModel(VueFormOldModel formModel, JSONObject object) {
		List<FormKv.FromKv> all = new ArrayList<>();
		try {
			FormKv.FromKv fromKv;
			if (!Func.equals(formModel.getType(), CommEnum.FILE.getDesc())) {
				//文本
				fromKv = new FormKv.FromKv();
				fromKv.setType("text");
				fromKv.setLable(formModel.getLabel());
				String value = object.getString(formModel.getProp());
				if (Func.isNotBlank(value)) {
					fromKv.setValue(value);
				}
				all.add(fromKv);
			} else if (Func.equals(formModel.getType(), CommEnum.FILE.getDesc())) {
				//附件file
				fromKv = new FormKv.FromKv();
				fromKv.setType("file");
				fromKv.setLable(formModel.getLabel());
				String value = object.getString(formModel.getProp());
				if (Func.isNotBlank(value)) {

					JSONArray jsonArray = JSONArray.parseArray(value);
					JSONObject jsonObject = jsonArray.getJSONObject(0);
					String name = (String) jsonObject.get("fileName");
					fromKv.setValue(name);
				}

				all.add(fromKv);


			}
		} catch (Exception e) {
			log.error("解析表单样式错误 " + JSON.toJSONString(formModel) + "  " + ExceptionUtil.getMessage(e));
			return all;
		}
		return all;
	}

	public static void getNewDateJson(String processName, List<ProcessInstanceVO> todoTaskVos) {
		if (CollectionUtils.isEmpty(todoTaskVos)) {
			return;
		}

		for (ProcessInstanceVO processInstanceVO : todoTaskVos) {
			// 正文内容不为空，添加字段：正文字段为空，只显示前三项
			List<DataJsonVO> newDataJson = new ArrayList<>();
			// 解析formkvs
			List<FormKv.FromKv> formKvs = processInstanceVO.getFromKvs();

			int i = 0;
			for (FormKv.FromKv fromKv : formKvs) {
				log.info("formKv->{}" + fromKv.getLable());
				DataJsonVO dataJsonVO = new DataJsonVO();
				if (Func.isNotEmpty(fromKv.getLable())) {
					dataJsonVO.setKey(fromKv.getLable());
				}
				if (Func.isNotEmpty(fromKv.getLable())) {
					log.info("formKv->{}" + fromKv.getLable());
					log.info("type" + fromKv.getType());
					if (Func.equals(fromKv.getType(), "file")) {
						if (Func.isNotEmpty(fromKv.getExt()) && Func.isNotEmpty(fromKv.getValue())) {
							FormKv.FileExt fileExt = (FormKv.FileExt) fromKv.getExt();

							if (Func.equals(fromKv.getLable(), "附件")) {
								dataJsonVO.setValue("上传了" + fileExt.getArray().size() + "附件");
							}
						} else {
							dataJsonVO.setValue("上传了0个附件");
						}

					} else if (Func.equals(fromKv.getType(), "image")) {
						if (Func.isNotEmpty(fromKv.getExt()) && Func.isNotEmpty(fromKv.getValue())) {
							FormKv.FileExt fileExt = (FormKv.FileExt) fromKv.getExt();
							if (Func.equals(fromKv.getLable(), "图片")) {
								dataJsonVO.setValue("上传了" + fileExt.getArray().size() + "图片");
							}
						} else {
							dataJsonVO.setValue("上传了0个图片");
						}

					} else if (Func.equals(fromKv.getType(), "form")) {
						//表格
						if (Func.isNotEmpty(fromKv.getExt()) && Func.isNotEmpty(fromKv.getValue())) {
							List<List<FormKv.FromKv>> list = fromKv.getChild();

							dataJsonVO.setValue("上传了" + list.size() + "表格明细");
						} else {
							dataJsonVO.setValue("上传了0行表格明细");
						}

					} else {
						dataJsonVO.setValue(fromKv.getValue());
					}
				}
				i++;
				if (Func.isNotEmpty(dataJsonVO)) {
					newDataJson.add(dataJsonVO);
				}
				if (i == 3) {
					break;
				}
			}

			if (Func.isEmpty(processName)) {
				log.info("SS1" + newDataJson);
				processInstanceVO.setNewDataJson(newDataJson);
			}

			if (Func.isNotEmpty(processName)) {
				if (formKvs.size() > 3) {
					for (int j = CommNumberEnum.NUMBER_3.getCode(); j < formKvs.size(); j++) {
						if (Func.isNotEmpty(formKvs.get(j).getValue()) && formKvs.get(j).getValue().contains(processName)) {
							DataJsonVO dataJsonVO = new DataJsonVO();
							dataJsonVO.setKey(formKvs.get(j).getLable());
							if (Func.isNotEmpty(formKvs.get(j).getValue())) {
								if (Func.equals(formKvs.get(j).getType(), "file")) {
									FormKv.FileExt fileExt = (FormKv.FileExt) formKvs.get(j).getExt();
									if (Func.isNotEmpty(formKvs.get(j).getExt()) && Func.isNotEmpty(formKvs.get(j).getValue())) {
										if (Func.equals(formKvs.get(j).getLable(), "附件")) {
											dataJsonVO.setValue("上传了" + fileExt.getArray().size() + "附件");
										}
									} else {
										dataJsonVO.setValue("上传了0个附件");
									}

								} else if (Func.equals(formKvs.get(j).getType(), "image")) {
									if (Func.isNotEmpty(formKvs.get(j).getExt()) && Func.isNotEmpty(formKvs.get(j).getValue())) {
										FormKv.FileExt fileExt = (FormKv.FileExt) formKvs.get(j).getExt();
										if (Func.equals(formKvs.get(j).getLable(), "图片")) {
											dataJsonVO.setValue("上传了" + fileExt.getArray().size() + "图片");
										}
									} else {
										dataJsonVO.setValue("上传了0个图片");
									}

								} else if (Func.equals(formKvs.get(j).getType(), "form")) {
									//表格
									if (Func.isNotEmpty(formKvs.get(j).getChild()) && Func.isNotEmpty(formKvs.get(j).getValue())) {
										List<List<FormKv.FromKv>> list = formKvs.get(j).getChild();

										dataJsonVO.setValue("上传了" + list.size() + "表格明细");
									} else {
										dataJsonVO.setValue("上传了0行表格明细");
									}

								} else {
									dataJsonVO.setValue(formKvs.get(j).getValue());
								}
								newDataJson.add(dataJsonVO);
							}

						}

					}
					processInstanceVO.setNewDataJson(newDataJson);
				}
				processInstanceVO.setNewDataJson(newDataJson);
				log.info("SS" + newDataJson);
			}
		}

	}


	@Getter
	@AllArgsConstructor
	public enum TypeEnum {
		/**
		 * 文件
		 */
		FILE("file", "文件"),
		/**
		 * 说明文字
		 */
		TIPS("tips", "说明文字"),
		/**
		 * 明细表格
		 */
		FORM("form", "明细表格");
		final String code;
		final String name;

	}
}
