package com.snszyk.flow.process.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.snszyk.core.tenant.mp.TenantEntity;

/**
 * <AUTHOR> 流程分组表
    */
@ApiModel(value="org-attila-flow-process-entity-ProcessGroup")
@Data
@EqualsAndHashCode(callSuper=true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "blade_process_group")
public class ProcessGroup extends TenantEntity {
    /**
     * 数据id
     */
    @TableField(value = "data_id")
    @ApiModelProperty(value="数据id")
    private Long dataId;

    /**
     * 分组名
     */
    @TableField(value = "group_name")
    @ApiModelProperty(value="分组名")
    private String groupName;

    /**
     * 排序
     */
    @TableField(value = "ranked")
    @ApiModelProperty(value="排序")
    private Integer ranked;
}
