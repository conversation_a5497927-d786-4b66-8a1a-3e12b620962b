package com.snszyk.flow.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.Objects;

/**
 * 流程表单关联表实体类
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Data
@TableName("blade_proc_form_relation")
@ApiModel(value = "ProcFormRelation对象", description = "流程表单关联表")
public class ProcFormRelation extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 流程模版id
	 */
	@ApiModelProperty(value = "流程模版id")
	private String procDefId;
	/**
	 * 表单id
	 */
	@ApiModelProperty(value = "实例id")
	private String procInstId;
	/**
	 * 表单id
	 */
	@ApiModelProperty(value = "表单id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long formId;

	@ApiModelProperty(value = "表单名称")
	private String formName;
	/**
	 * 表单json系统key
	 */
	@ApiModelProperty(value = "表单json key")
	private String sysKey;
	/**
	 * 表单json自定义key
	 */
	@ApiModelProperty(value = "表单json自定义key")
	private String customKey;
	/**
	 * 申请时间
	 */
	@ApiModelProperty(value = "申请时间")
	private Date applyTime;

	/**
	 * 表单kv
	 */
	private String formKv;

	/**
	 * 流程审核节点json
	 */
	@ApiModelProperty(value = "流程审核节点json")
	private String procDefNode;

	/**
	 * 企业id
	 */
	@ApiModelProperty(value = "企业id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long deptId;

	@ApiModelProperty(value = "业务数据ID")
	private String businessId;

	@ApiModelProperty(value = "抄送人ids")
	private String informUserIds;

	@Getter
	@AllArgsConstructor
	public enum StatusEnum {
		/**
		 * 审批中
		 */
		ING("0", "审批中"),
		/**
		 * 通过
		 */
		PASS("1", "通过"),
		/**
		 * 拒绝
		 */
		NO_PASS("2", "拒绝"),
		/**
		 * 取消
		 */
		CANCEL("3", "取消");;
		final String code;
		final String desc;

		public static StatusEnum from(String code) {
			for (StatusEnum statusEnum : StatusEnum.values()) {
				if (Objects.equals(statusEnum.code, code)) {
					return statusEnum;
				}
			}
			return null;
		}
	}

}
