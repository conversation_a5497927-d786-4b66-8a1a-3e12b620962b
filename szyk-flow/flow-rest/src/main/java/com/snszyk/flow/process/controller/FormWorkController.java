package com.snszyk.flow.process.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.flow.process.convert.bpmn.TransLateNodeInfo;
import com.snszyk.flow.process.entity.InstanceValues;
import com.snszyk.flow.process.entity.ProcessDefinition;
import com.snszyk.flow.process.entity.ProcessInstance;
import com.snszyk.flow.process.manage.FlowMqManage;
import com.snszyk.flow.process.service.*;
import com.snszyk.flow.process.vo.FormKv;
import com.snszyk.flow.process.vo.FormKvUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR> 类名称:FormWorkController
 * 类描述:
 * 创建人:杨小琪
 * 创建时间:2021/10/21 16:52
 * Version 1.0
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/form-test")
public class FormWorkController {
	private ProcessDefinitionService processDefinitionService;
	private ProcessInstanceService processInstanceService;
	private ProcessInstanceInformService processInstanceInformService;
	private ProcessTaskOperationService processTaskOperationService;

	private RuntimeService runtimeService;
	private TaskService taskService;
	private IdentityService identityService;
	private SzykRedis bladeRedis;
	private HistoryService historyService;
	private FlowMqManage flowMqManage;
	private ProcessEngine processEngine;
	private ProcessSpecialOperateService processSpecialOperateService;
	private ProcessInstanceRelationService processInstanceRelationService;

	private final TransLateNodeInfo transLateNodeInfo;

	private IFormFieldService formFieldService;
	private IInstanceValuesService instanceValuesService;

	@GetMapping("/work")
	@Transactional(rollbackFor = Exception.class)
	public void test() {
		List<ProcessInstance> processInstances = processInstanceService.getAllOld();
		//1974条
		int i = 0;
		for (ProcessInstance processInstance : processInstances) {
			i++;
			log.info("processId->" + processInstance.getId());
			log.info("这是第" + i + "条");
			insertInstanceValues(processInstance.getDefinitionId(), processInstance.getId());
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public void insertInstanceValues(Long definitionId, Long instanceId) {

		ProcessInstance instanceVO = processInstanceService.getById(instanceId);
		ProcessDefinition definitionVO = processDefinitionService.getById(definitionId);
		if (Func.isEmpty(instanceVO) || Func.isEmpty(definitionVO)) {
			return;
		}
		List<FormKv.FromKv> formKvs = FormKvUtil.exec(definitionVO.getFormStyleJson(), instanceVO.getDataJson());
		String valuesText = StringPool.EMPTY;
		String lableText = StringPool.EMPTY;
		//解析表单内容
		for (FormKv.FromKv kv : formKvs) {
			lableText += kv.getLable() + FormKvUtil.SPLIT_STR;
			if (Func.isEmpty(kv.getValue()) && !Func.equals(kv.getType(), FormKvUtil.TypeEnum.FORM.getCode())) {
				continue;
			}
			//说明文字无需处理
			if (Func.equals(kv.getType(), FormKvUtil.TypeEnum.TIPS.getCode())) {
				continue;
			}
			//明细表格的处理
			if (Func.equals(kv.getType(), FormKvUtil.TypeEnum.FORM.getCode())) {
				//取child
				List<List<FormKv.FromKv>> childList = kv.getChild();
				if (Func.isNotEmpty(childList) && childList.size() > 0) {
					for (FormKv.FromKv childKv : childList.get(0)) {
						lableText += childKv.getLable() + FormKvUtil.SPLIT_STR;
						if (Func.isEmpty(childKv.getValue())) {
							continue;
						}
						//说明文字无需处理
						if (Func.equals(childKv.getType(), FormKvUtil.TypeEnum.TIPS.getCode())) {
							continue;
						}
						//其他类型
						valuesText += childKv.getValue().replace(FormKvUtil.OLD_SPLIT_STR, FormKvUtil.SPLIT_STR) + FormKvUtil.SPLIT_STR;
					}
				}

				continue;
			}
			//其他类型
			valuesText += kv.getValue().replace(FormKvUtil.OLD_SPLIT_STR, FormKvUtil.SPLIT_STR) + FormKvUtil.SPLIT_STR;
		}
		//移除老数据
		instanceValuesService.remove(Wrappers.<InstanceValues>lambdaQuery().eq(InstanceValues::getProcessInstanceId, instanceVO.getId()));
		InstanceValues newInstanceValues = new InstanceValues();
		newInstanceValues.setProcessInstanceId(instanceVO.getId());
		newInstanceValues.setFlowableInstanceId(instanceVO.getFlowableInstanceId());
		newInstanceValues.setLableText(lableText);
		newInstanceValues.setValueText(valuesText);
		//添加新数据
		instanceValuesService.save(newInstanceValues);

	}
}
