package com.snszyk.flow.engine.feign;

import com.snszyk.flow.core.feign.IFlowAbleClient;
import com.snszyk.flow.engine.service.ActDeModelAssociationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2020/10/20 11:36
 * 类名称:FlowAbleClient
 * 类描述:
 * 创建人:86175
 * 创建时间:2020/10/20 11:36
 * Version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
public class FlowAbleClient implements IFlowAbleClient {

	private final ActDeModelAssociationService actDeModelAssociationService;

	@GetMapping(INIT_DATE)
	@Override
	public void createInittFlowData(@RequestParam("tenantId") String tenantId, @RequestParam("companyId") Long companyId) {
		actDeModelAssociationService.createInittFlowData(tenantId, companyId);
	}

//    /**
//     * 根据processId获取节点信息
//     */
//    @Override
//    @GetMapping(FLOW_NODE_DETAIL)
//    public Object getModelByProcessId(String processId){
//        ActDeModelAssociation actDeModelAssociation=actDeModelAssociationService.selectByProcessId(processId);
//        Object object=actDeModelAssociation.getFlowableJudegePerson();
//        return object;
//    }

}
