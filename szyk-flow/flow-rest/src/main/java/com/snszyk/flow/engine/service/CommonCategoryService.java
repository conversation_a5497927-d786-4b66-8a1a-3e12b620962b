package com.snszyk.flow.engine.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.flow.core.entity.CommonCategory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/9/27 9:34
 */
public interface CommonCategoryService extends IService<CommonCategory> {

	/**
	 * 流程分类（新建和编辑用）
	 *
	 * @return
	 */
	List<CommonCategory> selectByName();

	/**
	 * 根据模块名称查询所有分类
	 *
	 * @param moduleName
	 * @return List<CommonCategory>
	 */
	List<CommonCategory> selectByModuleName(String moduleName);


	/**
	 * 根据一级分类查询二级分类名称
	 *
	 * @param levelFirstName
	 * @return List<Map < String, Object>>
	 */
	List<Map<String, Object>> selectByLevelName(String levelFirstName);


	/**
	 * 根据二级分类查询一条记录
	 *
	 * @param levelSecondName
	 * @return
	 */
	CommonCategory selectByName(String levelSecondName);

	/**
	 * 按照id集合查询
	 *
	 * @param categoryIds
	 * @return
	 */
	List<CommonCategory> selectBatchIds(List<Long> categoryIds);

	/**
	 * 查询分页
	 *
	 * @param page
	 * @param commonCategory
	 * @return
	 */
	IPage<CommonCategory> pageList(IPage<CommonCategory> page, CommonCategory commonCategory);

	/**
	 * app-列表页面组装数据
	 *
	 * @return
	 */
	List<Map> appList();

	/**
	 * 查询
	 *
	 * @param firstId
	 * @return
	 */
	List<CommonCategory> selectByFirstId(Integer firstId);

	/**
	 * 根据firstId查询数据
	 *
	 * @param categoryId
	 * @return
	 */
	CommonCategory selectByMyFirstId(Integer categoryId);

	/**
	 * 根据CategoryId查询数据
	 *
	 * @param categoryId
	 * @return
	 */
	CommonCategory selectByCategoryId(Integer categoryId);

	/**
	 * 查询
	 *
	 * @return
	 */
	List<CommonCategory> selectByNameLimit();

	/**
	 * 查询
	 *
	 * @param firstIds
	 * @return
	 */
	List<CommonCategory> selectByFirstIds(List<Long> firstIds);
}
