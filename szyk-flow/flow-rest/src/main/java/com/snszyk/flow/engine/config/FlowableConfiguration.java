package com.snszyk.flow.engine.config;

import lombok.AllArgsConstructor;
import com.snszyk.flow.process.functions.MultipleAllExpressionFunction;
import com.snszyk.flow.process.functions.MultipleAnyExpressionFunction;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.flowable.spring.boot.FlowableProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Flowable配置类
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(FlowableProperties.class)
public class FlowableConfiguration implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {
	private final FlowableProperties flowableProperties;

	@Override
	public void configure(SpringProcessEngineConfiguration engineConfiguration) {
		engineConfiguration.setActivityFontName(flowableProperties.getActivityFontName());
		engineConfiguration.setLabelFontName(flowableProperties.getLabelFontName());
		engineConfiguration.setAnnotationFontName(flowableProperties.getAnnotationFontName());
		String variableScopeName = "execution";
		engineConfiguration.initShortHandExpressionFunctions();//注入扩展语法
		engineConfiguration.getShortHandExpressionFunctions().add(new MultipleAllExpressionFunction(variableScopeName));
		engineConfiguration.getShortHandExpressionFunctions().add(new MultipleAnyExpressionFunction(variableScopeName));
	}


}
