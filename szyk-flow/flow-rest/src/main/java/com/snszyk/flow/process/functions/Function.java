package com.snszyk.flow.process.functions;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 扩展SPEL语法 静态类
 */
@Slf4j
public class Function {

    /**
     * 包含任意
     * @param a
     * @param b
     * @return
     */
    public static boolean multipleAny(String a, String b) {
        log.info("a>{}", a);
        log.info("b>{}", b);
        List<String> var1List = Arrays.asList(a.split("@"));
        List<String> var2List = Arrays.asList(b.split("@"));
        log.info("aL>{}", var1List);
        log.info("bL>{}", var2List);
        for (String s : var1List) {
            if (var2List.contains(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 全部包含
     * @param a
     * @param b
     * @return
     */
    public static boolean multipleAll(String a, String b) {
        log.info("a>{}", a);
        log.info("b>{}", b);
        List<String> var1List = Arrays.asList(a.split("@"));
        List<String> var2List = Arrays.asList(b.split("@"));
        log.info("aL>{}", var1List);
        log.info("bL>{}", var2List);
        return var1List.containsAll(var2List);
    }

}
