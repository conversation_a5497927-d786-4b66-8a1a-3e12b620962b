package com.snszyk.flow.process.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.flow.process.cache.ProcessCacheKey;
import com.snszyk.flow.process.manage.ProcessDefinitionManage;
import com.snszyk.flow.process.vo.*;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.redis.cache.CacheKey;
import com.snszyk.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/app/process-definition")
public class ProcessDefinitionAppController {
    private final ProcessDefinitionManage processDefinitionManage;
    private final SzykRedis bladeRedis;
    /**
     * <AUTHOR>
     * @Description app审批入口，app的流程列表
     * @Date 9:58 2021/3/1
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "app列表页面", notes = "app列表页面")
    public R<List<AppFlowVO>> applist() {
        List<AppFlowVO> list=processDefinitionManage.getMenuList();
        return R.data(list);
    }
    /**
     * <AUTHOR>
     * @Description app流程发起空参表单啊（包含空参表单和流程节点的页面）
     * @Date 9:58 2021/3/1
     */
    @GetMapping("/start")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "app流程发起空参表单", notes = "app流程发起空参表单")
    public R<ProcessDefinitionStartVO> startFlow(Long id) {
        ProcessDefinitionStartVO processDefinitionStartVO=processDefinitionManage.selectById(id);
        return R.data(processDefinitionStartVO);
    }
    /**
     * <AUTHOR>
     * @Description 当有条件时，根据条件获取节点信息 (app流程发起页面，根据输入的条件获取相应的审批节点)
     * @Date 9:58 2021/3/1
     */
    @PostMapping("/start-condition")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "app流程发起空参表单", notes = "app流程发起空参表单")
    public R<List<UsersNodeVO>> startFlow(@RequestBody NodeConditionVO nodeConditionVO) {
        List<UsersNodeVO> usersNodeVos=processDefinitionManage.getNodeInfo(nodeConditionVO);
        return R.data(usersNodeVos);
    }
    /**
     * <AUTHOR>
     * @Description 流程定义二维码扫描，app获取获取预览的数据
     * @Date 9:58 2021/3/1
     */
    @GetMapping("/get-view")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "app扫描预览", notes = "app扫描预览")
    public R<ProcessDefinitionStartVO> data(Long definitionId) {

       ProcessDefinitionStartVO processDefinitionStartVO=processDefinitionManage.getData(definitionId);
        return R.data(processDefinitionStartVO);
    }
    /**
     * <AUTHOR>
     * @Description 流程定义，app二维码扫描
     * @Date 9:58 2021/3/1
     */
    @GetMapping("/checkQR")
    public R<Long> checkQr(@RequestParam Long id) {
        CacheKey key = ProcessCacheKey.FLOW_PROCESS_QR_CODE_PREVIEW.getKey(id);
        Boolean exists = bladeRedis.exists(key.getKey());
        if (exists) {
            return R.data(id);
        }
        throw new ServiceException("二维码不存在或已失效");
    }
    /**
     * <AUTHOR>
     * @Description pc端根据控件组类型获取流程定义
     * @Date 9:58 2021/3/1
     */
    @GetMapping("/trade/getbyformstyle")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "pc端根据控件组类型获取流程定义", notes = "pc端根据控件组类型获取流程定义")
    public R<List<TradeVO>> listR1(@RequestParam("formBusinessType") Integer formBusinessType){
        List<TradeVO> list= processDefinitionManage.getListByContiditionTrade(formBusinessType);
        return R.data(list);
    }


}
