package com.snszyk.flow.process.cmd;

import cn.hutool.core.util.RandomUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.process.enums.CommNumberEnum;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.cmd.AbstractDynamicInjectionCmd;
import org.flowable.engine.impl.dynamic.BaseDynamicSubProcessInjectUtil;
import org.flowable.engine.impl.dynamic.DynamicUserTaskBuilder;
import org.flowable.engine.impl.persistence.entity.DeploymentEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.task.service.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
public class AfterSignCmd extends AbstractDynamicInjectionCmd implements Command<Void> {


	protected String processInstanceId;

	private DynamicUserTaskBuilder signUserTaskBuilder;

	private String taskId;

	private Integer approverType;

	public AfterSignCmd(String processInstanceId, DynamicUserTaskBuilder signUserTaskBuilder, String taskId, Integer approverType) {
		this.processInstanceId = processInstanceId;
		this.signUserTaskBuilder = signUserTaskBuilder;
		this.taskId = taskId;
		this.approverType = approverType;
	}

	@Override
	public Void execute(CommandContext commandContext) {
		createDerivedProcessDefinitionForProcessInstance(commandContext, processInstanceId);
		return null;
	}

	@Override
	protected void updateBpmnProcess(CommandContext commandContext, Process process, BpmnModel bpmnModel, ProcessDefinitionEntity originalProcessDefinitionEntity, DeploymentEntity newDeploymentEntity) {
		TaskService taskService = CommandContextUtil.getTaskService(commandContext);
		TaskEntity taskEntity = taskService.getTask(taskId);
		//查找当前节点对应的执行执行实体（感觉兴趣的可以搜下流程执行的实例信息 表为ACT_RU_EXECUTION）
		ExecutionEntity currentExecutionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(taskEntity.getExecutionId());
		String activityId = currentExecutionEntity.getActivityId();
		FlowElement flowElement = process.getFlowElement(activityId, true);

		Activity activity = (Activity) flowElement;

		SequenceFlow currentTaskOutSequenceFlow = activity.getOutgoingFlows().get(0);
		String sourceRef = currentTaskOutSequenceFlow.getSourceRef();

		List<FlowElement> elements = addAfterNode(signUserTaskBuilder, sourceRef, approverType);
		elements.forEach(process::addFlowElement);
		sourceRef = signUserTaskBuilder.getId();

		for (SequenceFlow outgoingFlow : activity.getOutgoingFlows()) {
			outgoingFlow.setSourceRef(sourceRef);
		}

		bpmnModel.getLocationMap().clear();
		bpmnModel.getFlowLocationMap().clear();
		bpmnModel.getLabelLocationMap().clear();
		//当前流程的重新定义
		BaseDynamicSubProcessInjectUtil.processFlowElements(commandContext, process, bpmnModel, originalProcessDefinitionEntity, newDeploymentEntity);
	}

	@Override
	protected void updateExecutions(CommandContext commandContext, ProcessDefinitionEntity processDefinitionEntity, ExecutionEntity processInstance, List<ExecutionEntity> childExecutions) {

	}

	private static List<FlowElement> addAfterNode(DynamicUserTaskBuilder userTaskBuilder, String sourceNodeId, Integer approverType) {
		List<FlowElement> elements = new ArrayList<>();
		SequenceFlow newSeq = new SequenceFlow();
		String id = "seq".concat(RandomUtil.randomString(6));
		newSeq.setId(id);
		newSeq.setName("多级审批加线");
		newSeq.setSourceRef(sourceNodeId);
		newSeq.setTargetRef(userTaskBuilder.getId());
		elements.add(newSeq);


		UserTask userTask = new UserTask();
		userTask.setId(userTaskBuilder.getId());
		userTask.setAssignee(userTaskBuilder.getAssignee());
		userTask.setName(userTaskBuilder.getName());
		MultiInstanceLoopCharacteristics multiProperties = new MultiInstanceLoopCharacteristics();
		multiProperties.setElementVariable(userTaskBuilder.getId() + "assignee");
		multiProperties.setInputDataItem(userTaskBuilder.getId());

		//会签 所有人同意才能流转到下一节点
		/**
		 * flowable用户节点内置变量
		 * nrOfInstances 总实例数
		 * nrOfActiveInstances 已complete实例数
		 * 20个人   2
		 */
		if (Func.equals(approverType, CommNumberEnum.NUMBER_2.getCode())) {
			// 2 或签 一人同意就能流转到下一节点
			multiProperties.setCompletionCondition("${" + "nrOfActiveInstances >= 1" + "}");
		} else {
			//会签 所有人同意才能流转到下一节点
			multiProperties.setCompletionCondition("${" + "nrOfActiveInstances >= nrOfInstances" + "}");
		}
		userTask.setLoopCharacteristics(multiProperties);
		elements.add(userTask);

		return elements;
	}

}
