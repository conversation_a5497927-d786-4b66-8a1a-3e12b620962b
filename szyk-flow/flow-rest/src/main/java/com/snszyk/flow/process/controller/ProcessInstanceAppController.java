package com.snszyk.flow.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.core.utils.TaskFlowUtil;
import com.snszyk.flow.core.vo.PanelVO;
import com.snszyk.flow.process.dto.ProcessInstanceStartDTO;
import com.snszyk.flow.process.entity.ProcessInstance;
import com.snszyk.flow.process.enums.CommNumberEnum;
import com.snszyk.flow.process.manage.ProcessInstanceInformManage;
import com.snszyk.flow.process.manage.ProcessInstanceManage;
import com.snszyk.flow.process.manage.ProcessTaskOperationManage;
import com.snszyk.flow.process.service.ProcessInstanceInformService;
import com.snszyk.flow.process.service.ProcessInstanceService;
import com.snszyk.flow.process.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/app/process-instance")
public class ProcessInstanceAppController {

	private ProcessInstanceManage processInstanceManage;
	private ProcessTaskOperationManage processTaskOperationManage;
	private ProcessInstanceInformManage processInstanceInformManage;
	private ProcessInstanceService processInstanceService;

	private ProcessInstanceInformService processInstanceInformService;
	private TaskService taskService;

	/**
	 * 流程发起
	 * 1.判断流程定义是否存在
	 * 2.解析审批节点信息
	 * 3.调用flowable的接口发起流程
	 * 4.保存抄送人
	 * 5.发mq和保存redis
	 */


	@PostMapping("/launch")
	public R<Boolean> launch(@RequestBody @Valid ProcessInstanceStartDTO startDTO) {
		processInstanceManage.start(startDTO);
		return R.data(Boolean.TRUE);
	}

	/**
	 * 流程撤回
	 * 1.流程撤回的前提条件是：该流程的状态是审批中
	 * 2.流程实例修改状态
	 * 3.发mq消息
	 * 4.flowable将流程挂起
	 */
	@GetMapping("/revocation")
	public R<Boolean> revocation(@RequestParam Long id) {
		processInstanceManage.revocationById(id);
		return R.data(Boolean.TRUE);
	}

	/**
	 * 审批列表
	 *
	 * @param queryType 1,代办 2，已办 3，发起的 4，抄送
	 * @param query
	 * @return
	 */
	@GetMapping("/approve-page")
	public R<IPage<ProcessInstanceVO>> approvePage(ApprovePageQueryVO vo, @RequestParam(required = false) String keyword, @RequestParam Integer queryType, Query query, @RequestParam(required = false) String definationId, @RequestParam(required = false) Integer status) {
		IPage<ProcessInstanceVO> iPage = Condition.getPage(query);
		if (Func.equals(queryType, CommNumberEnum.NUMBER_1.getCode())) {
			iPage = processTaskOperationManage.appTodoTaskPageNew(vo, keyword, query, definationId);
			if (Func.isNotEmpty(iPage.getRecords())) {
				iPage.getRecords().forEach(item -> item.setIsRead(2));
			}
		} else if (Func.equals(queryType, CommNumberEnum.NUMBER_2.getCode())) {
			iPage = processTaskOperationManage.appDoneTaskPageNew(vo, keyword, query, definationId);

			if (Func.isNotEmpty(iPage.getRecords())) {
				iPage.getRecords().forEach(item -> item.setIsRead(1));
			}
		} else if (Func.equals(queryType, CommNumberEnum.NUMBER_3.getCode())) {
			iPage = processInstanceManage.launchPageNew(vo, keyword, null, query, definationId, status);
			if (Func.isNotEmpty(iPage.getRecords())) {
				iPage.getRecords().forEach(item -> item.setIsRead(1));
			}
		} else if (Func.equals(queryType, CommNumberEnum.NUMBER_4.getCode())) {
			iPage = processInstanceInformManage.appInform2MePage(vo, keyword, query, definationId);
		}

		List<ProcessInstanceVO> list = iPage.getRecords();
		if (list != null && !list.isEmpty()) {
			for (ProcessInstanceVO processInstanceVO : list) {
				if (processInstanceVO.getStatus() != null
					&& processInstanceVO.getStatus().intValue() != ProcessInstance.StatusEnum.ING
					.getCode().intValue()) {
					continue;
				}
				ProcessInstance instance = processInstanceService
					.getById(processInstanceVO.getProcessInstanceId());
				if (instance != null) {
					processInstanceVO
						.setFlowProcessInstanceId(instance.getFlowableInstanceId());
					try {
						Task flowableTask = taskService.createTaskQuery()
							.processInstanceId(instance.getFlowableInstanceId()).list().get(0);
						if (flowableTask != null) {
							processInstanceVO.setCurrentNodeId(flowableTask.getTaskDefinitionKey());
						}
					} catch (Exception e) {
						log.error("查询审批中的任务失败");
					}
				}
			}
		}

		return R.data(iPage);
	}

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //审批面板
	 * @Date 10:11 2021/5/6
	 * @Param
	 */
	@GetMapping("/approve-page-panel")
	public R<List<PanelVO>> panelList(Integer queryType, Integer status) {
		List<PanelVO> panelVos = new ArrayList<>();
		if (Func.equals(queryType, CommNumberEnum.NUMBER_1.getCode())) {
			//我的代办
			panelVos = processTaskOperationManage.appTodoTaskPanel();
		}
		if (Func.equals(queryType, CommNumberEnum.NUMBER_2.getCode())) {
			//我已审批
			panelVos = processTaskOperationManage.appDoneTaskPanel();
		}
		if (Func.equals(queryType, CommNumberEnum.NUMBER_3.getCode())) {
			//我已审批
			panelVos = processTaskOperationManage.launchTaskPanel(status);
		}
		if (Func.equals(queryType, CommNumberEnum.NUMBER_4.getCode())) {
			//我已审批
			panelVos = processInstanceInformManage.appInform2MePanel();
		}

		return R.data(panelVos);

	}

	/**
	 * v1--流程明细接口
	 * 旧的接口传参是processInstanceId和taskId
	 * 新的接口改造为传参是processInstanceId
	 */
	@GetMapping("/detail")
	public R<ProcessInstanceAppDetailVO> detail(@RequestParam Long id, @RequestParam(required = false) String flowableTaskId) {
		ProcessInstanceDetailVO detail = processInstanceManage.detail(id, flowableTaskId);
		ProcessInstanceAppDetailVO appDetailVO = BeanUtil.copy(detail, ProcessInstanceAppDetailVO.class);
		if (Func.equals(detail.getStatus(), ProcessInstance.StatusEnum.ING.getCode()) && Func.isNotEmpty(flowableTaskId)) {
			long count = taskService.createTaskQuery().taskId(flowableTaskId).active().count();
			appDetailVO.setIsTaskIdActive(count > 0 ? 1 : 0);
		} else {
			appDetailVO.setIsTaskIdActive(0);
		}
		appDetailVO.setFlowableTaskId(flowableTaskId);
		return R.data(appDetailVO);
	}

	/**
	 * v2--流程明细接口
	 * 新的明细接口
	 */
	@GetMapping("/detail/flow")
	public R<ProcessInstanceAppDetailVO> detailFlow(@RequestParam Long id) {
		ProcessInstanceDetailVO detail = processInstanceManage.detail(id, null);
		ProcessInstanceAppDetailVO appDetailVO = BeanUtil.copy(detail, ProcessInstanceAppDetailVO.class);
		ProcessInstance processInstance = new ProcessInstance();
		//id是销假流程，需要显示最新的销假流程节点
		ProcessInstance processInstance1 = processInstanceService.getOneById(id);
		if (Func.isNotEmpty(processInstance1) && Func.isNotEmpty(processInstance1.getRelationProcessId())) {
			//销假的关联id存在，获取最新的
			LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
			queryWrapper1.eq(ProcessInstance::getRelationProcessId, processInstance1.getRelationProcessId());
			queryWrapper1.orderByDesc(ProcessInstance::getCreateTime);
			processInstance = processInstanceService.list(queryWrapper1).get(0);

		}
		if (Func.isNotEmpty(processInstance1) && Func.isEmpty(processInstance1.getRelationProcessId())) {
			//id是请假流程
			LambdaQueryWrapper<ProcessInstance> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.eq(ProcessInstance::getRelationProcessId, id);
			queryWrapper.orderByDesc(ProcessInstance::getCreateTime);
			List<ProcessInstance> processInstanceList = processInstanceService.list(queryWrapper);
			if (processInstanceList.size() > 0) {
				processInstance = processInstanceList.get(0);
			} else {
				processInstance = processInstance1;
			}
		}

		String flowableTaskId = null;
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			List<Task> list = taskService.createTaskQuery().active()
				.processInstanceId(processInstance.getFlowableInstanceId())
				.taskAssignee(TaskFlowUtil.getTaskUser()).list();
			if (Func.isNotEmpty(list)) {
				flowableTaskId = list.get(0).getId();
			}
		}
		if (Func.equals(detail.getStatus(), ProcessInstance.StatusEnum.ING.getCode()) && Func.isNotEmpty(flowableTaskId)) {
			long count = taskService.createTaskQuery().taskId(flowableTaskId).active().count();
			appDetailVO.setIsTaskIdActive(count > 0 ? 1 : 0);
		} else {
			appDetailVO.setIsTaskIdActive(0);
		}
		//     修改抄送为已读
		if (Func.isEmpty(flowableTaskId)) {
			processInstanceInformService.updateReadByUserIdAndInstanceId(AuthUtil.getUserId(), processInstance.getId());
		}

		//跨组织判断当前的人原是否有任务
		List<NodeVO> nodes = appDetailVO.getNodes();
		for (NodeVO nodeVO : nodes) {
			if (Func.equals(nodeVO.getNodeType(), 2)) {

				//该审批未审批完成

				if (Func.equals(nodeVO.getStatus(), 0)) {
					List<UserVO> users = nodeVO.getUsers();
					for (UserVO userVO : users) {
						log.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@" + AuthUtil.getUserId() + "$$$" + Func.toLong(AuthUtil.getDeptId()));
						log.info("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@" + userVO.getUserId() + "$$$11" + userVO.getOrgId());
						if (Func.equals(userVO.getUserId(), AuthUtil.getUserId()) && Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
							appDetailVO.setFlowableTaskId(flowableTaskId);
							break;
						}

					}
				}
			}

		}

		//查询当前审批表单字段的可编辑属性
		appDetailVO.setFieldStatus(processInstanceManage.selectFieldStatus(id, appDetailVO));

		return R.data(appDetailVO);

	}

	/**
	 * @return com.snszyk.core.tool.api.R<java.lang.Boolean>
	 * <AUTHOR>
	 * @Description 销假按钮是否展示（判断：1登陆人是否为发起人2.判断是否为请假/调休的控件组3.判断是否是15天内的申请4.是否申请过销假5.是否拥有原processId）
	 * @Date 9:24 2021/3/8
	 * @Param [processId]
	 */
	@GetMapping("/check/button")
	public R<Boolean> checkButton(@RequestParam Long processId) {
		Boolean flag = processInstanceManage.checkButton(processId);
		return R.data(flag);

	}

	/**
	 * app关联审批单
	 *
	 * @param queryType 1， 我处理的  2，我发起的  3，抄送我的
	 * @param query
	 * @return
	 */
	@GetMapping("/relevance-page")
	public R<IPage<ProcessInstanceVO>> relevancePage(@RequestParam Long processDefinitionId, @RequestParam Integer queryType, Query query) {
		return R.data(processInstanceManage.relevancePage(processDefinitionId, queryType, query));
	}


	/**
	 * 打印 发送IM 文件消息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/print")
	public R print(Long id) {
		processInstanceManage.appFlowFromPrint(id);
		return R.success("打印文件已通过办公助手发送给你");
	}


	/**
	 * 根据当前用户和流程实例ID查询组织ID
	 *
	 * @param processInstanceId
	 * @return
	 */
	@GetMapping("/selectOrgId")
	public R<String> selectOrgId(@RequestParam("processInstanceId") String processInstanceId) {
		return R.data(processInstanceManage.selectOrgId(processInstanceId));

	}
}
