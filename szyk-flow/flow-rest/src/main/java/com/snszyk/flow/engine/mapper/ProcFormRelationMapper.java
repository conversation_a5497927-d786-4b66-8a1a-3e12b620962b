package com.snszyk.flow.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.flow.core.entity.ProcFormRelation;
import com.snszyk.flow.vo.ProcFormRelationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程表单关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
public interface ProcFormRelationMapper extends BaseMapper<ProcFormRelation> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param procFormRelation
	 * @return
	 */
	List<ProcFormRelationVO> selectProcFormRelationPage(IPage page, ProcFormRelationVO procFormRelation);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	ProcFormRelationVO selectOneById(@Param("id") Long id);

	/**
	 * 查询
	 *
	 * @param procInstId
	 * @return
	 */
	ProcFormRelation selectByProcInstId(@Param("procInstId") String procInstId);

	/**
	 * 更新
	 */
	void updateIng4Return();

	/**
	 * 查询
	 *
	 * @return
	 */
	List<ProcFormRelation> all();
}
