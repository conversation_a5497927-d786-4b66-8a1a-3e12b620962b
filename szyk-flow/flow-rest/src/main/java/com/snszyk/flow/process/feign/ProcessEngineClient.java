package com.snszyk.flow.process.feign;


import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.flow.process.dto.ProcessInstanceStartDTO;
import com.snszyk.flow.process.feign.IProcessEngineClient;
import com.snszyk.flow.process.manage.ProcessDefinitionManage;
import com.snszyk.flow.process.manage.ProcessInstanceManage;
import com.snszyk.flow.process.vo.*;
import com.snszyk.flow.vo.SelectNodeVO;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
/**
 * <AUTHOR>
@Slf4j
@ApiIgnore
@RestController
@AllArgsConstructor
public class ProcessEngineClient implements IProcessEngineClient {

    private ProcessInstanceManage processInstanceManage;
    private ProcessDefinitionManage processDefinitionManage;

    /**
     * 发起实例接口
     * @param startDTO
     * @return 流程实例id
     */
    @PostMapping(START_PROCESS_INSTANCE)
    @Override
    public R<Long> launchInstance(@Valid @RequestBody ProcessInstanceStartDTO startDTO) {
        try {
            return R.data(processInstanceManage.start(startDTO));
        } catch (Exception e) {
            log.error("feign-start-instance-error! params:{}", startDTO);
            log.error("e:{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }
    /**
     * 获取流程定义接口
     * @param id
     * @return 流程实例id
     */
    @GetMapping(GET_PROCESS_DEFINASTION)
    @Override
    public R<SelectNodeVO> getProcessDefination(Long id, String nodeId) {
        try {
            SelectNodeVO selectNode=new SelectNodeVO ();
            NodeConditionVO nodeConditionVO=new NodeConditionVO();
            nodeConditionVO.setId(id);
            nodeConditionVO.setNodeId(nodeId);
            //根据nodeId获取相应信息

           List<UsersNodeVO> nodes=processDefinitionManage.getNodeInfNew(nodeConditionVO);
           if(nodes.size()>0){
              for(UsersNodeVO usersNodeVO:nodes){
                  if(Func.equals(nodeId,usersNodeVO.getNodeId())&&Func.equals(3,usersNodeVO.getType())){
                     selectNode.setNodeId(usersNodeVO.getNodeId());
                      selectNode.setScopesType(usersNodeVO.getScopesType());
                     selectNode.setNodeSelectVOS(usersNodeVO.getNodeSelectVOS());
                     break;
                  }
              }
           }
           return R.data(selectNode);
        } catch (Exception e) {
            log.error("feign-get-defination-error! params:{}", id);
            log.error("e:{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * <AUTHOR>
     * @Description //撤销（交易）
     * @Date 11:25 2021/4/26
     * @Param
     * @return
     * public R<Boolean> revocation(@RequestParam Long id) {
     *         processInstanceManage.revocationById(id);
     *         return R.data(Boolean.TRUE);
     *     }
     */
    @GetMapping(REVOCAT_PROCESS_INSTANCE)
    @Override
    public R<Boolean> revocationProcess(@RequestParam Long id) {
        try {

            return  R.data(processInstanceManage.revocationById(id));
        } catch (Exception e) {
            log.error("feign-revocate-instance-error! params:{}", id);
            log.error("e:{}", e.getMessage());
            return R.fail(e.getMessage());
        }

    }

    /**
     * 获取审批节点的审批人，催办
     * @param id，nodeId
     * @return 流程实例id，流程节点的id
     */
    @GetMapping(GET_PROCESS_NODE)
    @Override
    public R<List<UserVO>> getProcessNode(Long id, String nodeId) {
        List<UserVO> users=processInstanceManage.getNodeUser(id,nodeId);
        return R.data(users);

    }
    /**
     * 获取审批详情
     * @param id
     * @return 流程实例id
     */
    @GetMapping(GET_PROCESS_DETAIL)
    @Override
    public  R<ProcessInstanceAppDetailVO> detailFlow( Long id) {
        ProcessInstanceDetailVO detail = processInstanceManage.detail(id, null);
        ProcessInstanceAppDetailVO appDetailVO = BeanUtil.copy(detail, ProcessInstanceAppDetailVO.class);
        return R.data(appDetailVO);

    }

}
