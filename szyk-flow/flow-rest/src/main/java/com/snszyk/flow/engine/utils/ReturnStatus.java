package com.snszyk.flow.engine.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.flow.core.entity.CompanyForm;
import com.snszyk.flow.core.entity.ProcFormRelation;
import com.snszyk.flow.engine.service.CompanyFormService;
import com.snszyk.flow.process.enums.CommNumberEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
@Component
@Slf4j
public class ReturnStatus {

	private static CompanyFormService companyFormService;
//    private static IRCSApproveClient ircsApproveClient;
//
//    private static IRCSApproveClient getIRCSApproveClient() {
//        if (ircsApproveClient== null) {
//            ircsApproveClient = SpringUtil.getBean(IRCSApproveClient.class);
//        }
//        return ircsApproveClient;
//    }

	private static CompanyFormService getCompanyFormService() {
		if (companyFormService == null) {
			companyFormService = SpringUtil.getBean(CompanyFormService.class);
		}
		return companyFormService;
	}

	public static void backStatus(ProcFormRelation procFormRelation) {
		/**
		 * 通用短信发送
		 * approvalStatus:1待审批,2:审批中,,3审批通过,4审批驳回,5审批撤回
		 * businessType："risk_credit","客户授信"；"risk_decision","业务决策"；"risk_decision_conference","业务决策上会申请"；
		 *                 "risk_contract","合同"；"risk_stock_inventory","库存盘点"
		 *                 "risk_payment","付款"；"risk_invoice","发票";"risk_receipt","收款"
		 * @return R
		 */
		log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>111" + procFormRelation.getFormId());
		Long formId = procFormRelation.getFormId();
		log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>formid" + formId);
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper.eq("id", formId);
		CompanyForm companyForm = getCompanyFormService().getOne(queryWrapper);
		log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>1112" + companyForm.toString());
		Long formCategoryId = companyForm.getFormCategoryId();

		String businessType = null;
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_50.getDesc())) {
			businessType = "risk_receipt";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_51.getDesc())) {
			businessType = "risk_payment";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_52.getDesc())) {
			businessType = "risk_invoice";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_53.getDesc())) {
			businessType = "risk_credit";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_54.getDesc())) {
			businessType = "risk_decision";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_55.getDesc())) {
			businessType = "risk_contract";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_56.getDesc())) {
			businessType = "risk_stock_inventory";
		}
		if (Func.equals(formCategoryId.toString(), CommNumberEnum.NUMBER_57.getDesc())) {
			businessType = "risk_decision_conference";
		}
		String status1 = Func.toStr(procFormRelation.getStatus());

		String status = null;
		if (Func.equals(status1, CommNumberEnum.NUMBER_0.getDesc())) {
			status = "2";
		}
		if (Func.equals(status1, CommNumberEnum.NUMBER_1.getDesc())) {
			status = "3";
		}
		if (Func.equals(status1, CommNumberEnum.NUMBER_2.getDesc())) {
			status = "4";
		}
		if (Func.equals(status1, CommNumberEnum.NUMBER_3.getDesc())) {
			status = "5";
		}
		log.info("#####################flowable状态" + status1);
		log.info("#####################" + status);
		log.info("#####################" + businessType);
		log.info("#####################" + procFormRelation.getBusinessId());
		if (Func.isNotEmpty(businessType)) {
//            getIRCSApproveClient().approveHandle(procFormRelation.getBusinessId(),businessType,status);
		}
	}
}
