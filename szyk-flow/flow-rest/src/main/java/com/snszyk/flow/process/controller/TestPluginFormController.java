package com.snszyk.flow.process.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.flow.process.service.TestPluginFormService;
import com.snszyk.flow.process.vo.TestPluginFormVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("test")
public class TestPluginFormController {

	private final TestPluginFormService testPluginFormService;

	@GetMapping("/page")
	public R<IPage<TestPluginFormVO>> approvePage(Query query) {
		IPage<TestPluginFormVO> iPage = testPluginFormService.testPage(query);
		return R.data(iPage);
	}


}

