<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.flow.process.mapper.ProcessTaskOperationMapper">
<!--  <resultMap id="BaseResultMap" type="com.snszyk.flow.process.entity.ProcessTaskOperation">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    &lt;!&ndash;@Table blade_process_task_operation&ndash;&gt;-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="flowable_task_id" jdbcType="VARCHAR" property="flowableTaskId" />-->
<!--    <result column="instance_id" jdbcType="BIGINT" property="instanceId" />-->
<!--    <result column="opinion" jdbcType="LONGVARCHAR" property="opinion" />-->
<!--    <result column="file" jdbcType="VARCHAR" property="file" />-->
<!--    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />-->
<!--    <result column="org_id" jdbcType="BIGINT" property="orgId" />-->
<!--    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
<!--    <result column="create_user" jdbcType="BIGINT" property="createUser" />-->
<!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
<!--    <result column="update_user" jdbcType="BIGINT" property="updateUser" />-->
<!--    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />-->
<!--    <result column="status" jdbcType="INTEGER" property="status" />-->
<!--  </resultMap>-->
<!--  <sql id="Base_Column_List">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    id, flowable_task_id, instance_id, opinion, `file`, tenant_id, org_id, create_time, -->
<!--    create_user, update_time, update_user, is_deleted, `status`-->
<!--  </sql>-->

  <select id="getByFlowableTaskId" resultType="com.snszyk.flow.process.entity.ProcessTaskOperation">
    select * from blade_process_task_operation where flowable_task_id = #{flowableTaskId,jdbcType=VARCHAR}
  </select>
</mapper>
