package com.snszyk.flow.process.controller;


import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.flow.process.entity.ProcessGroup;
import com.snszyk.flow.process.manage.ProcessGroupManage;
import com.snszyk.flow.process.vo.ProcessGropDefinationMoveVO;
import com.snszyk.flow.process.vo.ProcessGropDefinationVO;
import com.snszyk.flow.process.vo.ProcessGroupListVO;
import com.snszyk.flow.process.vo.ProcessGroupVO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/pc/process-group")
public class ProcessGroupPcController {

	private final ProcessGroupManage processGroupManage;

	/**
	 * 分组重命名
	 *
	 * @param processGroup
	 * @return wangbin
	 */
	@PostMapping("/reset-name")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分组重命名", notes = "传入分组id")
	public R<Boolean> resetNameById(@RequestBody ProcessGroup processGroup) {
		Boolean flag = processGroupManage.resetName(processGroup.getId(), processGroup.getGroupName());
		return R.status(flag);
	}

	/**
	 * 分组删除
	 *
	 * @param id
	 * @return wangbin
	 */
	@GetMapping("/delete")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分组删除", notes = "传入分组id")
	public R<Boolean> deleteById(@RequestParam("id") Long id) {
		Boolean flag = processGroupManage.delete(id);
		return R.status(flag);
	}

	/**
	 * 分组新建
	 *
	 * @param processGroupVO
	 * @return wangbin
	 */
	@PostMapping("/create")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分组新建")
	public R<Boolean> createGroup(@RequestBody ProcessGroupVO processGroupVO) {
		Boolean flag = processGroupManage.create(processGroupVO);
		return R.status(flag);
	}

	/**
	 * 分组列表（不包含流程定义，不包含淄矿分类）
	 *
	 * @return wangbin
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分组列表", notes = "组列表")
	public R<List<ProcessGroupListVO>> list() {
		List<ProcessGroupListVO> list = processGroupManage.getAllList();
		return R.data(list);
	}

	/**
	 * 分组列表(包含分组下的流程定义的列表 ， 不包括淄矿)
	 *
	 * @return wangbin
	 */
	@GetMapping("/process-definition/list")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "包含分组下的流程定义的列表")
	public R<List<ProcessGropDefinationVO>> listR() {
		List<ProcessGropDefinationVO> processGropDefinationVos = processGroupManage.getList();
		return R.data(processGropDefinationVos);
	}

	/**
	 * 分组列表(包含分组下的流程定义的列表 ， 包括淄矿)
	 *
	 * @return wangbin
	 */
	@GetMapping("/process-definition/list-zikuang")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分组列表", notes = "组列表")
	public R<List<ProcessGropDefinationVO>> listZiKuang() {
		List<ProcessGropDefinationVO> processGropDefinationVos = processGroupManage.getListZiKuang();
		return R.data(processGropDefinationVos);
	}

	/**
	 * 流程移动
	 *
	 * @param processGropDefinationMoveVO
	 * @return wangbin
	 */
	@PostMapping("/process-definition/flow/move")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "流程移动")
	public void groupOutMove(@RequestBody ProcessGropDefinationMoveVO processGropDefinationMoveVO) {
		processGroupManage.groupFlowMove(processGropDefinationMoveVO);
	}

	/**
	 * 分组移动
	 *
	 * @param ids
	 * @return wangbin
	 */
	@PostMapping("/group/move")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "分组移动")
	public void groupMove(@RequestBody JSONObject ids) {
		String id = (String) ids.get("ids");
		processGroupManage.groupMove(id);
	}

}
