package com.snszyk.flow.process.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.flow.process.entity.ProcessTaskOperation;

import java.util.List;

/**
 * <AUTHOR>
public interface ProcessTaskOperationService extends BaseService<ProcessTaskOperation> {


	/**
	 * 查询
	 *
	 * @param flowableTaskId
	 * @return
	 */
	ProcessTaskOperation getByFlowableTaskId(String flowableTaskId);

	/**
	 * 查询
	 *
	 * @param processInstanceId
	 * @param orgId
	 * @return
	 */
	List<ProcessTaskOperation> getByProcessInstanceId(Long processInstanceId, Long orgId);

	/**
	 * 查询
	 *
	 * @param instanceId
	 * @return
	 */
	List<Long> getApproverUserIdsByInstanceId(Long instanceId);

	/**
	 * 查询
	 *
	 * @param userId
	 * @return
	 */
	List<ProcessTaskOperation> getListByUserId(Long userId);
}
