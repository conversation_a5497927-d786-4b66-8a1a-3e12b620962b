package com.snszyk.flow.process.service.impl;



import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import com.snszyk.flow.process.mapper.ProcessInstanceRelationMapper;
import com.snszyk.flow.process.entity.ProcessInstanceRelation;
import com.snszyk.flow.process.service.ProcessInstanceRelationService;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Service
public class ProcessInstanceRelationServiceImpl  extends BaseServiceImpl<ProcessInstanceRelationMapper, ProcessInstanceRelation>  implements ProcessInstanceRelationService{


    @Override
    public int deleteByPrimaryKey(Long id) {
        return 0;
    }

    @Override
    public int insert(ProcessInstanceRelation record) {
        return 0;
    }

    @Override
    public int insertSelective(ProcessInstanceRelation record) {
        return 0;
    }

    @Override
    public ProcessInstanceRelation selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(ProcessInstanceRelation record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(ProcessInstanceRelation record) {
        return 0;
    }

    @Override
    public void updateStatus(Long id,int status) {
        LambdaUpdateWrapper<ProcessInstanceRelation> wrapper = Wrappers.<ProcessInstanceRelation>lambdaUpdate()
                .set(ProcessInstanceRelation::getStatus, status)
                .eq(ProcessInstanceRelation::getProcessInstanceId, id);
        this.update(wrapper);
    }
}
