package com.snszyk.flow.process.service;

import com.snszyk.flow.process.entity.InstanceValues;
import com.snszyk.flow.process.vo.InstanceValuesVO;
import com.snszyk.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 流程实例-表单全文信息表-检索用 服务类
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
public interface IInstanceValuesService extends BaseService<InstanceValues> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param instanceValues
	 * @return
	 */
	IPage<InstanceValuesVO> selectInstanceValuesPage(IPage<InstanceValuesVO> page, InstanceValuesVO instanceValues);

}
