package com.snszyk.flow.engine.flowable.global;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.flow.business.manage.PushMsgManage;
import com.snszyk.flow.business.service.IProcessService;
import com.snszyk.flow.core.constant.ProcessConstant;
import com.snszyk.flow.core.entity.BladeFlow;
import com.snszyk.flow.core.entity.InformRelation;
import com.snszyk.flow.core.entity.ProcFormRelation;
import com.snszyk.flow.core.entity.TaskRelation;
import com.snszyk.flow.engine.service.IProcFormRelationService;
import com.snszyk.flow.engine.service.InformRelationService;
import com.snszyk.flow.engine.service.TaskRelationService;
import com.snszyk.flow.engine.utils.FlowCache;
import com.snszyk.flow.engine.utils.FlowUtils;
import com.snszyk.flow.engine.utils.ReturnStatus;
import com.snszyk.flow.vo.AuditNodeVo;
import com.snszyk.flow.vo.FlowEventVO;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.common.engine.impl.event.FlowableEntityEventImpl;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 监听流程结束事件 进行抄送
 * <p>
 * 配置 com.snszyk.flow.engine.config.FlowableGlobListenerConfig
 * 事件类型参考
 * http://www.shareniu.com/flowable6.5_zh_document/bpm/index.html#getting.started.rest
 */
@Component
@Slf4j
public class GlobalFlowListener extends AbstractFlowableEngineEventListener {
	@Autowired
	private RuntimeService runtimeService;
	@Autowired
	private HistoryService historyService;
	@Autowired
	private InformRelationService informRelationService;
	@Autowired
	private IProcFormRelationService procFormRelationService;
	@Autowired
	private RepositoryService repositoryService;
	@Autowired
	private TaskService taskService;
	@Autowired
	private TaskRelationService taskRelationService;
	@Autowired
	private SzykRedis bladeRedis;
	@Autowired
	private PushMsgManage pushMsgManage;


	@Async
	@Override
	protected void taskCreated(FlowableEngineEntityEvent event) {
		log.error("taskCreated事件监听start#####################");



		log.info("taskCreated事件监听start#####################");
		if (event instanceof FlowableEntityEventImpl) {
			//得到流程定义id
			String processDefinitionId = event.getProcessDefinitionId();
			//得到流程实例id
			String processInstanceId = event.getProcessInstanceId();
			FlowableEntityEventImpl eventImpl = (FlowableEntityEventImpl) event;
			//得到任务实例
			TaskEntity entity = (TaskEntity) eventImpl.getEntity();
			log.info("审批人》{}", entity.getAssignee());
			ProcessDefinition processDefinition = FlowCache.getProcessDefinition(processDefinitionId);
			if (Func.isEmpty(processDefinition)) {
				processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).singleResult();
			}
			pushMsgManage.pushMsg(processInstanceId, entity, processDefinition);
			autoCompleted(processDefinitionId, processInstanceId, entity);
		}
		log.info("taskCreated事件监听end#####################");
	}

	private void autoCompleted(String processDefinitionId, String processInstanceId, TaskEntity entity) {
		/***
		 *
		 * 2.当前节点是或签的情况
		 *
		 */
		try {
			//自动审批
			/*1.获取当前节点的审批人*/
			log.info("当前节点审批人》{}", entity.getAssignee());
			/*2.获取上一节点的审批人*/
			//当前节点的执行id
			String executionId = entity.getExecutionId();
			log.info("执行executionId" + executionId);
			Execution execution = runtimeService.createExecutionQuery().executionId(executionId).singleResult();
			log.info("单人模式输出*****************" + execution);
			List<Execution> executionList = runtimeService.createExecutionQuery().executionId(executionId).list();
			log.info("会签或者或签输出******************" + executionList);
			if (Func.isNotEmpty(execution)) {
				log.info("#######进入");
				String currentActivityId = execution.getActivityId();
				log.info("获取当前节点currentActivityId" + currentActivityId);
				BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
				FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(currentActivityId);
				log.info("节点信息flowNode    " + flowNode);
				SequenceFlow sequenceFlow = flowNode.getIncomingFlows().get(0);
				if (Func.isNotEmpty(sequenceFlow)) {
					log.info("###################################################");
					//获取到上一节点
					String sourceRef = sequenceFlow.getSourceRef();
					log.info("上一节点" + sourceRef);
					List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().taskDefinitionKey(sourceRef).processInstanceId(processInstanceId).list();
					log.info("6666666666666666666666数量" + list.size());
					for (HistoricTaskInstance historicTaskInstance : list) {
						log.info("上一节点审批人" + historicTaskInstance.getAssignee());
						if (historicTaskInstance.getAssignee().equals(entity.getAssignee())) {
							//上一节点包含当前节点，该节点自动审批通过
							BladeFlow flow = new BladeFlow();
							flow.setFlag("1");
							flow.setTaskId(entity.getId());
							flow.setProcessInstanceId(processInstanceId);
							SpringUtil.getBean(IProcessService.class).completeTask1(flow);
							log.info("###############345");
						}
					}
				}
			} else {
				log.info("execution为空！！！！");
			}
		} catch (Exception e) {
			log.info("异常信息为：" + e.getStackTrace());
		}
	}


	@Override
	protected void processCreated(FlowableEngineEntityEvent event) {
		log.info("processCreated事件监听#####################");
	}

	@Override
	protected void processCompletedWithTerminateEnd(FlowableEngineEntityEvent event) {
		log.info("processCompletedWithTerminateEnd事件监听#####################");

//        NoticeParam param = new NoticeParam();
//			String content = dept.getDeptName() + "的企业联系人修改成功，修改后联系人姓名：" + deptDTO.getContactsName() + "，联系方式:" + deptDTO.getContactsMobile() + "，操作人：" + AuthUtil.getUserName() + "，" + DateUtil.formatDate(DateUtil.now());
//        NoticeParam.TxtExtra txtExtra = new NoticeParam.TxtExtra("标题", content, "","", new HashMap<>());
//        NoticeParam.Extra extra = new  NoticeParam.Extra(txtExtra, NoticeParam.ExtraType.WORK_EXAMINE);
//        param.setTargetId(String.valueOf(administrator.getId()));// 接收人ids
//        param.setContent("审批");
//        NoticeUtil.sendWorkHelpMessage(param, extra);

	}

	@Override
	protected void processCompleted(FlowableEngineEntityEvent event) {
		log.error("processCompleted事件监听#####################");


		//todo employy待改造
		log.info("event:{}", event);
		log.info("processCompleted事件监听#####################");
		String processInstanceId = event.getProcessInstanceId();
		String executionId = event.getExecutionId();
		String informUserIds = Func.toStr(runtimeService.getVariable(executionId, "informUserIds"));
		// 保存到抄送关系表
		InformRelation informRelation = new InformRelation();
		informRelation.setProcessInstanceId(processInstanceId);
		informRelation.setUserIds(informUserIds);
		informRelationService.save(informRelation);
		ProcFormRelation procFormRelation = procFormRelationService.getByProcInstId(processInstanceId);
		ProcessDefinition processDefinition = FlowCache.getProcessDefinition(event.getProcessDefinitionId());
		if (Func.isEmpty(processDefinition)) {
			processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(event.getProcessDefinitionId()).singleResult();
		}
		//事件回调
		FlowEventVO flowEventVO = BeanUtil.copy(procFormRelation, FlowEventVO.class);
		flowEventVO.setCategory(processDefinition.getCategory());
		flowEventVO.setParams(JSON.parseObject(procFormRelation.getFormKv(), HashMap.class));
		log.info("flowEventVo {}", flowEventVO);
		SpringUtil.publishEvent(new FlowableEvent(flowEventVO));
		//消息推送
		String title = String.format("审批-【%s】", processDefinition.getName());

		String formKv3 = FlowUtils.resolveForm3(procFormRelation);
		List<TaskRelation> taskRelations = taskRelationService.getTaskListByProcessInstanceId(processInstanceId);
		List<Long> approverIds = taskRelations.stream().map(TaskRelation::getCreateUser).collect(Collectors.toList());

		pushMsgManage.pushProcessEndMsg(procFormRelation, processDefinition, title, procFormRelation.getCreateUser(), formKv3, approverIds,processInstanceId);
		//todo :回调String businessId, String businessType,String approvalStatus
		ReturnStatus.backStatus(procFormRelation);
		log.info("taskCompleted事件end#########################");

	}

	@Override
	protected void taskCompleted(FlowableEngineEntityEvent event) {
		log.info("taskCompleted事件start,{}", event);
		//获取该实例对应的节点信息
		String executionId = event.getExecutionId();
		Execution execution = runtimeService.createExecutionQuery().executionId(executionId).singleResult();
		String parentExecutionId = execution.getParentId();
		ProcFormRelation procFormRelation = procFormRelationService.getByProcInstId(event.getProcessInstanceId());
		if (ObjectUtil.isEmpty(procFormRelation) || ObjectUtil.isEmpty(procFormRelation.getProcDefNode())) {
			throw new ServiceException("没有该流程实例的扩展信息");
		}
		List<AuditNodeVo> list = JSONObject.parseArray(procFormRelation.getProcDefNode(), AuditNodeVo.class);
		AuditNodeVo auditNodeVo = list.stream().filter(t -> Objects.equals(t.getNodeId(), execution.getActivityId())).findAny().orElse(null);
		if (ObjectUtil.isEmpty(auditNodeVo)) {
			throw new ServiceException("当前审核节点不存在");
		}
		if (!Objects.equals(auditNodeVo.getMultiInstanceType(), AuditNodeVo.MultiInstanceTypeEnum.SINGLE.getCode())) {
			log.info("进入多实例完成事件,{}", parentExecutionId);
			HashMap localVar = new HashMap(1);
			Boolean result = (Boolean) runtimeService.getVariable(executionId, ProcessConstant.PASS_KEY);
			log.info("任务监听器同意标识result,{}", result);
			Map<String, Object> map = runtimeService.getVariablesLocal(parentExecutionId);
			log.info("多实例全局变量,{}", map);
			Integer rejectedCount = Objects.isNull(map) || !map.containsKey(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER) ? 0 : (int) map.get(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER);
			Integer agreeCount = Objects.isNull(map) || !map.containsKey(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER) ? 0 : (int) map.get(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER);
			if (Boolean.FALSE.equals(result)) {
				localVar.put(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER, ++rejectedCount);
			} else {
				localVar.put(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER, ++agreeCount);
			}
			runtimeService.setVariablesLocal(parentExecutionId, localVar);
		}


	}

	@Override
	protected void taskAssigned(FlowableEngineEntityEvent event) {
		log.info("taskAssigned事件 start");

		log.info("taskAssigned事件 end");
	}
}
