package com.snszyk.flow.business.service;

import com.snszyk.flow.core.entity.BladeFlow;
import com.snszyk.flow.vo.FlowUserVO;

import java.util.List;

/**
 * <AUTHOR>
public interface IProcessService {

	/**
	 * 完成任务
	 *
	 * @param leave
	 * @return
	 */
	boolean completeTask(BladeFlow leave);

	/**
	 * 获取流转历史列表
	 *
	 * @param processInstanceId 流程实例id
	 * @param startActivityId   开始节点id
	 * @param endActivityId     结束节点id
	 * @return
	 */
	List<BladeFlow> historyFlowList(String processInstanceId, String startActivityId, String endActivityId);

	/**
	 * 查询
	 *
	 * @param processInstanceId
	 * @return
	 */
	List<FlowUserVO> getInformUsers(String processInstanceId);

	/**
	 * 更新
	 *
	 * @param flow
	 * @return
	 */
	boolean stopProcessInstance(BladeFlow flow);

	/**
	 * 更新
	 *
	 * @param taskId
	 * @return
	 */
	boolean isNextEnd(String taskId);

	/**
	 * 更新
	 *
	 * @param flow
	 */
	void completeTask1(BladeFlow flow);

	/**
	 * 查询
	 *
	 * @param informUserIds
	 * @param orgId
	 * @return
	 */
	List<FlowUserVO> getInformUsers(String informUserIds, Long orgId);
}
