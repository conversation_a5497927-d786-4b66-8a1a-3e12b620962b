/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.process.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.flow.process.entity.TestPluginForm;
import com.snszyk.flow.process.vo.TestPluginFormVO;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class TestPluginFormWrapper extends BaseEntityWrapper<TestPluginForm, TestPluginFormVO> {

	public static TestPluginFormWrapper build() {
		return new TestPluginFormWrapper();
	}

	@Override
	public TestPluginFormVO entityVO(TestPluginForm testPluginForm) {
		TestPluginFormVO testPluginFormVO = new TestPluginFormVO();
		BeanUtil.copyProperties(testPluginForm, testPluginFormVO);
		return testPluginFormVO;
	}

}
