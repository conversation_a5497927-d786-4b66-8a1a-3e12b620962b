package com.snszyk.flow.process.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.flow.process.entity.ProcessInstance;
import com.snszyk.flow.process.vo.*;

import java.util.List;

/**
 * <AUTHOR>
public interface ProcessInstanceService extends BaseService<ProcessInstance> {


	/**
	 * 更新
	 *
	 * @param processDefinitionIds
	 */
	void stopIngProcessInstance(List<Long> processDefinitionIds);

	/**
	 * 查询
	 *
	 * @param flowableInstanceId
	 * @return
	 */
	ProcessInstance getByFlowableInstanceId(String flowableInstanceId);

	/**
	 * 查询
	 *
	 * @param flowableTaskId
	 * @return
	 */
	ProcessInstance getProcessInstanceByFlowableTaskId(String flowableTaskId);

	/**
	 * 获取当前组织下审批中实例
	 *
	 * @param createUserIds
	 * @return
	 */
	List<ProcessInstance> getCurrentOrgIngListByCreateUser(List<Long> createUserIds);

	/**
	 * 删除
	 *
	 * @param ids
	 */
	void delStopProcessInstance(List<Long> ids);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	ProcessInstance getOneById(Long id);

	/**
	 * 获取当前组织下审批中实例
	 *
	 * @param createUserIds
	 * @param definitionIds
	 * @return
	 */
	List<ProcessInstance> getCurrentOrgIngListByCreateUser(List<Long> createUserIds, List<Long> definitionIds);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	ProcessInstance getByOldProcessId(Long id);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	List<ProcessInstance> selectByContion(Long id);

	/**
	 * 查询
	 *
	 * @param relationProcessId
	 * @return
	 */
	List<ProcessInstance> getListByOldProcessId(Long relationProcessId);

	/**
	 * 获取当前组织下审批中实例
	 *
	 * @param createUserIds
	 * @param definitionIds
	 * @param timeVO
	 * @return
	 */
	List<ProcessInstance> getCurrentOrgIngListByCreateUser(List<Long> createUserIds, List<Long> definitionIds, ApprovePageQueryVO timeVO);

	/**
	 * 查询
	 *
	 * @return
	 */
	List<ProcessInstance> getAllOld();

	/**
	 * 查询需我审批的列表数据
	 *
	 * @param iPage
	 * @param userList
	 * @param groupId
	 * @param timeVO
	 * @param processName
	 * @return
	 */
	List<MyTaskVO> selectMyTaskPage(IPage<ProcessInstanceVO> iPage, List<SimpleUserVO> userList, Long groupId, ApprovePageQueryVO timeVO, String processName);

	/**
	 * app按关键字查询需我审批的列表
	 *
	 * @param iPage
	 * @param userList
	 * @param keyword
	 * @param defId
	 * @param timevo
	 * @return
	 */
	List<MyTaskVO> selectPageByKeyWord(IPage<ProcessInstanceVO> iPage, List<SimpleUserVO> userList, String keyword, String defId, ApprovePageQueryVO timevo);

	/**
	 * 查询需我审批的任务数量
	 *
	 * @return
	 */
	Long selectMyUndoTaskCount();

	/**
	 * 查询我发起的列表
	 *
	 * @param page
	 * @param queryVO
	 * @return
	 */
	List<ProcessInstance> selectLaunchPage(IPage<ProcessInstanceVO> page, MyLaunchPageQueryVO queryVO);

	/**
	 * 查询我已审批的列表
	 *
	 * @param page
	 * @param queryVO
	 * @param userList
	 * @return
	 */
	List<ProcessInstance> doneTaskPageNew(IPage<ProcessInstanceVO> page, MyLaunchPageQueryVO queryVO, List<SimpleUserVO> userList);

}
