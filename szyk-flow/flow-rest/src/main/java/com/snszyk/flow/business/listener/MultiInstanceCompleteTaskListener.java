/*
 * Copyright (c) 2019. 九五数字科技(青岛)有限公司.  All rights reserved.
 */
package com.snszyk.flow.business.listener;

import com.snszyk.flow.core.constant.ProcessConstant;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;

/**
 * @author: wangxin
 * date: 2020/9/27
 * desc:
 */
@Component("multiInstanceCompleteTaskListener")
@Slf4j
public class MultiInstanceCompleteTaskListener implements ExecutionListener {

	@Autowired
	private RuntimeService runtimeService;

	/**
	 * 取得变量
	 *
	 * @param delegateTask
	 * @return
	 */
	private int getVar(DelegateTask delegateTask, String varName) {
		Integer variable = (Integer) delegateTask.getVariable(varName);
		return Objects.isNull(variable) ? 0 : variable;
	}

	@Override
	public void notify(DelegateExecution delegateExecution) {
		log.info("任务监听器delegateTask,{}", delegateExecution);
		String parentExecutionId = delegateExecution.getParentId();
		HashMap localVar = new HashMap(1);
		Boolean result = (Boolean) delegateExecution.getVariable(ProcessConstant.PASS_KEY);
		log.info("任务监听器同意标识result,{}", result);
		int rejectedCount = delegateExecution.hasVariable(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER) ? (int) delegateExecution.getVariable(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER) : 0;
		int agreeCount = delegateExecution.hasVariable(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER) ? (int) delegateExecution.getVariable(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER) : 0;
		if (Boolean.FALSE.equals(result)) {
			localVar.put(ProcessConstant.MULTIINSTANCE_REJECT_COUNTER, ++rejectedCount);
		} else {
			localVar.put(ProcessConstant.MULTIINSTANCE_AGREE_COUNTER, ++agreeCount);
		}
		runtimeService.setVariablesLocal(parentExecutionId, localVar);
	}
}
