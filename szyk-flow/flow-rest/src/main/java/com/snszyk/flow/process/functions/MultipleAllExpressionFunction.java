package com.snszyk.flow.process.functions;

import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.el.function.AbstractFlowableVariableExpressionFunction;
import org.flowable.variable.api.delegate.VariableScope;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
/**
 * <AUTHOR>
@Slf4j
public class MultipleAllExpressionFunction extends AbstractFlowableVariableExpressionFunction {


	public MultipleAllExpressionFunction(String variableScopeName) {
		super(variableScopeName, "multipleAll");
	}

	@Override
	protected boolean isMultiParameterFunction() {
		return false;
	}

	/**
	 * flowable扩展uel语法
	 * 全部包含
	 *
	 * @param variableScope
	 * @param variableNames
	 * @return
	 */
	public static boolean multipleAll(VariableScope variableScope, String variableNames) {
		log.info("执行multipleAll》》》》");
		String[] variables = StringUtils.split(variableNames, ",");
		String var1 = variables[0];
		String var2 = variables[1];
		log.info("var1>>{}", var1);
		log.info("var2>>{}", var2);
		Object variable = variableScope.getVariable(var2.trim());
		log.info("var---V>{}", variable.toString());
		List<String> var1List = Arrays.asList(var1.split("@"));
		List<String> var2List = Arrays.asList(variable.toString().split("@"));
		log.info("var1L>>{}", var1List);
		log.info("var2L>>{}", var2List);
		return var1List.containsAll(var2List);
	}
}
