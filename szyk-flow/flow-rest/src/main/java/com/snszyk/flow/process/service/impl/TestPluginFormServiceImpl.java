package com.snszyk.flow.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.flow.process.entity.TestPluginForm;
import com.snszyk.flow.process.mapper.TestPluginFormMapper;
import com.snszyk.flow.process.service.TestPluginFormService;
import com.snszyk.flow.process.vo.TestPluginFormVO;
import com.snszyk.flow.process.wrapper.TestPluginFormWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Service
public class TestPluginFormServiceImpl extends BaseServiceImpl<TestPluginFormMapper, TestPluginForm> implements TestPluginFormService {

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean add(TestPluginForm entity) {
		return this.saveOrUpdate(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TestPluginForm findByInstanceId(Long instanceId) {
		LambdaUpdateWrapper<TestPluginForm> wrapper = Wrappers.<TestPluginForm>lambdaUpdate()
			.eq(TestPluginForm::getInstanceId, instanceId)
			.eq(TestPluginForm::getIsDeleted, 0);

		TestPluginForm entity = this.getOne(wrapper);
		return entity;
	}

	@Override
	public IPage<TestPluginFormVO> testPage(Query query) {
		IPage<TestPluginForm> iPage = Condition.getPage(query);

		LambdaUpdateWrapper<TestPluginForm> wrapper = Wrappers.<TestPluginForm>lambdaUpdate()
			.eq(TestPluginForm::getIsDeleted, 0)
			.orderByDesc(TestPluginForm::getCreateTime);

		IPage<TestPluginForm> list = this.page(iPage, wrapper);

		if (list == null || CollectionUtils.isEmpty(list.getRecords())) {
			return null;
		}

		return TestPluginFormWrapper.build().pageVO(list);
	}
}
