package com.snszyk.flow.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.flow.core.entity.ActDeModelAssociation;
import com.snszyk.flow.core.vo.ActDeModelAssociationVO;
import com.snszyk.flow.core.vo.FlowableVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/28 15:18
 */
@Mapper
public interface ActDeModelAssociationMapper extends BaseMapper<ActDeModelAssociation> {

	/**
	 * 新增
	 *
	 * @param flowableVO
	 */
	void insert(FlowableVO flowableVO);

	/**
	 * 更新
	 *
	 * @param actDeModelAssociation
	 */
	void updateByMyId(ActDeModelAssociation actDeModelAssociation);

	/**
	 * 查询
	 *
	 * @param page
	 * @param actDeModelAssociationVO
	 * @return
	 */
	List<ActDeModelAssociationVO> selectMyPage(IPage<ActDeModelAssociationVO> page, ActDeModelAssociationVO actDeModelAssociationVO);

	/**
	 * 更新
	 *
	 * @param actDeModelAssociation
	 */
	void updateByProcessId(ActDeModelAssociation actDeModelAssociation);

	/**
	 * 查询
	 *
	 * @param actDeModelAssociation
	 * @return
	 */
	List<ActDeModelAssociation> selectByFirstId(ActDeModelAssociation actDeModelAssociation);

	/**
	 * 查询
	 *
	 * @param modelId
	 * @return
	 */
	ActDeModelAssociation selectByMyId(String modelId);

	/**
	 * 查询
	 *
	 * @param processIds
	 * @return
	 */
	List<ActDeModelAssociation> selectBatchByProcessIds(List<String> processIds);

	/**
	 * 查询
	 *
	 * @param actDeModelAssociation
	 * @return
	 */
	List<ActDeModelAssociation> selectByFirstStatusId(ActDeModelAssociation actDeModelAssociation);

	/**
	 * 查询
	 *
	 * @param actDeModelAssociation
	 * @return
	 */
	List<ActDeModelAssociation> selectByFirstId3(ActDeModelAssociation actDeModelAssociation);

	/**
	 * 查询
	 *
	 * @param processIds
	 * @return
	 */
	List<ActDeModelAssociation> selectMyBatchByProcessIds(List<String> processIds);

}
