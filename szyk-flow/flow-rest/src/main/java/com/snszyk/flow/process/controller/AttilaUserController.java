package com.snszyk.flow.process.controller;


import com.snszyk.core.tool.api.R;
import com.snszyk.flow.process.manage.ProcessInstanceManage;
import com.snszyk.flow.process.service.ProcessInstanceService;
import com.snszyk.system.vo.DeptUserAttilaVO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("attila-user")
public class AttilaUserController {

	private ProcessInstanceManage processInstanceManage;

	/**
	 * 获取企业下全部部门树及部门下人员列表 移至CommonController
	 */
	@GetMapping("/user/all-tree")
	@ApiOperation(value = "获取当前企业下全部部门树及部门下人员列表", notes = "获取当前企业下全部部门树及部门下人员列表")
	public R<List<DeptUserAttilaVO>> getDeptAndUserAllTree(@RequestParam(value = "nodeId", required = false) String nodeId,
														   @RequestParam(value = "processId", required = false) String processId,
														   @RequestParam(value = "orgId", required = false) Long orgId) {
		return R.data(processInstanceManage.getDeptAndUserAllTree(processId, nodeId, orgId));
	}


}

