<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.flow.process.mapper.ProcessDefinitionMapper">
  <resultMap id="BaseResultMap" type="com.snszyk.flow.process.entity.ProcessDefinition">
    <!--@mbg.generated-->
    <!--@Table blade_process_definition-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="flowable_deploy_id" jdbcType="VARCHAR" property="flowableDeployId" />
    <result column="ranked" jdbcType="INTEGER" property="ranked" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="process_name" jdbcType="VARCHAR" property="processName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="start_scope_type" jdbcType="INTEGER" property="startScopeType" />
    <result column="start_scope_ids" jdbcType="VARCHAR" property="startScopeIds" />
    <result column="admin_user_ids" jdbcType="VARCHAR" property="adminUserIds" />
    <result column="form_style_json" jdbcType="LONGVARCHAR" property="formStyleJson" />
    <result column="form_business_type" jdbcType="INTEGER" property="formBusinessType" />
    <result column="process_json" jdbcType="LONGVARCHAR" property="processJson" />
    <result column="has_condition" jdbcType="INTEGER" property="hasCondition" />
    <result column="inform_user_ids" jdbcType="VARCHAR" property="informUserIds" />
    <result column="inform_dept_ids" jdbcType="VARCHAR" property="informDeptIds" />
    <result column="inform_post_ids" jdbcType="VARCHAR" property="informPostIds" />
    <result column="inform_job_ids" jdbcType="VARCHAR" property="informJobIds" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
<!--    <result column="org_id" jdbcType="BIGINT" property="orgId" />-->
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="start_detail_names" jdbcType="VARCHAR" property="startDetailNames" />
    <result column="flag" jdbcType="INTEGER" property="flag" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, data_id, flowable_deploy_id, ranked, icon, process_name, remark, group_id, start_scope_type,
    start_scope_ids, admin_user_ids, form_style_json, form_business_type, process_json,
    has_condition, inform_user_ids,inform_dept_ids,inform_post_ids,inform_job_ids, tenant_id, org_id, create_time, create_user, update_time,
    update_user, is_deleted, `status`,flag,start_detail_names
  </sql>

  <select id="selectOneById" resultMap="BaseResultMap">
    select * from blade_process_definition where id=#{id,jdbcType=BIGINT}
    </select>

  <select id="getAllByFlowableDeployId" resultType="com.snszyk.flow.process.entity.ProcessDefinition">
    select * from blade_process_definition where flowable_deploy_id not like 'process%' and flowable_deploy_id != 'isempty'
  </select>

  <select id="getAllByDataId"  resultMap="BaseResultMap">

    select * from blade_process_definition where  data_id=#{dataId,jdbcType=BIGINT}
    </select>


    <select id="selectUnInitList" resultType="com.snszyk.flow.process.entity.ProcessDefinition">
      SELECT
          defTB.*
      FROM
          blade_process_definition defTB
      WHERE
      defTB.flag=0 and
          defTB.id not in ( SELECT process_definition_id id FROM blade_form_field WHERE is_deleted = 0  and process_definition_id is not null)
    </select>

</mapper>
