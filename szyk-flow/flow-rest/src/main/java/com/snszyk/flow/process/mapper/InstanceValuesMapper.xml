<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.flow.process.mapper.InstanceValuesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="instanceValuesResultMap" type="com.snszyk.flow.process.entity.InstanceValues">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="process_instance_id" property="processInstanceId"/>
        <result column="lable_text" property="lableText"/>
        <result column="value_text" property="valueText"/>
        <result column="org_id" property="orgId"/>
    </resultMap>


    <select id="selectInstanceValuesPage" resultMap="instanceValuesResultMap">
        select * from blade_instance_values where is_deleted = 0
    </select>

</mapper>
