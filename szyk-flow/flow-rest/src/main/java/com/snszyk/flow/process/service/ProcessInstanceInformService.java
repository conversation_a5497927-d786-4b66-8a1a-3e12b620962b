package com.snszyk.flow.process.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.flow.process.entity.ProcessInstanceInform;
import com.snszyk.flow.process.vo.SimpleUserVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
public interface ProcessInstanceInformService extends BaseService<ProcessInstanceInform> {


	/**
	 * 更新
	 *
	 * @param processInstanceId
	 */
	void updateStatusUnReadByProcessInstanceId(Long processInstanceId);

	/**
	 * 更新
	 *
	 * @param processInstanceId
	 * @param userId
	 */
	void updateStatusOnDetail(Long processInstanceId, Long userId);

	/**
	 * 查询
	 *
	 * @param instanceId
	 * @param orgId
	 * @return
	 */
	List<Long> getInformUserIdsByInstanceId(Long instanceId, Long orgId);

	/**
	 * 更新
	 *
	 * @param userId
	 * @param processInstanceId
	 * @return
	 */
	boolean updateReadByUserIdAndInstanceId(Long userId, Long processInstanceId);

	/**
	 * 查询
	 *
	 * @param userId
	 * @return
	 */
	List<ProcessInstanceInform> getListByUserId(Long userId);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	Map<Long, ProcessInstanceInform> getInformsByInstanceId(Long id);

	/**
	 * 查询抄送人列表
	 *
	 * @param processInstanceId
	 * @return 抄送人集合
	 */
	List<SimpleUserVO> selectUserList(Long processInstanceId);
}
