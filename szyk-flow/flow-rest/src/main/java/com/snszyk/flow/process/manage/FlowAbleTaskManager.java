package com.snszyk.flow.process.manage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.snszyk.flow.process.convert.bpmn.FlowConvertData;
import com.snszyk.flow.process.convert.bpmn.NodeModel;
import com.snszyk.flow.process.enums.CommNumberEnum;
import com.snszyk.flow.process.service.ProcessDefinitionService;
import com.snszyk.flow.process.service.ProcessInstanceService;
import com.snszyk.flow.process.service.ProcessTaskOperationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.flow.cache.FlowCacheKey;
import com.snszyk.flow.core.utils.TaskFlowUtil;
import com.snszyk.flow.process.entity.ProcessDefinition;
import com.snszyk.flow.process.entity.ProcessInstance;
import com.snszyk.flow.process.entity.ProcessTaskOperation;
import com.snszyk.flow.process.vo.UserVO;
import com.snszyk.flow.process.vo.UsersNodeVO;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.Func;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Component
public class FlowAbleTaskManager {

	private TaskService taskService;
	private ProcessInstanceManage processInstanceManage;
	private ProcessInstanceService processInstanceService;
	private SzykRedis bladeRedis;
	private ProcessTaskOperationService processTaskOperationService;
	private ProcessDefinitionService processDefinitionService;
	private HistoryService historyService;

	@Transactional(rollbackFor = Exception.class)
	public void updateFloableTask(TaskEntity updateEntity) {

		//        获取task 用户id，获取task 节点信息，获取task 实例ID
		//        ==redis锁==
		//        1.查询此实例-此节点-此用户是否在redis存在设置，若不存在则继续
		//        2.根据task查询当前流程实例ID-查询我方节点信息(当前审批中的节点)
		//        3.给当前task设置 DESCRIPTION_ 为 orgId
		//        ==redis锁释放==

		Long taskUerId = TaskFlowUtil.getUserId(updateEntity.getAssignee());
		String taskNodeId = updateEntity.getTaskDefinitionKey();
		String flowableInstanceId = updateEntity.getProcessInstanceId();

		String lockKey = "flow-task-update";
		boolean lock = false;
		try {
			if (lock) {
				ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(flowableInstanceId);
				if (Func.isEmpty(processInstance)) {
					return;
				}
				List<UsersNodeVO> usersNodeVos = JSON.parseArray(processInstance.getProcessUserNodeJson(), UsersNodeVO.class);
				UsersNodeVO uuVO = usersNodeVos.stream().filter(k -> Func.equals(k.getNodeId(), updateEntity.getTaskDefinitionKey())).findFirst().orElse(null);
				if (Func.isEmpty(uuVO)) {
					return;
				}
				String updateOrgId = "";
				for (UserVO user : uuVO.getUsers()) {
					if (Func.isEmpty(user.getOrgId())) {
					}
					if (Func.equals(user.getUserId(), taskUerId) && Func.isEmpty(selectRedisUser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId()))) {
						updateOrgId = Func.toStr(user.getOrgId());
						addRedisuser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId());
						break;
					}
				}
				updateEntity.setDescription(updateOrgId);
				taskService.saveTask(updateEntity);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (lock) {
				// 解锁
//                redisLock.forceUnlock(lockKey, LockType.REENTRANT);
			}
		}

	}


	/**
	 * 查询redis中是否有设置记录
	 *
	 * @param flowableInstanceId 实例ID
	 * @param taskNodeId         节点ID
	 * @param taskUerId          userId
	 * @param orgId              组织ID
	 * @return
	 */
	private String selectRedisUser(String flowableInstanceId, String taskNodeId, Long taskUerId, Long orgId) {

		return bladeRedis.get(FlowCacheKey.FLOW_TASK_ADD_USED.getKey(flowableInstanceId, taskNodeId, taskUerId, orgId, taskUerId));

	}

	/**
	 * 添加task已修orgId的用户
	 *
	 * @param flowableInstanceId
	 * @param taskNodeId
	 * @param taskUerId
	 * @param orgId
	 */
	private void addRedisuser(String flowableInstanceId, String taskNodeId, Long taskUerId, Long orgId) {

		bladeRedis.set(FlowCacheKey.FLOW_TASK_ADD_USED.getKey(flowableInstanceId, taskNodeId, taskUerId, orgId, taskUerId), "1");

	}


	@Async
	public void autoPass(TaskEntity entity) {

		try {
			TimeUnit.SECONDS.sleep(4);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(entity.getProcessInstanceId());
		//获取流程实例的节点信息
		List<UsersNodeVO> usersNodeVos = JSON.parseArray(processInstance.getProcessUserNodeJson(), UsersNodeVO.class);
		List<String> nodeList = usersNodeVos.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
		String beforeNodeId = nodeList.get(nodeList.indexOf(entity.getTaskDefinitionKey()) - 1);
		log.info("=====wzxautoPass上个节点的ID:" + beforeNodeId);

		//查询上个节点的审批记录
		List<ProcessTaskOperation> opList = processTaskOperationService.list(Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getInstanceId, processInstance.getId())
			.eq(ProcessTaskOperation::getCreateUser, TaskFlowUtil.getUserId(entity.getAssignee()))
			.eq(ProcessTaskOperation::getNodeId, beforeNodeId));
		log.info("=====wzxautoPass上个节点的审批记录:" + JSONArray.toJSONString(opList));

		List<String> nodeIds = usersNodeVos.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
		log.info("流程实例的节点信息-》" + nodeIds);
		//在获取流程定义的节点信息
		LambdaQueryWrapper<ProcessDefinition> queryW = Wrappers.lambdaQuery();
		queryW.eq(ProcessDefinition::getId, processInstance.getDefinitionId());
		String json = processDefinitionService.getOne(queryW).getProcessJson();
		String currentActivityId = entity.getTaskDefinitionKey();
		log.info("获取当前节点currentActivityId" + currentActivityId);

		List<NodeModel> nodes = FlowConvertData.parseBody(json);
		List<NodeModel> newNodes = new ArrayList<>();
		log.info("流程节点信息-》{}" + nodes);


		Map<String, NodeModel> nodeModelMap = nodes.stream().collect(Collectors.toMap(NodeModel::getResourceId, Function.identity()));
		log.info("流程节点信息-》{}" + nodeModelMap);
		int type = 0;
		if (Func.isNotEmpty(nodeModelMap.get(currentActivityId).getProperties())) {
			type = (int) nodeModelMap.get(currentActivityId).getProperties().get("approverType");
		}
		String lockKey = "flow-task-autopass";
		boolean lock = false;
		//2是或签
		if (type != CommNumberEnum.NUMBER_2.getCode()) {

			if (opList.size() > 0) {

				try {
					if (lock) {
						//上一届点有审批，当前节点为u审批，自动通过
						taskService.complete(entity.getId());
						ProcessTaskOperation taskOperation = new ProcessTaskOperation();
						taskOperation.setFlowableTaskId(entity.getId());
						taskOperation.setInstanceId(processInstance.getId());
						taskOperation.setOpinion("自动通过");
						taskOperation.setStatus(ProcessTaskOperation.StatusEnum.AUTO_PASS.getCode());
						taskOperation.setNodeId(entity.getTaskDefinitionKey());
						taskOperation.setTenantId("000001");
						taskOperation.setCreateUser(TaskFlowUtil.getUserId(entity.getAssignee()));
						taskOperation.setUpdateUser(TaskFlowUtil.getUserId(entity.getAssignee()));
						taskOperation.setCreateDept(null);
						processTaskOperationService.save(taskOperation);

					}

				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					if (lock) {
						// 解锁
//                                    redisLock.forceUnlock(lockKey, LockType.REENTRANT);
					}
				}


			}


		}
	}
}







