package com.snszyk.flow.process.service;


import com.snszyk.core.mp.base.BaseService;
import com.snszyk.flow.process.entity.ProcessInstanceRelation;

/**
 * <AUTHOR>
public interface ProcessInstanceRelationService extends BaseService<ProcessInstanceRelation> {


	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * 新增
	 *
	 * @param record
	 * @return
	 */
	int insert(ProcessInstanceRelation record);

	/**
	 * 新增
	 *
	 * @param record
	 * @return
	 */
	int insertSelective(ProcessInstanceRelation record);

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	ProcessInstanceRelation selectByPrimaryKey(Long id);

	/**
	 * 更新
	 *
	 * @param record
	 * @return
	 */
	int updateByPrimaryKeySelective(ProcessInstanceRelation record);

	/**
	 * 更新
	 *
	 * @param record
	 * @return
	 */
	int updateByPrimaryKey(ProcessInstanceRelation record);

	/**
	 * 更新
	 *
	 * @param id
	 * @param status
	 */
	void updateStatus(Long id, int status);
}
