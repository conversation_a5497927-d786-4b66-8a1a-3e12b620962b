package com.snszyk.flow.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.process.entity.ProcessInstanceInform;
import com.snszyk.flow.process.mapper.ProcessInstanceInformMapper;
import com.snszyk.flow.process.service.ProcessInstanceInformService;
import com.snszyk.flow.process.vo.SimpleUserVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Service
public class ProcessInstanceInformServiceImpl extends BaseServiceImpl<ProcessInstanceInformMapper, ProcessInstanceInform> implements ProcessInstanceInformService {

	/**
	 * 根据processInstanceId修改状态为未读
	 *
	 * @param processInstanceId
	 */
	@Override
	public void updateStatusUnReadByProcessInstanceId(Long processInstanceId) {
		LambdaUpdateWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaUpdate()
			.set(ProcessInstanceInform::getStatus, ProcessInstanceInform.StatusEnum.UNREAD.getCode())
			//审批完成，要更新抄送表的状态时，也要更新时间
			.set(ProcessInstanceInform::getUpdateTime, new Date())
			.eq(ProcessInstanceInform::getInstanceId, processInstanceId);
		this.update(wrapper);
	}

	@Override
	public void updateStatusOnDetail(Long processInstanceId, Long userId) {
		LambdaUpdateWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaUpdate()
			.eq(ProcessInstanceInform::getInstanceId, processInstanceId)
			.eq(ProcessInstanceInform::getUserId, userId);
		this.update(wrapper);
	}

	/**
	 * 获取抄送人ids
	 *
	 * @param instanceId
	 * @param orgId
	 * @return
	 */
	@Override
	public List<Long> getInformUserIdsByInstanceId(Long instanceId, Long orgId) {
		LambdaQueryWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaQuery()
//                .eq(ProcessInstanceInform::getOrgId, orgId)
			.eq(ProcessInstanceInform::getInstanceId, instanceId);
		List<ProcessInstanceInform> list = this.list(wrapper);
		if (Func.isEmpty(list)) {
			return null;
		}
		return list.stream().map(ProcessInstanceInform::getUserId).collect(Collectors.toList());
	}

	/**
	 * 修改抄送关系变为已读
	 *
	 * @param userId
	 * @param processInstanceId
	 * @return
	 */
	@Override
	public boolean updateReadByUserIdAndInstanceId(Long userId, Long processInstanceId) {
		LambdaUpdateWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaUpdate()
			.eq(ProcessInstanceInform::getUserId, userId)
			.eq(ProcessInstanceInform::getInstanceId, processInstanceId)
			.eq(ProcessInstanceInform::getStatus, ProcessInstanceInform.StatusEnum.UNREAD.getCode())
			.set(ProcessInstanceInform::getStatus, ProcessInstanceInform.StatusEnum.READ.getCode());
		return this.update(wrapper);
	}

	/**
	 * 获取用户的抄送关系
	 *
	 * @param userId
	 * @return
	 */
	@Override
	public List<ProcessInstanceInform> getListByUserId(Long userId) {
		LambdaQueryWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaQuery()
			.eq(ProcessInstanceInform::getUserId, userId);
		return this.list(wrapper);
	}

	@Override
	public Map<Long, ProcessInstanceInform> getInformsByInstanceId(Long id) {
		LambdaQueryWrapper<ProcessInstanceInform> wrapper = Wrappers.<ProcessInstanceInform>lambdaQuery()
			.eq(ProcessInstanceInform::getInstanceId, id);
		List<ProcessInstanceInform> list = this.list(wrapper);
		if (Func.isEmpty(list)) {
			return null;
		}
		Map<Long, Long> maps = new HashMap<>();
		Map<Long, ProcessInstanceInform> instanceMap = list.stream().collect(Collectors.toMap(ProcessInstanceInform::getId, Function.identity()));

		return instanceMap;
	}

	@Override
	public List<SimpleUserVO> selectUserList(Long processInstanceId) {

		List<ProcessInstanceInform> list = this.list(Wrappers.<ProcessInstanceInform>lambdaQuery()
			.eq(ProcessInstanceInform::getInstanceId, processInstanceId));
		if (Func.isEmpty(list)) {
			return null;
		}

		return list.stream().map(m -> {
			return new SimpleUserVO(m.getUserId(), m.getCreateDept());
		}).collect(Collectors.toList());
	}

}
