package com.snszyk.flow.process.functions;

import com.snszyk.core.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.el.function.AbstractFlowableVariableExpressionFunction;
import org.flowable.variable.api.delegate.VariableScope;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
@Slf4j
public class MultipleAnyExpressionFunction extends AbstractFlowableVariableExpressionFunction {

	public MultipleAnyExpressionFunction(String variableScopeName) {
		super(variableScopeName, "multipleAny");
	}

	@Override
	protected boolean isMultiParameterFunction() {
		return false;
	}

	/**
	 * flowable扩展uel语法
	 * 包含任意
	 *
	 * @param variableScope
	 * @param variableNames
	 * @return
	 */
	public static boolean multipleAny(VariableScope variableScope, String variableNames) {
		log.info("执行multipleAny》》》》");
		String[] variables = StringUtils.split(variableNames, ",");
		String var1 = variables[0];
		String var2 = variables[1];
		log.info("var1>>{}", var1);
		log.info("var2>>{}", var2);
		log.info("11111111111111111111111111111111111111111");
		Object variable = variableScope.getVariable(var2.trim());
		if (isNullOrEmpty(variable)) {
			variable = "111111111";
		}
		if (Func.isNotEmpty(variable.toString()) && variable != null) {
			List<String> var1List = Arrays.asList(var1.split("@"));
			List<String> var2List = Arrays.asList(variable.toString().split("@"));
			log.info("var1L>>{}", var1List);
			log.info("var2L>>{}", var2List);
			for (String s : var1List) {
				if (var2List.contains(s)) {
					return true;
				}
			}
		}

		return false;
	}

	public static boolean isNullOrEmpty(Object obj) {
		if (obj == null) {
			return true;
		}
		if (obj instanceof CharSequence) {
			return ((CharSequence) obj).length() == 0;
		}
		if (obj instanceof Collection) {
			return ((Collection) obj).isEmpty();
		}
		if (obj instanceof Map) {
			return ((Map) obj).isEmpty();
		}
		if (obj instanceof Object[]) {
			Object[] object = (Object[]) obj;
			if (object.length == 0) {
				return true;
			}
			boolean empty = true;
			for (int i = 0; i < object.length; i++) {
				if (!isNullOrEmpty(object[i])) {
					empty = false;
					break;
				}
			}
			return empty;
		}

		return false;
	}


}
