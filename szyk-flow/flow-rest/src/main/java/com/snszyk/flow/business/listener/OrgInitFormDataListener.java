package com.snszyk.flow.business.listener;

import com.snszyk.flow.engine.service.ActDeModelAssociationService;
import com.snszyk.flow.engine.service.CompanyFormService;
import com.snszyk.flow.process.manage.FormFieldManager;
import com.snszyk.flow.process.manage.ProcessDefinitionManage;
import com.snszyk.flow.process.manage.ProcessGroupManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: zhaomeinan
 * @Description: 创建组织后业务数据初始化
 * @Date: 19:54 2020/12/30
 * @Modificd By:
 * @Param:
 * @return:
 * @throw: 请描述异常信息
 */
@Slf4j(topic = "OrgInitFormDataListener")
@Component
public class OrgInitFormDataListener {

	@Autowired
	private CompanyFormService companyFormService;

	@Autowired
	private ActDeModelAssociationService actDeModelAssociationService;
	@Autowired
	private ProcessDefinitionManage processDefinitionManage;
	@Autowired
	private ProcessGroupManage processGroupManage;

	@Autowired
	private FormFieldManager formFieldManager;

}


















