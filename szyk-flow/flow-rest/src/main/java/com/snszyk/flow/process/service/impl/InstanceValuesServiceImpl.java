package com.snszyk.flow.process.service.impl;

import com.snszyk.flow.process.entity.InstanceValues;
import com.snszyk.flow.process.vo.InstanceValuesVO;
import com.snszyk.flow.process.mapper.InstanceValuesMapper;
import com.snszyk.flow.process.service.IInstanceValuesService;
import com.snszyk.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 流程实例-表单全文信息表-检索用 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-20
 */
@Service
public class InstanceValuesServiceImpl extends BaseServiceImpl<InstanceValuesMapper, InstanceValues> implements IInstanceValuesService {

	@Override
	public IPage<InstanceValuesVO> selectInstanceValuesPage(IPage<InstanceValuesVO> page, InstanceValuesVO instanceValues) {
		return page.setRecords(baseMapper.selectInstanceValuesPage(page, instanceValues));
	}

}
