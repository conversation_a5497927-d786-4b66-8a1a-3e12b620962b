/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.business.config;

import com.github.xiaoymin.knife4j.spring.extension.OpenApiExtensionResolver;
import com.google.common.collect.Lists;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.swagger.EnableSwagger;
import com.snszyk.core.swagger.SwaggerProperties;
import com.snszyk.core.swagger.SwaggerUtil;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Collections;
import java.util.List;

/**
 * Swagger配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableSwagger
@AllArgsConstructor
@Profile({"dev", "test"})
public class SwaggerConfigurationFlow {

	/**
	 * 引入swagger配置类
	 */
	private final SwaggerProperties swaggerProperties;

	/**
	 * 引入Knife4j扩展类
	 */
	private final OpenApiExtensionResolver openApiExtensionResolver;

	@Bean
	public Docket authDocket() {
		return docket("授权模块", Collections.singletonList(AppConstant.BASE_PACKAGES + ".auth"));
	}

	private Docket docket(String groupName, List<String> basePackages) {
		return new Docket(DocumentationType.SWAGGER_2)
			.groupName(groupName)
			.apiInfo(apiInfo())
			.ignoredParameterTypes(SzykUser.class)
			.select()
//			.apis(SwaggerUtil.basePackages(basePackages))
			.paths(PathSelectors.any())
			.build().securityContexts(securityContexts()).securitySchemes(securitySchemas())
			.extensions(openApiExtensionResolver.buildExtensions(groupName));
	}

	private List<SecurityContext> securityContexts() {
		return Collections.singletonList(SecurityContext.builder()
			.securityReferences(defaultAuth())
			.forPaths(PathSelectors.regex("^.*$"))
			.build());
	}

	List<SecurityReference> defaultAuth() {
		AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverywhere");
		AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
		authorizationScopes[0] = authorizationScope;
		return Lists.newArrayList(new SecurityReference(SwaggerUtil.clientInfo().getName(), authorizationScopes),
			new SecurityReference(SwaggerUtil.szykAuth().getName(), authorizationScopes),
			new SecurityReference(SwaggerUtil.szykTenant().getName(), authorizationScopes));
	}

	private List<SecurityScheme> securitySchemas() {
		return Lists.newArrayList(SwaggerUtil.clientInfo(), SwaggerUtil.szykAuth(), SwaggerUtil.szykTenant());
	}

	private ApiInfo apiInfo() {
		return new ApiInfoBuilder()
			.title(swaggerProperties.getTitle())
			.description(swaggerProperties.getDescription())
			.license(swaggerProperties.getLicense())
			.licenseUrl(swaggerProperties.getLicenseUrl())
			.termsOfServiceUrl(swaggerProperties.getTermsOfServiceUrl())
			.contact(new Contact(swaggerProperties.getContact().getName(), swaggerProperties.getContact().getUrl(), swaggerProperties.getContact().getEmail()))
			.version(swaggerProperties.getVersion())
			.build();
	}

}
