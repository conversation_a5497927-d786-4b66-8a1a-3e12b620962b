package com.snszyk.flow.engine.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.flow.core.entity.ProcFormRelation;
import com.snszyk.flow.dto.ProcFormRelationDTO;
import com.snszyk.flow.vo.FormShowVo;
import com.snszyk.flow.vo.ProcFormRelationVO;

import java.util.List;

/**
 * 流程表单关联表 服务类
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
public interface IProcFormRelationService extends BaseService<ProcFormRelation> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param procFormRelation
	 * @return
	 */
	IPage<ProcFormRelationVO> selectProcFormRelationPage(IPage<ProcFormRelationVO> page, ProcFormRelationVO procFormRelation);

	/**
	 * 根据流程Id 获取表单数据
	 *
	 * @param procDefId 流程id
	 * @return FormShowVo
	 */
	FormShowVo getByProcDefId(String procDefId);

	/**
	 * 根据实例id获取表单数据
	 *
	 * @param procInstId 实例id
	 * @return FormShowVo
	 */
	FormShowVo getFormByProcInstId(String procInstId);

	/**
	 * 根据实例id获取表单数据
	 *
	 * @param procInstIds 实例ids
	 * @return FormShowVo
	 */
	List<FormShowVo> getFormListByProcInstId(List<String> procInstIds);


	/**
	 * 保存流程表单关联关系
	 *
	 * @param dto 流程id和表单id
	 * @return Boolean
	 */
	Boolean submit(ProcFormRelationDTO dto);

	/**
	 * 根据流程id获取实例表单关联数据
	 *
	 * @param processInstanceId
	 * @return
	 */
	ProcFormRelation getByProcInstId(String processInstanceId);

	/**
	 * 更新
	 *
	 * @param processInstanceId
	 * @param status
	 * @return
	 */
	boolean updateStatusByProcessInstanceId(String processInstanceId, String status);

	/**
	 * 查询
	 *
	 * @param procInstIds
	 * @return
	 */
	List<ProcFormRelation> listByProcInstIds(List<String> procInstIds);

	/**
	 * 查询黁
	 *
	 * @return
	 */
	List<String> myIds();

	/**
	 * 查询
	 *
	 * @param processInstanceId
	 * @return
	 */
	ProcFormRelation getOneById(Long processInstanceId);

	/**
	 * 查询
	 *
	 * @param ids
	 * @return
	 */
	List<ProcFormRelation> listAllByIds(List<Long> ids);

	/**
	 * 更新
	 *
	 * @return
	 */
	boolean returnIngStatus();

	/**
	 * 查询
	 *
	 * @return
	 */
	List<ProcFormRelation> all();
}
