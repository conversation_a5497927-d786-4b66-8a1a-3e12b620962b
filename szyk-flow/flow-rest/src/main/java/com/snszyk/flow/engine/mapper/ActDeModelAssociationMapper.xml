<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.flow.engine.mapper.ActDeModelAssociationMapper">
  <resultMap id="BaseResultMap" type="com.snszyk.flow.core.entity.ActDeModelAssociation">
    <!--@mbg.generated-->
    <!--@Table act_de_model_association-->

    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="person_scope" jdbcType="LONGVARCHAR" property="personScope" />
    <result column="flowable_judege" jdbcType="LONGVARCHAR" property="flowableJudege" />
    <result column="flowable_judege_person" jdbcType="LONGVARCHAR" property="flowableJudegePerson" />
    <result column="drawing_pre" jdbcType="LONGVARCHAR" property="drawingPre" />
    <result column="form_category_id" jdbcType="BIGINT" property="formCategoryId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="ids" jdbcType="VARCHAR" property="ids" />
    <result column="person_value" jdbcType="VARCHAR" property="personValue" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="process_id" jdbcType="VARCHAR" property="processId" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
  </resultMap>
  <resultMap id="BaseMap" type="com.snszyk.flow.core.vo.ActDeModelAssociationVO">
    <!--@mbg.generated-->
    <!--@Table act_de_model_association-->

    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="person_scope" jdbcType="LONGVARCHAR" property="personScope" />
    <result column="flowable_judege" jdbcType="LONGVARCHAR" property="flowableJudege" />
    <result column="flowable_judege_person" jdbcType="LONGVARCHAR" property="flowableJudegePerson" />
    <result column="drawing_pre" jdbcType="LONGVARCHAR" property="drawingPre" />
    <result column="form_category_id" jdbcType="BIGINT" property="formCategoryId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="ids" jdbcType="VARCHAR" property="ids" />
    <result column="person_value" jdbcType="VARCHAR" property="personValue" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="level_name" property="levelName"/>
    <result column="status_name" property="statusName"/>
    <result column="category_name" property="categoryName"/>
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
     model_id, icon, person_scope, flowable_judege, drawing_pre, form_category_id,
    level,status,org_id,is_deleted,form_id,flowable_judege_person,ids,person_value,flow_name,flow_id,process_id,category_id,flag
  </sql>

  <delete id="insert" parameterType="com.snszyk.flow.core.entity.ActDeModelAssociation">
    insert into attila_de_model_association( model_id, icon, person_scope, flowable_judege, drawing_pre, form_category_id,
    level,status,org_id,is_deleted,form_id,flowable_judege_person,ids,person_value,flow_name,flow_id,process_id,category_id,flag) values(
     #{modelId,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, #{personScope,jdbcType=VARCHAR}, #{flowableJudege,jdbcType=VARCHAR}, #{drawingPre,jdbcType=VARCHAR}, #{formCategoryId,jdbcType=BIGINT},
    #{level},#{status},#{orgId,jdbcType=VARCHAR},#{isDeleted,jdbcType=INTEGER},#{formId,jdbcType=BIGINT},#{flowableJudegePerson,jdbcType=VARCHAR},#{ids,jdbcType=VARCHAR},#{personValue,jdbcType=VARCHAR},
    #{flowName,jdbcType=VARCHAR},#{flowId,jdbcType=VARCHAR},#{processId,jdbcType=VARCHAR},#{categoryId,jdbcType=BIGINT},#{flag,jdbcType=VARCHAR}
    )
    </delete>

  <update id="updateByMyId" parameterType="com.snszyk.flow.core.entity.ActDeModelAssociation">
    update attila_de_model_association  set
    icon=#{icon,jdbcType=VARCHAR},
    person_scope=#{personScope,jdbcType=VARCHAR},
    flowable_judege=#{flowableJudege,jdbcType=VARCHAR},
    drawing_pre=#{drawingPre,jdbcType=VARCHAR},
    form_category_id=#{formCategoryId,jdbcType=BIGINT},
    level=#{level},
    status=#{status},
    org_id=#{orgId,jdbcType=VARCHAR},
    is_deleted=#{isDeleted,jdbcType=INTEGER},
    form_id=#{formId,jdbcType=BIGINT},
    flowable_judege_person=#{flowableJudegePerson,jdbcType=VARCHAR},
    ids=#{ids,jdbcType=VARCHAR},
    person_value=#{personValue,jdbcType=VARCHAR},
    flow_id=#{flowId,jdbcType=VARCHAR},
    process_id=#{processId,jdbcType=VARCHAR}

    where model_id=#{modelId,jdbcType=VARCHAR}


    </update>




  <select id="selectMyPage" resultType="com.snszyk.flow.core.vo.ActDeModelAssociationVO">

    select
    b.level_second_name AS category_name,
    act.flow_name,c.form_name,act.status,act.is_deleted,act.org_id,act.level,act.icon,
    (CASE WHEN act.level=0 THEN '系统'  ELSE '企业' END) AS level_name,
     (CASE WHEN act.status=2 THEN '禁用' WHEN act.status=1 THEN '启用'  END) AS status_name,
    act.model_id,d.created,act.category_id from attila_de_model_association act
    left join  attila_common_category b on act.category_id=b.id
    left join attila_company_form c on act.form_id=c.id left join act_de_model d on act.model_id =d.id
    <where>
      act.org_id = #{param2.orgId,jdbcType=VARCHAR} and act.is_deleted=0
      <if test="param2.flowName!=null and param2.flowName!=''">
        and act.flow_name like concat('%', #{param2.flowName}, '%')
      </if>
      <if test="param2.status!=null and param2.status!=''">
        and act.status =#{param2.status,jdbcType=INTEGER}
      </if>
      <if test="param2.categoryId!=null and param2.categoryId!=''">
        and act.category_id =#{param2.categoryId}
      </if>
    </where>
    order by d.created desc
  </select>

  <update id="updateByProcessId">
    update attila_de_model_association  set
    flow_id=#{flowId,jdbcType=VARCHAR},
    process_id=#{processId,jdbcType=VARCHAR}

    where model_id=#{modelId,jdbcType=VARCHAR}
  </update>

  <select id="selectByFirstId" resultMap="BaseResultMap">
    select  DISTINCT c.level_first_name AS categoryName,(CASE WHEN b.level=0 THEN '系统'  ELSE '企业' END) AS level_name,
    (CASE WHEN b.status=2 THEN '禁用' WHEN b.status=1 THEN '启用'  END) AS status_name,

    b.model_id,
    b.flow_name,
    b.icon,
    b.form_category_id,
    b.level,
    b.status,
    b.org_id,
    b.form_id,
    b.ids,b.person_value,
    b.category_id,
    b.is_deleted,
    b.status,
    c.first_id,
    d.form_name as formName,
    d.id as formId

    FROM
     attila_de_model_association b left join attila_common_category c on b.form_category_id=c.first_id left join Attila_company_form d on b.form_id=d.id left join act_de_model act on b.model_id=act.id
    where b.form_category_id=#{formCategoryId,jdbcType=BIGINT} and b.org_id=#{orgId,jdbcType=VARCHAR} and b.is_deleted=0 and b.status=1 order by created desc
  </select>

  <select id="selectByMyId" resultMap="BaseResultMap">

    select  model_id, icon, person_scope, flowable_judege, drawing_pre, form_category_id,
    level,status,org_id,is_deleted,form_id,flowable_judege_person,ids,person_value,flow_name,flow_id,process_id,category_id from attila_de_model_association where model_id=#{modelId,jdbcType=VARCHAR}
  </select>

  <select id="selectBatchByProcessIds" parameterType="java.util.List" resultMap="BaseResultMap">
     select  model_id, icon, person_scope, drawing_pre, form_category_id,
    level,status,org_id,is_deleted,form_id,ids,person_value,flow_name,flow_id,process_id,category_id from attila_de_model_association
    where process_id in
    <foreach  item="ids"  collection="list"  index="index"  open="(" separator="," close=")">
      #{ids}
    </foreach>
    and is_deleted=0

  </select>

  <select id="selectByFirstStatusId" resultMap="BaseResultMap">
    select  DISTINCT c.level_first_name AS categoryName,(CASE WHEN b.level=0 THEN '系统'  ELSE '企业' END) AS level_name,
    (CASE WHEN b.status=2 THEN '禁用' WHEN b.status=1 THEN '启用'  END) AS status_name,

    b.model_id,
    b.flow_name,
    b.icon,
    b.form_category_id,
    b.level,
    b.status,
    b.org_id,
    b.form_id,
    b.category_id,
    b.is_deleted,
    b.status,
    c.first_id,
    d.form_name as formName,
    d.id as formId

    FROM
    attila_de_model_association b left join attila_common_category c on b.form_category_id=c.first_id left join Attila_company_form d on b.form_id=d.id left join act_de_model act on b.model_id=act.id
    where b.form_category_id=#{formCategoryId,jdbcType=BIGINT} and b.org_id=#{orgId,jdbcType=VARCHAR} and b.is_deleted=0 order by created desc

  </select>

  <select id="selectByFirstId3" resultMap="BaseResultMap">
    select b.*

    FROM
    attila_de_model_association b
    where b.form_category_id=#{formCategoryId,jdbcType=BIGINT} and b.org_id=#{orgId,jdbcType=VARCHAR} and b.is_deleted=0

  </select>

  <select id="selectMyBatchByProcessIds" parameterType="java.util.List" resultMap="BaseResultMap">
    select  model_id, icon, person_scope, drawing_pre, form_category_id,
    level,status,org_id,is_deleted,form_id,ids,person_value,flow_name,flow_id,process_id,category_id from attila_de_model_association
    where process_id in
    <foreach  item="ids"  collection="list"  index="index"  open="(" separator="," close=")">
      #{ids}
    </foreach>


  </select>
</mapper>
