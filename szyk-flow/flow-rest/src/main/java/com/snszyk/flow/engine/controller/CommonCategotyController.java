package com.snszyk.flow.engine.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.core.entity.CommonCategory;
import com.snszyk.flow.engine.service.CommonCategoryService;
import com.snszyk.flow.process.enums.CommEnum;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/9/27 15:42
 * 类名称:CommonCategotyController
 * 类描述:
 * 创建人:86175
 * 创建时间:2020/9/27 15:42
 * Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/flowable/form")
@Slf4j
public class CommonCategotyController extends SzykController {

	private CommonCategoryService commonCategoryService;
	@Autowired
	private Environment environment;

	@GetMapping("/category/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分类接口", notes = "分类接口查询")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "levelSecondName", value = "分类名", paramType = "query", dataType = "string"),

	})
	public R<List<CommonCategory>> categoryPageList(Query query, @ApiIgnore CommonCategory commonCategory) {
		if (true) {
			throw new ServiceException("老审批业务已终止，请升级版本后再使用");
		}
		QueryWrapper queryWrapper = new QueryWrapper();
/**
 * 淄矿能够看到自己的表单类型，firstId为6
 */

		log.info("77777777777777777777777777778888" + Func.toLong(AuthUtil.getDeptId()));
		log.info("7777777777777777777777777777888899" + environment.getProperty(CommEnum.YUNDUN_ORG_ID.getDesc()));
		if (Func.equals(Func.toLong(AuthUtil.getDeptId()), environment.getProperty(CommEnum.YUNDUN_ORG_ID.getDesc()))) {
			log.info("7777777777777777777777777777888899");
			queryWrapper.le("first_id", 6);
		} else {
			queryWrapper.le("first_id", 5);
		}
		return R.data(commonCategoryService.list(queryWrapper));
	}


//    @GetMapping("/category/list")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "分类接口", notes = "分类接口查询")
//
//    public R<List<CommonCategory>> categoryList(Query query, @ApiIgnore CommonCategory commonCategory){
//        return R.data(commonCategoryService.selectByModuleName());
//    }


	@GetMapping("/category/categorylist")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询一级分类接口", notes = "查询一级分类接口")

	public R<List<CommonCategory>> list() {
		if (true) {
			throw new ServiceException("老审批业务已终止，请升级版本后再使用");
		}
		if (Func.equals(Func.toLong(AuthUtil.getDeptId()), environment.getProperty(CommEnum.YUNDUN_ORG_ID.getDesc()))) {
			return R.data(commonCategoryService.selectByName());
		} else {

			return R.data(commonCategoryService.selectByNameLimit());
		}
	}

	/**
	 * 根据first_id获取表单的名称和id
	 */
	@GetMapping("/category/formlist")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取表单列表", notes = "获取表单列表")

	public R<List<CommonCategory>> formList(@RequestParam(required = true) Integer firstId) {
		if (true) {
			throw new ServiceException("老审批业务已终止，请升级版本后再使用");
		}
		return R.data(commonCategoryService.selectByFirstId(firstId));
	}


}
