package com.snszyk.flow.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.flow.process.entity.ProcessTaskOperation;
import com.snszyk.flow.process.mapper.ProcessTaskOperationMapper;
import com.snszyk.flow.process.service.ProcessTaskOperationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Service
public class ProcessTaskOperationServiceImpl extends BaseServiceImpl<ProcessTaskOperationMapper, ProcessTaskOperation> implements ProcessTaskOperationService {
	@Override
	public ProcessTaskOperation getByFlowableTaskId(String flowableTaskId) {
		return this.baseMapper.getByFlowableTaskId(flowableTaskId);
	}

	@Override
	public List<ProcessTaskOperation> getByProcessInstanceId(Long processInstanceId, Long orgId) {
		LambdaQueryWrapper<ProcessTaskOperation> wrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getInstanceId, processInstanceId)

			.orderByAsc(ProcessTaskOperation::getCreateTime);
		return this.list(wrapper);
	}

	@Override
	public List<Long> getApproverUserIdsByInstanceId(Long instanceId) {
		LambdaQueryWrapper<ProcessTaskOperation> wrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getInstanceId, instanceId);
		List<ProcessTaskOperation> list = this.list(wrapper);
		if (Func.isEmpty(list)) {
			return null;
		}
		return list.stream().map(ProcessTaskOperation::getCreateUser).collect(Collectors.toList());
	}

	@Override
	public List<ProcessTaskOperation> getListByUserId(Long userId) {
		LambdaQueryWrapper<ProcessTaskOperation> wrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getCreateUser, userId);
		return this.list(wrapper);
	}
}
