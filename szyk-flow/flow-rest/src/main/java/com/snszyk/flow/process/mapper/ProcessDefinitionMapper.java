package com.snszyk.flow.process.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.flow.process.entity.ProcessDefinition;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
@Mapper
public interface ProcessDefinitionMapper extends BaseMapper<ProcessDefinition> {

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	ProcessDefinition selectOneById(Long id);

	/**
	 * 查询
	 *
	 * @return
	 */
	List<ProcessDefinition> getAllByFlowableDeployId();

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	List<ProcessDefinition> getAllByDataId(Long id);

	/**
	 * 查询
	 *
	 * @return
	 */
	List<ProcessDefinition> selectUnInitList();
}
