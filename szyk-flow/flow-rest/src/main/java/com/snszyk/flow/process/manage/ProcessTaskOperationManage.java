package com.snszyk.flow.process.manage;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.flow.cache.FlowCacheKey;
import com.snszyk.flow.core.entity.ActDeModelAssociation;
import com.snszyk.flow.core.entity.CommonCategory;
import com.snszyk.flow.core.entity.ProcFormRelation;
import com.snszyk.flow.core.utils.TaskFlowUtil;
import com.snszyk.flow.core.vo.PanelVO;
import com.snszyk.flow.dto.AutoPassDTO;
import com.snszyk.flow.dto.ProcessSignAddDTO;
import com.snszyk.flow.dto.ProcessSignAddDTO.TypeEnum;
import com.snszyk.flow.engine.service.ActDeModelAssociationService;
import com.snszyk.flow.engine.service.CommonCategoryService;
import com.snszyk.flow.engine.service.FlowEngineService;
import com.snszyk.flow.engine.service.IProcFormRelationService;
import com.snszyk.flow.process.cmd.AfterSignCmd;
import com.snszyk.flow.process.cmd.BeforSignCmd;
import com.snszyk.flow.process.cmd.ParallelSignCmd;
import com.snszyk.flow.process.convert.bpmn.FlowConvertData;
import com.snszyk.flow.process.convert.bpmn.NodeModel;
import com.snszyk.flow.process.dto.ProcessTaskApproveDTO;
import com.snszyk.flow.process.dto.ProcessTurnTaskDTO;
import com.snszyk.flow.process.entity.*;
import com.snszyk.flow.process.enums.CommEnum;
import com.snszyk.flow.process.enums.CommNumberEnum;
import com.snszyk.flow.process.service.*;
import com.snszyk.flow.process.vo.*;
import com.snszyk.flow.vo.NextApproveVO;
import com.snszyk.message.service.IMessageLogicService;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.dto.UserDeptDTO;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.*;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.impl.dynamic.DynamicUserTaskBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Component
public class ProcessTaskOperationManage {

	private ProcessTaskOperationService processTaskOperationService;
	private ProcessInstanceService processInstanceService;
	private ProcessInstanceInformService processInstanceInformService;
	private ProcessDefinitionService processDefinitionService;
	private ProcessGroupService processGroupService;
	private ProcessGroupManage processGroupManage;
	private MsgManage msgManage;
	private IProcFormRelationService procFormRelationService;
	private FlowEngineService flowEngineService;
	private CommonCategoryService commonCategoryService;

	private RuntimeService runtimeService;
	private TaskService taskService;
	private HistoryService historyService;
	private RepositoryService repositoryService;

	private ProcessEngine processEngine;

	private ProcessSpecialOperateService processSpecialOperateService;
	private ProcessInstanceInformManage processInstanceInformManage;
	private ProcessInstanceRelationService processInstanceRelationService;

	private ActDeModelAssociationService actDeModelAssociationService;

	private IFormFieldService formFieldService;
	private IInstanceValuesService instanceValuesService;
	private IProcessLogService processLogService;

	private SzykRedis bladeRedis;

	private IUserService userService;
	private IDeptService deptService;


	private TestPluginFormService testPluginFormService;

	private IMessageLogicService messageLogicService;


	@Transactional(rollbackFor = Exception.class)
	public NextApproveVO approveTask(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO) { //人员离职，提示人员已经离职
		NextApproveVO nextApproveVO = new NextApproveVO();
		String processName = null;
		Long groupId = null;
		String createUserName = null;
		int tabNum = 1;
		if (Func.isNotEmpty(nextQueryVO)) {
			processName = nextQueryVO.getProcessName();
			groupId = nextQueryVO.getGroupId();
			createUserName = nextQueryVO.getCreateUserName();
			tabNum = Func.isEmpty(nextQueryVO.getTabNum()) ? 1 : nextQueryVO.getTabNum();
		}

		if (StringUtils.isBlank(taskApproveDTO.getFlowProcessInstanceId())) {
			throw new ServiceException("flowProcessInstanceId参数为空");
		}
		if (StringUtils.isBlank(taskApproveDTO.getCurrentNodeId())) {
			throw new ServiceException("该审批已由其他人受理");
		}

		Task task = processEngine.getTaskService().createTaskQuery()
			.taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
		if (task == null) {
			task = processEngine.getTaskService().createTaskQuery()
				.processInstanceId(taskApproveDTO.getFlowProcessInstanceId())
				.taskAssignee("taskUser_" + String.valueOf(AuthUtil.getUserId()))
				.taskDefinitionKey(taskApproveDTO.getCurrentNodeId()).singleResult();
		}

		if (task == null) {
			throw new ServiceException("该审批已由其他人受理");
		}

		log.info("登陆人" + AuthUtil.getUserId());
		log.info("任务中的审批人" + task.getAssignee());

		if (!task.getAssignee().contains(String.valueOf(AuthUtil.getUserId()))) {
			throw new ServiceException("该审批已由其他人受理");
		}

		taskApproveDTO.setFlowableTaskId(task.getId());

		ProcessTaskOperation oldTaskOperation = processTaskOperationService.getByFlowableTaskId(taskApproveDTO.getFlowableTaskId());

		// 无关人员不可操作
		if (Func.isNotEmpty(oldTaskOperation)) {
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			throw new ServiceException("该审批已被其他人受理");
		}

		ProcessInstance processInstance = processInstanceService.getProcessInstanceByFlowableTaskId(taskApproveDTO.getFlowableTaskId());

		//无关人员不可操作 --跨组织注释20211009 yxq
		if (Func.isEmpty(processInstance)) {
			throw new ServiceException("该审批已被其他人受理");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			throw new ServiceException("该审批已被其他人受理");
		}

		UserDTO userDeptByUserIdAndOrgId = userService.queryUserById(AuthUtil.getUserId());
		if (Func.isEmpty(userDeptByUserIdAndOrgId)) {
			throw new ServiceException("该审批节点审批人已经离职！");
		}

		// 转交拦截---根据流程实例id和taskid
		// 转交的时候不转交给自己拦截成功,
		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId())
			.eq(ProcessSpecialOperate::getTaskId, taskApproveDTO.getFlowableTaskId())
			.eq(ProcessSpecialOperate::getType, 8)
			.orderByDesc(ProcessSpecialOperate::getOperateDate);
		List<ProcessSpecialOperate> list = processSpecialOperateService.list(queryWrapper);

		if (Func.isNotEmpty(list) && list.size() > 0) {
			if (!Func.equals(list.get(0).getUserId(), AuthUtil.getUserId())) {
				throw new ServiceException("该审批已由其他人受理");
			}
		}

		ProcessInstanceVO processInstanceVo1 = new ProcessInstanceVO();

		//获取下一条可审批的数据-完成后删除下面原有逻辑
		nextApproveVO = selectPcNextApprove(taskApproveDTO, nextQueryVO);

		if (Func.equals(taskApproveDTO.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
			// 拒绝
			refuseTask(processInstance, taskApproveDTO);

			processInstanceRelationService.updateStatus(processInstance.getId(), 2);

		} else if (Func.equals(taskApproveDTO.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
			// 同意
			consentTask(processInstance, taskApproveDTO);

			// 保存任务操作
			ProcessTaskOperation taskOperation = ProcessTaskOperation.builder(processInstance, taskApproveDTO);
			HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
			taskOperation.setNodeId(taskInstance.getTaskDefinitionKey());
			processTaskOperationService.save(taskOperation);
		}

		//跨组织跟新人员的审批状态
		LambdaUpdateWrapper<ProcessInstanceRelation> query = Wrappers.lambdaUpdate();
		query.eq(ProcessInstanceRelation::getProcessInstanceId, processInstance.getId())
			.eq(ProcessInstanceRelation::getNodeId, taskApproveDTO.getCurrentNodeId())
			.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
			.set(ProcessInstanceRelation::getStatus, taskApproveDTO.getStatus());
		processInstanceRelationService.update(query);

		//拒绝或同意后更新表单信息
		if (Func.equals(taskApproveDTO.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())
			|| Func.equals(taskApproveDTO.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			updateDateJson(taskApproveDTO);
		}

		// 查询流程定义数据
		ProcessDefinition processDefinition = processDefinitionService.getOnlyOne(processInstance.getDefinitionId());

		// 启用外挂表单走业务逻辑
		if (Func.equals(taskApproveDTO.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			if (ObjectUtils.isNotEmpty(processDefinition.getIsForm()) && processDefinition.getIsForm() == 1) {
				TestPluginForm testPluginForm = taskApproveDTO.getTestPluginForm();
				testPluginForm.setInstanceId(processInstance.getId());
				testPluginFormService.add(taskApproveDTO.getTestPluginForm());
			}
		}

		return nextApproveVO;
	}


	private NextApproveVO getNextApproveVO(String processName, Long groupId, String createUserName, ProcessInstance processInstance, ProcessInstanceVO processInstanceVo1, NextApproveVO nextApproveVO) {
		List<Long> userIds = new ArrayList<>();
		List<ProcessInstance> orgInstanceList = new ArrayList<>();
		List<ProcessInstance> orgInstanceListAll = new ArrayList<>();

		if (Func.isNotEmpty(groupId) || Func.isNotEmpty(createUserName)) {
			if (Func.isNotEmpty(createUserName)) {
				// 根据createUserName查询user列表(匹配姓名)

			}
			List<Long> definitionIds = new ArrayList<>();
			if (Func.isNotEmpty(groupId)) {
				List<ProcessDefinition> definitions = processDefinitionService.getByCondition(null, groupId);

				definitions.forEach(item -> {
					definitionIds.add(item.getId());
				});
			}
			orgInstanceList = processInstanceService.getCurrentOrgIngListByCreateUser(userIds, definitionIds);
		}

		orgInstanceListAll = processInstanceService.getCurrentOrgIngListByCreateUser(null, null);

		//添加跨组织的流程实例
		//添加跨组织
		List<Long> processInstanceIds = orgInstanceListAll.stream().map(ProcessInstance::getId).collect(Collectors.toList());
		List<ProcessInstance> orgInstanceList1 = new ArrayList<>();

		Map<Long, List<ProcessTaskOperation>> mapTask = new HashMap<>();
		List<ProcessTaskOperation> taskOperationsNew = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {
			LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.in(ProcessTaskOperation::getInstanceId, processInstanceIds);
			taskOperationsNew = processTaskOperationService.list(queryWrapper);
		}

		if (Func.isNotEmpty(taskOperationsNew) && taskOperationsNew.size() > 0) {
			for (ProcessTaskOperation processTaskOperation : taskOperationsNew) {
				Long id = processTaskOperation.getInstanceId();
				List<ProcessTaskOperation> taskNeed = taskOperationsNew.stream().filter(t -> t.getInstanceId().equals(id)).collect(Collectors.toList());

				mapTask.put(processTaskOperation.getId(), taskNeed);
			}
		}
		//查询出所有的流程实例在跨组织表中是否有数据
		List<ProcessInstanceRelation> processInstanceRelationsNew1 = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {

			LambdaQueryWrapper<ProcessInstanceRelation> queryContion1 = Wrappers.lambdaQuery();
			queryContion1.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
				.in(ProcessInstanceRelation::getProcessInstanceId, processInstanceIds)
				.eq(ProcessInstanceRelation::getFlag, 0)
				.eq(ProcessInstanceRelation::getStatus, 0);
			processInstanceRelationsNew1 = processInstanceRelationService.list(queryContion1);
		}
		Map<Long, List<ProcessInstanceRelation>> mapRelations = new HashMap<>();
		if (Func.isNotEmpty(processInstanceRelationsNew1) && processInstanceRelationsNew1.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelationsNew1) {
				Long id = processInstanceRelation.getProcessInstanceId();
				List<ProcessInstanceRelation> taskR = processInstanceRelationsNew1.stream().filter(t -> t.getProcessInstanceId().equals(id)).collect(Collectors.toList());
				if (taskR.size() > 0) {
					mapRelations.put(processInstanceRelation.getProcessInstanceId(), taskR);
				}

			}

		}

		//去除第一个节点审批人是跨组织的人员的流程实例
		for (ProcessInstance processInstance1 : orgInstanceListAll) {

			List<ProcessTaskOperation> taskOperations = mapTask.get(processInstance1.getId());
			// 说明流程发起的的人所在的企业下没有任何人操作
			if (Func.isEmpty(taskOperations) || taskOperations.size() == 0) {
				List<ProcessInstanceRelation> processInstanceRelations = mapRelations.get(processInstance1.getId());
				if (Func.isEmpty(processInstanceRelations) || processInstanceRelations.size() == 0) {
					//说明第一审批人是跨组织人员
					orgInstanceList1.add(processInstance1);
				} else {
					for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
						String nodeId = processInstanceRelation.getNodeId();
						String nodeJson = processInstance1.getProcessUserNodeJson();
						List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);

						List<UserVO> userVos = list.get(0).getUsers();
						for (UserVO userVO : userVos) {
							if (Func.equals(userVO.getUserId(), AuthUtil.getUserId()) && Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								orgInstanceList1.add(processInstance1);
							}

						}

					}
				}

			} else {
				//审批操作表中有记录，但是流程实例的状态为0，该流程还在审批中
				if (Func.equals(processInstance1.getStatus(), 0)) {
					orgInstanceList1.add(processInstance1);
				}

			}
		}

		//添加跨组织
		LambdaQueryWrapper<ProcessInstanceRelation> queryContion = Wrappers.lambdaQuery();
		List<ProcessInstanceRelation> processInstanceRelations = processInstanceRelationService.list(queryContion);
		List<ProcessInstanceRelation> processInstanceRelationsNew = new ArrayList<>();
		if (processInstanceRelations.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
				Long userId = processInstanceRelation.getUserId();
				if (Func.equals(processInstanceRelation.getUserId(), AuthUtil.getUserId())) {
					//人员登录，跨组织表中的人员和登录的人员的id和orgId一样，然后 获取nodeid对应的status是否为0
					ProcessInstance processInstance2 = processInstanceService.getOneById(processInstanceRelation.getProcessInstanceId());

					String nodeJson = processInstance2.getProcessUserNodeJson();
					List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);
					Map<String, UsersNodeVO> maps = list.stream().collect(Collectors.toMap(UsersNodeVO::getNodeId, Function.identity(), (i1, i2) -> i1));
					List<String> nodeIds = list.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
					Boolean flag = false;
					for (UsersNodeVO usersNodeVO : list) {
						String nodeId = usersNodeVO.getNodeId();
						for (UserVO userVO : usersNodeVO.getUsers()) {
							if (Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								//说明审批人中有跨组织
								flag = true;
								break;
							}
						}
						//该需我审批列表中需要添加跨组织的审批
						if (flag) {
							//判断跨组织的审批是否到达审批，判断当前节点的上一个节点是否审批，若未审批，不将该流程实例加入
							//判断该节点是第几个
							int num = nodeIds.indexOf(nodeId);
							if (nodeIds.size() > 1 && num > 0) {
								//获取上一个节点
								int a = num - 1;
								String preNodeId = nodeIds.get(a);
								if (Func.equals(nodeId, processInstanceRelation.getNodeId())) {
									//查询上一个节点审批
									LambdaQueryWrapper<ProcessTaskOperation> queryWrapper2 = Wrappers.lambdaQuery();
									queryWrapper2.eq(ProcessTaskOperation::getInstanceId, processInstance2.getId())
										.eq(ProcessTaskOperation::getNodeId, preNodeId);

									List<ProcessTaskOperation> taskOperations = processTaskOperationService.list(queryWrapper2);

									//上一个节点已经审批
									if (taskOperations.size() > 0) {
										List<Integer> statusList = taskOperations.stream().map(ProcessTaskOperation::getStatus).collect(Collectors.toList());
										Boolean flagNew = statusList.contains(0);
										if (!flagNew) {
											processInstanceRelationsNew.add(processInstanceRelation);
										}
									}
								}
							} else {
								processInstanceRelationsNew.add(processInstanceRelation);
							}


						}


					}


				}
			}

		}
		if (Func.isNotEmpty(processInstanceRelationsNew) && processInstanceRelationsNew.size() > 0) {
			List<Long> ids = processInstanceRelationsNew.stream().map(ProcessInstanceRelation::getProcessInstanceId).collect(Collectors.toList());
			if (ids.size() > 0) {
				LambdaQueryWrapper<ProcessInstance> query1 = Wrappers.lambdaQuery();
				query1.in(ProcessInstance::getId, ids);
				List<ProcessInstance> newList = processInstanceService.list(query1);

				orgInstanceList1.addAll(newList);
			}
		}
		if (Func.isNotEmpty(orgInstanceList1)) {
			orgInstanceListAll.addAll(orgInstanceList1);
		}
		if (orgInstanceListAll.size() == 0) {
			//先判断无条件获取流程实例数量，若没有，直接判定没有需我审批
			nextApproveVO.setFlag(false);
			nextApproveVO.setTips("没有下一条审批");
		} else {//先判断带有条件的需我审批是否存在
			if (orgInstanceList.size() > 0 && !Func.equals(orgInstanceList.size(), orgInstanceListAll.size())) {
				//获取带条件的流程
				processInstanceVo1 = getProcessInstanceVO(processInstance, processInstanceVo1, orgInstanceList, processName);


				if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
					nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
					nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
					nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
					nextApproveVO.setFlag(true);
					nextApproveVO.setTips("获取到下一条审批");
				} else {
					processInstanceVo1 = getProcessInstanceVO(processInstance, processInstanceVo1, orgInstanceListAll, processName);
					if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
						nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
						nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
						nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
						nextApproveVO.setFlag(true);
						nextApproveVO.setTips("获取到下一条审批");
					} else {
						nextApproveVO.setFlag(false);
						nextApproveVO.setTips("没有下一条审批");
					}
				}
			}
			if (orgInstanceList.size() == 0) {
				//不存在
				processInstanceVo1 = getProcessInstanceVO(processInstance, processInstanceVo1, orgInstanceListAll, processName);


				if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
					nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
					nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
					nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
					nextApproveVO.setFlag(true);
					nextApproveVO.setTips("获取到下一条审批");
				} else {
					nextApproveVO.setFlag(false);
					nextApproveVO.setTips("没有下一条审批");
				}

			}


		}
		return nextApproveVO;
	}

	private ProcessInstanceVO getProcessInstanceVO(ProcessInstance processInstance, ProcessInstanceVO processInstanceVo1, List<ProcessInstance> orgInstanceList, String processName) {
		List<Task> tasks = new ArrayList<>();
		List<String> orgIngFlowableInstanceId = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
		TaskQuery taskQuery = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceId)
			.taskAssignee(TaskFlowUtil.getTaskUser()).active()
			.includeProcessVariables()
			.orderByTaskCreateTime()
			.desc();
		if (Func.isNotEmpty(processName)) {
			taskQuery.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(processName));

		}
		//获取到我的代办
		tasks = taskQuery.list();

		if (tasks.size() > 0) {
			List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasks);

			if (todoTaskVos.size() > 0) {
				for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
					if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
						continue;
					}
					LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
					queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
					ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

					processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
					processInstanceVo1.setProcessInstanceId(processInstance1.getId());
					processInstanceVo1.setIsOld(processInstance1.getIsOld());
					break;
				}
			}
		} else {
			TaskQuery taskQuery1 = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceId)
				.taskAssignee(TaskFlowUtil.getTaskUser()).active()
				.includeProcessVariables()
				.orderByTaskCreateTime()
				.desc();
			List<Task> tasks1 = taskQuery1.list();
			if (tasks1.size() > 0 && Func.isNotEmpty(tasks1.get(0).getProcessInstanceId())) {
				List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasks1);

				if (todoTaskVos.size() > 0) {
					for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
						if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
							continue;
						}
						LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
						queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
						ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

						processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
						processInstanceVo1.setProcessInstanceId(processInstance1.getId());
						processInstanceVo1.setIsOld(processInstance1.getIsOld());
						break;
					}
				}
			}
		}
		return processInstanceVo1;
	}


	private void consentTask(ProcessInstance processInstance, ProcessTaskApproveDTO taskApproveDTO) {
		try {
			taskService.complete(taskApproveDTO.getFlowableTaskId());
		} catch (Exception e) {
			log.error("flowable完成任务接口调用失败-{}", taskApproveDTO.getFlowableTaskId());
			throw new ServiceException("该审批已被其他人受理");
		}
	}

	private void refuseTask(ProcessInstance processInstance, ProcessTaskApproveDTO taskApproveDTO) {
		//修改流程实例状态为拒绝
		processInstanceService.changeStatus(Collections.singletonList(processInstance.getId()), ProcessInstance.StatusEnum.NO_PASS.getCode());
		//flowable流程实例挂起
		runtimeService.suspendProcessInstanceById(processInstance.getFlowableInstanceId());
		//修改实例抄送关系为未读
		processInstanceInformService.updateStatusUnReadByProcessInstanceId(processInstance.getId());

		// 保存任务操作
		ProcessTaskOperation taskOperation = ProcessTaskOperation.builder(processInstance, taskApproveDTO);
		HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
		taskOperation.setNodeId(taskInstance.getTaskDefinitionKey());
		processTaskOperationService.save(taskOperation);


		log.error("===============拒绝================");
		msgManage.pushNoPass(processInstance);

		//发送MQ消息通知
//		ProcessEventDTO eventDTO = BeanUtil.copy(processInstance, ProcessEventDTO.class);
//		eventDTO.setStatus(ProcessInstance.StatusEnum.NO_PASS.getCode());
//		eventDTO.setVars(JSON.parseObject(processInstance.getDataJson(), HashMap.class));
//		flowMqManage.sendMqMessage(eventDTO);
	}

	public IPage<ProcessInstanceVO> todoTaskPage(ApprovePageQueryVO timeVO, String processName, Long groupId, String createUserName, Query query) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);
		List<Long> userIds = new ArrayList<>();
		List<Long> userOrgIds = new ArrayList<>();

		if (Func.isNotEmpty(createUserName)) {
			// 根据createUserName查询user列表(匹配姓名)

		}
		List<Long> definitionIds = new ArrayList<>();
		if (Func.isNotEmpty(groupId)) {
			List<ProcessDefinition> definitions = processDefinitionService.getByCondition(null, groupId);
			if (Func.isEmpty(definitions)) {
				return page;
			}
			definitions.forEach(item -> {
				definitionIds.add(item.getId());
			});
		}
		//查询当前组织下的审批中实例
		List<ProcessInstance> orgInstanceLists = processInstanceService.list(Wrappers.<ProcessInstance>lambdaQuery()
			.eq(ProcessInstance::getStatus, ProcessInstance.StatusEnum.ING.getCode())
			.in(Func.isNotEmpty(userIds), ProcessInstance::getCreateUser, userIds)
			.in(Func.isNotEmpty(definitionIds), ProcessInstance::getDefinitionId, definitionIds));
		if (Func.isEmpty(orgInstanceLists)) {
			return page;
		}

		List<Long> processInstanceIds = orgInstanceLists.stream().map(ProcessInstance::getId).collect(Collectors.toList());
		List<ProcessInstance> orgInstanceList = new ArrayList<>();

		Map<Long, List<ProcessTaskOperation>> mapTask = new HashMap<>();
		List<ProcessTaskOperation> taskOperationsNew = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {
			LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.in(ProcessTaskOperation::getInstanceId, processInstanceIds);
			taskOperationsNew = processTaskOperationService.list(queryWrapper);
		}

		if (Func.isNotEmpty(taskOperationsNew) && taskOperationsNew.size() > 0) {
			for (ProcessTaskOperation processTaskOperation : taskOperationsNew) {
				Long id = processTaskOperation.getInstanceId();
				List<ProcessTaskOperation> taskNeed = taskOperationsNew.stream().filter(t -> t.getInstanceId().equals(id)).collect(Collectors.toList());

				mapTask.put(processTaskOperation.getId(), taskNeed);
			}
		}
		//查询出所有的流程实例在跨组织表中是否有数据
		List<ProcessInstanceRelation> processInstanceRelationsNew1 = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {

			LambdaQueryWrapper<ProcessInstanceRelation> queryContion1 = Wrappers.lambdaQuery();
			queryContion1.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
				.in(ProcessInstanceRelation::getProcessInstanceId, processInstanceIds)
				.eq(ProcessInstanceRelation::getFlag, 0)
				.eq(ProcessInstanceRelation::getStatus, 0);
			processInstanceRelationsNew1 = processInstanceRelationService.list(queryContion1);
		}
		Map<Long, List<ProcessInstanceRelation>> mapRelations = new HashMap<>();
		if (Func.isNotEmpty(processInstanceRelationsNew1) && processInstanceRelationsNew1.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelationsNew1) {
				Long id = processInstanceRelation.getProcessInstanceId();
				List<ProcessInstanceRelation> taskR = processInstanceRelationsNew1.stream().filter(t -> t.getProcessInstanceId().equals(id)).collect(Collectors.toList());
				if (taskR.size() > 0) {
					mapRelations.put(processInstanceRelation.getProcessInstanceId(), taskR);
				}

			}

		}

		//去除第一个节点审批人是跨组织的人员的流程实例
		for (ProcessInstance processInstance : orgInstanceLists) {
			List<ProcessTaskOperation> taskOperations = mapTask.get(processInstance.getId());
			//说明流程发起的的人所在的企业下没有任何人操作
			if (Func.isEmpty(taskOperations) || taskOperations.size() == 0) {
				//说明该流程实例没有任何操作，也说明当前的登陆人是第一个审批节点
				List<ProcessInstanceRelation> processInstanceRelations = mapRelations.get(processInstance.getId());
				if (Func.isEmpty(processInstanceRelations) || processInstanceRelations.size() == 0) {
					//说明第一审批人是跨组织人员
					orgInstanceList.add(processInstance);
				} else {
					for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
						String nodeId = processInstanceRelation.getNodeId();
						String nodeJson = processInstance.getProcessUserNodeJson();
						List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);

						List<UserVO> userVos = list.get(0).getUsers();
						for (UserVO userVO : userVos) {
							if (Func.equals(userVO.getUserId(), AuthUtil.getUserId()) && Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								orgInstanceList.add(processInstance);
							}

						}

					}
				}

			} else {
				//审批操作表中有记录，但是流程实例的状态为0，该流程还在审批中
				if (Func.equals(processInstance.getStatus(), 0)) {
					orgInstanceList.add(processInstance);
				}

			}
		}

		//添加跨组织
		LambdaQueryWrapper<ProcessInstanceRelation> queryContion = Wrappers.lambdaQuery();
		queryContion.eq(ProcessInstanceRelation::getFlag, 0)
//			.eq(ProcessInstanceRelation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
			.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
			.eq(ProcessInstanceRelation::getType, 1)
			.eq(ProcessInstanceRelation::getStatus, 0);
		List<ProcessInstanceRelation> processInstanceRelations = processInstanceRelationService.list(queryContion);
		Map<Long, ProcessInstance> instanceMap = new HashMap<>();
		Map<Long, List<ProcessTaskOperation>> operationMap = new HashMap<>();
		if (Func.isNotEmpty(processInstanceRelations) && processInstanceRelations.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
				ProcessInstance p = processInstanceService.getOneById(processInstanceRelation.getProcessInstanceId());
				instanceMap.put(processInstanceRelation.getId(), p);
				LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
				queryWrapper.eq(ProcessTaskOperation::getInstanceId, processInstanceRelation.getProcessInstanceId());
				List<ProcessTaskOperation> operations = processTaskOperationService.list(queryWrapper);
				operationMap.put(processInstanceRelation.getProcessInstanceId(), operations);

			}
		}
		List<ProcessInstanceRelation> processInstanceRelationsNew = new ArrayList<>();
		if (processInstanceRelations.size() > 0) {

			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
				if (Func.equals(processInstanceRelation.getUserId(), AuthUtil.getUserId())) {
//					&& Func.equals(processInstanceRelation.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
					//人员登录，跨组织表中的人员和登录的人员的id和orgId一样，然后 获取nodeid对应的status是否为0
					ProcessInstance processInstance = instanceMap.get(processInstanceRelation.getId());
					String nodeJson = processInstance.getProcessUserNodeJson();
					List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);
					List<String> nodeIds = list.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
					Boolean flag = false;
					for (UsersNodeVO usersNodeVO : list) {
						String nodeId = usersNodeVO.getNodeId();
						for (UserVO userVO : usersNodeVO.getUsers()) {
							if (Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								//说明审批人中有跨组织
								flag = true;
								break;
							}
						}
						if (flag) {
							//该需我审批列表中需要添加跨组织的审批
							//判断跨组织的审批是否到达审批，判断当前节点的上一个节点是否审批，若未审批，不将该流程实例加入
							//判断该节点是第几个
							int num = nodeIds.indexOf(nodeId);
							if (nodeIds.size() > 1 && num > 0) {
								//获取上一个节点
								int a = num - 1;
								String preNodeId = nodeIds.get(a);
								if (Func.equals(nodeId, processInstanceRelation.getNodeId())) {
									//查询上一个节点审批
									List<ProcessTaskOperation> taskOperations = operationMap.get(processInstance.getId()).stream().filter(t -> t.getNodeId().equals(preNodeId)).collect(Collectors.toList());

									//上一个节点已经审批
									if (taskOperations.size() > 0) {
										List<Integer> statusList = taskOperations.stream().map(ProcessTaskOperation::getStatus).collect(Collectors.toList());
										Boolean flagNew = statusList.contains(0);
										if (!flagNew) {
											processInstanceRelationsNew.add(processInstanceRelation);
										}
									}
								}
							} else {
								processInstanceRelationsNew.add(processInstanceRelation);
							}


						}


					}


				}
			}

		}
		if (Func.isNotEmpty(processInstanceRelationsNew) && processInstanceRelationsNew.size() > 0) {
			List<Long> ids = processInstanceRelationsNew.stream().map(ProcessInstanceRelation::getProcessInstanceId).collect(Collectors.toList());
			if (ids.size() > 0) {
				LambdaQueryWrapper<ProcessInstance> query1 = Wrappers.lambdaQuery();
				query1.in(ProcessInstance::getId, ids);
				List<ProcessInstance> newList = processInstanceService.list(query1);

				orgInstanceList.addAll(newList);
			}
		}

		if (Func.isEmpty(orgInstanceList)) {
			return page;
		}
		//流程实例按时间排序
		List<String> orgIngFlowableInstanceId = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());

		TaskQuery taskQuery = null;
		if (Func.isEmpty(timeVO) || Func.isEmpty(timeVO.getSeq()) || Func.equals(timeVO.getSeq(), 1)) {
			taskQuery = taskService.createTaskQuery()
				.processInstanceIdIn(orgIngFlowableInstanceId)
				.taskAssignee(TaskFlowUtil.getTaskUser()).active()
				.includeProcessVariables()
				.orderByTaskCreateTime().desc();
		} else if (Func.isNotEmpty(timeVO) && Func.isEmpty(timeVO.getSeq()) && Func.equals(timeVO.getSeq(), CommNumberEnum.NUMBER_2.getCode())) {
			taskQuery = taskService.createTaskQuery()
				.processInstanceIdIn(orgIngFlowableInstanceId)
				.taskAssignee(TaskFlowUtil.getTaskUser()).active()
				.includeProcessVariables()
				.orderByTaskCreateTime().asc();

		}


		//发起、完成时间过滤
		if (Func.isNotEmpty(timeVO.getStartTime()) || Func.isNotEmpty(timeVO.getEndTime()) || Func.isNotEmpty(timeVO.getCStartTime()) || Func.isNotEmpty(timeVO.getCEndTime())) {
			LambdaQueryWrapper<ProcessInstance> timeWrapper = Wrappers.<ProcessInstance>lambdaQuery()
				.in(Func.isNotEmpty(orgInstanceList), ProcessInstance::getId, orgInstanceList.stream().map(ProcessInstance::getId).collect(Collectors.toList()));
			List<Integer> endStatus = Arrays.asList(1, 2, 3, 4);
			String startTime = Func.isEmpty(timeVO.getStartTime()) ? StringPool.EMPTY : timeVO.getStartTime();
			String endTime = Func.isEmpty(timeVO.getEndTime()) ? StringPool.EMPTY : timeVO.getEndTime();
			String cStartTime = Func.isEmpty(timeVO.getCStartTime()) ? StringPool.EMPTY : timeVO.getCStartTime();
			String cEndTime = Func.isEmpty(timeVO.getCEndTime()) ? StringPool.EMPTY : timeVO.getCEndTime();
			timeWrapper.ge(Func.isNotEmpty(startTime), ProcessInstance::getCreateTime, startTime)
				.le(Func.isNotEmpty(endTime), ProcessInstance::getCreateTime, endTime);
			timeWrapper.and(Func.isNotEmpty(cStartTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.ge(ProcessInstance::getUpdateTime, cStartTime));
			timeWrapper.and(Func.isNotEmpty(cEndTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.le(ProcessInstance::getUpdateTime, cEndTime));
			List<ProcessInstance> timeList = processInstanceService.list(timeWrapper);
			if (Func.isNotEmpty(timeList)) {
				orgInstanceList = timeList;
				taskQuery.processInstanceIdIn(timeList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList()));
			} else {
				return page;
			}
		}
		//发起、完成时间过滤--END

		if (Func.isNotEmpty(processName)) {
			//正文value检索
			List<InstanceValues> instanceValuesList = instanceValuesService.list(Wrappers.<InstanceValues>lambdaQuery()
				.in(InstanceValues::getProcessInstanceId, orgInstanceList.stream().map(ProcessInstance::getId).collect(Collectors.toList()))
				.like(InstanceValues::getValueText, processName));
			if (Func.isNotEmpty(instanceValuesList)) {
				List<String> fidList = instanceValuesList.stream().map(InstanceValues::getFlowableInstanceId).collect(Collectors.toList());
				if (Func.isNotEmpty(fidList)) {
					taskQuery.or()
						.processInstanceIdIn(fidList)
						.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(processName))
						.endOr();
				}
			} else {
				taskQuery.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(processName));
			}
			//正文value检索END
		}
		log.info("==end:" + taskQuery.list());

		//去重，审批第一个节点是多个跨组织的同一个人

		List<Task> tasks = taskQuery.listPage(Func.toInt((query.getCurrent() - 1) * page.getSize()), Func.toInt(query.getSize()));
		LinkedList<Task> tasks1 = new LinkedList<>();
		List<String> processIds = new ArrayList<>();
		for (Task task : tasks) {

			if (Func.isEmpty(processIds) || !processIds.contains(task.getProcessInstanceId())) {
				processIds.add(task.getProcessInstanceId());
				tasks1.add(task);
			}
		}
		List<Task> tasknew = new ArrayList<>();
		for (Task task : tasks1) {
			String nodeId = task.getTaskDefinitionKey();
			String processId = task.getProcessInstanceId();
			ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(processId);
			LambdaQueryWrapper<ProcessTaskOperation> queryWrapper3 = Wrappers.lambdaQuery();
			queryWrapper3
//				.eq(ProcessTaskOperation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
				.eq(ProcessTaskOperation::getInstanceId, processInstance.getId())
				.eq(ProcessTaskOperation::getNodeId, nodeId).eq(ProcessTaskOperation::getCreateUser, AuthUtil.getUserId());
			List<ProcessTaskOperation> processTaskOperations = processTaskOperationService.list(queryWrapper3);
			if (Func.isEmpty(processTaskOperations) && processTaskOperations.size() == 0) {
				//说明该流程当前人已经审批过
				tasknew.add(task);
			}

		}

		if (Func.isEmpty(tasknew)) {
			return page;
		}
		if (tasks.size() == tasknew.size()) {
			page.setTotal(taskQuery.count());
		} else {
			Long num = Long.valueOf(tasks.size() - tasknew.size());
			Long num1 = Long.valueOf(taskQuery.count() - num);
			page.setTotal(num1);
		}

		List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasknew);
		//拼接摘要，高亮显示字段
		// getNewDateJson(processName, todoTaskVos);
		FormKvUtil.getNewDateJson(processName, todoTaskVos);
		page.setRecords(todoTaskVos);
		return page;
	}

	/**
	 * 构造voList
	 *
	 * @param tasks
	 * @return
	 */
	private List<ProcessInstanceVO> convertFlowableTask(List<Task> tasks) {
		List<String> flowableInstanceIds = new ArrayList<>();
		tasks.forEach(item -> {
			flowableInstanceIds.add(item.getProcessInstanceId());
		});
		//获取流程实例
		LambdaQueryWrapper<ProcessInstance> instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
			.in(ProcessInstance::getFlowableInstanceId, flowableInstanceIds);
		List<ProcessInstance> processInstances = processInstanceService.list(instanceWrapper);
		List<Long> definitionIds = new ArrayList<>();
		processInstances.forEach(item -> {
			definitionIds.add(item.getDefinitionId());
		});
		Map<String, ProcessInstance> instanceMap = processInstances.stream().collect(Collectors.toMap(ProcessInstance::getFlowableInstanceId, Function.identity()));
		//获取流程实例发起人
		Map<String, UserDept> userMap = new HashMap<>();
		List<UserDept> userDTOList = processInstances.stream().map(ins -> {
			UserDept uDept = new UserDept();
			uDept.setUserId(ins.getCreateUser());
			return uDept;
		}).collect(Collectors.toList());

		List<ProcessDefinition> definitions = processDefinitionService.selectByProcessDefinitionIds(definitionIds);
		if (Func.isEmpty(definitions)) {
			throw new ServiceException("查询流程定义失败！！");
		}
		Map<Long, ProcessDefinition> definitionMap = definitions.stream().collect(Collectors.toMap(ProcessDefinition::getId, Function.identity(), (i1, i2) -> i1));
		//获取流程分组
		List<Long> groupIds = definitions.stream().map(ProcessDefinition::getGroupId).collect(Collectors.toList());

		List<ProcessGroup> groups = processGroupManage.selectByGroupIds(groupIds);
		Map<Long, ProcessGroup> groupMap = groups.stream().collect(Collectors.toMap(ProcessGroup::getId, Function.identity()));

		return tasks.stream().map(item -> {
			ProcessInstanceVO todoTaskVO = new ProcessInstanceVO();
			todoTaskVO.setFlowableTaskId(item.getId());
			ProcessInstance processInstance = instanceMap.get(item.getProcessInstanceId());
			ProcessDefinition processDefinition = definitionMap.get(processInstance.getDefinitionId());
			todoTaskVO.setProcessInstanceId(processInstance.getId());
			todoTaskVO.setDataJson(processInstance.getDataJson());
			todoTaskVO.setProcessName(processDefinition.getProcessName());
			todoTaskVO.setFormStyleJson(processDefinition.getFormStyleJson());
			ProcessGroup processGroup = groupMap.get(processDefinition.getGroupId());
			todoTaskVO.setGroupName(processGroup.getGroupName());
//            UserDeptExtDTO createUser = userMap.get(processInstance.getCreateUser());
//			UserDept createUser = userMap.get(processInstance.getCreateUser() + "-" + processInstance.getOrgId());
//			if (Func.isNotEmpty(createUser)) {
//
//				todoTaskVO.setCreateUserName(createUser.getEmployeeName());
//				todoTaskVO.setDeptName(createUser.getDeptName());
//				//跨组织需要显示流程实例的orgId的name
//				todoTaskVO.setOrgName(createUser.getOrgName());
//				todoTaskVO.setAvatar(createUser.getAvatar());
//			}
			todoTaskVO.setCreateDate(processInstance.getCreateTime());
			todoTaskVO.setStatus(processInstance.getStatus());
			todoTaskVO.setStatusStr(ProcessInstance.StatusEnum.from(processInstance.getStatus()).getDesc());
			todoTaskVO.setCreateUserId(processInstance.getCreateUser());
			todoTaskVO.setFlowableTaskId(item.getId());
			todoTaskVO.setOldProcessId(processInstance.getRelationProcessId());
			todoTaskVO.setRemark(processInstance.getRemark());
			todoTaskVO.setIsOld(0);
			return todoTaskVO;
		}).collect(Collectors.toList());
	}

	/**
	 * 当前用户审批过的流程实例
	 *
	 * @param processName
	 * @param groupId
	 * @param createUserName
	 * @param query
	 * @return
	 */
	public IPage<ProcessInstanceVO> doneTaskPage(ApprovePageQueryVO timeVO, String processName, Long groupId, String createUserName, Query query) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);
		/**查询当前用户已审批的任务 任务操作记录表*/
		LambdaQueryWrapper<ProcessTaskOperation> taskWrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getCreateUser, AuthUtil.getUserId())
//			.eq(ProcessTaskOperation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
			.orderByDesc(ProcessTaskOperation::getCreateTime);
		List<ProcessTaskOperation> taskOperations = processTaskOperationService.list(taskWrapper);
		/**组装map*/
		//改变了顺序
		LinkedHashMap<Long, Date> maps = new LinkedHashMap<>();
		if (Func.isNotEmpty(taskOperations) && taskOperations.size() > 0) {
			for (ProcessTaskOperation processTaskOperation : taskOperations) {
				maps.put(processTaskOperation.getInstanceId(), processTaskOperation.getCreateTime());
			}
		}
		/**获取跨组织的流程实例*/
		LambdaQueryWrapper<ProcessInstanceRelation> queryContion = Wrappers.lambdaQuery();
		queryContion.eq(ProcessInstanceRelation::getFlag, 0)
//			.eq(ProcessInstanceRelation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
			.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
			.eq(ProcessInstanceRelation::getType, 1)
			.ne(ProcessInstanceRelation::getStatus, 0);
		List<ProcessInstanceRelation> processInstanceRelations = processInstanceRelationService.list(queryContion);
		if (processInstanceRelations.size() > 0) {
			Map<Long, Date> mapsNew = processInstanceRelations.stream().collect(Collectors.toMap(ProcessInstanceRelation::getProcessInstanceId, ProcessInstanceRelation::getUpdateTime, (i1, i2) -> i1));
			maps.putAll(mapsNew);
		}
		/**获取转交的流程实例*/
		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(ProcessSpecialOperate::getOwnerId, AuthUtil.getUserId());
		queryWrapper.orderByDesc(ProcessSpecialOperate::getCreateTime);
		List<ProcessSpecialOperate> processSpecialOperateList = processSpecialOperateService.list(queryWrapper);
		List<String> transimtList = processSpecialOperateList.stream().map(ProcessSpecialOperate::getProcessInstanceId).collect(Collectors.toList());
		List<String> tIds = transimtList.stream().distinct().collect(Collectors.toList());


		if (Func.isEmpty(taskOperations) && tIds.size() == 0 && maps.size() == 0) {
			return page;
		}

		if (tIds.size() > 0) {
			/**获取流程实例*/
			LambdaQueryWrapper<ProcessInstance> instanceWrapperT = Wrappers.<ProcessInstance>lambdaQuery()
				.in(ProcessInstance::getFlowableInstanceId, tIds);
			List<ProcessInstance> list = processInstanceService.list(instanceWrapperT);
			Map<Long, ProcessInstance> instanceMap = new HashMap<>();
			Map<String, ProcessSpecialOperate> listMap = new HashMap<>();
			for (ProcessInstance processInstance : list) {
				instanceMap.put(processInstance.getId(), processInstance);

			}
			LambdaQueryWrapper<ProcessSpecialOperate> queryM = Wrappers.lambdaQuery();
			queryM.eq(ProcessSpecialOperate::getOwnerId, AuthUtil.getUserId());
			queryM.eq(ProcessSpecialOperate::getOperateType, 8);
			queryM.orderByDesc(ProcessSpecialOperate::getCreateTime);
			List<ProcessSpecialOperate> processSpecialOperatesNew = processSpecialOperateService.list(queryM);
			if (Func.isNotEmpty(processSpecialOperatesNew) && processSpecialOperatesNew.size() > 0) {
			}
			for (ProcessSpecialOperate processSpecialOperate : processSpecialOperatesNew) {
				listMap.put(processSpecialOperate.getProcessInstanceId(), processSpecialOperate);
			}

			List<Long> idss = list.stream().map(ProcessInstance::getId).collect(Collectors.toList());
//            /**转交的流程实例拼入列表*/
			for (Long id : idss) {
				if (!maps.containsKey(id) && Func.isNotEmpty(listMap)) {
					ProcessInstance processInstance = instanceMap.get(id);
					ProcessSpecialOperate processSpecialOperates = listMap.get(processInstance.getFlowableInstanceId());
					if (Func.isNotEmpty(processSpecialOperates)) {
						maps.put(id, processSpecialOperates.getCreateTime());
					}
				}


			}


		}

		LinkedHashMap<Long, Date> finalOut = sortMapByValues(maps, timeVO);
		List<Long> keys = Arrays.asList(finalOut.keySet().toArray(new Long[finalOut.keySet().size()]));


		LambdaQueryWrapper<ProcessInstance> instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
			.in(ProcessInstance::getId, keys);

		//发起时间排序，默认是审批时间排序
		if (Func.isEmpty(timeVO) || Func.isEmpty(timeVO.getSeq())) {
			instanceWrapper.last("order by field(id," + Func.join(keys) + ")");
		} else if (Func.equals(timeVO.getSeq(), 1)) {
			instanceWrapper.orderByDesc(ProcessInstance::getCreateTime);
		} else if (Func.equals(timeVO.getSeq(), CommNumberEnum.NUMBER_2.getCode())) {
			instanceWrapper.orderByAsc(ProcessInstance::getCreateTime);
		}


		// 流程定义名称、分组 条件
		if (Func.isNotEmpty(groupId)) {
			List<ProcessDefinition> processDefinitions = processDefinitionService.getByCondition(processName, groupId);
			if (Func.isEmpty(processDefinitions)) {
				return page;
			}
			List<Long> processDefinitionIds = processDefinitions.stream().map(ProcessDefinition::getId).collect(Collectors.toList());
			instanceWrapper.in(ProcessInstance::getDefinitionId, processDefinitionIds);
		}
		//createUserName 条件
		if (Func.isNotEmpty(createUserName)) {
			List<Long> userIds = new ArrayList<>();
			instanceWrapper.in(ProcessInstance::getCreateUser, userIds);
		}
		// sql查询按照指定顺序
		//发起、完成时间过滤
		if (Func.isNotEmpty(timeVO.getStartTime()) || Func.isNotEmpty(timeVO.getEndTime()) || Func.isNotEmpty(timeVO.getCStartTime()) || Func.isNotEmpty(timeVO.getCEndTime())) {
			List<Integer> endStatus = Arrays.asList(1, 2, 3, 4);
			String startTime = Func.isEmpty(timeVO.getStartTime()) ? StringPool.EMPTY : timeVO.getStartTime();
			String endTime = Func.isEmpty(timeVO.getEndTime()) ? StringPool.EMPTY : timeVO.getEndTime();
			String cStartTime = Func.isEmpty(timeVO.getCStartTime()) ? StringPool.EMPTY : timeVO.getCStartTime();
			String cEndTime = Func.isEmpty(timeVO.getCEndTime()) ? StringPool.EMPTY : timeVO.getCEndTime();
			instanceWrapper.ge(Func.isNotEmpty(startTime), ProcessInstance::getCreateTime, startTime)
				.le(Func.isNotEmpty(endTime), ProcessInstance::getCreateTime, endTime);
			instanceWrapper.and(Func.isNotEmpty(cStartTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.ge(ProcessInstance::getUpdateTime, cStartTime));
			instanceWrapper.and(Func.isNotEmpty(cEndTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.le(ProcessInstance::getUpdateTime, cEndTime));
		}
		//发起、完成时间过滤--END
		//正文value检索
		if (Func.isNotEmpty(processName)) {
			List<ProcessDefinition> processDefinitions = processDefinitionService.getByCondition(processName, groupId);
			List<Long> processDefinitionIds = processDefinitions.stream().map(ProcessDefinition::getId).collect(Collectors.toList());

			List<ProcessInstance> piList = processInstanceService.list(instanceWrapper);
			if (Func.isNotEmpty(piList)) {
				keys = piList.stream().map(ProcessInstance::getId).collect(Collectors.toList());
			}
			List<InstanceValues> instanceValuesList = instanceValuesService.list(Wrappers.<InstanceValues>lambdaQuery()
				.in(InstanceValues::getProcessInstanceId, keys)
				.like(InstanceValues::getValueText, processName));

			if (Func.isEmpty(processDefinitionIds) && Func.isEmpty(instanceValuesList)) {
				return page;
			}
			if (Func.isNotEmpty(instanceValuesList)) {
				List<Long> idList = instanceValuesList.stream().map(InstanceValues::getProcessInstanceId).collect(Collectors.toList());
				if (Func.isNotEmpty(idList)) {
					instanceWrapper.and(ins -> ins.in(ProcessInstance::getId, idList)
						.or().in(Func.isNotEmpty(processDefinitionIds), ProcessInstance::getDefinitionId, processDefinitionIds));
				}
			} else {
				instanceWrapper.in(Func.isNotEmpty(processDefinitionIds), ProcessInstance::getDefinitionId, processDefinitionIds);
			}
			//正文value检索END
		}
		//查询流程实例list
		IPage<ProcessInstance> processInstancePage = processInstanceService.page(Condition.getPage(query), instanceWrapper);

		if (Func.isEmpty(processInstancePage.getRecords())) {
			return page;
		}
		List<ProcessInstance> myList = processInstancePage.getRecords();
		List<ProcessInstance> processInstances = new ArrayList<>();

		//转换VO
		List<ProcessInstanceVO> processInstanceVoList = convertProcessInstanceVO(myList);
		FormKvUtil.getNewDateJson(processName, processInstanceVoList);
		page.setRecords(processInstanceVoList);
		page.setTotal(processInstancePage.getTotal());
		return page;

	}

	private static LinkedHashMap<Long, Date> sortMapByValues(Map<Long, Date> maps, ApprovePageQueryVO timeVO) {
		LinkedHashMap<Long, Date> finalOut = new LinkedHashMap<>();
		if (Func.isEmpty(timeVO) || Func.equals(timeVO.getSeq(), 1)) {
			maps.entrySet()
				.stream()
				.sorted(Map.Entry.<Long, Date>comparingByValue().reversed())
				.forEachOrdered(e -> finalOut.put(e.getKey(), e.getValue()));
		} else if (Func.isEmpty(timeVO) || Func.equals(timeVO.getSeq(), CommNumberEnum.NUMBER_2.getCode())) {
			maps.entrySet()
				.stream()
				.sorted(Map.Entry.<Long, Date>comparingByValue())
				.forEachOrdered(e -> finalOut.put(e.getKey(), e.getValue()));
		} else {
			maps.entrySet()
				.stream()
				.sorted(Map.Entry.<Long, Date>comparingByValue().reversed())
				.forEachOrdered(e -> finalOut.put(e.getKey(), e.getValue()));
		}


		return finalOut;
	}

	private static LinkedHashMap<String, Date> sortMapByValuesNew(Map<String, Date> maps, ApprovePageQueryVO timeVO) {
		LinkedHashMap<String, Date> finalOut = new LinkedHashMap<>();
		if (Func.isEmpty(timeVO) || Func.equals(timeVO.getSeq(), 1)) {
			maps.entrySet()
				.stream()
				.sorted(Map.Entry.<String, Date>comparingByValue().reversed())
				.forEachOrdered(e -> finalOut.put(e.getKey(), e.getValue()));
		} else {
			maps.entrySet()
				.stream()
				.sorted(Map.Entry.<String, Date>comparingByValue())
				.forEachOrdered(e -> finalOut.put(e.getKey(), e.getValue()));
		}


		return finalOut;
	}


	/**
	 * VOList转换
	 *
	 * @param processInstances
	 * @return
	 */
	public List<ProcessInstanceVO> convertProcessInstanceVO(List<ProcessInstance> processInstances) {

		// 创建用户
		List<Long> createUserIds = new ArrayList<>();

		// 流程定义ID
		List<Long> processDefinitionIds = new ArrayList<>();

		// 流程实例ID
		List<Long> oldInsIds = new ArrayList<>();

		for (ProcessInstance item : processInstances) {
			createUserIds.add(item.getCreateUser());

			// 有的流程定义可能为空
			if (Func.equals(item.getIsOld(), 0) && Func.isNotEmpty(item.getDefinitionId())) {
				processDefinitionIds.add(item.getDefinitionId());
			}
			if (Func.equals(item.getIsOld(), 1)) {
				oldInsIds.add(item.getId());
			}
		}

		// 流程定义分组
		Map<Long, ProcessDefinition> definitionMap = new HashMap<>();
		// 流程分组分组
		Map<Long, ProcessGroup> groupMap = new HashMap<>();

		if (Func.isNotEmpty(processDefinitionIds)) {
			// 流程定义数据
			List<ProcessDefinition> processDefinitions = processDefinitionService.selectByProcessDefinitionIds(processDefinitionIds);

			definitionMap = processDefinitions.stream().collect(Collectors.toMap(ProcessDefinition::getId, Function.identity()));

			// 获取流程分组
			List<Long> groupIds = processDefinitions.stream().map(ProcessDefinition::getGroupId).collect(Collectors.toList());
			List<ProcessGroup> groups = processGroupManage.selectByGroupIds(groupIds);

			groupMap = groups.stream().collect(Collectors.toMap(ProcessGroup::getId, Function.identity()));
		}

		// 流程表单关联数据
		Map<Long, ProcFormRelation> oldInsMap = new HashMap<>();

		// 流程的关联数据
		Map<String, ActDeModelAssociation> oldDefinitionMap = new HashMap<>();

		// 分类数据
		Map<Integer, CommonCategory> oldCategoryMap = new HashMap<>();

		if (Func.isNotEmpty(oldInsIds)) {
			// 流程表单关联数据
			List<ProcFormRelation> oldInstanceList = procFormRelationService.listAllByIds(oldInsIds);
			oldInsMap = oldInstanceList.stream().collect(Collectors.toMap(ProcFormRelation::getId, Function.identity(), (i1, i2) -> i1));

			// 流程的关联数据
			List<String> oldFlowableDefinitionIds = oldInstanceList.stream().map(ProcFormRelation::getProcDefId).collect(Collectors.toList());
			List<ActDeModelAssociation> oldDefinitionList = flowEngineService.selectMyBatchIds(oldFlowableDefinitionIds);
			oldDefinitionMap = oldDefinitionList.stream().collect(Collectors.toMap(ActDeModelAssociation::getProcessId, Function.identity(), (i1, i2) -> i1));

			// 分类数据
			List<Long> oldFirstCategoryIds = oldDefinitionList.stream().map(ActDeModelAssociation::getFormCategoryId).collect(Collectors.toList());
			List<CommonCategory> oldCategoryList = commonCategoryService.selectByFirstIds(oldFirstCategoryIds);
			oldCategoryMap = oldCategoryList.stream().collect(Collectors.toMap(CommonCategory::getFirstId, Function.identity(), (i1, i2) -> i1));
		}

		Map<Long, ProcessDefinition> finalDefinitionMap = definitionMap;
		Map<Long, ProcessGroup> finalGroupMap = groupMap;
		Map<Long, ProcFormRelation> finalOldInsMap = oldInsMap;
		Map<String, ActDeModelAssociation> finalOldDefinitionMap = oldDefinitionMap;
		Map<Integer, CommonCategory> finalOldCategoryMap = oldCategoryMap;

		return processInstances.stream().map(item -> {
			ProcessInstanceVO processInstanceVo = new ProcessInstanceVO();
			processInstanceVo.setDataJson(item.getDataJson());

			if (Func.equals(item.getIsOld(), 0)) {
				ProcessDefinition processDefinition = finalDefinitionMap.get(item.getDefinitionId());
				processInstanceVo.setProcessName(processDefinition.getProcessName());
				processInstanceVo.setFormStyleJson(processDefinition.getFormStyleJson());
				processInstanceVo.setIsForm(processDefinition.getIsForm());
				ProcessGroup processGroup = finalGroupMap.get(processDefinition.getGroupId());
				processInstanceVo.setGroupName(processGroup.getGroupName());
				processInstanceVo.setIsOld(0);

			} else if (Func.equals(item.getIsOld(), 1)) {

				ProcFormRelation oldInstance = finalOldInsMap.get(item.getId());
				if (Func.isNotEmpty(oldInstance)) {
					ActDeModelAssociation oldDefinition = finalOldDefinitionMap.get(oldInstance.getProcDefId());
					processInstanceVo.setProcessName(oldDefinition.getFlowName());
					JSONArray oldFormStyleArr = new JSONArray();
					if (Func.isNotEmpty(oldInstance.getSysKey())) {
						oldFormStyleArr.addAll(JSON.parseArray(oldInstance.getSysKey()));
					}
					if (Func.isNotEmpty(oldInstance.getCustomKey())) {
						oldFormStyleArr.addAll(JSON.parseArray(oldInstance.getCustomKey()));
					}
					processInstanceVo.setFormStyleJson(JSON.toJSONString(oldFormStyleArr));
					CommonCategory oldCategory = finalOldCategoryMap.get(Func.toInt(oldDefinition.getFormCategoryId()));
					processInstanceVo.setGroupName(oldCategory.getLevelFirstName());
					processInstanceVo.setIsOld(1);
				}

			}
			processInstanceVo.setFlowableInstanceId(item.getFlowableInstanceId());
			processInstanceVo.setProcessInstanceId(item.getId());
			processInstanceVo.setDataJson(item.getDataJson());

			// 查询用户部门信息
			UserDeptDTO userDeptDto = userService.queryUserDept(item.getCreateUser(), item.getCreateDept());
			processInstanceVo.setCreateUserName(userDeptDto == null ? null : userDeptDto.getEmployeeName());
			processInstanceVo.setDeptName(userDeptDto == null ? null : userDeptDto.getDeptName());
			processInstanceVo.setOrgName(userDeptDto == null ? null : userDeptDto.getOrgName());
			processInstanceVo.setAvatar(userDeptDto == null ? null : userDeptDto.getAvatar());

			processInstanceVo.setCreateDate(item.getCreateTime());
			processInstanceVo.setStatus(item.getStatus());
			processInstanceVo.setStatusStr(ProcessInstance.StatusEnum.from(item.getStatus()).getDesc());
			processInstanceVo.setCreateUserId(item.getCreateUser());

			processInstanceVo.setOldProcessId(item.getRelationProcessId());
			return processInstanceVo;
		}).collect(Collectors.toList());
	}


	@Transactional(rollbackFor = Exception.class)
	@Async
	public void autoPass(AutoPassDTO autoPassDTO) {
		try {
			TimeUnit.SECONDS.sleep(2);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		taskService.complete(autoPassDTO.getFlowableTaskId());
		HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(autoPassDTO.getFlowableTaskId()).singleResult();
		ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(historicTaskInstance.getProcessInstanceId());
		ProcessTaskOperation taskOperation = new ProcessTaskOperation();
		taskOperation.setFlowableTaskId(autoPassDTO.getFlowableTaskId());
		taskOperation.setInstanceId(processInstance.getId());
		taskOperation.setOpinion("自动通过");
		taskOperation.setStatus(ProcessTaskOperation.StatusEnum.AUTO_PASS.getCode());
		taskOperation.setNodeId(historicTaskInstance.getTaskDefinitionKey());
		taskOperation.setTenantId("000001");
		taskOperation.setCreateUser(11111111L);
		taskOperation.setUpdateUser(11111111L);
		taskOperation.setCreateDept(autoPassDTO.getCreateDept());
		processTaskOperationService.save(taskOperation);

	}

	@Transactional(rollbackFor = Exception.class)
	@Async
	public void nodeAutoPass(String processDefinitionId, String flowableInstanceId, TaskEntity entity, String currentId, String assignee, String taskId, String desc) {
		try {
			TimeUnit.SECONDS.sleep(2);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		log.info("当前节点审批人》{}", assignee);
		//获取上一节点的审批人
		//当前节点的执行id
		String executionId = entity.getExecutionId();


		//判断当前节点是否是或签模式
		//先获取流程实例
		LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
		queryWrapper1.eq(ProcessInstance::getFlowableInstanceId, flowableInstanceId);
		ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);
		String nodeJson = processInstance1.getProcessUserNodeJson();
		//获取流程实例的节点信息
		List<UsersNodeVO> usersNodeVos = JSON.parseArray(nodeJson, UsersNodeVO.class);
		List<String> nodeIds = usersNodeVos.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
		log.info("流程实例的节点信息-》" + nodeIds);
		//在获取流程定义的节点信息
		LambdaQueryWrapper<ProcessDefinition> queryW = Wrappers.lambdaQuery();
		queryW.eq(ProcessDefinition::getId, processInstance1.getDefinitionId());
		String json = processDefinitionService.getOne(queryW).getProcessJson();

		if (Func.isNotEmpty(executionId)) {
			String currentActivityId = currentId;
			log.info("获取当前节点currentActivityId" + currentActivityId);

			List<NodeModel> nodes = FlowConvertData.parseBody(json);
			List<NodeModel> newNodes = new ArrayList<>();
			log.info("流程节点信息-》{}" + nodes);


			Map<String, NodeModel> nodeModelMap = nodes.stream().collect(Collectors.toMap(NodeModel::getResourceId, Function.identity()));
			log.info("流程节点信息-》{}" + nodeModelMap);
			int type = 0;
			if (Func.isNotEmpty(nodeModelMap.get(currentActivityId).getProperties())) {
				type = (int) nodeModelMap.get(currentActivityId).getProperties().get("approverType");
			}
			//2是或签
			if (type != CommNumberEnum.NUMBER_2.getCode()) {
				String beforeNodeId = null;
				int num = nodeIds.indexOf(currentActivityId);
				if (num > 0) {
					//上一个节点的nodeId
					beforeNodeId = nodeIds.get(num - 1);


					if (Func.isNotEmpty(beforeNodeId)) {

						log.info("sourceRef>{}" + beforeNodeId);

						log.info("start_>{}.........自动审批开始");
						List<HistoricActivityInstance> hisActivityInstanceList = ((HistoricActivityInstanceQuery) historyService
							.createHistoricActivityInstanceQuery()
							.processInstanceId(flowableInstanceId).activityType("userTask").activityId(beforeNodeId).taskAssignee(assignee)
							.finished().orderByHistoricActivityInstanceEndTime().desc())
							.list();
						log.info("@@" + JSONObject.toJSONString(hisActivityInstanceList));

						for (int i = 0; i < hisActivityInstanceList.size(); i++) {

							String str = assignee;
							String str1 = str.substring(0, str.indexOf("_"));
							String str2 = str.substring(str1.length() + 1, str.length());
							if (Func.equals(hisActivityInstanceList.get(i).getAssignee(), assignee)) {
								taskService.complete(taskId);
								HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
								ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(historicTaskInstance.getProcessInstanceId());
								ProcessTaskOperation taskOperation = new ProcessTaskOperation();
								taskOperation.setFlowableTaskId(taskId);
								taskOperation.setInstanceId(processInstance.getId());
								taskOperation.setOpinion("自动通过");
								taskOperation.setStatus(ProcessTaskOperation.StatusEnum.AUTO_PASS.getCode());
								taskOperation.setNodeId(historicTaskInstance.getTaskDefinitionKey());
								taskOperation.setTenantId("000001");
								taskOperation.setCreateUser(Long.valueOf(str2));
								taskOperation.setUpdateUser(Long.valueOf(str2));
								taskOperation.setCreateDept(null);
								log.info("1111111111111111");
								processTaskOperationService.save(taskOperation);
								log.info("111111111111111122222222222222");
								break;

							}


						}
						// }

					}
				}
			}

		}


	}


	private String resPreNodeId(BpmnModel bpmnModel, String currentNodeId) {
		FlowNode currentNode = (FlowNode) bpmnModel.getFlowElement(currentNodeId);
		//查询上一个节点方法改造
		SequenceFlow sequenceFlowNew = currentNode.getIncomingFlows().get(0);
		String preNodeId = sequenceFlowNew.getSourceRef();
		FlowNode preNode = (FlowNode) bpmnModel.getFlowElement(preNodeId);
		if (preNode instanceof UserTask) {
			return preNodeId;
		} else if (preNode instanceof StartEvent) {
			return null;
		}

		return resPreNodeId(bpmnModel, preNodeId);


	}

	/**
	 * 查询许我审批列表
	 * 1.
	 */
	public IPage<ProcessInstanceVO> appTodoTaskPage(ApprovePageQueryVO timeVO, String keyword, Query query, String definationId) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);
		List<Long> definationIds = new ArrayList<>();
		List<ProcessInstance> orgInstanceLists = new ArrayList<>();

		if (Func.isNotEmpty(definationId)) {
			//截取字符串
			String[] ids = definationId.split(",");
			//string 转为  long
			List<Long> idsNeed = Arrays.stream(ids)
				.map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
			definationIds.addAll(idsNeed);
			//获取当前组织下审批中实例
			orgInstanceLists = processInstanceService.getCurrentOrgIngListByCreateUser(null, definationIds, timeVO);
		} else {
			//获取当前组织下审批中实例
			orgInstanceLists = processInstanceService.getCurrentOrgIngListByCreateUser(null, null, timeVO);
		}


		//查询当前组织下的审批中实例

		List<Long> processInstanceIds = orgInstanceLists.stream().map(ProcessInstance::getId).collect(Collectors.toList());
		List<ProcessInstance> orgInstanceList = new ArrayList<>();

		Map<Long, List<ProcessTaskOperation>> mapTask = new HashMap<>();
		List<ProcessTaskOperation> taskOperationsNew = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {
			LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.in(ProcessTaskOperation::getInstanceId, processInstanceIds);
			taskOperationsNew = processTaskOperationService.list(queryWrapper);
		}

		if (Func.isNotEmpty(taskOperationsNew) && taskOperationsNew.size() > 0) {
			for (ProcessTaskOperation processTaskOperation : taskOperationsNew) {
				Long id = processTaskOperation.getInstanceId();
				List<ProcessTaskOperation> taskNeed = taskOperationsNew.stream().filter(t -> t.getInstanceId().equals(id)).collect(Collectors.toList());

				mapTask.put(processTaskOperation.getId(), taskNeed);
			}
		}
		//查询出所有的流程实例在跨组织表中是否有数据
		List<ProcessInstanceRelation> processInstanceRelationsNew1 = new ArrayList<>();
		if (Func.isNotEmpty(processInstanceIds)) {

			LambdaQueryWrapper<ProcessInstanceRelation> queryContion1 = Wrappers.lambdaQuery();
			queryContion1.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
				.in(ProcessInstanceRelation::getProcessInstanceId, processInstanceIds)
				.eq(ProcessInstanceRelation::getFlag, 0)
				.eq(ProcessInstanceRelation::getStatus, 0);
			processInstanceRelationsNew1 = processInstanceRelationService.list(queryContion1);
		}
		Map<Long, List<ProcessInstanceRelation>> mapRelations = new HashMap<>();
		if (Func.isNotEmpty(processInstanceRelationsNew1) && processInstanceRelationsNew1.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelationsNew1) {
				Long id = processInstanceRelation.getProcessInstanceId();
				List<ProcessInstanceRelation> taskR = processInstanceRelationsNew1.stream().filter(t -> t.getProcessInstanceId().equals(id)).collect(Collectors.toList());
				if (taskR.size() > 0) {
					mapRelations.put(processInstanceRelation.getProcessInstanceId(), taskR);
				}

			}

		}

		//去除第一个节点审批人是跨组织的人员的流程实例
		for (ProcessInstance processInstance : orgInstanceLists) {

			List<ProcessTaskOperation> taskOperations = mapTask.get(processInstance.getId());
			//说明流程发起的的人所在的企业下没有任何人操作
			if (Func.isEmpty(taskOperations) || taskOperations.size() == 0) {
				//说明该流程实例没有任何操作，也说明当前的登陆人是第一个审批节点
				List<ProcessInstanceRelation> processInstanceRelations = mapRelations.get(processInstance.getId());
				if (Func.isEmpty(processInstanceRelations) || processInstanceRelations.size() == 0) {
					//说明第一审批人是跨组织人员
					orgInstanceList.add(processInstance);
				} else {
					for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
						String nodeId = processInstanceRelation.getNodeId();
						String nodeJson = processInstance.getProcessUserNodeJson();
						List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);

						List<UserVO> userVos = list.get(0).getUsers();
						for (UserVO userVO : userVos) {
							if (Func.equals(userVO.getUserId(), AuthUtil.getUserId()) && Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								orgInstanceList.add(processInstance);
							}

						}

					}
				}

			} else {
				//审批操作表中有记录，但是流程实例的状态为0，该流程还在审批中
				if (Func.equals(processInstance.getStatus(), 0)) {
					orgInstanceList.add(processInstance);
				}

			}
		}

		//添加跨组织
		LambdaQueryWrapper<ProcessInstanceRelation> queryContion = Wrappers.lambdaQuery();
		queryContion.eq(ProcessInstanceRelation::getFlag, 0)
//			.eq(ProcessInstanceRelation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
			.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
			.eq(ProcessInstanceRelation::getType, 1)
			.eq(ProcessInstanceRelation::getStatus, 0);
		List<ProcessInstanceRelation> processInstanceRelations = processInstanceRelationService.list(queryContion);
		Map<Long, ProcessInstance> instanceMap = new HashMap<>();
		Map<Long, List<ProcessTaskOperation>> operationMap = new HashMap<>();
		if (Func.isNotEmpty(processInstanceRelations) && processInstanceRelations.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
				ProcessInstance p = processInstanceService.getOneById(processInstanceRelation.getProcessInstanceId());
				instanceMap.put(processInstanceRelation.getId(), p);
				LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
				queryWrapper.eq(ProcessTaskOperation::getInstanceId, processInstanceRelation.getProcessInstanceId());
				List<ProcessTaskOperation> operations = processTaskOperationService.list(queryWrapper);
				operationMap.put(processInstanceRelation.getProcessInstanceId(), operations);

			}
		}
		List<ProcessInstanceRelation> processInstanceRelationsNew = new ArrayList<>();
		if (processInstanceRelations.size() > 0) {
			for (ProcessInstanceRelation processInstanceRelation : processInstanceRelations) {
				Long userId = processInstanceRelation.getUserId();
				if (Func.equals(processInstanceRelation.getUserId(), AuthUtil.getUserId())) {
					//人员登录，跨组织表中的人员和登录的人员的id和orgId一样，然后 获取nodeid对应的status是否为0
					ProcessInstance processInstance = processInstanceService.getOneById(processInstanceRelation.getProcessInstanceId());

					String nodeJson = processInstance.getProcessUserNodeJson();
					List<UsersNodeVO> list = JSONObject.parseArray(nodeJson, UsersNodeVO.class);
					Map<String, UsersNodeVO> maps = list.stream().collect(Collectors.toMap(UsersNodeVO::getNodeId, Function.identity(), (i1, i2) -> i1));
					List<String> nodeIds = list.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
					Boolean flag = false;
					for (UsersNodeVO usersNodeVO : list) {
						String nodeId = usersNodeVO.getNodeId();
						for (UserVO userVO : usersNodeVO.getUsers()) {
							if (Func.equals(userVO.getOrgId(), Func.toLong(AuthUtil.getDeptId()))) {
								//说明审批人中有跨组织
								flag = true;
								break;
							}
						}
						if (flag) {
							//该需我审批列表中需要添加跨组织的审批
							//判断跨组织的审批是否到达审批，判断当前节点的上一个节点是否审批，若未审批，不将该流程实例加入
							//判断该节点是第几个
							int num = nodeIds.indexOf(nodeId);
							if (nodeIds.size() > 1 && num > 0) {
								//获取上一个节点
								int a = num - 1;
								String preNodeId = nodeIds.get(a);
								if (Func.equals(nodeId, processInstanceRelation.getNodeId())) {
									//查询上一个节点审批
									List<ProcessTaskOperation> taskOperations = operationMap.get(processInstance.getId()).stream().filter(t -> t.getNodeId().equals(preNodeId)).collect(Collectors.toList());

									//上一个节点已经审批
									if (taskOperations.size() > 0) {
										List<Integer> statusList = taskOperations.stream().map(ProcessTaskOperation::getStatus).collect(Collectors.toList());
										Boolean flagNew = statusList.contains(0);
										if (!flagNew) {
											processInstanceRelationsNew.add(processInstanceRelation);
										}
									}
								}
							} else {
								processInstanceRelationsNew.add(processInstanceRelation);
							}


						}


					}


				}
			}

		}
		if (Func.isNotEmpty(processInstanceRelationsNew) && processInstanceRelationsNew.size() > 0) {
			List<Long> ids = processInstanceRelationsNew.stream().map(ProcessInstanceRelation::getProcessInstanceId).collect(Collectors.toList());
			if (ids.size() > 0) {
				LambdaQueryWrapper<ProcessInstance> query1 = Wrappers.lambdaQuery();
				query1.in(ProcessInstance::getId, ids);
				List<ProcessInstance> newList = processInstanceService.list(query1);

				orgInstanceList.addAll(newList);
			}
		}


		if (Func.isEmpty(orgInstanceList)) {
			return page;
		}
		//流程实例id的集合
		List<String> orgIngFlowableInstanceIds = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
		TaskQuery taskQuery = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceIds)
			.taskAssignee(TaskFlowUtil.getTaskUser()).active()
			.includeProcessVariables()
			.orderByTaskCreateTime()
			.desc();
		if (Func.isNotEmpty(keyword)) {
			//发起人姓名和流程定义名

			List<Long> userIds = new ArrayList<>();

			List<ProcessInstance> orgInstances = processInstanceService.getCurrentOrgIngListByCreateUser(userIds, null, timeVO);
			if (Func.isEmpty(orgInstances)) {
				ProcessInstance i = new ProcessInstance();
				i.setId(99L);
				i.setFlowableInstanceId("99");
				orgInstances.add(i);
			}
			List<String> orgIngFlowableInstanceId = orgInstances.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
			log.info("orgIngFlowableInstanceId:{}" + orgIngFlowableInstanceId);
			//正文value检索
			List<InstanceValues> instanceValuesList = instanceValuesService.list(Wrappers.<InstanceValues>lambdaQuery()
				.in(InstanceValues::getProcessInstanceId, orgInstanceList.stream().map(ProcessInstance::getId).collect(Collectors.toList()))
				.like(InstanceValues::getValueText, keyword));
			if (Func.isNotEmpty(instanceValuesList)) {
				List<String> fidList = instanceValuesList.stream().map(InstanceValues::getFlowableInstanceId).collect(Collectors.toList());
				if (Func.isNotEmpty(fidList)) {
					//or() 和 endOr() 是左括号和右括号，中间用or连接条件
					taskQuery.or()
						.processInstanceIdIn(orgIngFlowableInstanceId)
						.processInstanceIdIn(fidList)
						.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(keyword))
						.endOr();
				}
			} else {
				taskQuery.or().processInstanceIdIn(orgIngFlowableInstanceId)
					.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(keyword)).endOr();
			}
			//正文value检索END

		}
		//==
		//查出任务列表
		List<Task> tasks = taskQuery.listPage(Func.toInt((query.getCurrent() - 1) * page.getSize()), Func.toInt(query.getSize()));
		List<Task> tasks1 = tasks.stream().collect(
			collectingAndThen(
				toCollection(() -> new TreeSet<>(comparing(Task::getProcessInstanceId))), ArrayList::new)
		);

		if (timeVO == null || Func.isEmpty(timeVO.getSeq()) || timeVO.getSeq() == 1) {
			tasks1 = tasks1.stream().sorted(Comparator.comparing(Task::getCreateTime).reversed()).collect(Collectors.toList());
		} else {
			tasks1 = tasks1.stream().sorted(Comparator.comparing(Task::getCreateTime)).collect(Collectors.toList());
		}
		List<Task> tasknew = new ArrayList<>();
		for (Task task : tasks1) {
			log.info("**" + task.getCreateTime() + "##" + task.getProcessInstanceId());
			String nodeId = task.getTaskDefinitionKey();
			String processId = task.getProcessInstanceId();
			ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(processId);
			LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper
//				.eq(ProcessTaskOperation::getOrgId, Func.toLong(AuthUtil.getDeptId()))
				.eq(ProcessTaskOperation::getInstanceId, processInstance.getId())
				.eq(ProcessTaskOperation::getNodeId, nodeId).eq(ProcessTaskOperation::getCreateUser, AuthUtil.getUserId());
			List<ProcessTaskOperation> processTaskOperations = processTaskOperationService.list(queryWrapper);
			if (Func.isEmpty(processTaskOperations) && processTaskOperations.size() == 0) {
				//说明该流程当前人已经审批过
				tasknew.add(task);
			}

		}

		if (Func.isEmpty(tasknew)) {
			return page;
		}
		if (tasks.size() == tasknew.size()) {
			page.setTotal(taskQuery.count());
		} else {
			page.setTotal(taskQuery.count() - (tasks.size() - tasknew.size()));
		}


		//构造VO
		List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasknew);
		//拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(keyword, todoTaskVos);
		page.setRecords(todoTaskVos);
		return page;
	}

	public IPage<ProcessInstanceVO> appDoneTaskPage(ApprovePageQueryVO timeVO, String keyword, Query query, String definationId) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);
		//查询当前用户已审批的任务 任务操作记录表
		LambdaQueryWrapper<ProcessTaskOperation> taskWrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getCreateUser, AuthUtil.getUserId())
			.orderByDesc(ProcessTaskOperation::getCreateTime);
		List<ProcessTaskOperation> taskOperations = processTaskOperationService.list(taskWrapper);

		/**组装map*/
		LinkedHashMap<Long, Date> maps = new LinkedHashMap<>();
		if (Func.isNotEmpty(taskOperations) && taskOperations.size() > 0) {
			for (ProcessTaskOperation processTaskOperation : taskOperations) {
				maps.put(processTaskOperation.getInstanceId(), processTaskOperation.getCreateTime());
			}
		}
		/**获取跨组织的流程实例*/
		LambdaQueryWrapper<ProcessInstanceRelation> queryContion = Wrappers.lambdaQuery();
		queryContion.eq(ProcessInstanceRelation::getFlag, 0)
			.eq(ProcessInstanceRelation::getUserId, AuthUtil.getUserId())
			.eq(ProcessInstanceRelation::getType, 1)
			.ne(ProcessInstanceRelation::getStatus, 0);
		List<ProcessInstanceRelation> processInstanceRelations = processInstanceRelationService.list(queryContion);
		if (processInstanceRelations.size() > 0) {
			Map<Long, Date> mapsNew = processInstanceRelations.stream().collect(Collectors.toMap(ProcessInstanceRelation::getProcessInstanceId, ProcessInstanceRelation::getUpdateTime, (i1, i2) -> i1));
			maps.putAll(mapsNew);
		}
		/**获取转交的流程实例*/
		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(ProcessSpecialOperate::getOwnerId, AuthUtil.getUserId());
		queryWrapper.orderByDesc(ProcessSpecialOperate::getCreateTime);
		List<ProcessSpecialOperate> processSpecialOperateList = processSpecialOperateService.list(queryWrapper);
		List<String> transimtList = processSpecialOperateList.stream().map(ProcessSpecialOperate::getProcessInstanceId).collect(Collectors.toList());
		List<String> tIds = transimtList.stream().distinct().collect(Collectors.toList());
		if (Func.isEmpty(taskOperations) && tIds.size() == 0 && maps.size() == 0) {
			return page;
		}
		List<Long> instanceIds = taskOperations.stream().map(ProcessTaskOperation::getInstanceId).collect(Collectors.toList());
		List<Long> ids = instanceIds.stream().distinct().collect(Collectors.toList());
		if (tIds.size() > 0) {
			/**获取流程实例*/
			LambdaQueryWrapper<ProcessInstance> instanceWrapperT = Wrappers.<ProcessInstance>lambdaQuery()
//                    .eq(ProcessInstance::getOrgId, Func.toLong(AuthUtil.getDeptId()))
				.in(ProcessInstance::getFlowableInstanceId, tIds);
			List<ProcessInstance> list = processInstanceService.list(instanceWrapperT);

			List<Long> idss = list.stream().map(ProcessInstance::getId).collect(Collectors.toList());

//            /**转交的流程实例拼入列表*/
			for (Long id : idss) {
				if (!maps.containsKey(id)) {
					if (!maps.containsKey(id)) {
						ProcessInstance processInstance = processInstanceService.getOneById(id);
						LambdaQueryWrapper<ProcessSpecialOperate> queryM = Wrappers.lambdaQuery();
						queryM.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId());
						queryM.eq(ProcessSpecialOperate::getOwnerId, AuthUtil.getUserId());
						queryM.eq(ProcessSpecialOperate::getOperateType, 8);
						queryM.orderByDesc(ProcessSpecialOperate::getCreateTime);

						List<ProcessSpecialOperate> processSpecialOperates = processSpecialOperateService.list(queryM);
						if (Func.isNotEmpty(processSpecialOperates) && processSpecialOperates.size() > 0) {
							maps.put(id, processSpecialOperates.get(0).getCreateTime());
						}
					}
				}


			}
		}

		LinkedHashMap<Long, Date> finalOut = sortMapByValues(maps, timeVO);
		List<Long> keys = Arrays.asList(finalOut.keySet().toArray(new Long[finalOut.keySet().size()]));


		LambdaQueryWrapper<ProcessInstance> instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
			.in(ProcessInstance::getId, keys);

		// 发起时间排序，默认是审批时间排序
		if (Func.isEmpty(timeVO) || Func.isEmpty(timeVO.getSeq())) {
			instanceWrapper.last("order by field(id," + Func.join(keys) + ")");
		} else if (Func.equals(timeVO.getSeq(), 1)) {
			instanceWrapper.orderByDesc(ProcessInstance::getCreateTime);
		} else if (Func.equals(timeVO.getSeq(), CommNumberEnum.NUMBER_2.getCode())) {
			instanceWrapper.orderByAsc(ProcessInstance::getCreateTime);
		}
		if (Func.isNotEmpty(keyword)) {
			List<Long> userIds = new ArrayList<>();
			if (userIds.size() > 0) {
				instanceWrapper.in(ProcessInstance::getCreateUser, userIds);
			}
			//正文value检索
			List<InstanceValues> instanceValuesList = instanceValuesService.list(Wrappers.<InstanceValues>lambdaQuery()
				.in(InstanceValues::getProcessInstanceId, keys)
				.like(InstanceValues::getValueText, keyword));
			if (Func.isNotEmpty(instanceValuesList)) {
				List<Long> idList = instanceValuesList.stream().map(InstanceValues::getProcessInstanceId).collect(Collectors.toList());
				if (Func.isNotEmpty(idList)) {
					instanceWrapper.or().in(ProcessInstance::getId, idList);
				}
			}
			//正文value检索END

		}
		if (Func.isNotEmpty(definationId)) {
			instanceWrapper.inSql(ProcessInstance::getDefinitionId, definationId);
		}
		//发起、完成时间过滤
		if (Func.isNotEmpty(timeVO)) {
			List<Integer> endStatus = Arrays.asList(1, 2, 3, 4);
			String startTime = Func.isEmpty(timeVO.getStartTime()) ? StringPool.EMPTY : timeVO.getStartTime();
			String endTime = Func.isEmpty(timeVO.getEndTime()) ? StringPool.EMPTY : timeVO.getEndTime();
			String cStartTime = Func.isEmpty(timeVO.getCStartTime()) ? StringPool.EMPTY : timeVO.getCStartTime();
			String cEndTime = Func.isEmpty(timeVO.getCEndTime()) ? StringPool.EMPTY : timeVO.getCEndTime();
			instanceWrapper.ge(Func.isNotEmpty(startTime), ProcessInstance::getCreateTime, startTime)
				.le(Func.isNotEmpty(endTime), ProcessInstance::getCreateTime, endTime);
			instanceWrapper.and(Func.isNotEmpty(cStartTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.ge(ProcessInstance::getUpdateTime, cStartTime));
			instanceWrapper.and(Func.isNotEmpty(cEndTime), w -> w.in(ProcessInstance::getStatus, endStatus)
				.le(ProcessInstance::getUpdateTime, cEndTime));
		}
		//发起、完成时间过滤--END
		IPage<ProcessInstance> processInstancePage = processInstanceService.page(Condition.getPage(query), instanceWrapper);
		if (Func.isEmpty(processInstancePage.getRecords())) {
			return page;
		}
		List<ProcessInstance> myList = processInstancePage.getRecords();
		List<ProcessInstance> processInstances = new ArrayList<>();

		Map<Long, ProcessInstance> myIds = myList.stream().collect(Collectors.toMap(ProcessInstance::getId, Function.identity()));
		for (Long id : keys) {
			if (myIds.keySet().contains(id)) {
				processInstances.add(myIds.get(id));
			}

		}
		List<ProcessInstanceVO> processInstanceVoList = convertProcessInstanceVO(processInstances);
		//拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(keyword, processInstanceVoList);
		page.setRecords(processInstanceVoList);
		page.setTotal(processInstancePage.getTotal());
		return page;

	}

	@Transactional(rollbackFor = Exception.class)
	public void turnTask(ProcessTurnTaskDTO taskApproveDTO) {
		ProcessTaskOperation oldTaskOperation = processTaskOperationService.getByFlowableTaskId(taskApproveDTO.getFlowableTaskId());
		if (Func.isNotEmpty(oldTaskOperation)) {
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			throw new ServiceException("该审批已被其他人受理");
		}
		ProcessInstance processInstance = processInstanceService.getProcessInstanceByFlowableTaskId(taskApproveDTO.getFlowableTaskId());
		if (Func.isEmpty(processInstance)) {
			throw new ServiceException("该审批已被其他人受理");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
//            throw new ServiceException("当前审批实例非审批中状态");
			throw new ServiceException("该审批已被其他人受理");
		}
		//转交
		TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
		//设置流程归属人
		taskService.setOwner(taskApproveDTO.getFlowableTaskId(), "1111");
		//设置流程操作人
		taskService.setAssignee(taskApproveDTO.getFlowableTaskId(), taskApproveDTO.getUserId());
		log.info("转交成功！......................");

	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 加签
	 * @Date: 16:25 2021/3/24
	 * @Modificd By:
	 * @Param: [processSignAddDTO]
	 * @return: boolean
	 * @throw: 请描述异常信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean addSign(ProcessSignAddDTO processSignAddDTO) {

		if (StringUtil.isAnyBlank(processSignAddDTO.getFlowableTaskId(), processSignAddDTO.getUserIds()) || processSignAddDTO.getType() == null
			|| processSignAddDTO.getProcessInstanceId() == null) {
			throw new ServiceException("参数为空");
		}
		if (processSignAddDTO.getApproverType() == null && TypeEnum.PARALLEL_SIGN.getCode().intValue() != processSignAddDTO.getType().intValue()) {
			throw new ServiceException("参数为空");
		}

		if (StringUtils.isBlank(processSignAddDTO.getCurrentNodeId())) {
			throw new ServiceException("该审批已由其他人受理");
		}

		ProcessSignAddDTO.TypeEnum typeEnum = ProcessSignAddDTO.TypeEnum.from(processSignAddDTO.getType());
		if (typeEnum == null) {
			throw new ServiceException("加签类型值不正确");
		}

		// 获取流程实例
		ProcessInstance processInstance = processInstanceService.getById(processSignAddDTO.getProcessInstanceId());

		if (processInstance == null) {
			throw new ServiceException("该审批已由其他人受理");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			throw new ServiceException("该审批已由其他人受理");
		}

		UserDTO userDeptByUserIdAndOrgId = userService.queryUserById(AuthUtil.getUserId());
		if (Func.isEmpty(userDeptByUserIdAndOrgId)) {
			throw new ServiceException("该审批节点审批人已经离职！");
		}

		ProcessTaskOperation oldTaskOperation = processTaskOperationService.getByFlowableTaskId(processSignAddDTO.getFlowableTaskId());
		if (Func.isNotEmpty(oldTaskOperation)) {
			throw new ServiceException("该审批已由其他人受理");
		}

		// 获取当前的任务
		Task task = processEngine.getTaskService().createTaskQuery().taskId(processSignAddDTO.getFlowableTaskId()).singleResult();

		if (task == null) {
			task = processEngine.getTaskService().createTaskQuery()
				.processInstanceId(processInstance.getFlowableInstanceId())
				.taskAssignee("taskUser_" + String.valueOf(AuthUtil.getUserId()))
				.taskDefinitionKey(processSignAddDTO.getCurrentNodeId()).singleResult();
		}
		if (task == null) {
			throw new ServiceException("该审批已由其他人受理");
		}

		processSignAddDTO.setFlowableTaskId(task.getId());
		// 并行加签的审批后，原加签节点再次并行加签报错
		log.info(AuthUtil.getUserId().toString());

		if (!task.getAssignee().contains(String.valueOf(AuthUtil.getUserId()))) {
			throw new ServiceException("该审批已由其他人受理");
		}

		if (TypeEnum.BEFORE_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()
			|| TypeEnum.PARALLEL_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {

			List<ProcessTaskOperation> processTaskOperationList = processTaskOperationService.list(
				Wrappers.<ProcessTaskOperation>query().lambda()
					.eq(ProcessTaskOperation::getNodeId, task.getTaskDefinitionKey())
					.eq(ProcessTaskOperation::getInstanceId, processInstance.getId()));

			if (processTaskOperationList != null && !processTaskOperationList.isEmpty()) {
				throw new ServiceException("该审批已由其他人受理");
			}
		}

		Integer approverType = processSignAddDTO.getApproverType();
		String userIds = this.usersDuplicateRemoval(processSignAddDTO.getUserIds());

		if (TypeEnum.PARALLEL_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {

			String oldUserIds = this.getNodeUserIds(processInstance.getProcessUserNodeJson(), task.getTaskDefinitionKey());
			if (this.checkContainsUser(userIds, oldUserIds)) {
				throw new ServiceException("加签人员已存在");
			}
			if (Arrays.stream(oldUserIds.split(CommEnum.DOU_HAO.getDesc())).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList()).size() > 1
				|| approverType == null) {

				approverType = this.getNodeApproveType(processInstance.getProcessUserNodeJson(), task.getTaskDefinitionKey(), processInstance.getDefinitionId());
			}
		}

		String newNodeId = null;
		if (TypeEnum.PARALLEL_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {
			newNodeId = task.getTaskDefinitionKey();
		} else {
			// 生成新的节点id
			newNodeId = this.getAddSignNodeId(processSignAddDTO.getType());
		}

		if (StringUtils.isBlank(newNodeId)) {
			throw new ServiceException("生成加签新节点id失败");
		}

		// 更新流程实例业务表process_user_node_json
		String processUserNodeJson = processInstance.getProcessUserNodeJson();
		if (StringUtils.isBlank(processUserNodeJson)) {
			throw new ServiceException("processUserNodeJson为空");
		}

		List<UsersNodeVO> usersNodeinfos = JSON.parseArray(processUserNodeJson, UsersNodeVO.class);

		if (usersNodeinfos != null && !usersNodeinfos.isEmpty()) {
			List<UsersNodeVO> usersNodeinfosNew = new ArrayList<>();

			for (UsersNodeVO usersNodeVO : usersNodeinfos) {

				if (task.getTaskDefinitionKey().equals(usersNodeVO.getNodeId())) {
					UsersNodeVO usersNodeVoAddSign = this.getUsersNodeVoAddSign(newNodeId, processSignAddDTO.getType(), approverType, userIds, Func.toLong(AuthUtil.getDeptId()));

					if (TypeEnum.BEFORE_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {

						usersNodeinfosNew.add(usersNodeVoAddSign);
						usersNodeinfosNew.add(usersNodeVO);
					} else if (TypeEnum.AFTER_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {

						usersNodeinfosNew.add(usersNodeVO);
						usersNodeinfosNew.add(usersNodeVoAddSign);
					} else if (TypeEnum.PARALLEL_SIGN.getCode().intValue() == processSignAddDTO.getType().intValue()) {

						List<UserVO> usersAdd = this.getAddUsers(userIds, Func.toLong(AuthUtil.getDeptId()));
						usersNodeVO.getUsers().addAll(usersAdd);
						usersNodeVO.setApproveType(approverType);
						usersNodeinfosNew.add(usersNodeVO);
					}
				} else {
					usersNodeinfosNew.add(usersNodeVO);
				}
			}

			ProcessInstance processInstance1 = new ProcessInstance();
			processInstance1.setId(processInstance.getId());
			processInstance1.setProcessUserNodeJson(JSON.toJSONString(usersNodeinfosNew));
			processInstanceService.updateById(processInstance1);
		}

		// 加签操作记录到历史表
		this.saveProcessSpecialOperate(Func.toLong(AuthUtil.getDeptId()),
			processSignAddDTO.getOprateReason(), processSignAddDTO.getFlowableTaskId(),
			processInstance.getFlowableInstanceId(),
			approverType, userIds,
			processSignAddDTO.getType(), newNodeId);

		// 更新bpm流程实例信息
		switch (typeEnum) {
			case BEFORE_SIGN:

				this.beforeSignBpmn(newNodeId, processSignAddDTO.getFlowableTaskId(), userIds, approverType);
				break;
			case AFTER_SIGN:

				this.afterSignBpmn(newNodeId, processSignAddDTO.getFlowableTaskId(), userIds, approverType);
				break;
			case PARALLEL_SIGN:

				this.parallelSignBpmnAdditional(processSignAddDTO.getFlowableTaskId(), approverType, userIds,
					this.getNodeUserIds(processUserNodeJson, task.getTaskDefinitionKey()));
				break;
			default:
		}

		// 加签后，在该节点的评论重新绑定nodeId
		LambdaUpdateWrapper<ProcessSpecialOperate> query = Wrappers.lambdaUpdate();
		query.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId());
		query.eq(ProcessSpecialOperate::getNodeId, processSignAddDTO.getCurrentNodeId());
		query.set(ProcessSpecialOperate::getNodeId, newNodeId);
		processSpecialOperateService.update(query);
		log.info("..........................................................." + "评论节点已经绑定");

		//发送代办消息
		try {
			log.error("============加签========================================");
			SpringUtil.getBean(MsgManage.class).pushddSignTaskCreated(processSignAddDTO.getFlowableTaskId(), processInstance.getFlowableInstanceId(), processSignAddDTO.getUserIds());
		} catch (Exception e) {
			log.error("推送消息失败》》{}", e.getMessage());
		}

		//向前或向后加签后-拷贝原节点的表单字段权限赋值给新的加签节点
		if (!Func.equals(TypeEnum.PARALLEL_SIGN.getCode(), processSignAddDTO.getType())) {
			saveNewNodeField(processSignAddDTO, newNodeId);
		}

		updatetaskOrgId(processInstance.getFlowableInstanceId());
		return true;
	}


	public void addFlowGroup(Long id, String userIds) {
		//查询该审批是否创建群组
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 人员去重
	 * @Date: 14:14 2021/4/6
	 * @Modificd By:
	 * @Param: [userIds]
	 * @return: java.lang.String
	 * @throw: 请描述异常信息
	 */
	public String usersDuplicateRemoval(String userIds) {
		if (StringUtils.isBlank(userIds)) {
			return null;
		}
		List<Long> userIdList = Arrays.stream(userIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

		List<Long> usersDuplicateRemoval = userIdList.stream().distinct().collect(Collectors.toList());

		return StringUtils.join(usersDuplicateRemoval, ",");
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 获取节点已配置的审核人员
	 * @Date: 9:36 2021/4/6
	 * @Modificd By:
	 * @Param: [processUserNodeJson, nodeId]
	 * @return: java.lang.String
	 * @throw: 请描述异常信息
	 */
	public String getNodeUserIds(String processUserNodeJson, String nodeId) {
		if (StringUtils.isBlank(processUserNodeJson) || StringUtils.isBlank(nodeId)) {
			return null;
		}

		List<UsersNodeVO> usersNodeVos = JSON.parseArray(processUserNodeJson, UsersNodeVO.class).stream().collect(Collectors.toList());
		if (usersNodeVos == null || usersNodeVos.isEmpty()) {
			return null;
		}

		for (UsersNodeVO usersNodeVO : usersNodeVos) {
			if (nodeId.equals(usersNodeVO.getNodeId())) {
				List<UserVO> users = usersNodeVO.getUsers();
				List<Long> ids = users.stream().map(UserVO::getUserId).collect(Collectors.toList());
				return StringUtils.join(ids, ",");
			}
		}
		return null;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 获取节点审批类型
	 * @Date: 14:37 2021/4/6
	 * @Modificd By:
	 * @Param: [processUserNodeJson, nodeId]
	 * @return: java.lang.Integer
	 * @throw: 请描述异常信息
	 */
	public Integer getNodeApproveType(String processUserNodeJson, String nodeId, Long definitionId) {
		if (StringUtils.isBlank(processUserNodeJson) || StringUtils.isBlank(nodeId)) {
			return null;
		}

		List<UsersNodeVO> usersNodeVos = JSON.parseArray(processUserNodeJson, UsersNodeVO.class)
			.stream().collect(Collectors.toList());
		if (usersNodeVos == null || usersNodeVos.isEmpty()) {
			return null;
		}

		for (UsersNodeVO usersNodeVO : usersNodeVos) {
			if (nodeId.equals(usersNodeVO.getNodeId()) && usersNodeVO.getApproveType() != null) {
				return usersNodeVO.getApproveType();
			}
		}

		ProcessDefinition processDefinition = processDefinitionService.getOnlyOne(definitionId);
		List<NodeModel> nodeModels = FlowConvertData.parseBody(processDefinition.getProcessJson());

		Map<String, NodeModel> modelMap = nodeModels.stream().collect(Collectors.toMap(NodeModel::getResourceId, Function.identity()));
		NodeModel nodeModel = modelMap.get(nodeId);

		if (Func.isNotEmpty(nodeModel) && Func.isNotEmpty(nodeModel.getProperties())) {
			JSONObject properties = nodeModel.getProperties();
			return properties.getInteger("approverType");
		}

		return null;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 并行加签校验用户是否已存在
	 * @Date: 14:56 2021/4/6
	 * @Modificd By:
	 * @Param: [userIds, oldUserIds]
	 * @return: boolean
	 * @throw: 请描述异常信息
	 */
	public boolean checkContainsUser(String userIds, String oldUserIds) {
		if (StringUtils.isBlank(userIds) || StringUtils.isBlank(userIds)) {
			return false;
		}
		List<Long> userIdList = Arrays.stream(userIds.split(","))
			.map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		List<Long> oldUserIdList = Arrays.stream(oldUserIds.split(","))
			.map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		for (Long userId : userIdList) {
			if (oldUserIdList.contains(userId)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 创建用户加签节点
	 * @Date: 11:26 2021/3/26
	 * @Modificd By:
	 * @Param: [newNodeId, type, approverType, userIds, orgId]
	 * @return: com.snszyk.flow.process.vo.UsersNodeVO
	 * @throw: 请描述异常信息
	 */
	public UsersNodeVO getUsersNodeVoAddSign(String newNodeId, Integer type, Integer approverType,
											 String userIds, Long orgId) {
		UsersNodeVO usersNodeVoAddSign = new UsersNodeVO();
		usersNodeVoAddSign.setNodeId(newNodeId);
		usersNodeVoAddSign.setType(type);
		usersNodeVoAddSign.setApproveType(approverType);
		usersNodeVoAddSign.setUsers(this.getAddUsers(userIds, orgId));
		return usersNodeVoAddSign;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 构造用户信息
	 * @Date: 11:47 2021/3/26
	 * @Modificd By:
	 * @Param: [userIds, orgId]
	 * @return: java.util.List<com.snszyk.flow.process.vo.UserVO>
	 * @throw: 请描述异常信息
	 */
	public List<UserVO> getAddUsers(String userIds, Long orgId) {
		List<Long> userIdList = Arrays.stream(userIds.split(","))
			.map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

		List<UserDTO> result = userService.queryUserDeptListByIds(userIdList);

		if (CollectionUtils.isEmpty(result)) {
			throw new ServiceException("获取用户信息失败");
		}

		List<UserVO> users = new ArrayList<>();
		for (UserDTO user : result) {
			UserVO userVO = new UserVO();
			userVO.setUserId(user.getId());
			userVO.setUsername(user.getRealName());
			userVO.setAvatar(user.getAvatar());
			userVO.setOrgId(orgId);
			users.add(userVO);
		}
		return users;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 加签操作记录到历史表
	 * @Date: 11:03 2021/3/26
	 * @Modificd By:
	 * @Param: [orgId, oprateReason, flowableTaskId, processInstanceId, approverType, userIds, type]
	 * @return: boolean
	 * @throw: 请描述异常信息
	 */
	public boolean saveProcessSpecialOperate(Long orgId, String oprateReason, String flowableTaskId,
											 String processInstanceId, Integer approverType, String userIds, Integer type,
											 String nodeId) {
		ProcessSpecialOperate processSpecialOperate = new ProcessSpecialOperate();
		processSpecialOperate.setOperateDate(new Date());
		processSpecialOperate.setDescription(oprateReason);
		processSpecialOperate.setTaskId(flowableTaskId);
		processSpecialOperate.setOwnerId(AuthUtil.getUserId());
		processSpecialOperate.setProcessInstanceId(processInstanceId);
		processSpecialOperate.setApproverType(approverType);
		processSpecialOperate.setUserIds(userIds);
		processSpecialOperate.setType(type);
		processSpecialOperate.setNodeId(nodeId);

		return processSpecialOperateService.save(processSpecialOperate);
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 获取加签节点id
	 * @Date: 10:52 2021/3/26
	 * @Modificd By:
	 * @Param: [type]
	 * @return: java.lang.String
	 * @throw: 请描述异常信息
	 */
	public String getAddSignNodeId(Integer type) {
		if (TypeEnum.BEFORE_SIGN.getCode().intValue() == type.intValue()) {
			return "asb" + RandomUtil.randomString(8);
		} else if (TypeEnum.AFTER_SIGN.getCode().intValue() == type.intValue()) {
			return "asa" + RandomUtil.randomString(8);
		}
		return null;
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: bpmn向前加签
	 * @Date: 10:39 2021/3/26
	 * @Modificd By:
	 * @Param: [newNodeId, flowableTaskId, userIds, approverType]
	 * @return: void
	 * @throw: 请描述异常信息
	 */
	public void beforeSignBpmn(String newNodeId, String flowableTaskId, String userIds,
							   Integer approverType) {
		// 获取当前的任务
		Task task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		if (task == null) {
			throw new ServiceException("任务不存在");
		}
		// 获取加签人员的集合
		List<String> collect = Arrays.stream(userIds.split(",")).map(TaskFlowUtil::getTaskUser).collect(Collectors.toList());

		// 新节点加入到审批中
		processEngine.getRuntimeService().setVariable(task.getExecutionId(), newNodeId, collect);

		DynamicUserTaskBuilder taskBuilder = new DynamicUserTaskBuilder();
		taskBuilder.setId(newNodeId);
		taskBuilder.setAssignee("${ " + newNodeId + "assignee}");
		taskBuilder.setName("向前加签节点");
		BeforSignCmd beforeSignCmd = new BeforSignCmd(task.getProcessInstanceId(), taskBuilder, flowableTaskId, approverType);

		processEngine.getManagementService().executeCommand(beforeSignCmd);
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: bpmn向后加签
	 * @Date: 10:41 2021/3/26
	 * @Modificd By:
	 * @Param: [newNodeId, flowableTaskId, userIds, approverType]
	 * @return: void
	 * @throw: 请描述异常信息
	 */
	public void afterSignBpmn(String newNodeId, String flowableTaskId, String userIds,
							  Integer approverType) {
		// 获取当前的任务
		Task task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		if (task == null) {
			throw new ServiceException("任务不存在");
		}

		// 获取加签人员的集合
		List<String> collect = Arrays.stream(userIds.split(",")).map(TaskFlowUtil::getTaskUser).collect(Collectors.toList());

		// 新节点加入到审批中
		processEngine.getRuntimeService().setVariable(task.getExecutionId(), newNodeId, collect);

		DynamicUserTaskBuilder taskBuilder = new DynamicUserTaskBuilder();
		taskBuilder.setId(newNodeId);
		taskBuilder.setAssignee("${ " + newNodeId + "assignee}");
		taskBuilder.setName("向后加签节点");
		AfterSignCmd afterSignCmd = new AfterSignCmd(task.getProcessInstanceId(), taskBuilder,
			flowableTaskId, approverType);

		processEngine.getManagementService().executeCommand(afterSignCmd);
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: bpmn并行加签
	 * @Date: 11:40 2021/3/26
	 * @Modificd By:
	 * @Param: [flowableTaskId, userIds]
	 * @return: void
	 * @throw: 请描述异常信息
	 */
	public void parallelSigBpmn(String flowableTaskId, String userIds) {
		Task task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId)
			.singleResult();
		if (task == null) {
			throw new ServiceException("任务不存在");
		}
		for (String userId : userIds.split(CommEnum.DOU_HAO.getDesc())) {
			processEngine.getRuntimeService()
				.addMultiInstanceExecution(task.getTaskDefinitionKey(), task.getProcessInstanceId(),
					Collections.singletonMap(task.getTaskDefinitionKey() + "assignee",
						(Object) TaskFlowUtil.getTaskUser(userId)));
		}
	}

	/**
	 * @Author: zhaomeinan
	 * @Description: 并行加签
	 * @Date: 9:55 2021/4/6
	 * @Modificd By:
	 * @Param: [flowableTaskId, approverType, userIds, oriUserIds]
	 * @return: void
	 * @throw: 请描述异常信息
	 */
	public void parallelSignBpmnAdditional(String flowableTaskId, Integer approverType,
										   String userIds, String oriUserIds) {
		// 获取当前的任务
		Task task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		if (task == null) {
			throw new ServiceException("任务不存在");
		}

		if (StringUtils.isNotBlank(oriUserIds)) {
			userIds = oriUserIds + "," + userIds;
		}

		// 获取加签人员的集合
		List<String> collect = Arrays.stream(userIds.split(",")).map(TaskFlowUtil::getTaskUser).collect(Collectors.toList());

		// 新节点加入到审批中
		processEngine.getRuntimeService().setVariable(task.getExecutionId(), task.getTaskDefinitionKey(), collect);

		ParallelSignCmd paSignCmd = new ParallelSignCmd(task.getProcessInstanceId(), flowableTaskId, approverType);
		processEngine.getManagementService().executeCommand(paSignCmd);
	}

	@Transactional(rollbackFor = Exception.class)
	public void transmit(String flowableTaskId, String userId, String oprateReason, String flowProcessInstanceId, String currentNodeId) {

		ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(flowProcessInstanceId);

		if (Func.isEmpty(processInstance)) {
			throw new ServiceException("该审批已由其他人受理");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			throw new ServiceException("该审批已被其他人受理");
		}

		// 过滤会签，会前中的人不可以转交
		List<UserNewNodeVO> userNewNodeVOList = JSON.parseArray(processInstance.getProcessUserNodeJson(), UserNewNodeVO.class);
		List<UserVO> userVos = new ArrayList<>();

		for (UserNewNodeVO userNewNodeVO : userNewNodeVOList) {
			if (Func.equals(userNewNodeVO.getNodeId(), currentNodeId)) {
				List<UserVO> users = userNewNodeVO.getUsers();
				userVos.addAll(users);
			}
		}

		if (userVos.size() > 0) {
			List<Long> userIds = userVos.stream().map(UserVO::getUserId).collect(Collectors.toList());
			if (userIds.contains(Long.parseLong(userId))) {
				throw new ServiceException("该审批节点不允许转交已经存在过的审批人！");
			}
		}

		UserDTO userDeptByUserIdAndOrgId = userService.queryUserById(AuthUtil.getUserId());
		if (Func.isEmpty(userDeptByUserIdAndOrgId)) {
			throw new ServiceException("该审批节点审批人已经离职！");
		}

		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId())
			.eq(ProcessSpecialOperate::getTaskId, flowableTaskId)
			.eq(ProcessSpecialOperate::getType, 8)
			.orderByDesc(ProcessSpecialOperate::getOperateDate);

		List<ProcessSpecialOperate> list = processSpecialOperateService.list(queryWrapper);

		if (Func.isNotEmpty(list) && list.size() > 0) {
			if (!Func.equals(list.get(0).getUserId(), AuthUtil.getUserId())) {
				throw new ServiceException("该审批已由其他人受理");
			}
		}

		if (Func.isEmpty(userId) || Func.isEmpty(flowableTaskId)) {
			throw new ServiceException("参数为空");
		}

		ProcessTaskOperation oldTaskOperation = processTaskOperationService.getByFlowableTaskId(flowableTaskId);
		if (Func.isNotEmpty(oldTaskOperation)) {
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
				throw new ServiceException("该审批已被其他人受理");
			}
			throw new ServiceException("该审批已被其他人受理");
		}

		// 判断当前节点进行过什么操作，3种类型测试
		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper1 = Wrappers.lambdaQuery();
		queryWrapper1.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId());
		queryWrapper1.eq(ProcessSpecialOperate::getNodeId, currentNodeId);
		queryWrapper1.orderByDesc(ProcessSpecialOperate::getOperateDate);
		List<ProcessSpecialOperate> processSpecialOperateList = processSpecialOperateService.list(queryWrapper1);

		Task task = null;
		if (processSpecialOperateList.size() == 0) {
			task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		}
		if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() == CommNumberEnum.NUMBER_7.getCode()) {
			task = processEngine.getTaskService().createTaskQuery().processInstanceId(flowProcessInstanceId).taskAssignee("taskUser_" + String.valueOf(AuthUtil.getUserId())).taskDefinitionKey(currentNodeId).singleResult();
		}
		if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() == CommNumberEnum.NUMBER_5.getCode()) {
			task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		}
		if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() != CommNumberEnum.NUMBER_7.getCode() && processSpecialOperateList.get(0).getType() != CommNumberEnum.NUMBER_5.getCode()) {
			task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
		}

		if (task == null) {
			throw new ServiceException("该审批已由其他人受理");
		}
		if (!task.getAssignee().contains(String.valueOf(AuthUtil.getUserId()))) {
			throw new ServiceException("该审批已由其他人受理");
		}

		Long orgId = Long.valueOf(task.getDescription());
		// 获取加签人员的集合
		List<String> collect = Arrays.asList(TaskFlowUtil.getTaskUser(userId));

		processEngine.getRuntimeService().setVariable(task.getExecutionId(), task.getTaskDefinitionKey(), collect);
		processEngine.getTaskService().setAssignee(task.getId(), TaskFlowUtil.getTaskUser(userId));

		log.info("转交通过..........");
		ProcessSpecialOperate processSpecialOperate = new ProcessSpecialOperate();
		processSpecialOperate.setOperateDate(DateUtil.now());
		// 操作类型:4.连续多级5.加签转交
		processSpecialOperate.setOperateType(8);
		processSpecialOperate.setDescription(oprateReason);
		processSpecialOperate.setUserId(Long.valueOf(userId));
		processSpecialOperate.setOwnerId(AuthUtil.getUserId());

		processSpecialOperate.setNodeId(task.getTaskDefinitionKey());
		processSpecialOperate.setTaskId(flowableTaskId);
		processSpecialOperate.setProcessInstanceId(task.getProcessInstanceId());
		processSpecialOperate.setTaskExecutionId(task.getExecutionId());
		processSpecialOperate.setType(8);
		processSpecialOperate.setCreateUser(AuthUtil.getUserId());
		processSpecialOperate.setUpdateUser(AuthUtil.getUserId());
		processSpecialOperate.setCreateDept(Func.toLong(AuthUtil.getDeptId()));
		processSpecialOperateService.saveOrUpdate(processSpecialOperate);

		//更新processInstance中人员的节点数据
		String nodeJson = processInstance.getProcessUserNodeJson();
		List<UsersNodeVO> usersNodeinfos = JSON.parseArray(nodeJson, UsersNodeVO.class);

		for (UsersNodeVO usersNodeVO : usersNodeinfos) {
			if (Func.equals(usersNodeVO.getNodeId(), processSpecialOperate.getNodeId())) {

				List<UserVO> users = usersNodeVO.getUsers();
				for (UserVO userVO : users) {
					//有转交的情况
					if (Func.equals(userVO.getUserId(), processSpecialOperate.getOwnerId())) {

						userVO.setUserId(processSpecialOperate.getUserId());

						UserDTO userOrgBasicInfo = userService.queryUserById(processSpecialOperate.getUserId());
						if (Func.isEmpty(userOrgBasicInfo)) {
							throw new ServiceException("渲染发起人节点失败");
						}

						userVO.setAvatar(userOrgBasicInfo.getAvatar());
						userVO.setDeptName(null);
						userVO.setUsername(userOrgBasicInfo.getRealName());
					}
				}


			}
		}
		String newNodeJson = JSON.toJSONString(usersNodeinfos);
		LambdaUpdateWrapper<ProcessInstance> query = Wrappers.lambdaUpdate();
		query.set(ProcessInstance::getProcessUserNodeJson, newNodeJson);

		// 操作跟新时间
		query.eq(ProcessInstance::getFlowableInstanceId, processSpecialOperate.getProcessInstanceId());
		processInstanceService.update(query);

		//发送代办消息
		try {
			log.error("===================转交=====================");
			SpringUtil.getBean(MsgManage.class).pushTransmitTaskCreated(flowableTaskId, processInstance.getFlowableInstanceId(), userId);
		} catch (Exception e) {
			log.error("推送消息失败》》{}", e.getMessage());
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public void sendInfoAdd(String userIds, Long processInstanceId, String currentNodeId, String flowableTaskId) {

		ProcessInstance processInstance = processInstanceService.getOneById(processInstanceId);

		if (Func.isEmpty(processInstance)) {
			throw new ServiceException("该审批已被其他人受理");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			throw new ServiceException("该审批已被其他人受理");
		}

		UserDTO userDeptByUserIdAndOrgId = userService.queryUserById(AuthUtil.getUserId());
		if (Func.isEmpty(userDeptByUserIdAndOrgId)) {
			throw new ServiceException("该审批节点审批人已经离职！");
		}

		// 发起人可以任意添加抄送人
		if (Func.equals(processInstance.getCreateUser(), AuthUtil.getUserId())) {

			addPersonNotice(userIds, processInstanceId, processInstance);

			log.info("抄送人添加..........");
		} else {
			// 判断当前节点进行过什么操作，3种类型测试
			LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper1 = Wrappers.lambdaQuery();
			queryWrapper1.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId());
			queryWrapper1.eq(ProcessSpecialOperate::getNodeId, currentNodeId);
			queryWrapper1.orderByDesc(ProcessSpecialOperate::getOperateDate);
			List<ProcessSpecialOperate> processSpecialOperateList = processSpecialOperateService.list(queryWrapper1);

			Task task = null;
			if (processSpecialOperateList.size() == 0) {
				task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
			}
			if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() == CommNumberEnum.NUMBER_7.getCode()) {
				task = processEngine.getTaskService().createTaskQuery().processInstanceId(processInstance.getFlowableInstanceId()).taskAssignee("taskUser_" + String.valueOf(AuthUtil.getUserId())).taskDefinitionKey(currentNodeId).singleResult();
			}
			if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() == CommNumberEnum.NUMBER_5.getCode()) {
				task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
			}
			if (processSpecialOperateList.size() > 0 && processSpecialOperateList.get(0).getType() != CommNumberEnum.NUMBER_7.getCode() && processSpecialOperateList.get(0).getType() != CommNumberEnum.NUMBER_5.getCode()) {
				task = processEngine.getTaskService().createTaskQuery().taskId(flowableTaskId).singleResult();
			}

			if (task == null) {
				throw new ServiceException("该审批已由其他人受理");
			}
			if (!task.getAssignee().contains(String.valueOf(AuthUtil.getUserId()))) {
				throw new ServiceException("该审批已由其他人受理");
			}

			if (Func.isEmpty(userIds) || Func.isEmpty(processInstanceId)) {
				throw new ServiceException("参数为空");
			}

			addPersonNotice(userIds, processInstanceId, processInstance);

			log.info("抄送人添加..........");
		}

	}

	private void addPersonNotice(String userIds, Long processInstanceId, ProcessInstance processInstance) {
		List<ProcessInstanceInform> list = new ArrayList<>();

		for (String userId : userIds.split(CommEnum.DOU_HAO.getDesc())) {

			ProcessInstanceInform p = new ProcessInstanceInform();
			p.setUserId(Long.valueOf(userId));
			p.setInstanceId(processInstanceId);
			list.add(p);
			processInstanceInformManage.addInfoPerson(userId, processInstanceId);

			UserDTO userOrgBasicInfo = userService.queryUserById(Long.valueOf(userId));
			if (Func.isEmpty(userOrgBasicInfo)) {
				throw new ServiceException("获取用户信息失败");
			}
			String nodeJson = processInstance.getProcessUserNodeJson();
			List<UsersNodeVO> usersNodeinfos = JSON.parseArray(nodeJson, UsersNodeVO.class);

			for (UsersNodeVO usersNodeVO : usersNodeinfos) {
				if (Func.equals(usersNodeVO.getType(), 2)) {
					List<UserVO> list1 = usersNodeVO.getUsers();
					UserVO userVO = new UserVO();
					userVO.setUserId(Long.valueOf(userId));
					userVO.setAvatar(userOrgBasicInfo.getAvatar());
					userVO.setUsername(userOrgBasicInfo.getRealName());
					list1.add(userVO);
					usersNodeVO.setUsers(list1);
				}
			}
			LambdaUpdateWrapper<ProcessInstance> query = Wrappers.lambdaUpdate();
			query.eq(ProcessInstance::getId, processInstanceId);
			query.set(ProcessInstance::getProcessUserNodeJson, JSON.toJSONString(usersNodeinfos));
			processInstanceService.update(query);
		}
	}

	public ProcessInstanceVO getNextApprove(Long processInstanceId) {
		ProcessInstanceVO processInstanceVo1 = new ProcessInstanceVO();
		List<ProcessInstance> orgInstanceList = processInstanceService.getCurrentOrgIngListByCreateUser(null, null);

		List<String> orgIngFlowableInstanceId = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
		TaskQuery taskQuery = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceId)
			.taskAssignee(TaskFlowUtil.getTaskUser()).active()
			.includeProcessVariables()
			.orderByTaskCreateTime()
			.desc();

		List<Task> tasks = taskQuery.list();
		if (Func.isEmpty(tasks)) {
			return processInstanceVo1;
		}
		List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasks);
		if (todoTaskVos.size() > 1) {
			for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
				if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstanceId)) {
					continue;
				}
				LambdaQueryWrapper<ProcessInstance> queryWrapper = Wrappers.lambdaQuery();
				queryWrapper.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
				ProcessInstance processInstance = processInstanceService.getOne(queryWrapper);

				processInstanceVo1 = BeanUtil.copy(processInstance, ProcessInstanceVO.class);
				processInstanceVo1.setProcessInstanceId(processInstance.getId());
				processInstanceVo1.setIsOld(processInstance.getIsOld());
				break;
			}
		}


		return processInstanceVo1;
	}

	public List<PanelVO> appTodoTaskPanel() {
		List<ProcessInstance> orgInstanceList = processInstanceService.getCurrentOrgIngListByCreateUser(null, null);
		if (Func.isEmpty(orgInstanceList)) {
			return null;
		}
		//流程实例id的集合
		List<String> orgIngFlowableInstanceId = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
		TaskQuery taskQuery = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceId)
			.taskAssignee(TaskFlowUtil.getTaskUser()).active()
			.taskDescription(Func.toStr(Func.toLong(AuthUtil.getDeptId())))
			.includeProcessVariables()
			.orderByTaskCreateTime()
			.desc();

		//查出任务列表
		List<Task> tasks = taskQuery.list();
		//获取流程实例
		List<String> collect = tasks.stream().distinct().map(n -> n.getProcessInstanceId()).collect(Collectors.toList());
		if (Func.isEmpty(collect)) {
			return null;
		}
		LambdaQueryWrapper<ProcessInstance> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.in(ProcessInstance::getFlowableInstanceId, collect);
		List<ProcessInstance> processInstanceList = processInstanceService.list(queryWrapper);


		//以前使用流程定义Id
		List<Long> definationIds = processInstanceList.stream().map(n -> n.getDefinitionId()).collect(Collectors.toList());


		//现在使用版本dataId控制
		List<ProcessDefinition> processDefinitions = processDefinitionService.selectByProcessDefinitionIds(definationIds);

		List<Long> dataIds = new ArrayList<>();
		for (ProcessDefinition processDefinition : processDefinitions) {
			dataIds.add(processDefinition.getDataId());
		}
		List<Long> ids = dataIds.stream().distinct().collect(Collectors.toList());

		List<PanelVO> panelVos = new ArrayList<>();
		for (Long id : ids) {
			if (Func.isNotEmpty(id)) {
				PanelVO panelVO = new PanelVO();

				List<ProcessDefinition> processDefinitions1 = processDefinitionService.getAllByDataId(id);

				List<ProcessDefinition> processDefinitionList = new ArrayList<>();
				for (ProcessDefinition processDefinition : processDefinitions1) {
					if (definationIds.contains(processDefinition.getId())) {
						processDefinitionList.add(processDefinition);
					}

				}


				List<Long> idsNeed = processDefinitionList.stream().map(n -> n.getId()).collect(Collectors.toList());
				LambdaQueryWrapper<ProcessInstance> query = Wrappers.lambdaQuery();
				String idss = StringUtils.join(idsNeed.toArray(), ",");
				query.inSql(ProcessInstance::getDefinitionId, idss);
				query.eq(ProcessInstance::getStatus, 0);
				List<ProcessInstance> list = processInstanceService.list(query);
				List<ProcessInstance> listNew = new ArrayList<>();
				for (ProcessInstance processInstance : list) {
					if (collect.contains(processInstance.getFlowableInstanceId())) {
						listNew.add(processInstance);

					}
				}


				panelVO.setFlowSum(Math.toIntExact(listNew.size()));
				panelVO.setFlowName(processDefinitionList.get(0).getProcessName());
				panelVO.setDataId(id);
				String defId = "";
				for (ProcessDefinition processDefinition : processDefinitionList) {
					defId += processDefinition.getId() + ",";

				}
				panelVO.setDefinationId(defId.substring(0, defId.length() - 1));
				panelVos.add(panelVO);
			}


		}

		List<PanelVO> panelVOList = panelVos.stream().sorted(comparing(PanelVO::getFlowSum).reversed()).collect(Collectors.toList());

		return panelVOList;

	}

	public List<PanelVO> appDoneTaskPanel() {
		//查询当前用户已审批的任务
		LambdaQueryWrapper<ProcessTaskOperation> taskWrapper = Wrappers.<ProcessTaskOperation>lambdaQuery()
			.eq(ProcessTaskOperation::getCreateUser, AuthUtil.getUserId())
			.orderByDesc(ProcessTaskOperation::getCreateTime);
		List<ProcessTaskOperation> taskOperations = processTaskOperationService.list(taskWrapper);
		if (Func.isEmpty(taskOperations)) {
			return null;
		}
		List<Long> processInstanceIds = taskOperations.stream().map(ProcessTaskOperation::getInstanceId).collect(Collectors.toList());
		LambdaQueryWrapper<ProcessInstance> instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
			.in(ProcessInstance::getId, processInstanceIds);
		List<ProcessInstance> processInstanceList = processInstanceService.list(instanceWrapper);


		//获取流程定义（3种情况：第一种流程定义互斥，第二种流程定义有重复，第三种，流程名称相同，流程定义id不一样）
		// 老数据如何处理
		List<Long> definationIds = processInstanceList.stream().map(ProcessInstance::getDefinitionId).collect(Collectors.toList());

		//现在使用版本dataId控制
		List<ProcessDefinition> processDefinitions = processDefinitionService.selectByProcessDefinitionIds(definationIds);

		List<Long> dataIds = new ArrayList<>();
		for (ProcessDefinition processDefinition : processDefinitions) {
			dataIds.add(processDefinition.getDataId());
		}
		List<Long> ids = dataIds.stream().distinct().collect(Collectors.toList());

		List<PanelVO> panelVos = new ArrayList<>();
		for (Long id : ids) {
			if (Func.isNotEmpty(id)) {
				PanelVO panelVO = new PanelVO();

				List<ProcessDefinition> processDefinitions1 = processDefinitionService.getAllByDataId(id);

				List<ProcessDefinition> processDefinitionList = new ArrayList<>();
				for (ProcessDefinition processDefinition : processDefinitions1) {
					if (definationIds.contains(processDefinition.getId())) {
						processDefinitionList.add(processDefinition);
					}

				}

				List<Long> idsNeed = processDefinitionList.stream().map(n -> n.getId()).collect(Collectors.toList());
				LambdaQueryWrapper<ProcessInstance> query = Wrappers.lambdaQuery();
				String idss = StringUtils.join(idsNeed.toArray(), ",");
				query.inSql(ProcessInstance::getDefinitionId, idss);
				List<ProcessInstance> list = processInstanceService.list(query);
				List<Long> taskIds = taskOperations.stream().map(n -> n.getInstanceId()).collect(Collectors.toList());
				int count = 0;
				for (ProcessInstance processInstance : list) {
					if (taskIds.contains(processInstance.getId())) {
						count++;
						panelVO.setIsOld(processInstance.getIsOld());
					}

				}


				panelVO.setFlowSum(count);
				if (Func.equals(panelVO.getIsOld(), 1)) {
					panelVO.setFlowName("[旧]" + processDefinitionList.get(0).getProcessName());
				}
				if (Func.equals(panelVO.getIsOld(), 0)) {
					panelVO.setFlowName(processDefinitionList.get(0).getProcessName());
				}

				panelVO.setDataId(id);
				String defId = "";
				for (ProcessDefinition processDefinition : processDefinitionList) {
					defId += processDefinition.getId() + ",";

				}
				panelVO.setDefinationId(defId.substring(0, defId.length() - 1));
				panelVos.add(panelVO);
			}


		}


		List<PanelVO> panelVOList = panelVos.stream().sorted(comparing(PanelVO::getFlowSum).reversed()).collect(Collectors.toList());

		return panelVOList;

	}

	public List<PanelVO> launchTaskPanel(Integer status) {
		LambdaQueryWrapper<ProcessInstance> instanceWrapper = null;
		if (Func.isNotEmpty(status)) {
			instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
				.eq(ProcessInstance::getCreateUser, AuthUtil.getUserId())
//				.eq(ProcessInstance::getOrgId, Func.toLong(AuthUtil.getDeptId()))
				.eq(ProcessInstance::getStatus, status)
				.orderByDesc(ProcessInstance::getCreateTime);
		} else {
			instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
				.eq(ProcessInstance::getCreateUser, AuthUtil.getUserId())
//				.eq(ProcessInstance::getOrgId, Func.toLong(AuthUtil.getDeptId()))

				.orderByDesc(ProcessInstance::getCreateTime);
		}
		List<ProcessInstance> processInstanceList = processInstanceService.list(instanceWrapper);
		//以前使用流程定义Id
		List<Long> definationIds = processInstanceList.stream().map(ProcessInstance::getDefinitionId).collect(Collectors.toList());
		//现在使用版本dataId控制
		List<ProcessDefinition> processDefinitions = processDefinitionService.selectByProcessDefinitionIds(definationIds);
		List<Long> dataIds = new ArrayList<>();
		for (ProcessDefinition processDefinition : processDefinitions) {
			dataIds.add(processDefinition.getDataId());
		}
		List<Long> ids = dataIds.stream().distinct().collect(Collectors.toList());

		List<PanelVO> panelVos = new ArrayList<>();
		int sum = 0;
		for (Long id : ids) {
			if (Func.isNotEmpty(id)) {
				PanelVO panelVO = new PanelVO();

				List<ProcessDefinition> processDefinitions1 = processDefinitionService.getAllByDataId(id);

				List<ProcessDefinition> processDefinitionList = new ArrayList<>();
				for (ProcessDefinition processDefinition : processDefinitions1) {
					if (definationIds.contains(processDefinition.getId())) {
						processDefinitionList.add(processDefinition);
					}

				}


				List<Long> idsNeed = processDefinitionList.stream().map(n -> n.getId()).collect(Collectors.toList());
				LambdaQueryWrapper<ProcessInstance> query = Wrappers.lambdaQuery();
				String idss = StringUtils.join(idsNeed.toArray(), ",");
				query.inSql(ProcessInstance::getDefinitionId, idss);
				List<ProcessInstance> list = processInstanceService.list(query);

				List<Long> taskIds = processInstanceList.stream().map(n -> n.getId()).collect(Collectors.toList());
				int count = 0;
				for (ProcessInstance processInstance : list) {
					if (taskIds.contains(processInstance.getId())) {

						count++;
						panelVO.setIsOld(processInstance.getIsOld());
					}

				}


				panelVO.setFlowSum(count);
				sum = sum + count;

				if (Func.equals(panelVO.getIsOld(), 1)) {
					panelVO.setFlowName("[旧]" + processDefinitionList.get(0).getProcessName());
				}
				if (Func.equals(panelVO.getIsOld(), 0)) {
					panelVO.setFlowName(processDefinitionList.get(0).getProcessName());
				}
				panelVO.setDataId(id);
				String defId = "";
				for (ProcessDefinition processDefinition : processDefinitionList) {
					defId += processDefinition.getId() + ",";

				}
				panelVO.setDefinationId(defId.substring(0, defId.length() - 1));
				panelVos.add(panelVO);
			}


		}

		List<PanelVO> panelVOList = panelVos.stream().sorted(comparing(PanelVO::getFlowSum).reversed()).collect(Collectors.toList());


		return panelVOList;
	}

	@Transactional(rollbackFor = Exception.class)
	public NextApproveVO approveAppTask(ProcessTaskApproveDTO taskApproveDTO, String keyword, String definationId, int tabNum) {


		if (StringUtils.isBlank(taskApproveDTO.getFlowProcessInstanceId())) {
			throw new ServiceException("flowProcessInstanceId参数为空");
		}
		if (StringUtils.isBlank(taskApproveDTO.getCurrentNodeId())) {
			throw new ServiceException("该审批已由其他人受理");
		}
		Task task = processEngine.getTaskService().createTaskQuery()
			.taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
		if (task == null) {
			task = processEngine.getTaskService().createTaskQuery()
				.processInstanceId(taskApproveDTO.getFlowProcessInstanceId())
				.taskAssignee("taskUser_" + String.valueOf(AuthUtil.getUserId()))
				.taskDefinitionKey(taskApproveDTO.getCurrentNodeId()).singleResult();
		}
		if (task == null) {
			throw new ServiceException("该审批已由其他人受理");
		}
		if (!task.getAssignee().contains(String.valueOf(AuthUtil.getUserId()))) {
			throw new ServiceException("该审批已由其他人受理");
		}

		taskApproveDTO.setFlowableTaskId(task.getId());

		ProcessTaskOperation oldTaskOperation = processTaskOperationService.getByFlowableTaskId(taskApproveDTO.getFlowableTaskId());
		if (Func.isNotEmpty(oldTaskOperation)) {
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
//                throw new ServiceException("该任务已通过");
				throw new ServiceException("该审批已被其他人受理");
			}
			if (Func.equals(oldTaskOperation.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
//                throw new ServiceException("该任务已拒绝");
				throw new ServiceException("该审批已被其他人受理");
			}
			throw new ServiceException("该审批已被其他人受理");
		}
		ProcessInstance processInstance = processInstanceService.getProcessInstanceByFlowableTaskId(taskApproveDTO.getFlowableTaskId());

		if (Func.isEmpty(processInstance)) {
			throw new ServiceException("该审批已被其他人受理");
		}
//        if(!Func.equals(processInstance.getOrgId(),Func.toLong(AuthUtil.getDeptId()))){
//            throw new ServiceException("你不在该组织下，无权操作审批");
//
//        }
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())) {
			throw new ServiceException("审批已通过");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			throw new ServiceException("审批已拒绝");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.STOP.getCode())) {
			throw new ServiceException("审批已禁用");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.DELETED.getCode())) {
			throw new ServiceException("审批已删除");
		}
		if (Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.CANCEL.getCode())) {
			throw new ServiceException("审批已撤回");
		}
		if (!Func.equals(processInstance.getStatus(), ProcessInstance.StatusEnum.ING.getCode())) {
			throw new ServiceException("该审批已被其他人受理");
		}

		//转交拦截---根据流程实例id和taskid
		LambdaQueryWrapper<ProcessSpecialOperate> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(ProcessSpecialOperate::getProcessInstanceId, processInstance.getFlowableInstanceId())
			.eq(ProcessSpecialOperate::getTaskId, taskApproveDTO.getFlowableTaskId())
			.eq(ProcessSpecialOperate::getType, 8)
			.orderByDesc(ProcessSpecialOperate::getOperateDate);
		List<ProcessSpecialOperate> list = processSpecialOperateService.list(queryWrapper);
		if (Func.isNotEmpty(list) && list.size() > 0) {
			if (!Func.equals(list.get(0).getUserId(), AuthUtil.getUserId())) {
				throw new ServiceException("该审批已由其他人受理");
			}
		}
		ProcessInstanceVO processInstanceVo1 = new ProcessInstanceVO();
		//获取下一条可审批的数据
		NextApproveVO nextApproveVO = new NextApproveVO();
		NextQueryVO nextQueryVO = new NextQueryVO();
		nextQueryVO = BeanUtil.copy(taskApproveDTO, NextQueryVO.class);
		nextQueryVO.setProcessName(keyword);
		nextQueryVO.setCreateUserName(keyword);
		nextQueryVO.setTabNum(Func.isEmpty(tabNum) ? NextQueryVO.TabNumEnum.UN_AUDIT.getCode() : tabNum);
		nextQueryVO.setDefinationId(definationId);
		nextApproveVO = selectAppNextApprove(taskApproveDTO, nextQueryVO);

		if (Func.equals(taskApproveDTO.getStatus(), ProcessTaskOperation.StatusEnum.NO_PASS.getCode())) {
			// 拒绝
			refuseTask(processInstance, taskApproveDTO);
		} else if (Func.equals(taskApproveDTO.getStatus(), ProcessTaskOperation.StatusEnum.PASS.getCode())) {
			// 同意
			consentTask(processInstance, taskApproveDTO);

			// 保存任务操作
			ProcessTaskOperation taskOperation = ProcessTaskOperation.builder(processInstance, taskApproveDTO);
			HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskApproveDTO.getFlowableTaskId()).singleResult();
			taskOperation.setNodeId(taskInstance.getTaskDefinitionKey());
			processTaskOperationService.save(taskOperation);

		}

		//拒绝或同意后更新表单信息
		if (Func.equals(taskApproveDTO.getStatus(), ProcessInstance.StatusEnum.PASS.getCode())
			|| Func.equals(taskApproveDTO.getStatus(), ProcessInstance.StatusEnum.NO_PASS.getCode())) {
			updateDateJson(taskApproveDTO);
		}

		return nextApproveVO;

	}

	private NextApproveVO getAppNextApproveVO(String keyword, String definationId, ProcessInstance processInstance, ProcessInstanceVO processInstanceVo1, NextApproveVO nextApproveVO) {
		List<Long> definationIds = new ArrayList<>();
		List<ProcessInstance> orgInstanceList = new ArrayList<>();
		List<ProcessInstance> orgInstances = new ArrayList<>();
		List<ProcessInstance> orgInstanceListAll = new ArrayList<>();
		TaskQuery taskQuery = null;
		TaskQuery taskQueryAll = null;
		if (Func.isNotEmpty(keyword) || Func.isNotEmpty(definationId)) {
			if (Func.isNotEmpty(definationId)) {
				//截取字符串
				String[] ids = definationId.split(",");
				//string 转为  long
				List<Long> idsNeed = Arrays.stream(ids)
					.map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
				definationIds.addAll(idsNeed);
			}
			//获取当前组织下审批中实例
			orgInstanceList = processInstanceService.getCurrentOrgIngListByCreateUser(null, definationIds);

			List<String> orgIngFlowableInstanceIds = orgInstanceList.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
			taskQuery = taskService.createTaskQuery().processInstanceIdIn(orgIngFlowableInstanceIds)
				.taskAssignee(TaskFlowUtil.getTaskUser()).active()
				.includeProcessVariables()
				.orderByTaskCreateTime()
				.desc();
			if (Func.isNotEmpty(keyword)) {
				//发起人姓名和流程定义名

				if (Func.isEmpty(orgInstances)) {
					ProcessInstance i = new ProcessInstance();
					i.setId(99L);
					i.setFlowableInstanceId("99");
					orgInstances.add(i);
				}
				List<String> orgIngFlowableInstanceId = orgInstances.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
				log.info("orgIngFlowableInstanceId:{}" + orgIngFlowableInstanceId);


				taskQuery.or().processInstanceIdIn(orgIngFlowableInstanceId)
					.processDefinitionNameLike(TaskFlowUtil.getLikeKeyword(keyword)).endOr();
			}
		}


		//获取当前组织下审批中实例
		orgInstanceListAll = processInstanceService.getCurrentOrgIngListByCreateUser(null, null);

		if (orgInstanceListAll.size() == 0) {
			//先判断无条件获取流程实例数量，若没有，直接判定没有需我审批
			nextApproveVO.setFlag(false);
			nextApproveVO.setTips("没有下一条审批");
		} else {
			if (orgInstanceList.size() > 0 || orgInstances.size() > 0) {
				List<Task> tasks = taskQuery.list();

				if (tasks.size() > 0) {
					List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasks);

					if (todoTaskVos.size() > 0) {
						for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
							if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
								continue;
							}
							LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
							queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
							ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

							processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
							processInstanceVo1.setProcessInstanceId(processInstance1.getId());
							processInstanceVo1.setIsOld(processInstance1.getIsOld());
							break;
						}
					}
				}


				if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
					nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
					nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
					nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
					nextApproveVO.setFlag(true);
					nextApproveVO.setTips("获取到下一条审批");
				} else {
					List<String> orgInstanceListAllNew = orgInstanceListAll.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
					taskQueryAll = taskService.createTaskQuery().processInstanceIdIn(orgInstanceListAllNew)
						.taskAssignee(TaskFlowUtil.getTaskUser()).active()
						.includeProcessVariables()
						.orderByTaskCreateTime()
						.desc();
					List<Task> tasksAll = taskQueryAll.list();

					if (tasksAll.size() > 0) {
						List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasksAll);

						if (todoTaskVos.size() > 0) {
							for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
								if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
									continue;
								}
								LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
								queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
								ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

								processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
								processInstanceVo1.setProcessInstanceId(processInstance1.getId());
								processInstanceVo1.setIsOld(processInstance1.getIsOld());
								break;
							}
						}
					}


					if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
						nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
						nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
						nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
						nextApproveVO.setFlag(true);
						nextApproveVO.setTips("获取到下一条审批");
					} else {
						nextApproveVO.setFlag(false);
						nextApproveVO.setTips("没有下一条审批");
					}

				}
			} else {
				List<String> orgInstanceListAllNew = orgInstanceListAll.stream().map(ProcessInstance::getFlowableInstanceId).collect(Collectors.toList());
				taskQueryAll = taskService.createTaskQuery().processInstanceIdIn(orgInstanceListAllNew)
					.taskAssignee(TaskFlowUtil.getTaskUser()).active()
					.includeProcessVariables()
					.orderByTaskCreateTime()
					.desc();
				List<Task> tasksAll = taskQueryAll.list();

				if (tasksAll.size() > 0) {
					List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasksAll);

					if (todoTaskVos.size() > 0) {
						for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
							if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
								continue;
							}
							LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
							queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
							ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

							processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
							processInstanceVo1.setProcessInstanceId(processInstance1.getId());
							processInstanceVo1.setIsOld(processInstance1.getIsOld());
							break;
						}
					}
				}


				if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
					nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
					nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
					nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
					nextApproveVO.setFlag(true);
					nextApproveVO.setTips("获取到下一条审批");
				} else {
					nextApproveVO.setFlag(false);
					nextApproveVO.setTips("没有下一条审批");
				}

			}
		}

		return nextApproveVO;
	}

	private void approveMethod(ProcessInstance processInstance, ProcessInstanceVO processInstanceVo1, NextApproveVO nextApproveVO, List<Task> tasks) {
		List<ProcessInstanceVO> todoTaskVos = convertFlowableTask(tasks);

		if (todoTaskVos.size() > 0) {
			for (ProcessInstanceVO processInstanceVo : todoTaskVos) {
				if (Func.equals(processInstanceVo.getProcessInstanceId(), processInstance.getId())) {
					continue;
				}
				LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
				queryWrapper1.eq(ProcessInstance::getId, processInstanceVo.getProcessInstanceId());
				ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);

				processInstanceVo1 = BeanUtil.copy(processInstance1, ProcessInstanceVO.class);
				processInstanceVo1.setProcessInstanceId(processInstance1.getId());
				processInstanceVo1.setIsOld(processInstance1.getIsOld());
				break;
			}
			if (Func.isNotEmpty(processInstanceVo1.getProcessInstanceId())) {
				nextApproveVO.setIsOld(processInstanceVo1.getIsOld());
				nextApproveVO.setProcessInstanceId(processInstanceVo1.getProcessInstanceId());
				nextApproveVO.setFlowableInstanceId(processInstanceVo1.getFlowableInstanceId());
				nextApproveVO.setFlag(true);
				nextApproveVO.setTips("获取到下一条审批");
			} else {
				nextApproveVO.setFlag(false);
				nextApproveVO.setTips("没有下一条审批");

			}

		}
	}

	/**
	 * 更新审批实例表单数据
	 *
	 * @param taskApproveDTO 操作参数信息
	 * @return true成功 false 失败
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean updateDateJson(ProcessTaskApproveDTO taskApproveDTO) {
		if (Func.isEmpty(taskApproveDTO) || Func.isEmpty(taskApproveDTO.getDataJson())) {
			return Boolean.TRUE;
		}

		ProcessInstance oldInstanceVO = processInstanceService.getOne(Wrappers.<ProcessInstance>lambdaQuery()
			.eq(ProcessInstance::getFlowableInstanceId, taskApproveDTO.getFlowProcessInstanceId()));
		if (Func.equals(oldInstanceVO.getDataJson(), taskApproveDTO.getDataJson())) {
			return true;
		}

		processInstanceService.update(Wrappers.<ProcessInstance>lambdaUpdate()
			.set(ProcessInstance::getDataJson, taskApproveDTO.getDataJson())
			.eq(ProcessInstance::getFlowableInstanceId, taskApproveDTO.getFlowProcessInstanceId()));

		// 添加编辑表单信息log
		processLogService.save(new ProcessLog(oldInstanceVO.getId(), oldInstanceVO.getDefinitionId(), oldInstanceVO.getDataJson(), taskApproveDTO.getDataJson()));

		// 更新流程实例正文检索信息表
		insertInstanceValues(oldInstanceVO.getDefinitionId(), oldInstanceVO.getId());

		return Boolean.TRUE;
	}


	/**
	 * 拷贝原节点的表单字段权限赋值给新的加签节点
	 *
	 * @param processSignAddDTO 加签信息
	 * @param newNodeId         新节点id
	 * @return true 成功 false 失败
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean saveNewNodeField(ProcessSignAddDTO processSignAddDTO, String newNodeId) {
		//查询当前节点的表单字段权限配置信息
		FormField fieldVo = formFieldService.getOne(Wrappers.<FormField>lambdaQuery()
			.eq(FormField::getProcessInstanceId, processSignAddDTO.getProcessInstanceId())
			.eq(FormField::getNodeId, processSignAddDTO.getCurrentNodeId())
			.last(" limit 1"));

		if (Func.isEmpty(fieldVo)) {
			log.info("===加签节点" + newNodeId + "没有查询到表单权限");
			return Boolean.TRUE;
		}
		//拷贝原节点的表单字段权限赋值给新的加签节点
		FormField newNodeField = new FormField();

		newNodeField.setFieldJson(fieldVo.getFieldJson());
		newNodeField.setProcessInstanceId(fieldVo.getProcessInstanceId());
		newNodeField.setNodeId(newNodeId);
		newNodeField.setDataId(fieldVo.getDataId());
		return formFieldService.save(newNodeField);
	}


	/**
	 * 保存流程实例的表单全文数据供检索
	 *
	 * @param definitionId 流程定义ID
	 * @param instanceId   流程实例ID
	 */
	@Transactional(rollbackFor = Exception.class)
	public void insertInstanceValues(Long definitionId, Long instanceId) {
		ProcessInstance instanceVO = processInstanceService.getById(instanceId);
		ProcessDefinition definitionVO = processDefinitionService.getById(definitionId);
		if (Func.isEmpty(instanceVO) || Func.isEmpty(definitionVO)) {
			return;
		}

		List<FormKv.FromKv> formKvs = FormKvUtil.exec(definitionVO.getFormStyleJson(), instanceVO.getDataJson());
		String valuesText = StringPool.EMPTY;
		String lableText = StringPool.EMPTY;
		// 解析表单内容
		for (FormKv.FromKv kv : formKvs) {
			lableText += kv.getLable() + FormKvUtil.SPLIT_STR;
			if (Func.isEmpty(kv.getValue()) && !Func.equals(kv.getType(), FormKvUtil.TypeEnum.FORM.getCode())) {
				continue;
			}
			// 说明文字无需处理
			if (Func.equals(kv.getType(), FormKvUtil.TypeEnum.TIPS.getCode())) {
				continue;
			}
			// 明细表格的处理
			if (Func.equals(kv.getType(), FormKvUtil.TypeEnum.FORM.getCode())) {
				//取child
				List<List<FormKv.FromKv>> childList = kv.getChild();
				if (Func.isEmpty(childList) || Func.isEmpty(childList.get(0))) {
					continue;
				}
				for (FormKv.FromKv childKv : childList.get(0)) {
					lableText += childKv.getLable() + FormKvUtil.SPLIT_STR;
					if (Func.isEmpty(childKv.getValue())) {
						continue;
					}
					// 说明文字无需处理
					if (Func.equals(childKv.getType(), FormKvUtil.TypeEnum.TIPS.getCode())) {
						continue;
					}
					// 其他类型
					valuesText += childKv.getValue().replace(FormKvUtil.OLD_SPLIT_STR, FormKvUtil.SPLIT_STR) + FormKvUtil.SPLIT_STR;
				}
				continue;
			}
			// 其他类型
			valuesText += kv.getValue().replace(FormKvUtil.OLD_SPLIT_STR, FormKvUtil.SPLIT_STR) + FormKvUtil.SPLIT_STR;
		}
		// 移除老数据
		instanceValuesService.remove(Wrappers.<InstanceValues>lambdaQuery().eq(InstanceValues::getProcessInstanceId, instanceVO.getId()));

		InstanceValues newInstanceValues = new InstanceValues();
		newInstanceValues.setProcessInstanceId(instanceVO.getId());
		newInstanceValues.setFlowableInstanceId(instanceVO.getFlowableInstanceId());
		newInstanceValues.setLableText(lableText);
		newInstanceValues.setValueText(valuesText);
		// 添加新数据
		instanceValuesService.save(newInstanceValues);
	}

	@Async
	public void updatetaskOrgId(String flowableInstanceId) {

		try {
			Thread.sleep(2000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		List<Task> taskList = taskService.createTaskQuery().processInstanceId(flowableInstanceId).list();

		if (Func.isEmpty(taskList)) {
			return;
		}
		for (Task task : taskList) {
			updateFloableTask(task);
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public void updateFloableTask(Task task) {

		//        获取task 用户id，获取task 节点信息，获取task 实例ID
		//        ==redis锁==
		//        1.查询此实例-此节点-此用户是否在redis存在设置，若不存在则继续
		//        2.根据task查询当前流程实例ID-查询我方节点信息(当前审批中的节点)
		//        3.给当前task设置 DESCRIPTION_ 为 orgId
		//        ==redis锁释放==

		Long taskUerId = TaskFlowUtil.getUserId(task.getAssignee());
		String taskNodeId = task.getTaskDefinitionKey();
		String flowableInstanceId = task.getProcessInstanceId();

		ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(flowableInstanceId);
		if (Func.isEmpty(processInstance)) {
			return;
		}
		List<UsersNodeVO> usersNodeVos = JSON.parseArray(processInstance.getProcessUserNodeJson(), UsersNodeVO.class);
		UsersNodeVO uuVO = usersNodeVos.stream().filter(k -> Func.equals(k.getNodeId(), task.getTaskDefinitionKey())).findFirst().orElse(null);
		if (Func.isEmpty(uuVO)) {
			return;
		}
		String updateOrgId = "";
		for (UserVO user : uuVO.getUsers()) {
			if (Func.isEmpty(user.getOrgId())) {

			}
			if (Func.equals(user.getUserId(), taskUerId) && Func.isEmpty(selectRedisUser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId()))) {
				updateOrgId = Func.toStr(user.getOrgId());
				addRedisuser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId());
				break;
			}
		}

		task.setDescription(updateOrgId);
		taskService.saveTask(task);

//		String lockKey = "flow-task-update";
//		boolean lock = false;
//		try {
//            lock = redisLock.tryLock(lockKey, LockType.REENTRANT, 5, 4, TimeUnit.SECONDS);
//			if (lock) {
//				ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(flowableInstanceId);
//				if (Func.isEmpty(processInstance)) {
//					return;
//				}
//				List<UsersNodeVO> usersNodeVos = JSON.parseArray(processInstance.getProcessUserNodeJson(), UsersNodeVO.class);
//				UsersNodeVO uuVO = usersNodeVos.stream().filter(k -> Func.equals(k.getNodeId(), task.getTaskDefinitionKey())).findFirst().orElse(null);
//				if (Func.isEmpty(uuVO)) {
//					return;
//				}
//				String updateOrgId = "";
//				for (UserVO user : uuVO.getUsers()) {
//					if (Func.isEmpty(user.getOrgId())) {
////						user.setOrgId(processInstance.getOrgId());
//					}
//					if (Func.equals(user.getUserId(), taskUerId) && Func.isEmpty(selectRedisUser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId()))) {
//						updateOrgId = Func.toStr(user.getOrgId());
//						addRedisuser(flowableInstanceId, taskNodeId, taskUerId, user.getOrgId());
//						break;
//					}
//				}
//
//				task.setDescription(updateOrgId);
//				taskService.saveTask(task);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			if (lock) {
//				// 解锁
//                redisLock.forceUnlock(lockKey, LockType.REENTRANT);
//			}
//		}

	}


	/**
	 * 查询redis中是否有设置记录
	 *
	 * @param flowableInstanceId 实例ID
	 * @param taskNodeId         节点ID
	 * @param taskUerId          userId
	 * @param orgId              组织ID
	 * @return
	 */
	private String selectRedisUser(String flowableInstanceId, String taskNodeId, Long taskUerId, Long orgId) {

		return bladeRedis.get(FlowCacheKey.FLOW_TASK_ADDSIGN_USED.getKey(flowableInstanceId, taskNodeId, taskUerId, orgId, taskUerId));

	}

	/**
	 * 添加task已修orgId的用户
	 *
	 * @param flowableInstanceId
	 * @param taskNodeId
	 * @param taskUerId
	 * @param orgId
	 */
	private void addRedisuser(String flowableInstanceId, String taskNodeId, Long taskUerId, Long orgId) {

		bladeRedis.set(FlowCacheKey.FLOW_TASK_ADDSIGN_USED.getKey(flowableInstanceId, taskNodeId, taskUerId, orgId, taskUerId), "1");

	}


	/**
	 * @param timeVO         时间查询参数
	 * @param processName
	 * @param groupId        分组ID
	 * @param createUserName 创建人名称
	 * @param query          查询参数
	 * @return
	 */
	public IPage<ProcessInstanceVO> todoTaskPageNew(ApprovePageQueryVO timeVO, String processName, Long groupId, String createUserName, Query query) {
		IPage<ProcessInstanceVO> iPage = Condition.getPage(query);

		// 查询创建人条件
		List<SimpleUserVO> userList = new ArrayList<>();
		if (StringUtils.isNoneEmpty(createUserName)) {
			userList = selectCreateUserList(createUserName);
			if (CollectionUtils.isEmpty(userList)) {
				return null;
			}
		}

		// 查询需我审批列表
		List<MyTaskVO> tasklist = processInstanceService.selectMyTaskPage(iPage, userList, groupId, timeVO, processName);
		if (Func.isEmpty(tasklist)) {
			return iPage;
		}

		// 组装需要的数据
		List<ProcessInstanceVO> todoTaskVos = convertFlowableTaskNew(tasklist);

		// 拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(processName, todoTaskVos);

		iPage.setRecords(todoTaskVos);

		return iPage;
	}


	/**
	 * 按名称查询不同组织下的人员信息
	 *
	 * @param createUserName
	 * @return
	 */
	private List<SimpleUserVO> selectCreateUserList(String createUserName) {

		List<SimpleUserVO> userList = new ArrayList<>();
		if (Func.isEmpty(createUserName)) {
			return userList;
		}
		if (Func.isNotEmpty(createUserName)) {

			List<UserDept> resUser = userService.queryUserDeptListByCondition(createUserName);

			if (CollectionUtils.isEmpty(resUser)) {
				return userList;
			}

			userList = resUser.stream().map(k -> {
				return new SimpleUserVO(k.getUserId(), k.getDeptId());
			}).collect(Collectors.toList());
		}
		return userList;
	}


	/**
	 * 组装返回数据
	 *
	 * @param tasks
	 * @return
	 */
	private List<ProcessInstanceVO> convertFlowableTaskNew(List<MyTaskVO> tasks) {
		// 流程实例ID
		List<String> flowableInstanceIds = tasks.stream().map(MyTaskVO::getProcessInstanceId).collect(Collectors.toList());

		// 获取流程实例
		LambdaQueryWrapper<ProcessInstance> instanceWrapper = Wrappers.<ProcessInstance>lambdaQuery()
			.in(ProcessInstance::getFlowableInstanceId, flowableInstanceIds);
		List<ProcessInstance> processInstances = processInstanceService.list(instanceWrapper);
		if (CollectionUtils.isEmpty(processInstances)) {
			return null;
		}

		// 流程定义ID
		List<Long> definitionIds = processInstances.stream().map(ProcessInstance::getDefinitionId).collect(Collectors.toList());

		Map<String, ProcessInstance> instanceMap = processInstances.stream().collect(Collectors.toMap(ProcessInstance::getFlowableInstanceId, Function.identity()));

		// 获取流程定义
		List<ProcessDefinition> definitions = processDefinitionService.selectByProcessDefinitionIds(definitionIds);
		if (Func.isEmpty(definitions)) {
			throw new ServiceException("查询流程定义失败！！");
		}

		Map<Long, ProcessDefinition> definitionMap = definitions.stream().collect(Collectors.toMap(ProcessDefinition::getId, Function.identity(), (i1, i2) -> i1));
		// 获取流程分组
		List<Long> groupIds = definitions.stream().map(ProcessDefinition::getGroupId).collect(Collectors.toList());

		// 流程分组
		List<ProcessGroup> groups = processGroupManage.selectByGroupIds(groupIds);
		Map<Long, ProcessGroup> groupMap = groups.stream().collect(Collectors.toMap(ProcessGroup::getId, Function.identity()));

		return tasks.stream().map(item -> {
			ProcessInstanceVO todoTaskVO = new ProcessInstanceVO();
			todoTaskVO.setFlowableTaskId(item.getId());
			ProcessInstance processInstance = instanceMap.get(item.getProcessInstanceId());
			ProcessDefinition processDefinition = definitionMap.get(processInstance.getDefinitionId());
			todoTaskVO.setProcessInstanceId(processInstance.getId());
			todoTaskVO.setDataJson(processInstance.getDataJson());
			todoTaskVO.setProcessName(processDefinition.getProcessName());
			todoTaskVO.setFormStyleJson(processDefinition.getFormStyleJson());
			todoTaskVO.setIsForm(processDefinition.getIsForm());
			ProcessGroup processGroup = groupMap.get(processDefinition.getGroupId());
			todoTaskVO.setGroupName(processGroup.getGroupName());

			// 查询用户部门信息
			UserDeptDTO userDeptDto = userService.queryUserDept(processInstance.getCreateUser(), processInstance.getCreateDept());
			todoTaskVO.setCreateUserName(userDeptDto == null ? null : userDeptDto.getEmployeeName());
			todoTaskVO.setDeptName(userDeptDto == null ? null : userDeptDto.getDeptName());
			//跨组织需要显示流程实例的orgId的name
			todoTaskVO.setOrgName(userDeptDto == null ? null : userDeptDto.getOrgName());
			todoTaskVO.setAvatar(userDeptDto == null ? null : userDeptDto.getAvatar());

			todoTaskVO.setCreateDate(processInstance.getCreateTime());
			todoTaskVO.setStatus(processInstance.getStatus());
			todoTaskVO.setStatusStr(ProcessInstance.StatusEnum.from(processInstance.getStatus()).getDesc());
			todoTaskVO.setCreateUserId(processInstance.getCreateUser());
			todoTaskVO.setFlowableTaskId(item.getId());
			todoTaskVO.setOldProcessId(processInstance.getRelationProcessId());
			todoTaskVO.setRemark(processInstance.getRemark());
			todoTaskVO.setIsOld(0);
			return todoTaskVO;
		}).collect(Collectors.toList());
	}


	/**
	 * app查询需我审批的列表
	 *
	 * @param vo           时间参数
	 * @param keyword      关键字
	 * @param query        分页参数
	 * @param definationId
	 * @return 需我审批列表
	 */
	public IPage<ProcessInstanceVO> appTodoTaskPageNew(ApprovePageQueryVO vo, String keyword, Query query, String definationId) {
		IPage<ProcessInstanceVO> iPage = Condition.getPage(query);
		//查询创建人条件
		List<SimpleUserVO> userList = selectCreateUserList(keyword);
		//查询需我审批列表
		List<MyTaskVO> tasklist = processInstanceService.selectPageByKeyWord(iPage, userList, keyword, definationId, vo);
		if (Func.isEmpty(tasklist)) {
			return iPage;
		}
		//组装需要的数据
		List<ProcessInstanceVO> todoTaskVos = convertFlowableTaskNew(tasklist);
		//拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(keyword, todoTaskVos);
		iPage.setRecords(todoTaskVos);
		return iPage;
	}

	public void nodeCrossPass(String processDefinitionId, String flowableInstanceId, TaskEntity entity) {
		log.info("跨组织审批........................");
		try {
			TimeUnit.SECONDS.sleep(2);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		log.info("当前节点审批人》{}", entity.getAssignee());

		//获取上一节点的审批人
		//当前节点的执行id
		String executionId = entity.getExecutionId();

		Execution execution = runtimeService.createExecutionQuery().executionId(executionId).singleResult();
		if (Func.isNotEmpty(execution)) {
			//排除当前节点不是第一个审批节点
			ProcessInstance byFlowableInstanceId = processInstanceService.getByFlowableInstanceId(flowableInstanceId);
			String currentActivityId = execution.getActivityId();
			//获取流程实例的节点信息
			List<UsersNodeVO> usersNodeVos = JSON.parseArray(byFlowableInstanceId.getProcessUserNodeJson(), UsersNodeVO.class);
			List<String> nodeIds = usersNodeVos.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
			log.info("流程实例的节点信息-》" + nodeIds);
			if (nodeIds.indexOf(currentActivityId) > 1) {


				String str = entity.getAssignee();
				String str1 = str.substring(0, str.indexOf("_"));
				String str2 = str.substring(str1.length() + 1, str.length());

				LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
				queryWrapper.eq(ProcessTaskOperation::getInstanceId, byFlowableInstanceId.getId());
				queryWrapper.eq(ProcessTaskOperation::getNodeId, currentActivityId);
				queryWrapper.eq(ProcessTaskOperation::getCreateUser, Long.valueOf(str2));
				List<ProcessTaskOperation> list = processTaskOperationService.list(queryWrapper);
				if (Func.isNotEmpty(list) && list.size() > 0) {
					//获取orgId

					taskService.complete(entity.getId());
					HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(entity.getId()).singleResult();
					ProcessInstance processInstance = processInstanceService.getByFlowableInstanceId(historicTaskInstance.getProcessInstanceId());
					ProcessTaskOperation taskOperation = new ProcessTaskOperation();
					taskOperation.setFlowableTaskId(entity.getId());
					taskOperation.setInstanceId(processInstance.getId());
					taskOperation.setOpinion("自动通过");
					taskOperation.setStatus(ProcessTaskOperation.StatusEnum.AUTO_PASS.getCode());
					taskOperation.setNodeId(historicTaskInstance.getTaskDefinitionKey());
					taskOperation.setTenantId("000001");
					//跨组织显示相应的企业人员的orgid
					taskOperation.setCreateUser(Long.valueOf(str2));
					taskOperation.setUpdateUser(Long.valueOf(str2));
					taskOperation.setCreateDept(null);
					processTaskOperationService.save(taskOperation);
				}
			}
		}


	}

	@Transactional(rollbackFor = Exception.class)
	@Async
	public void nodeCheckPass(String processDefinitionId, String flowableInstanceId, TaskEntity entity, String assignee, String currentId, String taskId, String desc) {
		try {
			TimeUnit.SECONDS.sleep(5);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		log.info("当前节点审批人》{}", assignee);
		//获取上一节点的审批人
		//当前节点的执行id
		String executionId = entity.getExecutionId();


		//判断当前节点是否是或签模式
		LambdaQueryWrapper<ProcessInstance> queryWrapper1 = Wrappers.lambdaQuery();
		queryWrapper1.eq(ProcessInstance::getFlowableInstanceId, flowableInstanceId);
		ProcessInstance processInstance1 = processInstanceService.getOne(queryWrapper1);
		String nodeJson = processInstance1.getProcessUserNodeJson();
		//获取流程实例的节点信息
		List<UsersNodeVO> usersNodeVos = JSON.parseArray(nodeJson, UsersNodeVO.class);
		List<String> nodeIds = usersNodeVos.stream().map(UsersNodeVO::getNodeId).collect(Collectors.toList());
		log.info("流程实例的节点信息-》" + nodeIds);
		//在获取流程定义的节点信息
		LambdaQueryWrapper<ProcessDefinition> queryW = Wrappers.lambdaQuery();
		queryW.eq(ProcessDefinition::getId, processInstance1.getDefinitionId());
		String json = processDefinitionService.getOne(queryW).getProcessJson();

		if (Func.isNotEmpty(executionId)) {
			String currentActivityId = currentId;
			log.info("获取当前节点currentActivityId" + currentActivityId);

			List<NodeModel> nodes = FlowConvertData.parseBody(json);
			List<NodeModel> newNodes = new ArrayList<>();
			log.info("流程节点信息-》{}" + nodes);


			Map<String, NodeModel> nodeModelMap = nodes.stream().collect(Collectors.toMap(NodeModel::getResourceId, Function.identity()));
			log.info("流程节点信息-》{}" + nodeModelMap);
			int type = 0;
			if (Func.isNotEmpty(nodeModelMap.get(currentActivityId).getProperties())) {
				type = (int) nodeModelMap.get(currentActivityId).getProperties().get("approverType");
			}
			//2是或签
			if (type != CommNumberEnum.NUMBER_2.getCode()) {
				String beforeNodeId = null;
				int num = nodeIds.indexOf(currentActivityId);
				if (num > 0) {

					beforeNodeId = nodeIds.get(num - 1);


					if (Func.isNotEmpty(beforeNodeId)) {
						ProcessInstance byFlowableInstanceId = processInstanceService.getByFlowableInstanceId(flowableInstanceId);
						log.info("sourceRef>{}" + beforeNodeId);

						log.info("start_>{}.........自动审批开始");

						//获取上一节点是否有assign通过审批,第一次进来获取不到已审批的节点，所以不能自动通过
						List<HistoricActivityInstance> hisActivityInstanceListPre = ((HistoricActivityInstanceQuery) historyService
							.createHistoricActivityInstanceQuery()
							.processInstanceId(flowableInstanceId).activityType("userTask").activityId(beforeNodeId).taskAssignee(assignee)
							.finished().orderByHistoricActivityInstanceEndTime().desc())
							.list();
						LambdaQueryWrapper<ProcessTaskOperation> queryWrapper = Wrappers.lambdaQuery();
						queryWrapper.eq(ProcessTaskOperation::getInstanceId, byFlowableInstanceId.getId());
						queryWrapper.eq(ProcessTaskOperation::getNodeId, beforeNodeId);
						queryWrapper.eq(ProcessTaskOperation::getCreateUser, TaskFlowUtil.getUserId(assignee));
						List<ProcessTaskOperation> list = processTaskOperationService.list(queryWrapper);

						if (list.size() > 0) {
							if (hisActivityInstanceListPre.size() == 0) {
								try {
									TimeUnit.SECONDS.sleep(5);
								} catch (InterruptedException e) {
									e.printStackTrace();
								}
							}
							//上一届点有审批，当前节点为u审批，自动通过
							taskService.complete(taskId);
							HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
							ProcessInstance processInstance = processInstanceService.getProcessInstanceByFlowableTaskId(flowableInstanceId);
							ProcessTaskOperation taskOperation = new ProcessTaskOperation();
							taskOperation.setFlowableTaskId(taskId);
							taskOperation.setInstanceId(processInstance.getId());
							taskOperation.setOpinion("自动通过");
							taskOperation.setStatus(ProcessTaskOperation.StatusEnum.AUTO_PASS.getCode());
							taskOperation.setNodeId(historicTaskInstance.getTaskDefinitionKey());
							taskOperation.setTenantId("000001");
							taskOperation.setCreateUser(TaskFlowUtil.getUserId(assignee));
							taskOperation.setUpdateUser(TaskFlowUtil.getUserId(assignee));
							taskOperation.setCreateDept(null);
							log.info("1111111111111111");
							processTaskOperationService.save(taskOperation);
							log.info("111111111111111122222222222222");
						}
						// }

					}
				}
			}

		}


	}

	public void nodeAutoPass2(String processDefinitionId, String flowableInstanceId, TaskEntity entity) {
		log.info("" + processDefinitionId);
		log.info("" + flowableInstanceId);
		log.info("" + JSONObject.toJSONString(entity));

	}

	public IPage<ProcessInstanceVO> doneTaskPageNew(ApprovePageQueryVO vo, String processName, Long groupId, String createUserName, Query query) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);

		// 查询创建人条件
		List<SimpleUserVO> userList = new ArrayList<>();
		if (StringUtils.isNotEmpty(createUserName)) {
			userList = selectCreateUserList(createUserName);
			if (CollectionUtils.isEmpty(userList)) {
				return null;
			}
		}

		MyLaunchPageQueryVO queryVO = BeanUtil.copy(vo, MyLaunchPageQueryVO.class);

		if (Func.isEmpty(queryVO)) {
			queryVO = new MyLaunchPageQueryVO();
		}
		queryVO.setProcessName(processName);
		queryVO.setGroupId(groupId);
		queryVO.setCreateUser(AuthUtil.getUserId());
		queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
		queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.WEB.getCode());

		// 查询流程实例数据
		List<ProcessInstance> myList = processInstanceService.doneTaskPageNew(page, queryVO, userList);
		if (Func.isEmpty(myList)) {
			return page;
		}

		// entity转换vo
		List<ProcessInstanceVO> processInstanceVoList = convertProcessInstanceVO(myList);

		// 拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(processName, processInstanceVoList);

		page.setRecords(processInstanceVoList);
		return page;
	}


	public IPage<ProcessInstanceVO> appDoneTaskPageNew(ApprovePageQueryVO vo, String keyword, Query query, String definationId) {
		IPage<ProcessInstanceVO> page = Condition.getPage(query);
		//查询创建人条件
		List<SimpleUserVO> userList = selectCreateUserList(keyword);
		MyLaunchPageQueryVO queryVO = BeanUtil.copy(vo, MyLaunchPageQueryVO.class);
		if (Func.isEmpty(queryVO)) {
			queryVO = new MyLaunchPageQueryVO();
		}
		queryVO.setProcessName(keyword);
		queryVO.setCreateUser(AuthUtil.getUserId());
		queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
		queryVO.setDefinationId(definationId);
		queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.APP.getCode());
		List<ProcessInstance> processList = processInstanceService.doneTaskPageNew(page, queryVO, userList);

		if (Func.isEmpty(processList)) {
			return page;
		}
		List<ProcessInstanceVO> processInstanceVoList = convertProcessInstanceVO(processList);
		//拼接摘要，高亮显示字段
		FormKvUtil.getNewDateJson(keyword, processInstanceVoList);
		page.setRecords(processInstanceVoList);
		return page;
	}

	/**
	 * 查询下一条可审批的数据
	 *
	 * @param taskApproveDTO 审批的信息
	 * @param nextQueryVO    列表查询条件
	 * @return 下一条可审批的数据
	 */
	@Transactional(rollbackFor = Exception.class)
	public NextApproveVO selectPcNextApprove(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO) {
//        自动下一条
//        0.查询所有需我审批的流程ID备用
//        1.按条件查询当前列表所有审批中的数据,获取当前数据的下一条(需要我审批的数据),若没有获取到，则获取第一条
//        2.按条件没获取到下一条，则清空所有条件查询当前列表所有审批中的数据，获取第一条需要审批的数据(判断是否需要审批)
//        3.组装数据返回
		if (Func.isEmpty(nextQueryVO)) {
			nextQueryVO = new NextQueryVO();
			nextQueryVO.setTabNum(NextQueryVO.TabNumEnum.UN_AUDIT.getCode());
		} else if (Func.isEmpty(nextQueryVO.getTabNum())) {
			nextQueryVO.setTabNum(NextQueryVO.TabNumEnum.UN_AUDIT.getCode());
		}

		Long nextInsId = null;

		//查询所有需我审批的流程ID备用
		List<Long> insIdList = selectMyInsIdList();

		if (Func.isEmpty(insIdList)) {
			return selectNextWrapper(nextInsId);
		}

		//按条件查询当前列表所有审批中的数据,获取当前数据的下一条(需要我审批的数据),若没有获取到，则从第一条再获取一遍
		nextInsId = selectNextByQuery(taskApproveDTO, nextQueryVO, insIdList);
		if (Func.isNotEmpty(nextInsId)) {
			return selectNextWrapper(nextInsId);
		}

		//2.按条件没获取到下一条，则清空所有条件查询当前列表所有审批中的数据，获取第一条需要审批的数据(判断是否需要审批)
		nextInsId = selectNextNoQuery(taskApproveDTO, nextQueryVO, insIdList);

		//组装返回数据
		return selectNextWrapper(nextInsId);
	}


	/**
	 * 下一条数据信息组装
	 *
	 * @param nextInsId
	 * @return
	 */
	private NextApproveVO selectNextWrapper(Long nextInsId) {
		NextApproveVO resVO = new NextApproveVO();

		if (Func.isNotEmpty(nextInsId)) {
			ProcessInstance nextInstance = processInstanceService.getOneById(nextInsId);

			if (Func.isNotEmpty(nextInstance)) {
				resVO.setProcessInstanceId(nextInsId);
				resVO.setIsOld(nextInstance.getIsOld());
				resVO.setFlowableInstanceId(nextInstance.getFlowableInstanceId());
				resVO.setFlag(true);
				resVO.setTips(NextApproveVO.TIPS_EXIST);
				return resVO;
			}
		}

		resVO.setFlag(false);
		resVO.setTips(NextApproveVO.TIPS_UN_EXIST);

		return resVO;
	}


	/**
	 * 查询所有需我审批的流程ID
	 *
	 * @return
	 */
	private List<Long> selectMyInsIdList() {
		IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
		//查询需我审批列表
		List<MyTaskVO> tasklist = processInstanceService.selectMyTaskPage(iPage, null, null, null, null);

		if (Func.isNotEmpty(tasklist)) {
			List<String> flowableInsIdList = tasklist.stream().map(MyTaskVO::getProcessInstanceId).collect(Collectors.toList());
			List<ProcessInstance> insList = processInstanceService.list(Wrappers.<ProcessInstance>lambdaQuery()
				.in(ProcessInstance::getFlowableInstanceId, flowableInsIdList));
			return insList.stream().map(ProcessInstance::getId).collect(Collectors.toList());
		} else {
			return null;
		}
	}


	/**
	 * 按条件查询当前列表所有审批中的数据,获取当前数据的下一条(需要我审批的数据),若没有获取到，则从第一条再获取一遍
	 *
	 * @param taskApproveDTO
	 * @param nextQueryVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Long selectNextByQuery(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO, List<Long> insIdList) {

		if (Func.equals(NextQueryVO.TabNumEnum.UN_AUDIT.getCode(), nextQueryVO.getTabNum())) {
			// 需我审批的列表
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
			ApprovePageQueryVO timeVO = BeanUtil.copy(nextQueryVO, ApprovePageQueryVO.class);

			// 查询创建人条件
			List<SimpleUserVO> userList = selectCreateUserList(nextQueryVO.getCreateUserName());
			List<MyTaskVO> tasklist = processInstanceService.selectMyTaskPage(iPage, userList, nextQueryVO.getGroupId(), timeVO, nextQueryVO.getProcessName());
			if (Func.isEmpty(tasklist)) {
				return null;
			}

			int taskIndex = 0;
			Long firstTask = null;
			for (int i = 0; i < tasklist.size(); i++) {
				if (!Func.equals(tasklist.get(i).getProcessInstanceId(), taskApproveDTO.getFlowProcessInstanceId())
					&& firstTask == null) {
					firstTask = tasklist.get(i).getBladeInsId();
				}
				if (Func.equals(tasklist.get(i).getProcessInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					taskIndex = i;
					break;
				}
			}
			if (tasklist.size() - 1 > taskIndex) {
				return tasklist.get(taskIndex + 1).getBladeInsId();
			} else {
				return firstTask;
			}

		} else if (Func.equals(NextQueryVO.TabNumEnum.AUDITED.getCode(), nextQueryVO.getTabNum())) {
			// 我已审批的列表-审批中的
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);

			// 查询创建人条件
			List<SimpleUserVO> userList = selectCreateUserList(nextQueryVO.getCreateUserName());
			MyLaunchPageQueryVO queryVO = BeanUtil.copy(nextQueryVO, MyLaunchPageQueryVO.class);

			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.WEB.getCode());
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());

			List<ProcessInstance> myList = processInstanceService.doneTaskPageNew(iPage, queryVO, userList);
			if (Func.isEmpty(myList)) {
				return null;
			}
			return checkNext(myList, taskApproveDTO.getFlowProcessInstanceId(), insIdList);
		} else if (Func.equals(NextQueryVO.TabNumEnum.CREATED.getCode(), nextQueryVO.getTabNum())) {
			// 我发起的
			IPage<ProcessInstanceVO> page = new Page<>(1, 10000);
			MyLaunchPageQueryVO queryVO = BeanUtil.copy(nextQueryVO, MyLaunchPageQueryVO.class);

			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));

			List<ProcessInstance> myList = processInstanceService.selectLaunchPage(page, queryVO);
			if (Func.isEmpty(myList)) {
				return null;
			}
			return checkNext(myList, taskApproveDTO.getFlowProcessInstanceId(), insIdList);
		}

		return null;
	}


	/**
	 * 组装下一条数据
	 *
	 * @param myList
	 * @param flowProcessInstanceId
	 * @return
	 */
	public Long checkNext(List<ProcessInstance> myList, String flowProcessInstanceId, List<Long> insIdList) {
		int nowIndex = -1;
		Long firstInsId = 0L;
		Long nextInsId = 0L;

		for (int i = 0; i < myList.size(); i++) {
			if (insIdList.contains(myList.get(i).getId()) && firstInsId.longValue() == 0
				&& !Func.equals(flowProcessInstanceId, myList.get(i).getFlowableInstanceId())) {
				firstInsId = myList.get(i).getId();
			}
			if (Func.equals(myList.get(i).getFlowableInstanceId(), flowProcessInstanceId)) {
				nowIndex = i;
			}
			if (nowIndex > -1 && i > nowIndex && insIdList.contains(myList.get(i).getId())) {
				nextInsId = myList.get(i).getId();
				break;
			}
		}

		//返回下一条
		if (nextInsId.longValue() > 0) {
			return nextInsId;
		}

		//返回列表从上往下的第一条
		if (firstInsId.longValue() > 0) {
			return firstInsId;
		}
		return null;
	}


	/**
	 * 查询列表的所有数据，获取第一条(排除当前条)
	 *
	 * @param taskApproveDTO
	 * @param nextQueryVO
	 * @param insIdList
	 * @return
	 */
	public Long selectNextNoQuery(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO, List<Long> insIdList) {

		if (Func.equals(NextQueryVO.TabNumEnum.UN_AUDIT.getCode(), nextQueryVO.getTabNum())) {
			// 需我审批的列表
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
			List<MyTaskVO> tasklist = processInstanceService.selectMyTaskPage(iPage, null, null, null, null);
			if (Func.isEmpty(tasklist)) {
				return null;
			}

			Long next = null;
			for (MyTaskVO task : tasklist) {
				if (insIdList.contains(task.getBladeInsId())
					&& !Func.equals(task.getProcessInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					next = task.getBladeInsId();
					break;
				}
			}

			return next;
		} else if (Func.equals(NextQueryVO.TabNumEnum.AUDITED.getCode(), nextQueryVO.getTabNum())) {
			// 我已审批的列表-审批中的
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
			// 查询创建人条件
			MyLaunchPageQueryVO queryVO = new MyLaunchPageQueryVO();
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.WEB.getCode());
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());

			List<ProcessInstance> myList = processInstanceService.doneTaskPageNew(iPage, queryVO, null);
			if (Func.isEmpty(myList)) {
				return null;
			}

			Long next = null;
			for (ProcessInstance process : myList) {
				if (insIdList.contains(process.getId())
					&& !Func.equals(process.getFlowableInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					next = process.getId();
					break;
				}
			}

			return next;
		} else if (Func.equals(NextQueryVO.TabNumEnum.CREATED.getCode(), nextQueryVO.getTabNum())) {
			// 我发起的
			IPage<ProcessInstanceVO> page = new Page<>(1, 10000);
			MyLaunchPageQueryVO queryVO = new MyLaunchPageQueryVO();
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));

			List<ProcessInstance> myList = processInstanceService.selectLaunchPage(page, queryVO);
			if (Func.isEmpty(myList)) {
				return null;
			}

			Long next = null;
			for (ProcessInstance process : myList) {
				if (insIdList.contains(process.getId())
					&& !Func.equals(process.getFlowableInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					next = process.getId();
					break;
				}
			}
			return next;
		}

		return null;
	}


	/**
	 * app查询自动下一条
	 *
	 * @param taskApproveDTO
	 * @param nextQueryVO
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public NextApproveVO selectAppNextApprove(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO) {
//        自动下一条
//        0.查询所有需我审批的流程ID备用
//        1.按条件查询当前列表所有审批中的数据,获取当前数据的下一条(需要我审批的数据),若没有获取到，则获取第一条
//        2.按条件没获取到下一条，则清空所有条件查询当前列表所有审批中的数据，获取第一条需要审批的数据(判断是否需要审批)
//        3.组装数据返回
		Long nextInsId = null;
		//查询所有需我审批的流程ID备用
		List<Long> insIdList = selectMyInsIdList();
		if (Func.isEmpty(insIdList)) {
			return selectNextWrapper(nextInsId);
		}

		//按条件查询当前列表所有审批中的数据,获取当前数据的下一条(需要我审批的数据),若没有获取到，则从第一条再获取一遍
		nextInsId = selectAppNextByQuery(taskApproveDTO, nextQueryVO, insIdList);
		if (Func.isNotEmpty(nextInsId)) {
			return selectNextWrapper(nextInsId);
		}

		//2.按条件没获取到下一条，则清空所有条件查询当前列表所有审批中的数据，获取第一条需要审批的数据(判断是否需要审批)
		nextInsId = selectAppNextNoQuery(taskApproveDTO, nextQueryVO, insIdList);

		//组装返回数据
		return selectNextWrapper(nextInsId);
	}


	/**
	 * 按条件查询app下一条
	 *
	 * @param taskApproveDTO
	 * @param nextQueryVO
	 * @param insIdList
	 * @return
	 */
	public Long selectAppNextByQuery(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO, List<Long> insIdList) {

		if (Func.equals(NextQueryVO.TabNumEnum.UN_AUDIT.getCode(), nextQueryVO.getTabNum())) {
			//需我审批的列表
			//==============================================
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
			//查询创建人条件
			List<SimpleUserVO> userList = selectCreateUserList(nextQueryVO.getCreateUserName());
			//查询需我审批列表
			ApprovePageQueryVO vo = BeanUtil.copy(nextQueryVO, ApprovePageQueryVO.class);
			List<MyTaskVO> tasklist = processInstanceService.selectPageByKeyWord(iPage, userList, nextQueryVO.getProcessName(), nextQueryVO.getDefinationId(), vo);
			//==============================================
			if (Func.isEmpty(tasklist)) {
				return null;
			}
			int taskIndex = 0;
			Long firstTask = null;
			for (int i = 0; i < tasklist.size(); i++) {
				if (!Func.equals(tasklist.get(i).getProcessInstanceId(), taskApproveDTO.getFlowProcessInstanceId())
					&& firstTask == null) {
				}
				if (Func.equals(tasklist.get(i).getProcessInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					taskIndex = i;
					break;
				}
			}
			if (tasklist.size() - 1 > taskIndex) {
			} else {
				return firstTask;
			}

		} else if (Func.equals(NextQueryVO.TabNumEnum.AUDITED.getCode(), nextQueryVO.getTabNum())) {
			//我已审批的列表-审批中的
			IPage<ProcessInstanceVO> page = new Page<>(1, 100000);
			//查询创建人条件
			List<SimpleUserVO> userList = selectCreateUserList(nextQueryVO.getCreateUserName());
			MyLaunchPageQueryVO queryVO = BeanUtil.copy(nextQueryVO, MyLaunchPageQueryVO.class);
			if (Func.isEmpty(queryVO)) {
				queryVO = new MyLaunchPageQueryVO();
			}
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.APP.getCode());
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			List<ProcessInstance> myList = processInstanceService.doneTaskPageNew(page, queryVO, userList);
			//=====================================
			if (Func.isEmpty(myList)) {
				return null;
			}
			return checkNext(myList, taskApproveDTO.getFlowProcessInstanceId(), insIdList);

		} else if (Func.equals(NextQueryVO.TabNumEnum.CREATED.getCode(), nextQueryVO.getTabNum())) {
			//我发起的
			IPage<ProcessInstanceVO> page = new Page<>(1, 100000);
			MyLaunchPageQueryVO queryVO = BeanUtil.copy(nextQueryVO, MyLaunchPageQueryVO.class);
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			List<ProcessInstance> myList = processInstanceService.selectLaunchPage(page, queryVO);
			if (Func.isEmpty(myList)) {
				return null;
			}
			return checkNext(myList, taskApproveDTO.getFlowProcessInstanceId(), insIdList);
		}

		return null;
	}


	/**
	 * 按无条件获取app下一条
	 *
	 * @param taskApproveDTO
	 * @param nextQueryVO
	 * @param insIdList
	 * @return
	 */
	private Long selectAppNextNoQuery(ProcessTaskApproveDTO taskApproveDTO, NextQueryVO nextQueryVO, List<Long> insIdList) {
		if (Func.equals(NextQueryVO.TabNumEnum.UN_AUDIT.getCode(), nextQueryVO.getTabNum())) {
			//需我审批的列表
			IPage<ProcessInstanceVO> iPage = new Page<>(1, 10000);
			List<MyTaskVO> tasklist = processInstanceService.selectPageByKeyWord(iPage, null, null, null, null);
			if (Func.isEmpty(tasklist)) {
				return null;
			}
			Long next = null;
			for (MyTaskVO task : tasklist) {
			}
			return next;
		} else if (Func.equals(NextQueryVO.TabNumEnum.AUDITED.getCode(), nextQueryVO.getTabNum())) {
			//我已审批的列表-审批中的
			IPage<ProcessInstanceVO> page = new Page<>(1, 100000);
			//查询创建人条件
			MyLaunchPageQueryVO queryVO = new MyLaunchPageQueryVO();
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			queryVO.setQueryClient(MyLaunchPageQueryVO.QueryClientEnum.APP.getCode());
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			List<ProcessInstance> myList = processInstanceService.doneTaskPageNew(page, queryVO, null);
			if (Func.isEmpty(myList)) {
				return null;
			}
			Long next = null;
			for (ProcessInstance process : myList) {
				if (insIdList.contains(process.getId())
					&& !Func.equals(process.getFlowableInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					next = process.getId();
					break;
				}
			}
			return next;
		} else if (Func.equals(NextQueryVO.TabNumEnum.CREATED.getCode(), nextQueryVO.getTabNum())) {
			//我发起的
			IPage<ProcessInstanceVO> page = new Page<>(1, 10000);
			MyLaunchPageQueryVO queryVO = new MyLaunchPageQueryVO();
			queryVO.setStatus(ProcessInstance.StatusEnum.ING.getCode());
			queryVO.setCreateUser(AuthUtil.getUserId());
			queryVO.setOrgId(Func.toLong(AuthUtil.getDeptId()));
			List<ProcessInstance> myList = processInstanceService.selectLaunchPage(page, queryVO);
			if (Func.isEmpty(myList)) {
				return null;
			}

			Long next = null;
			for (ProcessInstance process : myList) {
				if (insIdList.contains(process.getId())
					&& !Func.equals(process.getFlowableInstanceId(), taskApproveDTO.getFlowProcessInstanceId())) {
					next = process.getId();
					break;
				}
			}
			return next;
		}

		return null;
	}

}
