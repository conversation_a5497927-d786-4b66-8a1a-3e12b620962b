package com.snszyk.flow.process.controller;

import com.snszyk.core.tool.api.R;
import com.snszyk.flow.process.manage.ProcessInstanceInformManage;
import com.snszyk.flow.process.vo.CornerMarkVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/pc/process-instance-inform")
public class ProcessInstanceInformPcController {
	private ProcessInstanceInformManage processInstanceInformManage;

	/**
	 * 审核数据数量
	 *
	 * @return wangbin
	 */
	@GetMapping("/corner-mark")
	public R<CornerMarkVO> cornerMark() {
		CornerMarkVO cornerMarkVO = processInstanceInformManage.cornerMarknew();

		cornerMarkVO.setTotalNum(cornerMarkVO.getTodoNum() + cornerMarkVO.getInformUnReadNum());
		return R.data(cornerMarkVO);
	}
}
