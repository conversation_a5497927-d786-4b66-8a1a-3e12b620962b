package com.snszyk.flow.process.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.flow.process.entity.ProcessDefinitionCommon;
import com.snszyk.flow.process.mapper.ProcessDefinitionCommonMapper;
import com.snszyk.flow.process.service.ProcessDefinitionCommonService;
import com.snszyk.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
/**
 * <AUTHOR>
@AllArgsConstructor
@Slf4j
@Service
public class ProcessDefinitionCommonServiceImpl extends BaseServiceImpl<ProcessDefinitionCommonMapper, ProcessDefinitionCommon> implements ProcessDefinitionCommonService {
}
