<!--
 * @description:
 * @param:
 * @author: Fei
 * @return:
 * @Date: 2021-01-29 11:08:21
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>审批表单</title>
    <style>
        body {
            font-family: Microsoft YaHei;
            line-height: 22px;
            font-size: smaller;
        }
        .td{}
        .table{}
        .red {
            color: red;
        }
        .print-page-box{
            padding: 10px;
        }
        .print-page-title{
            margin-top: 0px;
            text-align: center;
        }
        .print-page-subtitle{
            text-align: left;

            padding: 10px 0 10px 0;
        }

        .print-page-subtitle >span{
            display: inline-block;
            width: 47%;
            padding: 0 1%;
            font-size: 18px;
        }
        .print-page-subtitle-company{
            width:33%;
            word-break:break-all;
        }
        .print-page-subtitle-push-data{
            text-align: right;
            float: right;
            /*padding-left: 15px;*/
        }
        .print-page-table{
            border-collapse: collapse;
            width: 100%;
            line-height: 22px;
            border:solid black;
            border-width:1px;
        }
        .print-page-table td{
            height: 30px;
            border-color: black;
            text-indent: 10px;
            border:solid black;
            /* border-width:0px 1px 1px 0px; */
            border-width:1px;
            /*padding:10px 0px;*/
            word-break:break-all;
        }
        .print-page-table-title{
            width: 25%;
            line-height: 30px;
        }
        .print-page-table-in{
            border-collapse: collapse;
            width: 100%;
            line-height: 22px;

            border-width:0;
        }
        .print-page-table-in td{
            height: 30px;
            border-color: black;
            text-indent: 10px;
            border:solid black;
            border-width:0px;
            /*padding:10px 0px;*/
            padding: 0
        }
        .print-page-table-control{
            width: 98%;
            margin: 1%;
        }
        .print-page-table-chaosong{
            border: none;
        }
        .print-page-table-chaosong td{
            border: none;
        }
    </style>
</head>
<body>
<div class="print-page-box">
    <h1 class="print-page-title">${data.title}</h1>
    <div class="print-page-subtitle">
        <span class="print-page-subtitle-company">${data.applyUserOrgName}</span>
        <span class="print-page-subtitle-push-data">申请日期:${data.applyDate}</span>
    </div>
    <table class="print-page-table">
        <tr>
            <td class="print-page-table-title" valign="top">申请人</td>
            <td>${data.applyUserName}</td>
        </tr>
        <tr>
            <td class="print-page-table-title" valign="top">申请人部门</td>
            <td>${data.applyUserDeptName}</td>
        </tr>

        <!-- 表单信息  begin -->
        <% for(item in data.fromKvs){ %>
            <% if(item.type=="tips"){ %>
                <tr>
                    <td colspan="2">${item.lable}</td>
                </tr>
            <% } else if(item.type=="desc"){ %>
                <tr>
                    <td colspan="2">${nvl(item.value,"--")}</td>
                </tr>
            <% } else if(item.type=="form"){ %>
                <tr>
                    <td colspan="2" style="padding: 0 10px 10px 10px">
                        <% for(kv in item.child){ %>

                            <div style="padding-top: 10px">${item.lable}${kvLP.size>1?"-"}${kvLP.size>1?kvLP.index}</div>
                            <table border="1" class="print-page-table" >
                            <% for(cd in kv){ %>
                                <tr>
                                    <td class="print-page-table-title" valign="top">${cd.lable}</td>
                                    <td>${nvl(cd.value,"--")}</td>
                                </tr>
                            <% } %>
                            </table>
                        <% } %>
                    </td>
                </tr>
            <% } else { %>
                <tr>
                    <td class="print-page-table-title" valign="top">${item.lable}</td>
                    <td>${nvl(item.value,"--")}</td>
                </tr>
            <% } %>
        <% } %>

        <!-- 表单信息  end -->

        <!-- 流程信息  begin -->
        <tr>
            <td rowspan="${data.comments.~size+1}" class="print-page-table-title" valign="top">审批流程</td>
<!--            <td rowspan="2" class="print-page-table-title" valign="top">审批流程</td>-->
        </tr>
        <% for(item in data.comments){ %>
        <tr>
            <td style="padding: 0">
                <table class="print-page-table-in">
                    <tr>
                        <td>${item.opUserName}&nbsp;&nbsp;${item.opStatusName} ${item.otherName}</td>
                        <td style="text-align: right;padding-right: 10px;width: 160px">${item.opDate}</td>
                    </tr>
                    <% if(!isEmpty(item.comments)){ %>
                    <% if(!isEmpty(item.commentFlag)&&item.commentFlag==0&&item.nodeType==7){ %>
                    <tr>
                        <td colspan="2"><del>${item.comments}</del></td>
                    </tr>
                    <% }else{ %>
                    <tr>
                        <td colspan="2">${item.comments}</td>
                    </tr>
                    <% } %>
                    <% } %>
                    <% if(!isEmpty(item.commentFlag)&&item.commentFlag==0&&item.nodeType==7){ %>
                    <% for(ad in item.additional){ %>
                    <tr>
                        <td colspan="2"><del>${ad}</del></td>
                    </tr>
                    <% } %>
                    <% }else{ %>
                    <% for(ad in item.additional){ %>
                    <tr>
                         <td colspan="2">${ad}</td>
                    </tr>
                    <% } %>
                    <% } %>

                </table>
            </td>
        </tr>
        <% } %>
        <!-- 流程信息  end -->
    </table>

</div>
</body>
</html>