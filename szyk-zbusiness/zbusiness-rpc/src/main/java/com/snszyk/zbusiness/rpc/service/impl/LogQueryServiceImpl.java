package com.snszyk.zbusiness.rpc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.zbusiness.rpc.constant.databocean.DoConstant;
import com.snszyk.zbusiness.rpc.service.ILogQueryService;
import com.snszyk.zbusiness.rpc.util.DataOceanUtil;
import com.snszyk.zbusiness.rpc.util.HttpclientUtils;
import com.snszyk.zbusiness.rpc.vo.LoginLogVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @atuthor
 * @date 2023-05-06
 * @apiNote
 */
@Service
@Slf4j
public class LogQueryServiceImpl implements ILogQueryService {


	@Value("${do.client-id}")
	private String clientId;
	@Value("${do.client-secret}")
	private String clientSecret;
	@Value("${do.token-url}")
	private String tokenUrl;
	@Value("${do.data-url}")
	private String dataUrl;
	private static final Integer SUCCESS_CODE = 200;
	private final SzykRedis szykRedis;

	public LogQueryServiceImpl(SzykRedis szykRedis) {
		this.szykRedis = szykRedis;
	}


	@Override
	public IPage<JSONObject> loginLog(LoginLogVo vo) {
		IPage<JSONObject> page = new Page<>();
		String token = getToken();
		String urlStr = dataUrl + "?version=" + DoConstant.VERSION
			+ "&client_id=" + clientId + "&access_token=" + token;
		//定义发送数据
		JSONObject param = new JSONObject();
		param.put("ResourceName", DoConstant.USER_LOGIN_LOG);
		param.put("pageNumber", vo.getPageNumber());
		param.put("pageSize", vo.getPageSize());
		//拼接条件
		String condition = " 1 = 1 ";

		if (StringUtils.isNotBlank(vo.getOrgName())) {
			condition = condition + " and org_name like '%" + vo.getOrgName() + "%'";
		}
		if (StringUtils.isNotBlank(vo.getUserType())) {
			condition = condition + " and user_type like '%" + vo.getUserType() + "%'";
		}
		if (StringUtils.isNotBlank(vo.getUserName())) {
			condition = condition + " and user_name like '%" + vo.getUserName() + "%'";
		}
		if (StringUtils.isNotBlank(vo.getUserCode())) {
			condition = condition + " and user_code like '%" + vo.getUserCode() + "%'";
		}
		if(StringUtils.isNotBlank(vo.getLoginStartTime()) && StringUtils.isNotBlank(vo.getLoginEndTime())
		&& vo.getLoginStartTime().equals(vo.getLoginEndTime())){
			condition = condition + " and login_time like '" + vo.getLoginStartTime() + "%'";
		}else if (StringUtils.isNotBlank(vo.getLoginStartTime())) {
			condition = condition + " and login_time >= '" + vo.getLoginStartTime() + "'" ;
		}else if (StringUtils.isNotBlank(vo.getLoginEndTime())) {
			condition = condition + " and login_time <= '" + vo.getLoginEndTime() + "'" ;
		}

		String bmdmCondition = "";
		if (CollectionUtils.isNotEmpty(vo.getOrgCode())) {
			for (String organCode : vo.getOrgCode()) {
				bmdmCondition = bmdmCondition + " org_code= '" + organCode + "' or ";
			}

		}
		if (StringUtils.isNotBlank(bmdmCondition)) {
			// 检查并移除末尾的 " or "
			if (bmdmCondition.endsWith(" or ")) {
				bmdmCondition = bmdmCondition.substring(0, bmdmCondition.length() - 4);
			}
			condition = condition + " and ( " + bmdmCondition + " ) ";
		}
		param.put("Condition", condition);

		//增加排序
		JSONArray orderCondition = new JSONArray();
		JSONObject bureauSort = new JSONObject();
		bureauSort.put("Name","bureau_sort");
		bureauSort.put("Order","asc");
		bureauSort.put("Nulls","first");
		orderCondition.add(bureauSort);
		JSONObject orgSort = new JSONObject();
		orgSort.put("Name","org_sort");
		orgSort.put("Order","asc");
		orgSort.put("Nulls","first");
		orderCondition.add(orgSort);
		JSONObject loginTime = new JSONObject();
		loginTime.put("Name","login_time");
		loginTime.put("Order","desc");
		orderCondition.add(loginTime);

		param.put("OrderCondition",orderCondition);

		String body = "";
		try {
			body = HttpclientUtils.doPostJson(urlStr, param.toJSONString());
			//log.error("=============接口返参：" + body);
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (StringUtils.isNotBlank(body)) {
			JSONObject result = JSON.parseObject(body);
			Integer code = result.getInteger("code");
			if (!SUCCESS_CODE.equals(code)) {
				//如果返回报错
				throw new ServiceException(result.getString("message"));
			}
			JSONArray jsonArray = result.getJSONArray("result");
			if(null != jsonArray && jsonArray.size() > 0){
				List<JSONObject> list = result.getJSONArray("result").toJavaObject(List.class);
				page.setRecords(list);
			}
			page.setPages(result.getInteger("totalPage"));
			page.setCurrent(vo.getPageNumber());
			page.setSize(vo.getPageSize());
			page.setTotal(result.getInteger("totalRows"));
		}
		return page;
	}

	private String getToken() {
		//先获取缓存中的token值
		String token = StrUtil.nullToEmpty((String) szykRedis.get(DoConstant.TOKEN_CACHE));
		if (StringUtils.isEmpty(token)) {
			token = DataOceanUtil.getToken(tokenUrl, clientId, clientSecret);
			if (StringUtils.isNotBlank(token)) {
				// Token存入缓存中
				szykRedis.setEx(DoConstant.TOKEN_CACHE, token, Long.valueOf(DoConstant.EXPIRES_IN));
			} else {
				getToken();
			}

		}
		return token;
	}
}
