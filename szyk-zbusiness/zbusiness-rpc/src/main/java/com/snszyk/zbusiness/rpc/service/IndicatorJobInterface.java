package com.snszyk.zbusiness.rpc.service;

public interface IndicatorJobInterface {

	//登录率
	void startLoginRateHandle(String dateStr);

	//督办次数
//	void startSuperviseHandle(String dateStr);

	void startTaskBackRateHandle(String dateStr);

	//用户活跃率
	void startUserActiveRateHandle(String dateStr);

	//任务完成率
	void startTaskFinishRateHandle(String dateStr);

	//项目审批完成率
	void startProjectApproveFinishRateHandle(String dateStr);

	//生成各组织各系统的得分
	void handleOrgScore(String dateStr);

	//生成各组织报表
	void handleReportOrg(String dateStr);

	//生成各系统报表
	void handleReportSystem(String dateStr);

	//清空本月的指标数据
	void clearCurMonthIndicatorData(String dateStr);
}
