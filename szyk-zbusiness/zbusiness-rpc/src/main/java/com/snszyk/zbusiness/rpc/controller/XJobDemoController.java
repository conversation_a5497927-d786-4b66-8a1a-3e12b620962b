/**
 * 文件名：StaticsReportController.java
 * 版权： Copyright 2002-2007 QLYS. All Rights Reserved.
 * 描述：
 * 修改人：杜洋洋
 * 修改时间：2021年04月30日
 * 修改内容：新增
 **/
package com.snszyk.zbusiness.rpc.controller;


import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.rpc.entity.XxlJobInfo;
import com.snszyk.zbusiness.rpc.service.IXJobRPCApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "xjob测试demo")
@RestController
@AllArgsConstructor
@RequestMapping("job")
public class XJobDemoController extends BaseCrudController {


    private final IXJobRPCApi api;

    @ApiOperation("新增任务")
    @PostMapping("add")
    public R<Integer> addJob() {
        XxlJobInfo info = new XxlJobInfo()
				//任务描述
                .setJobDesc("山能集团互联网锁定任务20231230")
				//任务作者
                .setAuthor("侯双双")
				//任务执行时间,CRON表达式
                .setScheduleConf("0/30 * * * * ?")
				//任务执行类，具体执行业务类的类名
                .setExecutorHandler("HelloWorldJobHandler")
				//任务参数，任务执行时使用的参数
                .setExecutorParam("112.asdf,asdf;axcz");

		return R.data(api.addAndStartJob(info,123l,"NET_LOCK"));

    }

    @ApiOperation("开始任务")
    @PostMapping("start")
    public R<String> startJob(@ApiParam(value = "任务id")  @RequestParam("id") Integer id) {

        return R.data(api.startJob(id));
    }
    @ApiOperation("暂停任务")
    @PostMapping("stop")
    public R<String> stopJob(@ApiParam(value = "任务id")  @RequestParam("id") Integer id) {

        return R.data(api.stopJob(id));
    }
    @ApiOperation("删除任务")
    @PostMapping("remove")
    public R<Boolean> removeJob(@ApiParam(value = "任务id")  @RequestParam("id") Integer id) {

        return R.data(api.removeJob(id));
    }

	@ApiOperation("根据业务删除任务")
	@PostMapping("removeByBusiness")
	public R<Boolean> removeByBusiness(@ApiParam(value = "业务id")  @RequestParam("id") Long businessId, @ApiParam(value = "业务类型")  @RequestParam("businessType") String businessType) {

		return R.data(api.removeJobByBusinessId(businessId, businessType));
	}

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
