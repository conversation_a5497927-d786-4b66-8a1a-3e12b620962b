package com.snszyk.zbusiness.rpc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.system.entity.SzykDeptOb;
import com.snszyk.zbusiness.person.dto.PersonDto;
import com.snszyk.zbusiness.person.dto.PersonalBaseDto;
import com.snszyk.zbusiness.rpc.dto.AssetSyncResultDto;
import com.snszyk.zbusiness.rpc.vo.PersonConditionVo;
import com.snszyk.zbusiness.stat.vo.IndicatorDatasetVo;

import java.util.List;
import java.util.Map;

/**
 * @atuthor
 * @date 2023-05-06
 * @apiNote
 */
public interface IDataOceanService {

	List<Map<String,String>> getSupplier(Integer pageNumber);

	List<Map<String,String>> getPerson(Integer pageNumber);

	Boolean syncSupplier();

	Boolean syncPerson();

    Boolean initSupplier(Integer pageNum);

	Boolean initPerson(Integer pageNum);

	IPage<PersonalBaseDto> conditionPerson(PersonConditionVo vo,List<String> users);


    Boolean syncOrg(String parentCode);

	List<SzykDeptOb> initOrg(String parentCode, Integer pageNum);

	Boolean syncDeptOb();

	Boolean syncDept(String s,Long parentId);
	Boolean doDept();

	Boolean syncOrgJob();

    Boolean syncIamOrg();

	Boolean syncMdmOrg();

	Boolean syncMdmOrgJob();

	PersonalBaseDto queryByJobNo(String jobNo);

	Boolean syncOceanPerson();

	Boolean updateMdmPersonOrg(Long orgId);

	Boolean aqgkUserIndicator(String systemNo,String dateStr);

	Boolean aqgkIndicator(String systemNo, String dateStr);

	/**
	 * 获取实物资产数据
	 * @param pageNumber 页码
	 * @return 实物资产数据列表
	 */
	List<Map<String,String>> getAssetArchive(Integer pageNumber);

	/**
	 * 获取实物资产数据（根据更新时间）
	 * @param pageNumber 页码
	 * @param updateTime 更新时间
	 * @return 实物资产数据列表
	 */
	List<Map<String,String>> getAssetArchiveByUpdateTime(Integer pageNumber, String updateTime);

	/**
	 * 同步实物资产数据到设备台账（定时任务）
	 * @return 执行结果
	 */
	AssetSyncResultDto syncAssetArchiveJob();

	/**
	 * 同步实物资产数据到设备台账（手动更新）
	 * @return 执行结果
	 */
	AssetSyncResultDto syncAssetArchiveManual();
}
