package com.snszyk.zbusiness.rpc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实物资产同步结果DTO
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AssetSyncResultDto", description = "实物资产同步结果")
public class AssetSyncResultDto {

    /**
     * 新增数据数量
     */
    @ApiModelProperty(value = "新增数据数量")
    private int newCount;

    /**
     * 更新数据数量
     */
    @ApiModelProperty(value = "更新数据数量")
    private int updateCount;

    /**
     * 删除数据数量
     */
    @ApiModelProperty(value = "删除数据数量")
    private int deleteCount;

    /**
     * 跳过数据数量
     */
    @ApiModelProperty(value = "跳过数据数量")
    private int skipCount;

    /**
     * 总处理数据数量
     */
    @ApiModelProperty(value = "总处理数据数量")
    private int totalCount;

    /**
     * 成功处理数量
     */
    @ApiModelProperty(value = "成功处理数量")
    private int successCount;

    /**
     * 失败处理数量
     */
    @ApiModelProperty(value = "失败处理数量")
    private int failCount;
}
