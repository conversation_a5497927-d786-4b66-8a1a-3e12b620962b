package com.snszyk.zbusiness.rpc.dto;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @atuthor
 * @date 2023-05-22
 * @apiNote
 */
@Data
@ToString
@Accessors(chain = true)
@ApiModel(value = "PersonConditionDto", description = "人员查")
public class PersonConditionDto extends BaseCrudSlimVo {

	@ApiModelProperty("人员姓名")
	private String personalName;

	@ApiModelProperty("部门姓名")
	private String departName;

}
