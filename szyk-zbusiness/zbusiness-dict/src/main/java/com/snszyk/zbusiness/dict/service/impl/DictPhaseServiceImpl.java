/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.entity.DictPhase;
import com.snszyk.zbusiness.dict.mapper.DictPhaseMapper;
import com.snszyk.zbusiness.dict.service.IDictPhaseService;
import com.snszyk.zbusiness.dict.vo.DictPhaseVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * DictPhaseServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictPhaseServiceImpl extends BaseCrudServiceImpl<DictPhaseMapper, DictPhase, DictPhaseDto, DictPhaseVo> implements IDictPhaseService {

	@Override
	public List<DictPhaseDto> listByDictValue(String dictValue) {
		LambdaQueryWrapper<DictPhase> queryWrapper = Wrappers.<DictPhase>query().lambda()
			.eq(StringUtils.isNotEmpty(dictValue), DictPhase::getDictValue, dictValue);

		List<DictPhase> list = baseMapper.selectList(queryWrapper);


		return Objects.requireNonNull(BeanUtil.copy(list, DictPhaseDto.class));
	}

	@Override
	public List<DictPhaseDto> listByStatusOrValue(String dictValue, String dictStatus, String necessaryType) {
		LambdaQueryWrapper<DictPhase> queryWrapper = Wrappers.<DictPhase>query().lambda()
			.like(StringUtils.isNotEmpty(dictValue), DictPhase::getDictValue, dictValue)
			.eq(StringUtils.isNotEmpty(dictStatus), DictPhase::getDictStatus, dictStatus)
			.eq(StringUtils.isNotEmpty(necessaryType), DictPhase::getNecessaryType, necessaryType)
			.orderByAsc(DictPhase::getSort)
			.orderByDesc(DictPhase::getCreateTime);

		List<DictPhase> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictPhaseDto.class));
	}

	@Override
	public List<DictPhaseDto> listAll() {
		List<DictPhase> list = super.list();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return Objects.requireNonNull(BeanUtil.copy(list, DictPhaseDto.class));
	}

	@Override
	public boolean updateByStatus(Long id, String dictStatus) {//
		LambdaUpdateWrapper<DictPhase> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(DictPhase::getDictStatus, dictStatus);
		updateWrapper.eq(DictPhase::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public boolean updateByType(Long id, String necessaryType) {
		LambdaUpdateWrapper<DictPhase> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(DictPhase::getNecessaryType, necessaryType);
		updateWrapper.eq(DictPhase::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public DictPhaseDto update(DictPhaseVo vo) {
		DictPhase entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(true));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, DictPhaseDto.class);
		} else {
			return null;
		}
	}
}
