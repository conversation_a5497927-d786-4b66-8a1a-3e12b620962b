package com.snszyk.zbusiness.dict.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.dto.DictDeleteDto;
import com.snszyk.zbusiness.dict.dto.DictMajorDto;
import com.snszyk.zbusiness.dict.enums.DictCodeEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.dict.service.IDictMajorService;
import com.snszyk.zbusiness.dict.util.ZeroPaddingUtil;
import com.snszyk.zbusiness.dict.vo.DictMajorDeleteVo;
import com.snszyk.zbusiness.dict.vo.DictMajorPageVo;
import com.snszyk.zbusiness.dict.vo.DictMajorStatusVo;
import com.snszyk.zbusiness.dict.vo.DictMajorVo;
import com.snszyk.zbusiness.project.service.IPlExternalReferenceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DictMajorService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictMajorService extends BaseCrudLogicService<DictMajorDto, DictMajorVo> {

	private final IDictMajorService dictMajorService;

	private final IDictBizService dictBizService;

	@Resource
	private ZeroPaddingUtil zeroPaddingUtil;

	@Resource
	private IPlExternalReferenceService plExternalReferenceService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.dictMajorService;
	}

	@Transactional(rollbackFor = Exception.class)
	public DictMajorDto saveOrUpdate(DictMajorVo vo) {

		DictMajorDto dto = new DictMajorDto();

		vo.setDictValue(vo.getDictValue().trim());
		vo.setAbbreviation(vo.getAbbreviation().trim());

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<DictMajorDto> list = dictMajorService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.MAJOR_NAME_DATA_REPEAT.getMessage());
			}

			List<DictMajorDto> abbreviationList = dictMajorService.listByAbbreviation(vo.getAbbreviation());
			if (!CollectionUtils.isEmpty(abbreviationList)) {
				throw new ServiceException(ExceptionEnum.MAJOR_ABBREVIATION_DATA_REPEAT.getMessage());
			}

			//vo.setDictKey(zeroPaddingUtil.majorKey());
			//改为时间戳来保证key唯一
			vo.setDictKey(DictEnum.SC.getCode() + String.valueOf(System.currentTimeMillis()));

			dto = dictMajorService.save(vo);
		} else {
			// 更新
			List<DictMajorDto> list = dictMajorService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, Long> map = list.stream().collect(Collectors.toMap(DictMajorDto::getDictValue, DictMajorDto::getId));
				if (map.containsKey(vo.getDictValue()) && !map.get(vo.getDictValue()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.MAJOR_NAME_DATA_REPEAT.getMessage());
				}
			}

			List<DictMajorDto> abbreviationList = dictMajorService.listByAbbreviation(vo.getAbbreviation());
			if (!CollectionUtils.isEmpty(abbreviationList)) {
				Map<String, Long> map = abbreviationList.stream().collect(Collectors.toMap(DictMajorDto::getAbbreviation, DictMajorDto::getId));
				if (map.containsKey(vo.getAbbreviation()) && !map.get(vo.getAbbreviation()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.MAJOR_ABBREVIATION_DATA_REPEAT.getMessage());
				}
			}

			dto = dictMajorService.update(vo);
		}

		return dto;
	}

	public IPage<DictMajorDto> page(DictMajorPageVo vo) {

		//只有集团的可以编辑
		boolean  editFlag=false;
		Dept currentUnit = DeptScopeUtil.getLoginDept();
		if(currentUnit.getParentId()==0l){
			editFlag=true;
		}
		IPage<DictMajorDto> page = dictMajorService.pageList(vo);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		for (DictMajorDto dictMajorDto : page.getRecords()) {
			String dictValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dictMajorDto.getDictStatus());
			dictMajorDto.setDictStatusName(dictValue);
			dictMajorDto.setEditFlag(editFlag);
		}

		return page;
	}

	public List<DictMajorDto> list(String dictStatus) {

		return dictMajorService.listByDictStatus(dictStatus);
	}

	public DictMajorDto detail(Long id) {
		DictMajorDto result = dictMajorService.fetchById(id);

		String dictValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), result.getDictStatus());
		result.setDictStatusName(dictValue);

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(DictMajorStatusVo vo) {

		return dictMajorService.updateByStatus(vo.getId(), vo.getDictStatus());
	}

	@Transactional(rollbackFor = Exception.class)
	public List<DictDeleteDto> delete(DictMajorDeleteVo vo) {
		List<DictDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			DictDeleteDto dto = new DictDeleteDto();

			DictMajorDto dictMajorDto = dictMajorService.fetchById(id);
			dto.setDictValue(dictMajorDto.getDictValue());

			// 校验是否引用
			Boolean check = plExternalReferenceService.isReference(DictEnum.SC.getCode(), dictMajorDto.getDictKey());
			if (check) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.PROJECT_REFERENCE_DATA.getMessage());
				result.add(dto);
				continue;
			}

			Boolean flag = dictMajorService.deleteById(id);
			dto.setResult(flag);

			result.add(dto);
		}

		return result;
	}

}
