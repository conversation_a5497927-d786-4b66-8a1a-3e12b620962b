/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.dict.dto.ClassifyDetailDto;
import com.snszyk.zbusiness.dict.dto.ClassifyDto;
import com.snszyk.zbusiness.dict.dto.ClassifyTreeDto;
import com.snszyk.zbusiness.dict.service.logic.ClassifyLogicService;
import com.snszyk.zbusiness.dict.vo.ClassifyPageVo;
import com.snszyk.zbusiness.dict.vo.ClassifySaveVo;
import com.snszyk.zbusiness.dict.vo.ClassifyStatusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 知识分类 控制器
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-dict/classify")
@Api(value = "知识分类", tags = "知识分类接口")
public class ClassifyController extends BaseCrudController {


	private final ClassifyLogicService classifyLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return classifyLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "ClassifySaveVo")
	public R<BaseCrudDto> save(@RequestBody ClassifySaveVo v) {
		BaseCrudDto baseCrudDto = classifyLogicService.save(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@PostMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "ClassifyPageVo")
	public R<IPage<ClassifyDto>> page(@RequestBody ClassifyPageVo v) {
		return R.data(classifyLogicService.pageList(v));
	}

	/**
	 * 分页
	 */
	@PostMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表查询", notes = "ClassifyPageVo")
	public R<List<ClassifyDto>> list(@RequestBody ClassifyPageVo v) {
		return R.data(classifyLogicService.list(v));
	}

	/**
	 * 分页
	 */
	@PostMapping("tree/list")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "列表查询（树）", notes = "ClassifyPageVo")
	public R<List<ClassifyTreeDto>> treeList() {
		return R.data(classifyLogicService.treeList());
	}

	/**
	 * 根据ID获取数据
	 */
	@Override
	@GetMapping("/fetchById/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<ClassifyDetailDto> fetchById(@PathVariable Long id) {
		ClassifyDetailDto baseCrudDto = classifyLogicService.fetchById(id);
		return R.data(baseCrudDto);
	}

	/**
	 * 删除
	 */
	@DeleteMapping("/deleteById")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "id")
	public R<Boolean> deleteByIds(Long id) {
		Boolean result = classifyLogicService.deleteById(id);
		return R.data(result);
	}


	/**
	 * 停用/启用
	 */
	@PutMapping("/status")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "启用/停用", notes = "enableType")
	public R<BaseCrudDto> status(@RequestBody ClassifyStatusVo vo) {
		return R.data(classifyLogicService.status(vo));
	}

	/**
	 * 排序
	 *
	 * @param idList
	 * @return
	 */
	@PostMapping("/sort")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "排序", notes = "排序")
	@ApiImplicitParam(name = "idList", value = "所有的id集合（按顺序）", required = true)
	public R<Boolean> sort(@RequestBody List<Long> idList) {
		return R.data(classifyLogicService.sort(idList));
	}

}
