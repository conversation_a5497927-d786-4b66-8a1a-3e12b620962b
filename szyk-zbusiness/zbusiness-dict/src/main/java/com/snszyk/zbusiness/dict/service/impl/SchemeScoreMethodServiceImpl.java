/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.SchemeScoreMethodDto;
import com.snszyk.zbusiness.dict.entity.SchemeScoreMethod;
import com.snszyk.zbusiness.dict.mapper.SchemeScoreMethodMapper;
import com.snszyk.zbusiness.dict.service.ISchemeScoreMethodService;
import com.snszyk.zbusiness.dict.vo.SchemeScoreMethodVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * SchemeScoreMethodServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class SchemeScoreMethodServiceImpl extends BaseCrudServiceImpl<SchemeScoreMethodMapper, SchemeScoreMethod, SchemeScoreMethodDto, SchemeScoreMethodVo> implements ISchemeScoreMethodService {

	@Override
	public List<SchemeScoreMethodDto> listBySchemeEvaluateId(Long schemeEvaluateId) {
		LambdaQueryWrapper<SchemeScoreMethod> queryWrapper = Wrappers.<SchemeScoreMethod>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeEvaluateId), SchemeScoreMethod::getSchemeEvaluateId, schemeEvaluateId)
			.orderByAsc(SchemeScoreMethod::getCreateTime);

		List<SchemeScoreMethod> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeScoreMethodDto.class));
	}

	@Override
	public List<SchemeScoreMethodDto> listBySchemeId(Long schemeId) {
		LambdaQueryWrapper<SchemeScoreMethod> queryWrapper = Wrappers.<SchemeScoreMethod>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), SchemeScoreMethod::getSchemeId, schemeId)
			.orderByAsc(SchemeScoreMethod::getId);

		List<SchemeScoreMethod> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeScoreMethodDto.class));
	}

	@Override
	public List<SchemeScoreMethodDto> listAll() {
		List<SchemeScoreMethod> list = super.list();

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeScoreMethodDto.class));
	}

	@Override
	public int deleteBySchemeId(Long schemeId) {
		LambdaQueryWrapper<SchemeScoreMethod> queryWrapper = Wrappers.<SchemeScoreMethod>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), SchemeScoreMethod::getSchemeId, schemeId);

		return baseMapper.delete(queryWrapper);
	}
}
