/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import com.snszyk.zbusiness.dict.entity.DictLabel;
import com.snszyk.zbusiness.dict.mapper.DictLabelMapper;
import com.snszyk.zbusiness.dict.service.IDictLabelService;
import com.snszyk.zbusiness.dict.vo.DictLabelPageVo;
import com.snszyk.zbusiness.dict.vo.DictLabelVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * DictLabelServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictLabelServiceImpl extends BaseCrudServiceImpl<DictLabelMapper, DictLabel, DictLabelDto, DictLabelVo> implements IDictLabelService {

	@Override
	public List<DictLabelDto> listByClassifyId(Long classifyId) {
		LambdaQueryWrapper<DictLabel> queryWrapper = Wrappers.<DictLabel>query().lambda()
			.eq(ObjectUtils.isNotEmpty(classifyId), DictLabel::getClassifyId, classifyId);

		List<DictLabel> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelDto.class));
	}

	@Override
	public List<DictLabelDto> listByDictValue(String dictValue) {
		LambdaQueryWrapper<DictLabel> queryWrapper = Wrappers.<DictLabel>query().lambda()
			.eq(ObjectUtils.isNotEmpty(dictValue), DictLabel::getDictValue, dictValue);

		List<DictLabel> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelDto.class));
	}

	@Override
	public List<DictLabelDto> listByDictStatus(String dictStatus) {
		LambdaQueryWrapper<DictLabel> queryWrapper = Wrappers.<DictLabel>query().lambda()
			.eq(StringUtils.isNotEmpty(dictStatus), DictLabel::getDictStatus, dictStatus)
			.orderByDesc(DictLabel::getCreateTime);

		List<DictLabel> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelDto.class));
	}

	@Override
	public List<DictLabelDto> listByDictStatus(String dictValue, String dictStatus) {
		LambdaQueryWrapper<DictLabel> queryWrapper = Wrappers.<DictLabel>query().lambda()
			.like(StringUtils.isNotEmpty(dictValue), DictLabel::getDictValue, dictValue)
			.eq(StringUtils.isNotEmpty(dictStatus), DictLabel::getDictStatus, dictStatus)
			.orderByDesc(DictLabel::getCreateTime);

		List<DictLabel> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelDto.class));
	}

	@Override
	public IPage<DictLabelDto> pageList(DictLabelPageVo vo, List<Long> classifyIdList) {
		LambdaQueryWrapper<DictLabel> queryWrapper = Wrappers.<DictLabel>query().lambda()
			.in(ObjectUtils.isNotEmpty(classifyIdList), DictLabel::getClassifyId, classifyIdList)
			.like(StringUtils.isNotEmpty(vo.getDictValue()), DictLabel::getDictValue, vo.getDictValue())
			.eq(StringUtils.isNotEmpty(vo.getDictStatus()), DictLabel::getDictStatus, vo.getDictStatus())
			.orderByDesc(DictLabel::getCreateTime);

		Page<DictLabel> page = new Page(vo.getCurrent(), vo.getSize());
		IPage<DictLabel> labelPage = baseMapper.selectPage(page, queryWrapper);

		return labelPage.convert(DictLabel -> BeanUtil.copyProperties(DictLabel, DictLabelDto.class));
	}

	@Override
	public boolean updateByStatus(Long id, String dictStatus) {
		LambdaUpdateWrapper<DictLabel> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(DictLabel::getDictStatus, dictStatus);
		updateWrapper.eq(DictLabel::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public DictLabelDto update(DictLabelVo vo) {
		DictLabel entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(true));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, DictLabelDto.class);
		} else {
			return null;
		}
	}
}
