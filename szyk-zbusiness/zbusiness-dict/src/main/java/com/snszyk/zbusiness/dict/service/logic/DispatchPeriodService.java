package com.snszyk.zbusiness.dict.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.dto.*;
import com.snszyk.zbusiness.dict.enums.DictCodeEnum;
import com.snszyk.zbusiness.dict.enums.DispatchPeriodDetailStatusEnum;
import com.snszyk.zbusiness.dict.enums.DispatchPeriodStatusEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.dict.service.IDispatchPeriodDetailService;
import com.snszyk.zbusiness.dict.service.IDispatchPeriodService;
import com.snszyk.zbusiness.dict.vo.*;
import com.snszyk.zbusiness.project.dto.ProjectDispatchDto;
import com.snszyk.zbusiness.project.service.IProjectDispatchService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DispatchPeriodService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DispatchPeriodService extends BaseCrudLogicService<DispatchPeriodDto, DispatchPeriodVo> {

	private final IDispatchPeriodService dispatchPeriodService;

	private final IDispatchPeriodDetailService dispatchPeriodDetailService;

	private final IDictBizService dictBizService;

	private final IDeptService deptService;

	private final IProjectDispatchService projectDispatchService;




	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.dispatchPeriodService;
	}

	public IPage<DispatchPeriodPageDto> page(DispatchPeriodPageVo vo) {
		//查询当前登录人所属组织
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		IPage<DispatchPeriodPageDto> page = dispatchPeriodService.pageList(vo, dept.getUnitId());
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		for (DispatchPeriodPageDto dto : page.getRecords()) {
			// 调度周期
			String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), dto.getPeriodType());
			dto.setPeriodTypeName(periodType);
			// 状态
			String periodStatus = dictBizService.getValue(DictCodeEnum.PERIOD_STATUS.getCode(), dto.getPeriodStatus());
			dto.setPeriodStatusName(periodStatus);
			//是否可以删除
			boolean canDel= DeptScopeUtil.canDel(dto.getCompanyId(), dto.getCreateUser());
			canDel=canDel&&Objects.equals(dto.getPeriodStatus(),DispatchPeriodStatusEnum.ALL_OPEN.getCode());
			dto.setDelFlag(canDel);
			//获取最新组织信息
			if (Func.isNotEmpty(dto.getCompanyId())) {
				dto.setCompanyName(SysCache.getDeptName(dto.getCompanyId()));
			}
		}
		return page;
	}

	public IPage<DispatchPeriodDetailPageDto> detailPage(DispatchPeriodDetailPageVo vo) {

		List<Long> idList = new ArrayList<>();
		if (!StringUtils.isEmpty(vo.getPeriodType()) || !StringUtils.isEmpty(vo.getDeptId())) {
//			// 当前用户部门  20230512 修改
//			List<Long> deptIds = deptService.getChildDeptByIds(Arrays.asList(vo.getDeptId()));

			List<DispatchPeriodDto> periodList = dispatchPeriodService.listByPeriodType(vo.getPeriodType(), Arrays.asList(vo.getDeptId()));
			if (CollectionUtils.isEmpty(periodList)) {
				return null;
			}
			idList.addAll(periodList.stream().map(DispatchPeriodDto::getId).collect(Collectors.toList()));
		}


		IPage<DispatchPeriodDetailPageDto> page = dispatchPeriodDetailService.pageList(vo, idList);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		List<DispatchPeriodDto> periodList = dispatchPeriodService.listAll();
		Map<Long, DispatchPeriodDto> periodMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(periodList)) {
			periodMap.putAll(periodList.stream().collect(Collectors.toMap(DispatchPeriodDto::getId, Function.identity())));
		}

		for (DispatchPeriodDetailPageDto dto : page.getRecords()) {

			Long id = dto.getId();

			if (!CollectionUtils.isEmpty(periodMap) && periodMap.containsKey(dto.getDispatchPeriodId())) {
				BeanUtil.copyProperties(periodMap.get(dto.getDispatchPeriodId()), dto);
			}

			// 调度周期
			String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), dto.getPeriodType());
			dto.setPeriodTypeName(periodType);

			dto.setId(id);
		}

		return page;
	}

	public List<DispatchPeriodDetailPageDto> detailList() {

		List<DispatchPeriodDetailDto> list = dispatchPeriodDetailService.listAll();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		List<DispatchPeriodDto> periodList = dispatchPeriodService.listAll();
		Map<Long, DispatchPeriodDto> periodMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(periodList)) {
			periodMap.putAll(periodList.stream().collect(Collectors.toMap(DispatchPeriodDto::getId, Function.identity())));
		}

		List<DispatchPeriodDetailPageDto> result = new ArrayList<>();
		for (DispatchPeriodDetailDto detailDto : list) {
			DispatchPeriodDetailPageDto pageDto = new DispatchPeriodDetailPageDto();

			if (!CollectionUtils.isEmpty(periodMap) && periodMap.containsKey(detailDto.getDispatchPeriodId())) {
				BeanUtil.copyProperties(periodMap.get(detailDto.getDispatchPeriodId()), pageDto);
			}

			BeanUtil.copyProperties(detailDto, pageDto);

			// 调度周期
			String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), pageDto.getPeriodType());
			pageDto.setPeriodTypeName(periodType);

			result.add(pageDto);
		}

		return result;
	}

	public DispatchPeriodDto detail(Long id) {
		DispatchPeriodDto result = dispatchPeriodService.fetchById(id);

		// 调度周期
		String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), result.getPeriodType());
		result.setPeriodTypeName(periodType);

		// 状态
		String periodStatus = dictBizService.getValue(DictCodeEnum.PERIOD_STATUS.getCode(), result.getPeriodStatus());
		result.setPeriodStatusName(periodStatus);

		// 明细
		List<DispatchPeriodDetailDto> list = dispatchPeriodDetailService.listByPeriodId(id);
		for (DispatchPeriodDetailDto detailDto : list) {
			String dictValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), detailDto.getPeriodDetailStatus());
			detailDto.setPeriodDetailStatusName(dictValue);
		}
		result.setList(list);
		//获取最新组织信息
		if (Func.isNotEmpty(result.getCompanyId())) {
			result.setCompanyName(SysCache.getDeptName(result.getCompanyId()));
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public DispatchPeriodDto saveOrUpdate(DispatchPeriodVo vo) {

		DispatchPeriodDto result = new DispatchPeriodDto();

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<DispatchPeriodDto> list = dispatchPeriodService.listByTime(vo.getCompanyId(), vo.getPeriodType(), vo.getStartTime(), vo.getEndTime());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.TYPE_TIME_EXIST.getMessage());
			}
		} else {
			// 更新
			List<DispatchPeriodDto> list = dispatchPeriodService.listByTime(vo.getCompanyId(), vo.getPeriodType(), vo.getStartTime(), vo.getEndTime());
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, DispatchPeriodDto> map = list.stream().collect(Collectors.toMap(item -> item.getPeriodType() + item.getStartTime() + item.getEndTime(), Function.identity()));
				if (map.containsKey(vo.getPeriodType() + vo.getStartTime() + vo.getEndTime()) && !map.get(vo.getPeriodType() + vo.getStartTime() + vo.getEndTime()).getId().equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.TYPE_TIME_EXIST.getMessage());
				}
			}
		}

		List<DispatchPeriodDetailVo> filterList = vo.getList().stream().filter(item -> item.getPeriodDetailStatus().equals(DispatchPeriodDetailStatusEnum.YES.getCode())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(filterList)) {
			vo.setPeriodStatus(DispatchPeriodStatusEnum.ALL_CLOSE.getCode());
		}
		if (!CollectionUtils.isEmpty(filterList) && filterList.size() == vo.getList().size()) {
			vo.setPeriodStatus(DispatchPeriodStatusEnum.ALL_OPEN.getCode());
		}
		if (!CollectionUtils.isEmpty(filterList) && filterList.size() != vo.getList().size()) {
			vo.setPeriodStatus(DispatchPeriodStatusEnum.PART_OPEN.getCode());
		}

		if (ObjectUtils.isEmpty(vo.getId())) {
			result = dispatchPeriodService.save(vo);
		} else {
			result = dispatchPeriodService.update(vo);
		}

		if (!StringUtils.isEmpty(vo.getId())) {
			dispatchPeriodDetailService.deleteByPeriodId(vo.getId());
		}

		for (DispatchPeriodDetailVo detailVo : vo.getList()) {
			detailVo.setDispatchPeriodId(result.getId());
			dispatchPeriodDetailService.save(detailVo);
		}


		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(DispatchPeriodStatusVo vo) {

		boolean result = dispatchPeriodService.updateByStatus(vo.getId(), vo.getPeriodStatus());

		// 更新明细
		dispatchPeriodDetailService.updateByStatus(vo.getId(), vo.getPeriodStatus().equals(DispatchPeriodStatusEnum.ALL_OPEN.getCode()) ? DispatchPeriodDetailStatusEnum.YES.getCode() : DispatchPeriodDetailStatusEnum.NO.getCode());

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<CommonDeleteDto> delete(DispatchPeriodDeleteVo vo) {
		List<CommonDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			CommonDeleteDto dto = new CommonDeleteDto();

			DispatchPeriodDto periodDto = dispatchPeriodService.fetchById(id);
			// 调度周期
			String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), periodDto.getPeriodType());

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			dto.setName(periodDto.getCompanyName() + "," + periodDto.getYear() + "年,周期类型为" + periodType + ",开始时间:" + sdf.format(periodDto.getStartTime()) + ",结束时间:" + sdf.format(periodDto.getEndTime()));

			 //校验是否引用
			List<ProjectDispatchDto> list = projectDispatchService.listByPeriodId(id);
			if (!CollectionUtils.isEmpty(list)) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.PROJECT_DISPATCH_DATA.getMessage());
				result.add(dto);
				continue;
			}
			//只可以删除自己创建的
			if (!DeptScopeUtil.canDel(periodDto.getCompanyId(),periodDto.getCreateUser())) {
				dto.setResult(false);
				dto.setMessage(com.snszyk.zbusiness.project.enums.ExceptionEnum.NO_SELF_CANT_DEL.getMessage());
				result.add(dto);
				continue;
			}

			Boolean flag = dispatchPeriodService.deleteById(id);
			dto.setResult(flag);

			// 删除明细
			dispatchPeriodDetailService.deleteByPeriodId(id);

			result.add(dto);
		}

		return result;
	}

	public List<DispatchPeriodDetailPageDto> detailList(Long deptId) {

		//调度周期
		List<DispatchPeriodDto> periodList = dispatchPeriodService.listByDeptId(deptId);
		if (CollectionUtil.isEmpty(periodList)) {
			return new ArrayList<>();
		}
		Map<Long, DispatchPeriodDto> periodMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(periodList)) {
			periodMap.putAll(periodList.stream().collect(Collectors.toMap(DispatchPeriodDto::getId, Function.identity())));
		}
		List<Long> periodIdList = periodList.stream().map(e -> e.getId()).collect(Collectors.toList());
		List<DispatchPeriodDetailDto> detailList = dispatchPeriodDetailService.listByDispatchIds(periodIdList);
		if (CollectionUtils.isEmpty(detailList)) {
			return null;
		}
		List<DispatchPeriodDetailPageDto> result = new ArrayList<>();
		for (DispatchPeriodDetailDto detailDto : detailList) {
			DispatchPeriodDetailPageDto pageDto = new DispatchPeriodDetailPageDto();

			if (!CollectionUtils.isEmpty(periodMap) && periodMap.containsKey(detailDto.getDispatchPeriodId())) {
				BeanUtil.copyProperties(periodMap.get(detailDto.getDispatchPeriodId()), pageDto);
			}

			BeanUtil.copyProperties(detailDto, pageDto);

			// 调度周期
			String periodType = dictBizService.getValue(DictCodeEnum.PERIOD_TYPE.getCode(), pageDto.getPeriodType());
			pageDto.setPeriodTypeName(periodType);

			result.add(pageDto);
		}
		return result;
	}
}
