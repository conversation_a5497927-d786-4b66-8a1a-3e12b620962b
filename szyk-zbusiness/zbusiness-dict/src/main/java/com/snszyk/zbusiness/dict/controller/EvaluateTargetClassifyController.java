package com.snszyk.zbusiness.dict.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.dict.dto.CommonDeleteDto;
import com.snszyk.zbusiness.dict.dto.EvaluateTargetClassifyDto;
import com.snszyk.zbusiness.dict.service.logic.EvaluateTargetClassifyService;
import com.snszyk.zbusiness.dict.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 指标分类 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/evaluate-target-classify")
@Api(value = "指标分类", tags = "指标分类接口")
@Slf4j
public class EvaluateTargetClassifyController extends BaseCrudController {

	private final EvaluateTargetClassifyService evaluateTargetClassifyService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return evaluateTargetClassifyService;
	}

	@PostMapping("/page")
	@ApiOperation(value = "分页")
	public R<IPage<EvaluateTargetClassifyDto>> page(@RequestBody EvaluateTargetClassifyPageVo vo) {
		IPage<EvaluateTargetClassifyDto> pageQueryResult = evaluateTargetClassifyService.page(vo);
		return R.data(pageQueryResult);
	}

	@GetMapping("/list")
	@ApiOperation(value = "列表")
	public R<List<EvaluateTargetClassifyDto>> list(@ApiParam(value = "停用/启用") @RequestParam(required = false) String classifyStatus, @ApiParam(value = "所属组织ID") @RequestParam(required = false) Long companyId) {
		List<EvaluateTargetClassifyDto> listQueryResult = evaluateTargetClassifyService.list(classifyStatus, companyId);
		return R.data(listQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<EvaluateTargetClassifyDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		EvaluateTargetClassifyDto baseCrudDto = evaluateTargetClassifyService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存/编辑")
	public R<EvaluateTargetClassifyDto> save(@RequestBody EvaluateTargetClassifyVo vo) {
		EvaluateTargetClassifyDto baseCrudDto = evaluateTargetClassifyService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody EvaluateTargetClassifyStatusVo vo) {
		Boolean result = evaluateTargetClassifyService.status(vo);
		return R.data(result);
	}

	@PostMapping("/score")
	@ApiOperation(value = "是否加分项")
	public R<Boolean> score(@RequestBody EvaluateTargetClassifyScoreVo vo) {
		Boolean result = evaluateTargetClassifyService.score(vo);
		return R.data(result);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<CommonDeleteDto>> delete(@RequestBody EvaluateTargetClassifyDeleteVo vo) {
		List<CommonDeleteDto> result = evaluateTargetClassifyService.delete(vo);
		return R.data(result);
	}
}
