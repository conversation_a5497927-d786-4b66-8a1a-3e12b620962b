/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.dict.dto.ClassifyDto;
import com.snszyk.zbusiness.dict.entity.Classify;
import com.snszyk.zbusiness.dict.mapper.ClassifyMapper;
import com.snszyk.zbusiness.dict.service.IClassifyService;
import com.snszyk.zbusiness.dict.vo.ClassifySaveVo;
import com.snszyk.zbusiness.dict.vo.ClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 知识分类 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@AllArgsConstructor
@Service
public class ClassifyServiceImpl extends BaseCrudServiceImpl<ClassifyMapper, Classify, ClassifyDto, ClassifyVo> implements IClassifyService {

	@Override
	protected Wrapper<Classify> beforePage(ClassifyVo v) {
		if (null == v) {
			return Wrappers.emptyWrapper();
		}
		LambdaQueryWrapper<Classify> wrapper = Wrappers.lambdaQuery(Classify.class);

		wrapper.eq(ObjectUtil.isNotEmpty(v.getClassifyName()), Classify::getClassifyName, v.getClassifyName())
			.eq(ObjectUtil.isNotEmpty(v.getCollectType()), Classify::getCollectType, v.getCollectType())
			.eq(ObjectUtil.isNotEmpty(v.getClassifyStatus()), Classify::getClassifyStatus, v.getClassifyStatus());
		if (v.getId() != null) {
			wrapper.ne(ObjectUtil.isNotEmpty(v.getId()), Classify::getId, v.getId());
		}
		wrapper.orderByAsc(Classify::getSort);
		return wrapper;
	}

	@Override
	public Boolean removeByIds(List<Long> idList) {
		return super.removeByIds(idList);
	}

	@Override
	public Boolean saveOrUpdateBatch(List<ClassifyVo> classifyVoList) {
		return super.saveOrUpdateBatch(BeanUtil.copy(classifyVoList, Classify.class));
	}

	@Override
	public List<ClassifyDto> myList(String collectType, String classifyName) {
		List<Classify> list = super.lambdaQuery()
			.like(StringUtil.isNotBlank(classifyName), Classify::getClassifyName, classifyName)
			.eq(StringUtil.isNotBlank(collectType), Classify::getCollectType, collectType)
			.orderByAsc(Classify::getSort)
			.list();
		if (ObjectUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(list, ClassifyDto.class);
	}

	@Override
	public List<ClassifyDto> listByIds(List<Long> ids) {
		List<Classify> list = super.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(ids), Classify::getId, ids)
			.list();
		if (ObjectUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(list, ClassifyDto.class);
	}

	@Override
	public List<ClassifyDto> listByMenuId(Long menuId, String collectType, String classifyStatus) {
		List<Classify> list = super.lambdaQuery()
			.like(ObjectUtil.isNotEmpty(menuId), Classify::getLinkMenuId, menuId)
			.eq(ObjectUtil.isNotEmpty(collectType), Classify::getCollectType, collectType)
			.eq(ObjectUtil.isNotEmpty(classifyStatus), Classify::getClassifyStatus, classifyStatus)
			.list();

		if (ObjectUtil.isEmpty(list)) {
			return Collections.emptyList();
		}

		return BeanUtil.copy(list, ClassifyDto.class);
	}

	@Override
	public Boolean updateMenu(Long id) {
		return this.lambdaUpdate().set(Classify::getLinkMenuId,null).set(Classify::getLinkMenuName,null).update();
	}

	@Override
	public Boolean updateVo(ClassifyVo v) {
		Classify classify = BeanUtil.copyProperties(v,Classify.class);
		this.baseMapper.updateById(classify);
		return null;
	}

}
