/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.dict.dto.RwPeriodDto;
import com.snszyk.zbusiness.dict.dto.RwPeriodPageDto;
import com.snszyk.zbusiness.dict.entity.RwPeriod;
import com.snszyk.zbusiness.dict.mapper.RwPeriodMapper;
import com.snszyk.zbusiness.dict.service.IRwPeriodService;
import com.snszyk.zbusiness.dict.vo.RwPeriodPageVo;
import com.snszyk.zbusiness.dict.vo.RwPeriodVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * RwPeriodServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwPeriodServiceImpl extends BaseCrudServiceImpl<RwPeriodMapper, RwPeriod, RwPeriodDto, RwPeriodVo> implements IRwPeriodService {


	@Override
	public IPage<RwPeriodPageDto> pageList(RwPeriodPageVo vo,List<Long> deptIdList) {

		IPage<RwPeriod> majorPage = baseMapper.dataPage(vo, deptIdList);
		return majorPage.convert(RwPeriod -> BeanUtil.copyProperties(RwPeriod, RwPeriodPageDto.class));
	}

	@Override
	public List<RwPeriodDto> listByTime(Long companyId, String periodType, Date startTime, Date endTime) {
		LambdaQueryWrapper<RwPeriod> queryWrapper = Wrappers.<RwPeriod>query().lambda()
			.eq(ObjectUtils.isNotEmpty(companyId), RwPeriod::getCompanyId, companyId)
			.eq(StringUtils.isNotEmpty(periodType), RwPeriod::getPeriodType, periodType)
			.eq(ObjectUtils.isNotEmpty(startTime), RwPeriod::getStartTime, startTime)
			.eq(ObjectUtils.isNotEmpty(endTime), RwPeriod::getEndTime, endTime)
			.orderByDesc(RwPeriod::getCreateTime);

		List<RwPeriod> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, RwPeriodDto.class));
	}

	@Override
	public List<RwPeriodDto> listByPeriodType(String periodType, List<Long> deptIdList) {
		LambdaQueryWrapper<RwPeriod> queryWrapper = Wrappers.<RwPeriod>query().lambda()
			.eq(StringUtils.isNotEmpty(periodType), RwPeriod::getPeriodType, periodType)
			.in(CollectionUtils.isNotEmpty(deptIdList), RwPeriod::getCompanyId, deptIdList)
			.orderByDesc(RwPeriod::getCreateTime);

		List<RwPeriod> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, RwPeriodDto.class));
	}

	@Override
	public List<RwPeriodDto> listByDeptId(Long deptId) {
		LambdaQueryWrapper<RwPeriod> queryWrapper = Wrappers.<RwPeriod>query().lambda()
			.eq(ObjectUtils.isNotEmpty(deptId), RwPeriod::getCompanyId, deptId)
			.orderByDesc(RwPeriod::getCreateTime);

		List<RwPeriod> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, RwPeriodDto.class));
	}

	@Override
	public List<RwPeriodDto> listAll() {
		List<RwPeriod> list = super.list();

		return Objects.requireNonNull(BeanUtil.copy(list, RwPeriodDto.class));
	}

	@Override
	public boolean updateByStatus(Long id, String periodStatus) {
		LambdaUpdateWrapper<RwPeriod> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(RwPeriod::getPeriodStatus, periodStatus);
		updateWrapper.eq(RwPeriod::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public RwPeriodDto update(RwPeriodVo vo) {
		RwPeriod entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, RwPeriodDto.class);
		} else {
			return null;
		}
	}
}
