/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.DictLabelClassifyDto;
import com.snszyk.zbusiness.dict.entity.DictLabelClassify;
import com.snszyk.zbusiness.dict.mapper.DictLabelClassifyMapper;
import com.snszyk.zbusiness.dict.service.IDictLabelClassifyService;
import com.snszyk.zbusiness.dict.vo.DictLabelClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * DictLabelClassifyServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictLabelClassifyServiceImpl extends BaseCrudServiceImpl<DictLabelClassifyMapper, DictLabelClassify, DictLabelClassifyDto, DictLabelClassifyVo> implements IDictLabelClassifyService {

	@Override
	public List<DictLabelClassifyDto> listByAll() {
		LambdaQueryWrapper<DictLabelClassify> queryWrapper = Wrappers.<DictLabelClassify>query().lambda()
			.eq(DictLabelClassify::getIsDeleted, 0);

		List<DictLabelClassify> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelClassifyDto.class));
	}

	@Override
	public List<DictLabelClassifyDto> listByParentId(Long parentId) {
		LambdaQueryWrapper<DictLabelClassify> queryWrapper = Wrappers.<DictLabelClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(parentId), DictLabelClassify::getParentId, parentId);

		List<DictLabelClassify> list = baseMapper.selectList(queryWrapper);

		return Objects.requireNonNull(BeanUtil.copy(list, DictLabelClassifyDto.class));
	}

	@Override
	public DictLabelClassifyDto update(DictLabelClassifyVo vo) {
		DictLabelClassify entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, DictLabelClassifyDto.class);
		} else {
			return null;
		}
	}
}
