/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 后评价小组成员表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("evaluate_group_member")
@EqualsAndHashCode(callSuper = false)
public class EvaluateGroupMember extends BaseCrudEntity {

	/**
	 * 小组id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long groupId;
	/**
	 * 工号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String jobNo;
	/**
	 * 成员姓名
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String memberName;
	/**
	 * 权属单位id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long manageOrgId;
	/**
	 * 权属单位名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String manageOrgName;
	/**
	 * 部门id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long departId;
	/**
	 * 部门名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String departName;
	/**
	 * 手机号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String phoneNo;

}
