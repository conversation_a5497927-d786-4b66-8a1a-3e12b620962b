/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.SchemeTargetEvaluateDto;
import com.snszyk.zbusiness.dict.entity.SchemeTargetEvaluate;
import com.snszyk.zbusiness.dict.mapper.SchemeTargetEvaluateMapper;
import com.snszyk.zbusiness.dict.service.ISchemeTargetEvaluateService;
import com.snszyk.zbusiness.dict.vo.SchemeTargetEvaluateVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * SchemeTargetEvaluateServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class SchemeTargetEvaluateServiceImpl extends BaseCrudServiceImpl<SchemeTargetEvaluateMapper, SchemeTargetEvaluate, SchemeTargetEvaluateDto, SchemeTargetEvaluateVo> implements ISchemeTargetEvaluateService {

	@Override
	public List<SchemeTargetEvaluateDto> listBySchemeId(Long schemeId) {
		LambdaQueryWrapper<SchemeTargetEvaluate> queryWrapper = Wrappers.<SchemeTargetEvaluate>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), SchemeTargetEvaluate::getSchemeId, schemeId)
			.orderByAsc(SchemeTargetEvaluate::getSchemeClassifyId)
			.orderByAsc(SchemeTargetEvaluate::getId);

		List<SchemeTargetEvaluate> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetEvaluateDto.class));
	}

	@Override
	public List<SchemeTargetEvaluateDto> listAll() {
		List<SchemeTargetEvaluate> list = super.list();

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetEvaluateDto.class));
	}

	@Override
	public int deleteBySchemeId(Long schemeId) {
		LambdaQueryWrapper<SchemeTargetEvaluate> queryWrapper = Wrappers.<SchemeTargetEvaluate>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), SchemeTargetEvaluate::getSchemeId, schemeId);

		return baseMapper.delete(queryWrapper);
	}
}
