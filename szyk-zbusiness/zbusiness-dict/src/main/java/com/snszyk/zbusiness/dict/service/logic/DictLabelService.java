package com.snszyk.zbusiness.dict.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.dto.DictDeleteDto;
import com.snszyk.zbusiness.dict.dto.DictLabelClassifyDto;
import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import com.snszyk.zbusiness.dict.enums.DictCodeEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.dict.service.IDictLabelClassifyService;
import com.snszyk.zbusiness.dict.service.IDictLabelService;
import com.snszyk.zbusiness.dict.util.ZeroPaddingUtil;
import com.snszyk.zbusiness.dict.vo.DictLabelDeleteVo;
import com.snszyk.zbusiness.dict.vo.DictLabelPageVo;
import com.snszyk.zbusiness.dict.vo.DictLabelStatusVo;
import com.snszyk.zbusiness.dict.vo.DictLabelVo;
import com.snszyk.zbusiness.project.service.IPlExternalReferenceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DictLabelService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictLabelService extends BaseCrudLogicService<DictLabelDto, DictLabelVo> {

	private final IDictLabelService dictLabelService;

	private final IDictLabelClassifyService dictLabelClassifyService;

	private final IDictBizService dictBizService;

	@Resource
	private ZeroPaddingUtil zeroPaddingUtil;

	@Resource
	private IPlExternalReferenceService plExternalReferenceService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.dictLabelService;
	}

	@Transactional(rollbackFor = Exception.class)
	public DictLabelDto saveOrUpdate(DictLabelVo vo) {

		DictLabelDto dto = new DictLabelDto();

		vo.setDictValue(vo.getDictValue().trim());

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<DictLabelDto> list = dictLabelService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.LABEL_NAME_DATA_REPEAT.getMessage());
			}

			//vo.setDictKey(zeroPaddingUtil.labelKey());
			//改为时间戳来保证key唯一
			vo.setDictKey(DictEnum.PL.getCode() + String.valueOf(System.currentTimeMillis()));

			dto = dictLabelService.save(vo);
		} else {
			// 更新
			List<DictLabelDto> list = dictLabelService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, Long> map = list.stream().collect(Collectors.toMap(DictLabelDto::getDictValue, DictLabelDto::getId));
				if (map.containsKey(vo.getDictValue()) && !map.get(vo.getDictValue()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.LABEL_NAME_DATA_REPEAT.getMessage());
				}
			}

			dto = dictLabelService.update(vo);
		}

		return dto;
	}

	/**
	 *  有权限即可维护，但是只能维护本单位的项目标签，不能修改其他单位的项目标签
	 * @param vo
	 * @return
	 */
	public IPage<DictLabelDto> page(DictLabelPageVo vo) {
		List<Long> classifyIdList = new ArrayList<>();
		if (!StringUtils.isEmpty(vo.getClassifyId())) {
			classifyList(classifyIdList, vo.getClassifyId());
		}
		IPage<DictLabelDto> page = dictLabelService.pageList(vo, classifyIdList);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}
		//只能维护自己单位的
		List<Dept> currentUnitAll = DeptScopeUtil.getCurrentUnitAll();
		List<Long> deptIdList = currentUnitAll.stream().map(e -> e.getId()).collect(Collectors.toList());
		for (DictLabelDto dictLabelDto : page.getRecords()) {
			if(deptIdList.contains(dictLabelDto.getCreateDept())){
				dictLabelDto.setEditFlag(true);
			}else{
				dictLabelDto.setEditFlag(false);
			}
			DictLabelClassifyDto classifyDto = dictLabelClassifyService.fetchById(dictLabelDto.getClassifyId());
			dictLabelDto.setClassifyName(classifyDto.getName());

			String dictValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dictLabelDto.getDictStatus());
			dictLabelDto.setDictStatusName(dictValue);
		}

		return page;
	}

	public List<Long> classifyList(List<Long> result, Long classifyId) {
		result.add(classifyId);

		List<DictLabelClassifyDto> list = dictLabelClassifyService.listByParentId(classifyId);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}
		for (DictLabelClassifyDto dto : list) {
			classifyList(result, dto.getId());
		}
		return result;
	}

	public List<DictLabelDto> list(String dictValue, String dictStatus) {
		List<DictLabelDto> result = dictLabelService.listByDictStatus(dictValue, dictStatus);
		if (CollectionUtils.isEmpty(result)) {
			return null;
		}
		for (DictLabelDto dto : result) {
			DictLabelClassifyDto classifyDto = dictLabelClassifyService.fetchById(dto.getClassifyId());
			dto.setClassifyName(classifyDto.getName());

			String statusValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dto.getDictStatus());
			dto.setDictStatusName(statusValue);
		}
		return result;
	}

	public DictLabelDto detail(Long id) {
		DictLabelDto result = dictLabelService.fetchById(id);

		DictLabelClassifyDto classifyDto = dictLabelClassifyService.fetchById(result.getClassifyId());
		result.setClassifyName(classifyDto.getName());

		String dictValue = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), result.getDictStatus());
		result.setDictStatusName(dictValue);

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(DictLabelStatusVo vo) {

		return dictLabelService.updateByStatus(vo.getId(), vo.getDictStatus());
	}

	@Transactional(rollbackFor = Exception.class)
	public List<DictDeleteDto> delete(DictLabelDeleteVo vo) {
		List<DictDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			DictDeleteDto dto = new DictDeleteDto();

			DictLabelDto dictLabelDto = dictLabelService.fetchById(id);
			dto.setDictValue(dictLabelDto.getDictValue());

			// 校验是否引用
			Boolean check = plExternalReferenceService.isReference(DictEnum.PL.getCode(), dictLabelDto.getDictKey());
			if (check) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.PROJECT_REFERENCE_DATA.getMessage());
				result.add(dto);
				continue;
			}

			Boolean flag = dictLabelService.deleteById(id);
			dto.setResult(flag);

			result.add(dto);
		}

		return result;
	}
}
