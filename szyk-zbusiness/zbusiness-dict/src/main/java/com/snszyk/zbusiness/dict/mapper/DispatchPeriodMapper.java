package com.snszyk.zbusiness.dict.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.dict.entity.DispatchPeriod;
import com.snszyk.zbusiness.dict.vo.DispatchPeriodPageVo;
import org.apache.ibatis.annotations.Param;

public interface DispatchPeriodMapper extends BaseMapper<DispatchPeriod> {
    IPage<DispatchPeriod> dataPage(@Param("page") DispatchPeriodPageVo vo, @Param("deptId") Long deptId);
}
