/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.dict.dto.SchemeTargetDto;
import com.snszyk.zbusiness.dict.dto.SchemeTargetPageDto;
import com.snszyk.zbusiness.dict.entity.SchemeTarget;
import com.snszyk.zbusiness.dict.mapper.SchemeTargetMapper;
import com.snszyk.zbusiness.dict.service.ISchemeTargetService;
import com.snszyk.zbusiness.dict.vo.SchemeTargetPageVo;
import com.snszyk.zbusiness.dict.vo.SchemeTargetVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * SchemeTargetServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class SchemeTargetServiceImpl extends BaseCrudServiceImpl<SchemeTargetMapper, SchemeTarget, SchemeTargetDto, SchemeTargetVo> implements ISchemeTargetService {

	@Override
	public IPage<SchemeTargetPageDto> pageList(SchemeTargetPageVo vo,Long deptId) {
//		LambdaQueryWrapper<SchemeTarget> queryWrapper = Wrappers.<SchemeTarget>query().lambda()
//			.eq(SchemeTarget::getCompanyId,deptId)
//			.like(StringUtils.isNotEmpty(vo.getSchemeNo()), SchemeTarget::getSchemeNo, vo.getSchemeNo())
//			.like(StringUtils.isNotEmpty(vo.getSchemeName()), SchemeTarget::getSchemeName, vo.getSchemeName())
//			.like(StringUtils.isNotEmpty(vo.getCompanyName()), SchemeTarget::getCompanyName, vo.getCompanyName())
//			.orderByDesc(SchemeTarget::getCreateTime);
		//v1.5改为xml sql实现
        vo.setCompanyId(deptId);
		Page<SchemeTarget> page = new Page(vo.getCurrent(), vo.getSize());
		IPage<SchemeTargetDto> majorPage = baseMapper.pageList(page, vo);

		return majorPage.convert(SchemeTarget -> BeanUtil.copyProperties(SchemeTarget, SchemeTargetPageDto.class));
	}

	@Override
	public List<SchemeTargetDto> listBySchemeStatus(String schemeStatus) {
		LambdaQueryWrapper<SchemeTarget> queryWrapper = Wrappers.<SchemeTarget>query().lambda()
			.eq(StringUtils.isNotEmpty(schemeStatus), SchemeTarget::getSchemeStatus, schemeStatus)
			.orderByDesc(SchemeTarget::getCreateTime);

		List<SchemeTarget> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetDto.class));
	}

	@Override
	public List<SchemeTargetDto> listAll() {
		List<SchemeTarget> list = super.list();

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetDto.class));
	}

	@Override
	public List<SchemeTargetDto> listByStatusAndCompany(String schemeStatus, List<Long> companyIds) {
		LambdaQueryWrapper<SchemeTarget> queryWrapper = Wrappers.<SchemeTarget>query().lambda()
			.eq(StringUtils.isNotEmpty(schemeStatus), SchemeTarget::getSchemeStatus, schemeStatus)
			.in(CollectionUtils.isNotEmpty(companyIds), SchemeTarget::getCompanyId, companyIds)
			.orderByDesc(SchemeTarget::getCreateTime);

		List<SchemeTarget> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetDto.class));
	}

	@Override
	public List<SchemeTargetDto> listByNameAndCompany(String schemeName, Long companyId) {
		LambdaQueryWrapper<SchemeTarget> queryWrapper = Wrappers.<SchemeTarget>query().lambda()
			.eq(StringUtils.isNotEmpty(schemeName), SchemeTarget::getSchemeName, schemeName)
			.eq(ObjectUtils.isNotEmpty(companyId), SchemeTarget::getCompanyId, companyId)
			.orderByDesc(SchemeTarget::getCreateTime);

		List<SchemeTarget> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, SchemeTargetDto.class));
	}

	@Override
	public boolean updateByStatus(Long id, String schemeStatus) {
		LambdaUpdateWrapper<SchemeTarget> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(SchemeTarget::getSchemeStatus, schemeStatus);
		updateWrapper.eq(SchemeTarget::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public SchemeTargetDto update(SchemeTargetVo vo) {
		SchemeTarget entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, SchemeTargetDto.class);
		} else {
			return null;
		}
	}
}
