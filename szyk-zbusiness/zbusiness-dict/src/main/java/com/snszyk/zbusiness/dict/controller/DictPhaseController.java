package com.snszyk.zbusiness.dict.controller;

import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.dict.dto.DictDeleteDto;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.service.logic.DictPhaseService;
import com.snszyk.zbusiness.dict.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目阶段 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dict-phase")
@Api(value = "项目阶段", tags = "项目阶段接口")
@Slf4j
public class DictPhaseController extends BaseCrudController {

	private final DictPhaseService dictPhaseService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return dictPhaseService;
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存/编辑")
	public R<DictPhaseDto> save(@RequestBody DictPhaseVo vo) {
		DictPhaseDto baseCrudDto = dictPhaseService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/list")
	@ApiOperation(value = "列表")
	public R<List<DictPhaseDto>> list(@RequestBody DictPhaseListVo vo) {
		List<DictPhaseDto> listQueryResult = dictPhaseService.list(vo);
		return R.data(listQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<DictPhaseDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		DictPhaseDto baseCrudDto = dictPhaseService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody DictPhaseStatusVo vo) {
		Boolean result = dictPhaseService.status(vo);
		return R.data(result);
	}

	@PostMapping("/type")
	@ApiOperation(value = "是否必要节点")
	public R<Boolean> type(@RequestBody DictPhaseTypeVo vo) {
		Boolean result = dictPhaseService.type(vo);
		return R.data(result);
	}

	@PostMapping("/sort")
	@ApiOperation(value = "排序")
	public R<Boolean> sort(@RequestBody DictPhaseSortVo vo) {
		Boolean result = dictPhaseService.sort(vo);
		return R.data(result);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<DictDeleteDto>> delete(@RequestBody DictPhaseDeleteVo vo) {
		List<DictDeleteDto> result = dictPhaseService.delete(vo);
		return R.data(result);
	}
}
