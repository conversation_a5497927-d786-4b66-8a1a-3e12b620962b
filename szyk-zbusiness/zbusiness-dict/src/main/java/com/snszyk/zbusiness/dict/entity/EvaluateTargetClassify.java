/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标分类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("evaluate_target_classify")
@EqualsAndHashCode(callSuper = false)
public class EvaluateTargetClassify extends BaseCrudEntity {

	/**
	 * 指标分类名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String classifyName;
	/**
	 * 所属组织id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 是否加分项：0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String addScore;
	/**
	 * 状态：0启用1停用
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String classifyStatus;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;

}
