/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 考核周期
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rw_period")
@EqualsAndHashCode(callSuper = false)
public class RwPeriod extends BaseCrudEntity {

	/**
	 * 周期类型，字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String periodType;
	/**
	 * 年份
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer year;
	/**
	 * 所属组织id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 开始时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date startTime;
	/**
	 * 结束时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date endTime;
	/**
	 * 周期状态：0全部开启1部分关闭3全部关闭
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String periodStatus;

}
