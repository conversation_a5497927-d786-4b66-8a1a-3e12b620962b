package com.snszyk.zbusiness.dict.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.dict.dto.DictDeleteDto;
import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import com.snszyk.zbusiness.dict.service.logic.DictLabelService;
import com.snszyk.zbusiness.dict.vo.DictLabelDeleteVo;
import com.snszyk.zbusiness.dict.vo.DictLabelPageVo;
import com.snszyk.zbusiness.dict.vo.DictLabelStatusVo;
import com.snszyk.zbusiness.dict.vo.DictLabelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目标签字典 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dict-label")
@Api(value = "项目标签字典", tags = "项目标签字典接口")
@Slf4j
public class DictLabelController extends BaseCrudController {

	private final DictLabelService dictLabelService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return dictLabelService;
	}


	@PostMapping("/save")
	@ApiOperation(value = "保存/编辑")
	public R<DictLabelDto> save(@RequestBody DictLabelVo vo) {
		DictLabelDto baseCrudDto = dictLabelService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/page")
	@ApiOperation(value = "分页")
	public R<IPage<DictLabelDto>> page(@RequestBody DictLabelPageVo vo) {
		IPage<DictLabelDto> pageQueryResult = dictLabelService.page(vo);
		return R.data(pageQueryResult);
	}

	@GetMapping("/list")
	@ApiOperation(value = "列表")
	public R<List<DictLabelDto>> list(@ApiParam(value = "字典名称") @RequestParam(required = false) String dictValue,
									  @ApiParam(value = "停用/启用") @RequestParam(required = false) String dictStatus) {
		List<DictLabelDto> listQueryResult = dictLabelService.list(dictValue, dictStatus);
		return R.data(listQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<DictLabelDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		DictLabelDto baseCrudDto = dictLabelService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody DictLabelStatusVo vo) {
		Boolean result = dictLabelService.status(vo);
		return R.data(result);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<DictDeleteDto>> delete(@RequestBody DictLabelDeleteVo vo) {
		List<DictDeleteDto> result = dictLabelService.delete(vo);
		return R.data(result);
	}
}
