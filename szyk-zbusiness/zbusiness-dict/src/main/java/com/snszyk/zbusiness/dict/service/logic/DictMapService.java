package com.snszyk.zbusiness.dict.service.logic;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.dict.dto.DictCommonDto;
import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import com.snszyk.zbusiness.dict.dto.DictMajorDto;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.service.*;
import com.snszyk.zbusiness.dict.vo.DictLabelVo;
import com.snszyk.zbusiness.dict.vo.DictMajorVo;
import com.snszyk.zbusiness.dict.vo.DictPhaseVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class DictMapService implements IDictMapService {

	private final IDictMajorService dictMajorService;

	private final IDictPhaseService dictPhaseService;

	private final IDictCommonService dictCommonService;

	private final IDictLabelService dictLabelService;
	/**
	 * 专业分类map
	 *
	 * @return
	 */
	@Override
	public Map<String, String> getProjectMajorDictMap() {
		Map<String, String> projectMajorMap = new HashMap<>();
		List<DictMajorDto> list = dictMajorService.list(new DictMajorVo());
		if (ObjectUtil.isNotEmpty(list)) {
			projectMajorMap = list.stream()
				.collect(Collectors.toMap(DictMajorDto::getDictKey, DictMajorDto::getDictValue));
		}
		return projectMajorMap;
	}

	@Override
	public Map<String, String> getProjectMajorDictValueMap() {
		Map<String, String> projectMajorMap = new HashMap<>();
		List<DictMajorDto> list = dictMajorService.list(new DictMajorVo());
		if (ObjectUtil.isNotEmpty(list)) {
			projectMajorMap = list.stream()
				.collect(Collectors.toMap(DictMajorDto::getDictValue, DictMajorDto::getDictKey));
		}
		return projectMajorMap;
	}

	/**
	 * 项目标签
	 *
	 * @return
	 */
	@Override
	public Map<String, String> getProjectLabelMap() {
		Map<String, String> projectLabelMap = new HashMap<>();
		List<DictLabelDto> list = dictLabelService.list(new DictLabelVo());
		if (ObjectUtil.isNotEmpty(list)) {
			projectLabelMap = list.stream()
				.collect(Collectors.toMap(DictLabelDto::getDictKey, DictLabelDto::getDictValue, (a, b) -> a));
		}
		return projectLabelMap;
	}

	/**
	 * 项目阶段
	 *
	 * @return
	 */
	@Override
	public Map<String, String> getProjectPhaseDictMap() {
		Map<String, String> projectPhaseMap = new HashMap<>();
		List<DictPhaseDto> list = dictPhaseService.list(new DictPhaseVo());
		if (ObjectUtil.isNotEmpty(list)) {
			projectPhaseMap = list.stream()
				.collect(Collectors.toMap(DictPhaseDto::getDictKey, DictPhaseDto::getDictValue));
		}
		return projectPhaseMap;
	}
	/**
	 * 查询公共字典表
	 *
	 * @return
	 */
	/**
	 * 查询公共字典表
	 *
	 * @return
	 */
	@Override
	public Map<String, String> getCommonDictMap(String dictCode) {
		Map<String, String> commonMap = new HashMap<>();
		List<DictCommonDto> list = dictCommonService.listByDictValue(dictCode, null);
		if (ObjectUtil.isNotEmpty(list)) {
			commonMap = list.stream()
				.collect(Collectors.toMap(DictCommonDto::getDictKey, DictCommonDto::getDictValue));
		}
		return commonMap;
	}



}
