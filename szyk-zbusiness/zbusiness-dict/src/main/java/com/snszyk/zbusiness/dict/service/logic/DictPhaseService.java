package com.snszyk.zbusiness.dict.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.dto.DictDeleteDto;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.enums.DictCodeEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.dict.service.IDictPhaseService;
import com.snszyk.zbusiness.dict.util.ZeroPaddingUtil;
import com.snszyk.zbusiness.dict.vo.*;
import com.snszyk.zbusiness.project.service.IPlExternalReferenceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * DictPhaseService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class DictPhaseService extends BaseCrudLogicService<DictPhaseDto, DictPhaseVo> {

	private final IDictPhaseService dictPhaseService;

	private final IDictBizService dictBizService;

	@Resource
	private ZeroPaddingUtil zeroPaddingUtil;

	@Resource
	private IPlExternalReferenceService plExternalReferenceService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.dictPhaseService;
	}

	@Transactional(rollbackFor = Exception.class)
	public DictPhaseDto saveOrUpdate(DictPhaseVo vo) {

		DictPhaseDto dto = new DictPhaseDto();

		vo.setDictValue(vo.getDictValue().trim());

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<DictPhaseDto> list = dictPhaseService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.PHASE_NAME_DATA_REPEAT.getMessage());
			}

			//vo.setDictKey(zeroPaddingUtil.phaseKey());
			//改为时间戳来保证key唯一
			vo.setDictKey(DictEnum.PP.getCode() + String.valueOf(System.currentTimeMillis()));
			vo.setSort(zeroPaddingUtil.phaseSort());

			dto = dictPhaseService.save(vo);
		} else {
			// 更新
			List<DictPhaseDto> list = dictPhaseService.listByDictValue(vo.getDictValue());
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, Long> map = list.stream().collect(Collectors.toMap(DictPhaseDto::getDictValue, DictPhaseDto::getId));
				if (map.containsKey(vo.getDictValue()) && !map.get(vo.getDictValue()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.PHASE_NAME_DATA_REPEAT.getMessage());
				}
			}

			dto = dictPhaseService.update(vo);
		}

		return dto;
	}

	public List<DictPhaseDto> list(DictPhaseListVo vo) {
		//只有集团的可以编辑
		boolean  editFlag=false;
		Dept currentUnit = DeptScopeUtil.getLoginDept();
		if(currentUnit.getParentId()==0l){
			editFlag=true;
		}
		List<DictPhaseDto> result = dictPhaseService.listByStatusOrValue(vo.getDictValue(), vo.getDictStatus(), vo.getNecessaryType());
		if (CollectionUtils.isEmpty(result)) {
			return null;
		}
		for (DictPhaseDto dto : result) {
			String necessaryTypeName = dictBizService.getValue(DictCodeEnum.NECESSARY_TYPE.getCode(), dto.getNecessaryType());
			dto.setNecessaryTypeName(necessaryTypeName);

			String dictStatusName = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dto.getDictStatus());
			dto.setDictStatusName(dictStatusName);
			dto.setEditFlag(editFlag);

		}
		return result;
	}

	public Boolean sort(DictPhaseSortVo vo) {
		if (CollectionUtils.isEmpty(vo.getList())) {
			return null;
		}

		for (int i = 0; i < vo.getList().size(); i++) {
			DictPhaseVo phaseVo = vo.getList().get(i);
			phaseVo.setSort(i);
			dictPhaseService.save(phaseVo);
		}
		return true;
	}

	public DictPhaseDto detail(Long id) {
		DictPhaseDto result = dictPhaseService.fetchById(id);

		String necessaryTypeName = dictBizService.getValue(DictCodeEnum.NECESSARY_TYPE.getCode(), result.getNecessaryType());
		result.setNecessaryTypeName(necessaryTypeName);

		String dictStatusName = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), result.getDictStatus());
		result.setDictStatusName(dictStatusName);

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(DictPhaseStatusVo vo) {

		DictPhaseDto dictPhaseDto = dictPhaseService.fetchById(vo.getId());
		// 必要节点不可停用
		if (dictPhaseDto.getNecessaryType().equals("0") && vo.getDictStatus().equals("1")) {
			throw new ServiceException(ExceptionEnum.LABEL_REQUIRED_STOP.getMessage());
		}

		return dictPhaseService.updateByStatus(vo.getId(), vo.getDictStatus());
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean type(DictPhaseTypeVo vo) {

		return dictPhaseService.updateByType(vo.getId(), vo.getNecessaryType());
	}

	@Transactional(rollbackFor = Exception.class)
	public List<DictDeleteDto> delete(DictPhaseDeleteVo vo) {
		List<DictDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			DictDeleteDto dto = new DictDeleteDto();

			DictPhaseDto dictPhaseDto = dictPhaseService.fetchById(id);
			dto.setDictValue(dictPhaseDto.getDictValue());

			// 校验是否引用
			if (dictPhaseDto.getNecessaryType().equals("0")) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.LABEL_REQUIRED_REFERENCE.getMessage());
				result.add(dto);
				continue;
			}

			// 校验是否引用
			Boolean check = plExternalReferenceService.isReference(DictEnum.PP.getCode(), dictPhaseDto.getDictKey());
			if (check) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.PROJECT_LIBRARY_REFERENCE_DATA.getMessage());
				result.add(dto);
				continue;
			}


			Boolean flag = dictPhaseService.deleteById(id);
			dto.setResult(flag);

			result.add(dto);
		}

		return result;
	}
}
