/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目阶段字典表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("dict_phase")
@EqualsAndHashCode(callSuper = false)
public class DictPhase extends BaseCrudEntity {

	/**
	 * 字典值
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dictKey;
	/**
	 * 字典名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dictValue;
	/**
	 * 是否必要节点，业务字典necessary_type
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String necessaryType;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String outputDocument;
	/**
	 * 排序
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer sort;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 状态，业务字典enable_type
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dictStatus;

}
