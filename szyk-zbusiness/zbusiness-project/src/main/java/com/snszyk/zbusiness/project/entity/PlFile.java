/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库项目文档表实体类
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlFile extends BaseCrudEntity {

	/**
	* 项目id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	* 附件id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long attachId;
	/**
	* 业务id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long businessId;
	/**
	* 业务类型
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String businessType;


}
