/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.project.dto.ProjectMemberDto;
import com.snszyk.zbusiness.project.entity.ProjectMember;
import com.snszyk.zbusiness.project.mapper.ProjectMemberMapper;
import com.snszyk.zbusiness.project.service.IProjectMemberService;
import com.snszyk.zbusiness.project.vo.ProjectMemberVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * ProjectMemberServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectMemberServiceImpl extends BaseCrudServiceImpl<ProjectMemberMapper, ProjectMember, ProjectMemberDto, ProjectMemberVo> implements IProjectMemberService {


	@Override
	public List<ProjectMemberDto> listByProjectId(Long projectId) {
		LambdaQueryWrapper<ProjectMember> queryWrapper = Wrappers.<ProjectMember>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectMember::getProjectId, projectId);

		List<ProjectMember> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectMemberDto.class));
	}

	@Override
	public int deleteByProjectId(Long projectId) {
		LambdaQueryWrapper<ProjectMember> queryWrapper = Wrappers.<ProjectMember>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectMember::getProjectId, projectId);

		return baseMapper.delete(queryWrapper);
	}
}
