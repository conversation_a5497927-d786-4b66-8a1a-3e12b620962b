/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目评审意见表实体类
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectExpertComment extends BaseCrudEntity {

	/**
	* 专家分配id
	*/

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectExpertId;
	/**
	* 专家id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long expertId;
	/**
	* 项目id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	* 专家用户id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long userId;
	/**
	* 专家用户姓名
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String userName;

	/**
	* 评审意见
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String reviewComment;
	/**
	* 评审时间
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime reviewTime;
	/**
	* 转发时间
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime sendTime;


}
