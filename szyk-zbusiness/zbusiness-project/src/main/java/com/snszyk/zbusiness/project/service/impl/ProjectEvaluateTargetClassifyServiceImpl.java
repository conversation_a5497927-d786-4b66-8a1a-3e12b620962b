/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.project.dto.ProjectEvaluateTargetClassifyDto;
import com.snszyk.zbusiness.project.entity.ProjectEvaluateTargetClassify;
import com.snszyk.zbusiness.project.mapper.ProjectEvaluateTargetClassifyMapper;
import com.snszyk.zbusiness.project.service.IProjectEvaluateTargetClassifyService;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateTargetClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * ProjectEvaluateTargetClassifyServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectEvaluateTargetClassifyServiceImpl extends BaseCrudServiceImpl<ProjectEvaluateTargetClassifyMapper, ProjectEvaluateTargetClassify, ProjectEvaluateTargetClassifyDto, ProjectEvaluateTargetClassifyVo> implements IProjectEvaluateTargetClassifyService {


	@Override
	public List<ProjectEvaluateTargetClassifyDto> listByEvaluateId(Long projectEvaluateId) {
		LambdaQueryWrapper<ProjectEvaluateTargetClassify> queryWrapper = Wrappers.<ProjectEvaluateTargetClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectEvaluateId), ProjectEvaluateTargetClassify::getProjectEvaluateId, projectEvaluateId)
			.orderByAsc(ProjectEvaluateTargetClassify::getClassifyId);
		List<ProjectEvaluateTargetClassify> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectEvaluateTargetClassifyDto.class));
	}

	@Override
	public boolean updateByScore(Long id, BigDecimal finalScore) {
		boolean result = this.lambdaUpdate()
			.eq(ProjectEvaluateTargetClassify::getId, id)
			.set(ProjectEvaluateTargetClassify::getFinalScore, finalScore)
			.update();

		return result;
	}

	@Override
	public int deleteByEvaluateId(Long projectEvaluateId) {
		LambdaQueryWrapper<ProjectEvaluateTargetClassify> queryWrapper = Wrappers.<ProjectEvaluateTargetClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectEvaluateId), ProjectEvaluateTargetClassify::getProjectEvaluateId, projectEvaluateId);

		return baseMapper.delete(queryWrapper);
	}
}
