/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.mapper;

import com.snszyk.zbusiness.project.entity.ProjectBatchNo;
import com.snszyk.zbusiness.project.vo.ProjectBatchNoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 项目批次号 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface ProjectBatchNoMapper extends BaseMapper<ProjectBatchNo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param projectBatchNo
	 * @return
	 */
	List<ProjectBatchNoVo> selectProjectBatchNoPage(IPage page, ProjectBatchNoVo projectBatchNo);

}
