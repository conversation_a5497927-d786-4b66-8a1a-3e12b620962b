package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.project.dto.ProjectEvaluateTargetClassifyDto;
import com.snszyk.zbusiness.project.service.IProjectEvaluateTargetClassifyService;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateTargetClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ProjectEvaluateTargetClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectEvaluateTargetClassifyService extends BaseCrudLogicService<ProjectEvaluateTargetClassifyDto, ProjectEvaluateTargetClassifyVo> {

	private final IProjectEvaluateTargetClassifyService projectEvaluateTargetClassifyService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectEvaluateTargetClassifyService;
	}


}
