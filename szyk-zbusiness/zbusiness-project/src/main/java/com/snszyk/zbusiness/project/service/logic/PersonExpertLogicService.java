/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.utils.DictUtil;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.project.dto.PersonExpertDto;
import com.snszyk.zbusiness.project.dto.PersonExpertGroupDto;
import com.snszyk.zbusiness.project.dto.ProjectExpertDto;
import com.snszyk.zbusiness.project.service.IPersonExpertGroupService;
import com.snszyk.zbusiness.project.service.IPersonExpertService;
import com.snszyk.zbusiness.project.service.IProjectExpertService;
import com.snszyk.zbusiness.project.vo.ExpertGroupPositionVo;
import com.snszyk.zbusiness.project.vo.PersonExpertPageVo;
import com.snszyk.zbusiness.project.vo.PersonExpertVo;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDeleteDto;
import com.snszyk.zbusiness.resource.vo.RsEquipmentDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsEquipmentStatusVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 专家库 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@AllArgsConstructor
@Service
public class PersonExpertLogicService extends BaseCrudLogicService<PersonExpertDto, PersonExpertVo> {

	private final IPersonExpertService personExpertService;
	private final IProjectExpertService projectExpertService;
	private final IPersonExpertGroupService groupService;
	private final IDeptService deptService;
	private final IPersonExpertGroupService personExpertGroupService;


	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.personExpertService;
	}

	public PersonExpertDto saveOrUpdate(PersonExpertVo v) {
		if (v.getExpertStatus() == null) {
			v.setExpertStatus(CommonConstant.ONE_STR);
		}
		//分组
		List<ExpertGroupPositionVo> groupPositionVoList = v.getGroupPositionVoList();
		String collect = groupPositionVoList.stream().map(e -> e.getGroupId() + "_" + e.getPosition()).
			collect(Collectors.joining(","));
		v.setGroupPosition(collect);
		//phone repeat
		List<PersonExpertDto> expertDtoList = personExpertService.listByPhone(v.getPhoneNo(), v.getId());
		if (CollectionUtil.isNotEmpty(expertDtoList)) {
			throw new com.snszyk.core.log.exception.ServiceException("联系方式不能重复!");
		}
		//所属同一个上级分组，工号进行唯一性校验
		if (CollectionUtil.isNotEmpty(groupPositionVoList)) {
			for (ExpertGroupPositionVo groupPositionVo : groupPositionVoList) {
				Long groupId = groupPositionVo.getGroupId();
				if(StringUtil.isBlank(v.getJobNo())){
					continue;
				}
				List<PersonExpertDto> personExpertDtoList = personExpertService.checkByGroupId(groupId, v.getJobNo(), v.getId());
				if (CollectionUtil.isNotEmpty(personExpertDtoList)) {
					throw new com.snszyk.core.log.exception.ServiceException("同一个分组,工号不能重复!");
				}
			}
		}
		return personExpertService.save(v);
	}

	public List<RsSoftwareDeleteDto> delete(RsEquipmentDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = new ArrayList<>();
		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			RsSoftwareDeleteDto deleteDto = new RsSoftwareDeleteDto();
			PersonExpertDto dto = personExpertService.fetchById(id);
			deleteDto.setName(dto.getUserName());
			deleteDto.setResult(true);
			//如已被引用则不允许删除
			List<ProjectExpertDto> projectExpertDtos = projectExpertService.listByExportId(id);
			if (CollectionUtil.isNotEmpty(projectExpertDtos)) {
				deleteDto.setResult(false);
				deleteDto.setMessage("已关联项目评审,无法删除!");
				result.add(deleteDto);
				continue;
			}

			// 删除附件数据
			Boolean flag = personExpertService.deleteById(id);
			deleteDto.setResult(flag);
			result.add(deleteDto);

		}
		return result;
	}

	/**
	 * 分页查询
	 * @param v
	 * @return
	 */
	public IPage<PersonExpertDto> pageList(PersonExpertPageVo v) {
		if (v.getGroupId() != null) {
			v.getGroupIdList().add(v.getGroupId());
		}
		//数据的范围
		if (Objects.equals(v.getUnitRange(), CommonConstant.TWO)) {
			//如果传的是groupId 级联查询
			if (v.getGroupId() != null) {
				List<PersonExpertGroupDto> expertGroupDtos = groupService.listChildren(v.getGroupId());
				expertGroupDtos.forEach(e -> v.getGroupIdList().add(e.getId()));
			}
		} else {
			// 根据勾选的查询
			if (Func.isNotBlank(v.getGroupIdListStr())) {
				String[] split = v.getGroupIdListStr().split(",");
				for (String s : split) {
					v.getGroupIdList().add(Long.valueOf(s));
				}
			}
			v.getGroupIdList().add(v.getGroupId());
		}
		//分页
		IPage<PersonExpertDto> page = this.personExpertService.pageList(v);
		List<PersonExpertDto> records = page.getRecords();
		Map<String, String> positionMap = DictUtil.getMap(DictBizEnum.EXPERT_POSITION);
		List<PersonExpertGroupDto> groupList = groupService.all();
		Map<Long, String> groupNameMap = groupList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getGroupName()));
		for (PersonExpertDto dto : records) {
			String deptName = dto.getDeptName();
			String orgName = dto.getOrgName();
			if (Func.isBlank(orgName)) {
				dto.setOrgName(deptName);
			}
			handlePosition(dto, groupNameMap, positionMap);
		}
		return page;
	}

	public PersonExpertDto detail(Long id) {
		PersonExpertDto detail = personExpertService.detail(id);
		if (detail == null) {
			return detail;
		}
		//所属单位
		String deptName = detail.getDeptName();
		String orgName = detail.getOrgName();
		if (Func.isBlank(orgName)) {
			detail.setOrgName(deptName);
		}
		//分组职位
		Map<String, String> positionMap = DictUtil.getMap(DictBizEnum.EXPERT_POSITION);
		List<PersonExpertGroupDto> groupList = groupService.all();
		Map<Long, String> groupNameMap = groupList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getGroupName()));
		handlePosition(detail, groupNameMap, positionMap);
		return detail;
	}

	private static void handlePosition(PersonExpertDto detail, Map<Long, String> groupNameMap, Map<String, String> positionMap) {
		String groupPositionId = detail.getGroupPosition();
		if (Func.isNotBlank(groupPositionId)) {
			String[] split = groupPositionId.split(",");
			if (split.length > 0) {
				List<ExpertGroupPositionVo> groupPositionVoList = new ArrayList<>();
				for (String s : split) {
					String[] split1 = s.split("_");
					ExpertGroupPositionVo expertGroupPositionVo = new ExpertGroupPositionVo();
					//分组id
					String groupId = split1[0];
					expertGroupPositionVo.setGroupId(Long.valueOf(groupId));
					//分组name
					String groupName = groupNameMap.get(Long.valueOf(groupId));
					expertGroupPositionVo.setGroupName(groupName);
					//职位
					String position = split1[1];
					String positionName = positionMap.get(position);
					expertGroupPositionVo.setPosition(position);
					expertGroupPositionVo.setPositionName(positionName);
					groupPositionVoList.add(expertGroupPositionVo);
				}
				detail.setGroupPositionVoList(groupPositionVoList);
			}

		}
	}

	/**
	 * 启用
	 * @param vo
	 * @return
	 */
	public Boolean status(RsEquipmentStatusVo vo) {
		return this.personExpertService.updateStatus(vo);
	}
}
