package com.snszyk.zbusiness.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.service.IPlBaseService;
import com.snszyk.zbusiness.project.service.logic.ProjectProgressNewService;
import com.snszyk.zbusiness.project.service.logic.ProjectProgressService;
import com.snszyk.zbusiness.project.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目进度 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/project_progress_new")
@Api(value = "项目进度（新）", tags = "项目进度（新）")
@Slf4j
public class ProjectProgressNewController extends BaseCrudController {

	private final ProjectProgressNewService projectProgressService;


	@PostMapping("/page")
	@ApiOperation(value = "分页")
	public R<IPage<ProjectBasePageDto>> page(@RequestBody ProjectProgressPageNewVo vo) {
		IPage<ProjectBasePageDto> pageQueryResult = projectProgressService.page(vo);
		return R.data(pageQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IPlBaseService.class )
	public R<ProjectProgressDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		ProjectProgressDto baseCrudDto = projectProgressService.detail(id);
		return R.data(baseCrudDto);
	}

	@GetMapping("/detail/edit")
	@ApiOperation(value = "根据ID获取数据（编辑查询）")
	@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IPlBaseService.class )
	public R<ProjectProgressDto> detailEdit(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		ProjectProgressDto baseCrudDto = projectProgressService.detailEdit(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存")
	public R<ProjectProgressDto> save(@RequestBody ProjectProgressNewVo vo) {
		ProjectProgressDto baseCrudDto = projectProgressService.save(vo);
		return R.data(baseCrudDto);
	}

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
