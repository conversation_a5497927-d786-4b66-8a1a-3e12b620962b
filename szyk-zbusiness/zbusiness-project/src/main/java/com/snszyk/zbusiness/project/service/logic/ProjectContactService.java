package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.project.dto.ProjectContactDto;
import com.snszyk.zbusiness.project.service.IProjectContactService;
import com.snszyk.zbusiness.project.vo.ProjectContactVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ProjectContactService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectContactService extends BaseCrudLogicService<ProjectContactDto, ProjectContactVo> {

	private final IProjectContactService projectContactService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectContactService;
	}


}
