/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目分配专家表实体类
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectExpert extends BaseCrudEntity {


	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long sendOrgId;
	/**
	* 专家分组id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long expertGroupId;
	/**
	* 专家id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long expertId;
	/**
	* 项目id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	* 用户id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long userId;
	/**
	* 用户姓名
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String userName;
	/**
	* 转发人id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long sendUserId;
	/**
	* 转发人姓名
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String sendUserName;
	/**
	* 转发时间
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime sendTime;
	/**
	* 是否线上：0是1否
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Boolean onlineStatus;
	/**
	* 评审状态，数据字典
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String reviewStatus;

	/**
	 * 是否失效
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Boolean isInvalid;
}
