package com.snszyk.zbusiness.project.designpattern.strategy.impl;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.project.designpattern.factory.DictTypeFactory;
import com.snszyk.zbusiness.project.designpattern.strategy.DictTypeStrategy;
import com.snszyk.zbusiness.project.service.IPlBaseService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class DictPhaseStrategyImpl implements DictTypeStrategy, InitializingBean {
	@Resource
	private IPlBaseService plBaseService;

	@Override
	public Boolean isReference(String dictKey) {
		return ObjectUtil.isNotEmpty(plBaseService.getByDictKey(null, null, dictKey, null));
	}

	@Override
	public void afterPropertiesSet() {
		DictTypeFactory.register(DictEnum.PP.getCode(), this);
	}
}
