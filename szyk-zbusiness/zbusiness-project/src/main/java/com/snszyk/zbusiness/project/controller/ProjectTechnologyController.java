/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.ProjectTechnologyDto;
import com.snszyk.zbusiness.project.service.logic.ProjectTechnologyLogicService;
import com.snszyk.zbusiness.project.vo.ProjectTechnologyPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 科技项目库 控制器
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-project/projecttechnology")
@Api(value = "科技项目库", tags = "科技项目库接口")
public class ProjectTechnologyController extends BaseCrudController {

	// private final IProjectTechnologyService projectTechnologyService;

	private final ProjectTechnologyLogicService projectTechnologyLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return projectTechnologyLogicService;
	}



	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "ProjectTechnologyVo")
	public R<IPage<ProjectTechnologyDto>> pageList(ProjectTechnologyPageVo v) {
		IPage<ProjectTechnologyDto> pageQueryResult =projectTechnologyLogicService.pageList(v);
		return R.data(pageQueryResult);
	}


	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<ProjectTechnologyDto> detail(Long id) {
		ProjectTechnologyDto baseCrudDto = projectTechnologyLogicService.detail(id);
		return R.data(baseCrudDto);
	}


}
