package com.snszyk.zbusiness.project.service.logic;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.enums.DictCommonEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.service.IDictMapService;
import com.snszyk.zbusiness.dict.service.IDictPhaseService;
import com.snszyk.zbusiness.project.designpattern.factory.DictTypeFactory;
import com.snszyk.zbusiness.project.designpattern.strategy.DictContext;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.enums.*;
import com.snszyk.zbusiness.project.service.*;
import com.snszyk.zbusiness.project.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class IPlExternalReferenceLogicService implements IPlExternalReferenceService {

	private final IPlBaseService plBaseService;

	private final IPlInitiationService plInitiationService;

	private final IPlStageService plStageService;

	private final IPlFileService plFileService;

	private final IPlContactService plContactService;

	private final IPlDispatchLogService plDispatchLogService;

	private final IPlMemberService plMemberService;

	private final IPlFundPlanService plFundPlanService;

	private final IAttachService attachService;

	private final IDictMapService dictMapService;

	private final IDictPhaseService dictPhaseService;

	private final IProjectProgressService projectProgressService;

	/*private final IPlApprovalOpinionService plApprovalOpinionService;

	private final IProjectApprovalOpinionService projectApprovalOpinionService;*/

	@Override
	public Boolean isReference(String dictBizKey, String dictKey) {
		return new DictContext(DictTypeFactory.getDictTypeStrategy(dictBizKey)).handle(dictKey);
	}

	/**
	 * 同步项目进度/验收数据
	 * v1.5项目验收的stage 同步到项目进度了
	 *
	 * @param projectProgressDto
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void synchronizeProjectStage(ProjectProgressDto projectProgressDto) {
		if (ObjectUtil.isEmpty(projectProgressDto.getStageList())) {
			return;
		}

		if (projectProgressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode())) {
			// 项目节点
			plBaseService.updateByProjectNode(projectProgressDto.getProjectId(), ProjectNodeEnum.PROJECT_ACCEPTANCE.getCode());
			// 项目验收
			plBaseService.updateByAcceptanceStatus(projectProgressDto.getProjectId(), AcceptanceStatusEnum.ACCEPTED.getCode());
		}

		// 删除历史数据
		plStageService.deleteByProjectId(projectProgressDto.getProjectId());

		if (CollectionUtils.isEmpty(projectProgressDto.getStageList())) {
			return;
		}

		// 更新项目阶段
		List<ProjectStageDto> filterList = projectProgressDto.getStageList().stream().filter(item -> item.getActualStartTime() != null).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(filterList)) {
			List<ProjectStageDto> stageList = filterList.stream().sorted(Comparator.comparing(ProjectStageDto::getStageSort, Comparator.reverseOrder())).collect(Collectors.toList());

			plBaseService.updateByProjectPhase(projectProgressDto.getProjectId(), stageList.get(0).getStageKey(), stageList.get(0).getStageName());
		}

		if (!CollectionUtils.isEmpty(projectProgressDto.getStageList())) {
			// 删除交付物历史数据
			plFileService.deleteByProjectId(projectProgressDto.getProjectId(), null, ProjectFileEnum.PROJECT_DELIVERABLES.getCode());
		}


		for (ProjectStageDto stageDto : projectProgressDto.getStageList()) {
			PlStageVo plStageVo = BeanUtil.copyProperties(stageDto, PlStageVo.class);

			plStageVo.setId(null);
			plStageVo.setProjectStage(stageDto.getStageKey());
			plStageVo.setProjectStageName(stageDto.getStageName());
			plStageVo.setProjectStageSort(stageDto.getStageSort());

			PlStageDto plStageDto = plStageService.save(plStageVo);

			// 交付物
			if (CollectionUtils.isEmpty(stageDto.getFileIdList())) {
				continue;
			}

			List<PlFileVo> fileVoList = new ArrayList<>();

			for (Long fileId : stageDto.getFileIdList()) {
				PlFileVo fileVo = new PlFileVo();
				fileVo.setAttachId(fileId);
				fileVo.setProjectId(projectProgressDto.getProjectId());
				fileVo.setBusinessId(plStageDto.getId());
				fileVo.setBusinessType(ProjectFileEnum.PROJECT_DELIVERABLES.getCode());
				fileVoList.add(fileVo);
			}

			// 批量添加项目库附件
			plFileService.saveBatch(fileVoList);
		}
	}

	/**
	 * 同步项目申报数据
	 *
	 * @param projectBaseDto
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void synchronizeProject(ProjectBaseDto projectBaseDto) {

		// 项目数据
		PlBaseVo baseVo = new PlBaseVo();
		BeanUtil.copyProperties(projectBaseDto, baseVo);
		baseVo.setId(projectBaseDto.getId());
		baseVo.setProjectStatus(ProjectStatusEnum.PROJECT_STATUS_0.getDictKey());
		baseVo.setProjectNode(ProjectNodeEnum.PROJECT_APPROVAL.getCode());
		baseVo.setAcceptanceStatus(AcceptanceStatusEnum.NOT_ACCEPTED.getCode());
		baseVo.setCreateDept(projectBaseDto.getCreateDept());
		baseVo.setCreateUser(projectBaseDto.getCreateUser());

		boolean result = plBaseService.insert(baseVo);

		// 项目立项数据
		PlInitiationVo initiationVo = new PlInitiationVo();
		BeanUtil.copyProperties(projectBaseDto.getIntroduceDto(), initiationVo);
		initiationVo.setId(null);
		initiationVo.setProjectId(projectBaseDto.getId());
		initiationVo.setPlanStartDate(projectBaseDto.getPlanStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
		initiationVo.setPlanCompleteDate(projectBaseDto.getPlanCompleteDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
		initiationVo.setPlanTenderDate(projectBaseDto.getPlanTenderDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

		plInitiationService.save(initiationVo);

		// 项目联系人数据
		PlContactVo contactVo = new PlContactVo();
		BeanUtil.copyProperties(projectBaseDto.getContactDto(), contactVo);
		contactVo.setId(null);
		contactVo.setProjectId(projectBaseDto.getId());

		plContactService.save(contactVo);

		// 项目成员数据
		if (!CollectionUtils.isEmpty(projectBaseDto.getMemberList())) {
			for (ProjectMemberDto memberDto : projectBaseDto.getMemberList()) {
				PlMemberVo memberVo = new PlMemberVo();
				BeanUtil.copyProperties(memberDto, memberVo);
				memberVo.setId(null);
				memberVo.setProjectId(projectBaseDto.getId());

				plMemberService.save(memberVo);
			}
		}

		// 项目资金计划
		PlFundPlanVo fundPlanVO = new PlFundPlanVo();
		BeanUtil.copyProperties(projectBaseDto, fundPlanVO);
		fundPlanVO.setId(null);
		fundPlanVO.setProjectId(projectBaseDto.getId());

		plFundPlanService.save(fundPlanVO);

		// 附件
		if (!CollectionUtils.isEmpty(projectBaseDto.getFileIdList())) {

			List<PlFileVo> fileVoList = new ArrayList<>();

			for (Long fileId : projectBaseDto.getFileIdList()) {
				PlFileVo fileVo = new PlFileVo();
				fileVo.setAttachId(fileId);
				fileVo.setProjectId(projectBaseDto.getId());
				fileVo.setBusinessId(projectBaseDto.getId());
				fileVo.setBusinessType(ProjectFileEnum.PROJECT_FILE.getCode());
				fileVoList.add(fileVo);
			}

			// 批量添加项目库附件
			plFileService.saveBatch(fileVoList);
		}
		//项目审查数据
		/*List<ProjectApprovalOpinionDto> projectApprovalOpinionDtos = projectApprovalOpinionService.fetchByIdProject(projectBaseDto.getId());
		if(CollectionUtils.isNotEmpty(projectApprovalOpinionDtos)){
			projectApprovalOpinionDtos.forEach(dto ->{
				PlApprovalOpinionVo plApprovalOpinionVo = BeanUtil.copyProperties(dto,PlApprovalOpinionVo.class);
				plApprovalOpinionVo.setId(null);
				plApprovalOpinionService.save(plApprovalOpinionVo);
			});
		}*/

	}

	/**
	 * 同步项目调度数据
	 *
	 * @param projectDispatchDto
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void synchronizeProjectDispatch(ProjectDispatchDto projectDispatchDto) {
		if (CollectionUtils.isEmpty(projectDispatchDto.getList())) {
			return;
		}

		for (ProjectDispatchDetailDto detailDto : projectDispatchDto.getList()) {
			PlDispatchLogVo vo = new PlDispatchLogVo();
			vo.setProjectId(detailDto.getProjectId());
			vo.setDispatchId(projectDispatchDto.getId());
			vo.setDispatchNo(projectDispatchDto.getDispatchNo());
			vo.setPeriodName(projectDispatchDto.getPeriodName());
			vo.setDispatchDeadline(projectDispatchDto.getDispatchDeadline());
			vo.setSubmitTime(detailDto.getSubmitTime());

			if (ObjectUtil.isNotEmpty(detailDto.getSubmitTime())) {
				if (projectDispatchDto.getDispatchDeadline().getTime() >= detailDto.getSubmitTime().getTime()) {
					vo.setDispatchIntensity(DispatchIntensityEnum.NORMAL.getCode());
				}
				if (projectDispatchDto.getDispatchDeadline().getTime() < detailDto.getSubmitTime().getTime()) {
					vo.setDispatchIntensity(DispatchIntensityEnum.DELAY.getCode());
				}
			}

			vo.setProjectPhase(detailDto.getProjectPhase());
			vo.setThisQuarterProgress(detailDto.getThisQuarterProgress());
			vo.setPushProgress(detailDto.getPushProgress());
			vo.setMainQuestion(detailDto.getMainQuestion());
			vo.setNextQuarterPlan(detailDto.getNextQuarterPlan());

			plDispatchLogService.save(vo);
		}
	}

	/**
	 * 根据项目id查询项目信息
	 *
	 * @param projectId
	 * @return
	 */
	@Override
	public PlInfo4ProjectProgressDto getPlInfoById(Long projectId) {
		// 查询基础信息
		PlBaseDto plBaseDto = plBaseService.fetchById(projectId);
		if(plBaseDto==null){
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		dictConvert(Arrays.asList(plBaseDto));

		// 查询联系人信息
		PlContactVo plContactVo = new PlContactVo();
		plContactVo.setProjectId(projectId);
		PlContactDto plContactDto = plContactService.fetchOne(plContactVo);

		// 查询联系人信息
		PlFundPlanVo plFundPlanVo = new PlFundPlanVo();
		plFundPlanVo.setProjectId(projectId);
		PlFundPlanDto plFundPlanDto = plFundPlanService.fetchOne(plFundPlanVo);

		// 查询项目进度信息
		PlStageVo stageVo = new PlStageVo();
		stageVo.setProjectId(projectId);
		//项目阶段信息
		List<PlStageDto> stageDtoList = plStageService.list(stageVo);
		List<PlStageShowDto> showDtos = new LinkedList<>();
		//v1.5  项目验收申请通过后，项目进度提报中的项目验收阶段以及之前前的阶段不能再更新
		ProjectProgressVo projectProgressVo = new ProjectProgressVo();

		projectProgressVo.setProjectId(projectId);
		projectProgressVo.setProgressStatus(ProgressStatusEnum.PASS.getCode());
		List<ProjectProgressDto> projectProgressDtoList = projectProgressService.listByProjectIdStatus(projectId, ProgressStatusEnum.PASS.getCode());
		boolean haveAccept;

		if (CollectionUtil.isNotEmpty(projectProgressDtoList)) {
			haveAccept = true;
		} else {
			haveAccept = false;
		}

		if (ObjectUtil.isNotEmpty(stageDtoList)) {

			Optional<PlStageDto> first = stageDtoList.stream().filter(e -> ProjectStageEnum.PROJECT_ACCEPT.getDictKey().equals(e.getProjectStage())).findFirst();
			Integer stageSort;
			if (first.isPresent()) {
				PlStageDto plStageDto = first.get();
				if (StringUtil.isNotBlank(plStageDto.getProjectStageSort())) {
					stageSort = Integer.valueOf(plStageDto.getProjectStageSort());
				} else {
					stageSort = null;
				}
			} else {
				stageSort = null;
			}
			List<DictPhaseDto> phaseList = dictPhaseService.listAll();
			Map<String, DictPhaseDto> phaseMap = new HashMap<>();
			if (!org.springframework.util.CollectionUtils.isEmpty(phaseList)) {
				phaseMap.putAll(phaseList.stream().collect(Collectors.toMap(DictPhaseDto::getDictKey, Function.identity())));
			}
			showDtos = stageDtoList.stream().map(d -> {
				PlStageShowDto dto = BeanUtil.copyProperties(d, PlStageShowDto.class);
				dto.setSort(d.getProjectStageSort());
				dto.setCanEdit(CommonConstant.ONE);
				//v1.5 验收通过后验收阶段和之前得阶段不可以修改
				if (haveAccept && stageSort != null && dto.getSort() != null && Integer.parseInt(dto.getSort()) < stageSort) {
					dto.setCanEdit(CommonConstant.ZERO);
				}
				PlFileVo fileVo = new PlFileVo();
				fileVo.setProjectId(d.getProjectId());
				fileVo.setBusinessId(d.getId());
				List<PlFileDto> fileDtoList = plFileService.list(fileVo);
				if (ObjectUtil.isNotEmpty(fileDtoList)) {
					// 映射附件id集合
					List<Long> attachIdList = fileDtoList.stream().map(PlFileDto::getAttachId).collect(Collectors.toList());
					// 交付物集合
					List<Attach> attachList = attachService.listByFileIds(attachIdList);
					dto.setFileList(attachList);
				}
				// 必要节点
				if (!StringUtils.isEmpty(d.getProjectStage()) && !ObjectUtils.isEmpty(phaseMap) && phaseMap.containsKey(d.getProjectStage())) {
					dto.setNecessaryType(phaseMap.get(d.getProjectStage()).getNecessaryType());
				}
				dto.setStageKey(d.getProjectStage());
				dto.setStageName(d.getProjectStageName());
				dto.setSort(d.getProjectStageSort());
				return dto;
			}).collect(Collectors.toList());
		}

		PlInfo4ProjectProgressDto dto = new PlInfo4ProjectProgressDto();
		dto.setBaseDto(plBaseDto);
		dto.setContactDto(plContactDto);
		dto.setFundPlanDto(plFundPlanDto);
		dto.setStageDtoList(showDtos);

		return dto;

	}

	/**
	 * 根据建设单位id查询项目库信息
	 *
	 * @param constructionUnitId
	 * @return
	 */
	@Override
	public List<PlBaseDto> listPlBaseByConstructionUnitId(Long constructionUnitId) {
		PlBaseVo baseVo = new PlBaseVo();
		baseVo.setConstructionUnitId(constructionUnitId);
		return plBaseService.list(baseVo);
	}

	public void dictConvert(List<PlBaseDto> list) {
		if (ObjectUtil.isEmpty(list)) {
			return;
		}
		// 查询专业分类字典
		Map<String, String> projectMajorDictMap = dictMapService.getProjectMajorDictMap();
		// 查询项目阶段字典
		Map<String, String> projectPhaseDictMap = dictMapService.getProjectPhaseDictMap();
		// 查询项目分类字典
		Map<String, String> projectTypeMap = dictMapService.getCommonDictMap(DictEnum.PC.getCode());
		// 查询项目标签字典
		Map<String, String> projectLabelMap = dictMapService.getProjectLabelMap();
		list.forEach(d -> {
			d.setSpecialtyClassificationName(projectMajorDictMap.get(d.getSpecialtyClassification()));
			d.setProjectPhaseName(projectPhaseDictMap.get(d.getProjectPhase()));
			d.setProjectClassificationName(projectTypeMap.get(d.getProjectClassification()));
			d.setProjectStatusName(DictCommonEnum.getValue(DictCommonEnum.PROJECT_STATUS_0.getCode(), d.getProjectStatus()));
			d.setReviewStatusName(DictCommonEnum.getValue(DictCommonEnum.REVIEW_STATUS_0.getCode(), d.getReviewStatus()));
			// 项目标签key集合
			if (ObjectUtil.isNotEmpty(d.getProjectLabel())) {
				List<String> projectLabelKeyList = Arrays.asList(d.getProjectLabel().split(","));

				if (ObjectUtil.isNotEmpty(projectLabelKeyList)) {
					List<PlProjectLabel> projectLabelList = new ArrayList<>();

					projectLabelKeyList.forEach(k -> {
						PlProjectLabel projectLabel = new PlProjectLabel();
						projectLabel.setDictKey(k);
						projectLabel.setDictValue(projectLabelMap.get(k));
						projectLabelList.add(projectLabel);
					});
					d.setProjectLabelList(projectLabelList);
				}

			}


		});
	}
}
