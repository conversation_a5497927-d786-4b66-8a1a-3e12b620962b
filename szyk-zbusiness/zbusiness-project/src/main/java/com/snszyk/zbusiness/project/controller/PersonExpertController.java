/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.PersonExpertDto;
import com.snszyk.zbusiness.project.service.IPersonExpertService;
import com.snszyk.zbusiness.project.service.logic.PersonExpertLogicService;
import com.snszyk.zbusiness.project.vo.PersonExpertPageVo;
import com.snszyk.zbusiness.project.vo.PersonExpertVo;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDeleteDto;
import com.snszyk.zbusiness.resource.vo.RsEquipmentDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsEquipmentStatusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 专家库 控制器
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-project/personexpert")
@Api(value = "专家库", tags = "专家库接口")
public class PersonExpertController extends BaseCrudController {

	private final PersonExpertLogicService personExpertLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return personExpertLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "PersonExpertVo")
	public R<PersonExpertDto> saveOrUpdate(@RequestBody @Valid PersonExpertVo v) {
		PersonExpertDto baseCrudDto = personExpertLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "PersonExpertVo")
	public R<IPage<PersonExpertDto>> pageList(PersonExpertPageVo v) {
		IPage<PersonExpertDto> pageQueryResult = personExpertLogicService.pageList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 根据ID获取数据
	 */
	@Override
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IPersonExpertService.class )
	public R<BaseCrudDto> fetchById(Long id) {
		PersonExpertDto baseCrudDto =personExpertLogicService.detail(id);
		return R.data(baseCrudDto);
	}
	/**
	 * 删除
	 */
	@PostMapping("/delete")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "删除", notes = "id")
	public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsEquipmentDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = personExpertLogicService.delete(vo);
		return R.data(result);
	}
	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody RsEquipmentStatusVo vo) {
		Boolean result = personExpertLogicService.status(vo);
		return R.data(result);
	}

}
