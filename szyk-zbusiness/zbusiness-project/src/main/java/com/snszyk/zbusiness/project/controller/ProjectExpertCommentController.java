///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.project.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.snszyk.core.crud.controller.BaseCrudController;
//import com.snszyk.core.crud.dto.BaseCrudDto;
//import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.project.service.logic.ProjectExpertCommentLogicService;
//import com.snszyk.zbusiness.project.vo.ProjectExpertCommentVo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//
///**
// * 项目评审意见表 控制器
// *
// * <AUTHOR>
// * @since 2024-01-09
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("zbusiness-project/projectexpertcomment")
//@Api(value = "项目评审意见表", tags = "项目评审意见表接口")
//public class ProjectExpertCommentController extends BaseCrudController {
//
//	// private final IProjectExpertCommentService projectExpertCommentService;
//
//	private final ProjectExpertCommentLogicService projectExpertCommentLogicService;
//
//	@Override
//	protected BaseCrudLogicService fetchBaseLogicService() {
//		return projectExpertCommentLogicService;
//	}
//
//	/**
//	 * 保存
//	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "保存", notes = "ProjectExpertCommentVo")
//	public R<BaseCrudDto> save(@RequestBody ProjectExpertCommentVo v) {
//		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
//		return R.data(baseCrudDto);
//	}
//
//	/**
//	 * 分页
//	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "分页", notes = "ProjectExpertCommentVo")
//	public R<IPage<BaseCrudDto>> page(ProjectExpertCommentVo v) {
//		IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
//		return R.data(pageQueryResult);
//	}
//
//	/**
//	 * 列表
//	 */
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "列表", notes = "ProjectExpertCommentVo")
//	public R<List<BaseCrudDto>> list(ProjectExpertCommentVo v) {
//		List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
//		return R.data(listQueryResult);
//	}
//
//	/**
//	 * 获取单条
//	 */
//	@GetMapping("/fetchOne")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "获取单条数据", notes = "ProjectExpertCommentVo")
//	public R<BaseCrudDto> fetchOne(ProjectExpertCommentVo v) {
//		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
//		return R.data(baseCrudDto);
//	}
//
//	/**
//	 * 根据ID获取数据
//	 */
//	@Override
//	@GetMapping("/fetchById")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "根据ID获取数据", notes = "id")
//	public R<BaseCrudDto> fetchById(Long id) {
//		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
//		return R.data(baseCrudDto);
//	}
//
//	/**
//	 * 删除
//	 */
//	@Override
//	@PostMapping("/deleteById")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "删除", notes = "id")
//	public R<Boolean> deleteById(Long id) {
//		Boolean result = this.fetchBaseLogicService().deleteById(id);
//		return R.data(result);
//	}
//
//}
