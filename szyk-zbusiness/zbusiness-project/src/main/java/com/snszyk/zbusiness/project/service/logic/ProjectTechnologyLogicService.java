/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.zbusiness.project.dto.ProjectTechnologyDto;
import com.snszyk.zbusiness.project.service.IProjectTechnologyService;
import com.snszyk.zbusiness.project.vo.ProjectTechnologyPageVo;
import com.snszyk.zbusiness.project.vo.ProjectTechnologyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 科技项目库 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@AllArgsConstructor
@Service
public class ProjectTechnologyLogicService extends BaseCrudLogicService<ProjectTechnologyDto, ProjectTechnologyVo> {

	private final IProjectTechnologyService projectTechnologyService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectTechnologyService;
	}

	public IPage<ProjectTechnologyDto> pageList(ProjectTechnologyPageVo v) {
		v.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
		return projectTechnologyService.pageList(v);
	}

	public ProjectTechnologyDto detail(Long id) {
		return projectTechnologyService.detail(id);
	}
}
