<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.PersonExpertMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="personExpertResultMap" type="com.snszyk.zbusiness.project.entity.PersonExpert">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="group_position" property="groupPosition"/>
        <result column="attribute" property="attribute"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="job_no" property="jobNo"/>
        <result column="org_name" property="orgName"/>
        <result column="phone_no" property="phoneNo"/>
        <result column="expert_status" property="expertStatus"/>
    </resultMap>


    <select id="selectPersonExpertPage" resultMap="personExpertResultMap">
        select *
        from person_expert
        where is_deleted = 0
    </select>
    <select id="detail" resultType="com.snszyk.zbusiness.project.dto.PersonExpertDto">

        select t.id,
               t.group_position,
               t.attribute,
               t.user_id,
               t.user_name,
               t.job_no,
               t.org_name,
               t.phone_no,
               t.expert_status,
               t.create_user,
               t.create_dept,
               t.create_time,
               t.update_user,
               t.update_time,
               t.status,
               t.is_deleted,
               t.org_id,
               t1.dept_name,
               t2.dict_value as expertStatusName,
               t3.dict_value as attributeName

        from person_expert t
                 left join szyk_dept  t1 on t.org_id=t1.id
            left join szyk_dict_biz t2 on t.expert_status=t2.dict_key and t2.code='expert_status' and t2.is_deleted=0
            left join szyk_dict_biz t3 on t.attribute=t3.dict_key and t3.code='expert_attr' and t3.is_deleted=0
        where t.id = #{id}
          and t.is_deleted = 0
    </select>

    <select id="pageList" resultType="com.snszyk.zbusiness.project.dto.PersonExpertDto">

        select t.id, t.group_position, t.attribute, t.user_id, t.user_name, t.job_no, t.org_name, t.phone_no,
        t.expert_status, t.org_id, replace(t1.full_org_name,'顶级-','') as deptName,
        t2.dict_value as expertStatusName,
        t3.dict_value as attributeName from person_expert t
        left join szyk_dept t1 on t.org_id=t1.id and t1.is_deleted=0
        left join szyk_dict_biz t2 on t.expert_status=t2.dict_key and t2.code='expert_status' and t2.is_deleted=0
        left join szyk_dict_biz t3 on t.attribute=t3.dict_key and t3.code='expert_attr' and t3.is_deleted=0
        where t.is_deleted = 0
        <if test="v.attribute!= null and v.attribute !=''">
            and t.attribute = #{v.attribute}
        </if>

        <if test="v.jobNo!= null">
            and t.job_no like concat('%',#{v.jobNo},'%')
        </if>

        <if test="v.phoneNo!= null">
            and t.phone_no = #{v.phoneNo}
        </if>
        <if test="v.userName!= null and v.userName !=''">
            and t.user_name like concat('%',#{v.userName},'%')
        </if>
        <if test="v.expertStatus!= null and v.expertStatus!=''">
            and t.expert_status = #{v.expertStatus}
        </if>

        <if test="v.groupIdList!= null and v.groupIdList.size()>0">
            and (<foreach collection="v.groupIdList" item="item"
                          index="index" separator=" or ">
            t.group_position like concat('%', #{item}, '%')</foreach>)
        </if>
        order by t.create_time desc
    </select>

</mapper>
