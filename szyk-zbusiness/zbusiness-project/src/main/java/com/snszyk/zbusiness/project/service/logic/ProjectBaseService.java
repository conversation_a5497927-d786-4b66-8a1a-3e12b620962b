package com.snszyk.zbusiness.project.service.logic;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.enums.OperationTypeEnum;
import com.snszyk.common.utils.CommonUtil;
import com.snszyk.common.utils.DigitUtil;
import com.snszyk.core.crud.dto.BaseCrudSlimDto;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserInfo;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.service.IUserService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.system.vo.DeptVO;
import com.snszyk.task.constant.MsgContentConstant;
import com.snszyk.task.dto.SzykTaskDto;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.service.ISzykTaskService;
import com.snszyk.zbusiness.dict.dto.DictCommonDto;
import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import com.snszyk.zbusiness.dict.dto.DictMajorDto;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.service.IDictCommonService;
import com.snszyk.zbusiness.dict.service.IDictLabelService;
import com.snszyk.zbusiness.dict.service.IDictMajorService;
import com.snszyk.zbusiness.flow.dto.InstanceDto;
import com.snszyk.zbusiness.flow.enums.FirstProcessEnum;
import com.snszyk.zbusiness.flow.enums.InstanceStatusEnum;
import com.snszyk.zbusiness.flow.service.IInstanceService;
import com.snszyk.zbusiness.knowledge.service.IBaseLogicService;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.enums.*;
import com.snszyk.zbusiness.project.service.*;
import com.snszyk.zbusiness.project.util.ProjectCodePaddingUtil;
import com.snszyk.zbusiness.project.vo.*;
import com.snszyk.zbusiness.resource.enums.UnitLevelEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ProjectBaseService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */

@Service
@Slf4j
public class
ProjectBaseService extends BaseCrudLogicService<ProjectBaseDto, ProjectBaseVo> {

	private final Integer corLenvel = 1;
	private final Integer secLenvel = 2;

	private final IProjectBaseService projectBaseService;

	private final IProjectContactService projectContactService;

	private final IProjectIntroduceService projectIntroduceService;

	private final IProjectMemberService projectMemberService;

	private final IProjectFileService projectFileService;

	private final IAttachService attachService;

	private final IDictBizService dictBizService;

	private final IDictMajorService dictMajorService;

	private final IDictCommonService dictCommonService;

	private final IDictLabelService dictLabelService;

	private final IPlBaseService plBaseService;

	private final IUserService userService;

	private final IDeptService deptService;

	private final IPlExternalReferenceService plExternalReferenceService;

	private final IBaseLogicService baseLogicService;

	private final ProjectCodePaddingUtil projectCodePaddingUtil;

	private final ProjectBatchNoLogicService batchNoLogicService;

	private final ISzykTaskService szykTaskService;

	private final IPlFileService plFileService;

	private final IInstanceService instanceService;

	private final ProjectFileService projectLogicFileService;

	private final IProjectVersionService projectVersionService;

	@Value("${system.website-url}")
	private String websiteUrl;


	private final String projectUrl = "/#/project-manage/project-submit/submit-view/";


	public ProjectBaseService(IProjectBaseService projectBaseService, IProjectContactService projectContactService, IProjectIntroduceService projectIntroduceService, IProjectMemberService projectMemberService,
							  IProjectFileService projectFileService, IAttachService attachService, IDictBizService dictBizService, IDictMajorService dictMajorService, IDictCommonService dictCommonService,
							  IDictLabelService dictLabelService, IPlBaseService plBaseService,
							  IUserService userService, IDeptService deptService, IPlExternalReferenceService plExternalReferenceService,
							  IBaseLogicService baseLogicService, ProjectCodePaddingUtil projectCodePaddingUtil,
							  ProjectBatchNoLogicService batchNoLogicService, ISzykTaskService szykTaskService,
							  IPlFileService plFileService, IInstanceService instanceService, ProjectFileService projectLogicFileService, IProjectVersionService projectVersionService) {
		this.projectBaseService = projectBaseService;

		this.projectContactService = projectContactService;
		this.projectIntroduceService = projectIntroduceService;
		this.projectMemberService = projectMemberService;
		this.projectFileService = projectFileService;
		this.attachService = attachService;
		this.dictBizService = dictBizService;
		this.dictMajorService = dictMajorService;
		this.dictCommonService = dictCommonService;
		this.dictLabelService = dictLabelService;
		this.plBaseService = plBaseService;
		this.userService = userService;
		this.deptService = deptService;
		this.plExternalReferenceService = plExternalReferenceService;
		this.baseLogicService = baseLogicService;
		this.projectCodePaddingUtil = projectCodePaddingUtil;
		this.batchNoLogicService = batchNoLogicService;
		this.szykTaskService = szykTaskService;

		this.plFileService = plFileService;
		this.instanceService = instanceService;
		this.projectLogicFileService = projectLogicFileService;
		this.projectVersionService = projectVersionService;
	}

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectBaseService;
	}

	/**
	 * 获取实例服务
	 * @return IInstanceService
	 */
	public IInstanceService getInstanceService() {
		return this.instanceService;
	}

	public IPage<ProjectBasePageDto> page(ProjectBasePageVo vo) {

		Optional.ofNullable(vo.getReviewStatus()).ifPresent(e -> vo.setReviewStatus(e.replaceAll(",", "|")));
		// 超级管理员可以查看全部的
		vo.setAdmin(AuthUtil.isAdmin());
		Dept currentDept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		Dept currentUnit = SysCache.getDept(currentDept.getUnitId());
		IPage<ProjectBasePageDto> page = projectBaseService.pageList(vo, currentDept.getId(), currentUnit.getUnitLevel());
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		// 当前用户是否为集团的项目联络员

		// 专业分类
		List<DictMajorDto> majorList = dictMajorService.listByDictStatus(null);
		Map<String, DictMajorDto> majorMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(majorList)) {
			majorMap.putAll(majorList.stream().collect(Collectors.toMap(DictMajorDto::getDictKey, Function.identity())));
		}
		// 项目分类
		List<DictCommonDto> pcList = dictCommonService.listByDictStatus(DictEnum.PC.getCode(), null);
		Map<String, DictCommonDto> pcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pcList)) {
			pcMap.putAll(pcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		// 列支渠道
		List<DictCommonDto> dcList = dictCommonService.listByDictStatus(DictEnum.DC.getCode(), null);
		Map<String, DictCommonDto> dcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dcList)) {
			dcMap.putAll(dcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		// 项目标签
		List<DictLabelDto> labelList = dictLabelService.listByDictStatus(null);
		Map<String, DictLabelDto> labelMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(labelList)) {
			labelMap.putAll(labelList.stream().collect(Collectors.toMap(DictLabelDto::getDictKey, Function.identity())));
		}
		// 审查状态
		List<DictBiz> statusList = dictBizService.getList(DictBizEnum.PROJECT_REVIEW.getCode());
		Map<String, DictBiz> statusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(statusList)) {
			statusMap.putAll(statusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}
		// 对审查结论字典赋值

		Map<String, String> opinionMap = new HashMap<>();
		List<DictBiz> opinionList = dictBizService.getList(DictBizEnum.PROJECT_APPROVAL_OPINION.getCode());
		opinionMap = opinionList.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
		// 所有的本单位下的
		List<Dept> currentUnitAll = DeptScopeUtil.getCurrentUnitAll();
		List<Long> currentUnitAllDeptIdList = currentUnitAll.stream().map(e -> e.getId()).collect(Collectors.toList());


		// 优化...
		/*List<Long> collect = page.getRecords().stream().map(e -> e.getCreateUser()).collect(Collectors.toList());
		List<User> users = userService.listByIds(collect);
		Map<Long, User> userNameMap = users.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));*/
		// 是否为集团的联络人
		boolean headProjectContact = DeptScopeUtil.isHeadProjectContact();
		// 是否是二级单位的联络人
		boolean secondProjectContact = DeptScopeUtil.isSecondProjectContact();

		for (ProjectBasePageDto dto : page.getRecords()) {
			// 创建的部门
			Dept createDept = SysCache.getDept(dto.getCreateDept());
			// 部门登录的编辑权限

			String reviewStatus = dto.getReviewStatus();
			boolean statusEdit = false;
			if (ReviewStatusEnum.TO_BE_REPORTED.getCode().equals(reviewStatus) || ReviewStatusEnum.CANCEL.getCode().equals(reviewStatus)
				|| ReviewStatusEnum.RETURN.getCode().equals(reviewStatus)) {
				// 增加当前审批节点是0发起人时才可编辑
				if (Func.isEmpty(dto.getInstanceId())) {
					statusEdit = true;
				} else {
					InstanceDto instanceDto = instanceService.fetchById(dto.getInstanceId());
					if (instanceDto.getCurrFlowSort().equals(CommonConstant.ZERO)) {
						statusEdit = true;
					}
				}
			}

			if (statusEdit) {
				if (!DeptScopeUtil.isUnitLogin() && currentDept.getId().equals(createDept.getId())) {
					dto.setEditFlag(true);

				}
				// 单位登录的编辑权限
				if (DeptScopeUtil.isUnitLogin()) {
					if (currentUnitAllDeptIdList.contains(dto.getCreateDept())) {
						dto.setEditFlag(true);
					}
				}
			}
			// 作废标志
			if (ReviewStatusEnum.RETURN.getCode().equals(dto.getReviewStatus())
				&& FirstProcessEnum.STEP_NAME.getValue().equals(dto.getCurrStepName())
				&& AuthUtil.getUserId().equals(dto.getCreateUser())) {
				dto.setNullifyFlag(true);
			}
			// 补充材料的状态名
			String supplementStatus = dto.getSupplementStatus();
			String supplementStatusName = SupplementStatusEnum.getNameByCode(supplementStatus);
			dto.setSupplementStatusName(supplementStatusName);
			// v1.5补充材料的归档权限
			if (Objects.equals(dto.getSupplementStatus(), SupplementStatusEnum.REPORTED.getCode()) && headProjectContact) {
				dto.setPlaceOnFileFlag(true);
			} else {
				dto.setPlaceOnFileFlag(false);
			}

			// v1.5是否具有上传补资料的按钮
			// 新增加的按钮，审批结束后，审批结论为“原则同意立项”的项目，对应的提报部门的用户可看到此按钮
			// 1 原则同意立项 2 待提交状态或者已提交状态 3提报部门||二级单位的联络人
			Boolean pass = Objects.equals(dto.getCorpApproveResult(), ProjectApprovalOpinionEnum.PASS.getCode());
			Boolean reportStatus = Objects.equals(dto.getSupplementStatus(), SupplementStatusEnum.TO_DO_REPORT.getCode())
				|| Objects.equals(dto.getSupplementStatus(), SupplementStatusEnum.REPORTED.getCode());
			Boolean supplyDept = false;
			//是否是集团的
			if (UnitLevelEnum.UNIT_LEVEL_1.getCode().toString().equals(dto.getSupplyLevel())) {
				supplyDept = Objects.equals(dto.getCreateDept(), Long.valueOf(AuthUtil.getDeptId()));
				dto.setSupplementUploadFlag(pass && reportStatus && supplyDept);
			} else {
				supplyDept = Objects.equals(dto.getCompanyId(), Long.valueOf(AuthUtil.getDeptId()));
				dto.setSupplementUploadFlag(pass && reportStatus && supplyDept && secondProjectContact);
			}
			// 是否具有删除权限
			if (dto.getEditFlag() && ReviewStatusEnum.TO_BE_REPORTED.getCode().equals(reviewStatus)) {
				if (DeptScopeUtil.canDel(dto.getCreateDept(), dto.getCreateUser())) {
					dto.setDelFlag(true);
				}
			}

			// 专业分类
			if (!ObjectUtils.isEmpty(majorMap) && !ObjectUtils.isEmpty(dto.getSpecialtyClassification()) && majorMap.containsKey(dto.getSpecialtyClassification())) {
				dto.setSpecialtyClassificationName(majorMap.get(dto.getSpecialtyClassification()).getDictValue());
			}
			// 项目分类
			if (!ObjectUtils.isEmpty(pcMap) && !ObjectUtils.isEmpty(dto.getProjectClassification()) && pcMap.containsKey(dto.getProjectClassification())) {
				dto.setProjectClassificationName(pcMap.get(dto.getProjectClassification()).getDictValue());
			}
			// 列支渠道
			if (!ObjectUtils.isEmpty(dcMap) && !ObjectUtils.isEmpty(dto.getDistributionChannel()) && dcMap.containsKey(dto.getDistributionChannel())) {
				dto.setDistributionChannelName(dcMap.get(dto.getDistributionChannel()).getDictValue());
			}
			// 项目标签
			if (!ObjectUtils.isEmpty(labelMap) && !ObjectUtils.isEmpty(dto.getProjectLabel())) {
				List<PlProjectLabel> projectLabelList = new ArrayList();
				List<String> projectLabelByte = Arrays.asList(dto.getProjectLabel().split(","));
				for (String key : projectLabelByte) {
					if (labelMap.containsKey(key)) {
						PlProjectLabel plProjectLabel = new PlProjectLabel();
						plProjectLabel.setDictKey(labelMap.get(key).getDictKey());
						plProjectLabel.setDictValue(labelMap.get(key).getDictValue());
						projectLabelList.add(plProjectLabel);
					}
				}
				dto.setProjectLabelList(projectLabelList);
			}
			// 审查状态
			if (!ObjectUtils.isEmpty(statusMap) && !ObjectUtils.isEmpty(dto.getReviewStatus()) && statusMap.containsKey(dto.getReviewStatus())) {
				if (dto.getReviewStatus().equals(InstanceStatusEnum.RETURN_FLOW.getCode()) || dto.getReviewStatus().equals(InstanceStatusEnum.CHECKING.getCode())) {
					dto.setReviewStatusName(statusMap.get(dto.getReviewStatus()).getDictValue() + "(" + dto.getCurrStepName() + ")");
				} else {
					dto.setReviewStatusName(statusMap.get(dto.getReviewStatus()).getDictValue());
				}

			}

			// 创建人
			User user = UserCache.getUser(dto.getCreateUser());
			if (user != null) {
				dto.setCreateUserName(user.getRealName());
			}
			// 查询集团审查意见
			// 审核未通过，集团看到所有，二级单位看到二级
			if (!ReviewStatusEnum.PASS.getCode().equals(dto.getReviewStatus())) {
				if (currentUnit.getUnitLevel().equals(corLenvel)) {
				} else if (currentUnit.getUnitLevel().equals(secLenvel)) {
					dto.setCorpApproveRemark(null);
					dto.setCorpApproveResult(null);
				} else {
					dto.setCorpApproveRemark(null);
					dto.setCorpApproveResult(null);
					dto.setSecApproveRemark(null);
				}
			}
			if (StringUtil.isNotBlank(dto.getCorpApproveResult())) {
				dto.setCorpApproveResultName(opinionMap.get(dto.getCorpApproveResult()));
			}
			// 撤回标识，如果当前标识可撤回，当前状态是审核中并且当前人是创建人
			if (Func.isNotEmpty(dto.getCancelFlag()) && dto.getCancelFlag() && dto.getReviewStatus().equals(ReviewStatusEnum.UNDER_REVIEW.getCode()) && dto.getCreateUser().equals(AuthUtil.getUserId())) {
				dto.setCancelFlag(true);
			} else {
				dto.setCancelFlag(false);
			}
			/*if(StringUtil.isNotBlank(dto.getCurrStepName())){
				dto.setReviewStatusName(dto.getReviewStatusName() + "(" + dto.getCurrStepName() +")");
			}*/
			// 组织信息更新
			if (Func.isNotEmpty(dto.getCompanyId())) {
				dto.setCompanyName(SysCache.getDeptName(dto.getCompanyId()));
			}
			if (Func.isNotEmpty(dto.getConstructionUnitId())) {
				//建设单位如果是部门的话，需要加上单位名称
				Dept dept = SysCache.getDept(dto.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						dto.setConstructionUnitName(dept.getDeptName());
					} else {
						dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
				//单独处理
				//针对当前登录用户的业务范围是山东能源的做以下展示调整:公司名称是“山东能源”的，建设单位只显示部门就可以，展示的是山能的总部的数据。

				if (DeptScopeUtil.isHeadPerson() && Objects.equals(dto.getCompanyName(), "山东能源") && dept != null) {
					//如果是集团的，不显示部门
					dto.setConstructionUnitName(dept.getDeptName());
				}
				if (DeptScopeUtil.isHeadPerson() && !Objects.equals(dto.getCompanyName(), "山东能源") && dept != null) {
					//公司名称是二三四级单位的，建设单位展示到单位级别，不显示部门。
					dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()));
				}

			}
			if (Func.isNotEmpty(dto.getInvestmentSubjectId())) {
				dto.setInvestmentSubjectName(SysCache.getDeptName(dto.getInvestmentSubjectId()));
			}

		}


		return page;
	}


	public List<ProjectBaseDto> list(Long constructionUnitId) {
		List<ProjectBaseDto> list = projectBaseService.listByUnitNoStatus(constructionUnitId, ReviewStatusEnum.TO_BE_REPORTED.getCode());
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		// 获取最新组织信息
		list.forEach(dto -> {
			if (Func.isNotEmpty(dto.getCompanyId())) {
				dto.setCompanyName(SysCache.getDeptName(dto.getCompanyId()));
			}
			if (Func.isNotEmpty(dto.getConstructionUnitId())) {
				//建设单位如果是部门的话，需要加上单位名称
				Dept dept = SysCache.getDept(dto.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						dto.setConstructionUnitName(dept.getDeptName());
					} else {
						dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}
			if (Func.isNotEmpty(dto.getInvestmentSubjectId())) {
				dto.setInvestmentSubjectName(SysCache.getDeptName(dto.getInvestmentSubjectId()));
			}
			if (Func.isNotEmpty(dto.getLeadOrgId())) {
				dto.setLeadOrgName(SysCache.getDeptName(dto.getLeadOrgId()));
			}
		});
		return list;
	}

	public ProjectBaseDto detail(Long id) {
		Dept dept = deptService.getById(AuthUtil.getDeptId());
		dept = deptService.getById(dept.getUnitId());
		// ProjectBaseDto result = projectBaseService.fetchById(id);
		ProjectBaseDto result = projectBaseService.fetchByIdIgnoreDelete(id);
		if (result == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		// 项目介绍
		ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
		result.setIntroduceDto(introduceDto);

		// 联系人
		ProjectContactDto contactDto = projectContactService.getByProjectId(id);
		result.setContactDto(contactDto);

		// 项目成员
		List<ProjectMemberDto> memberList = projectMemberService.listByProjectId(id);
		if (!CollectionUtils.isEmpty(memberList)) {

			result.setMemberList(memberList);
		}

		// 附件
		List<ProjectFileDto> fileDtoList = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode());
		if (!CollectionUtils.isEmpty(fileDtoList)) {
			// 不可删除map,key-附件id，value-不可删除标志
			Map<Long, Long> undeletableMap = fileDtoList.stream().collect(Collectors.toMap(ProjectFileDto::getAttachId, ProjectFileDto::getUndeletable));

			List<Attach> attachList = attachService.listByFileIds(new ArrayList<>(undeletableMap.keySet()));
			if (!CollectionUtils.isEmpty(attachList)) {
				result.setFileList(attachList.stream()
					.map(attach -> {
						SzykAttachDto attachDto = BeanUtil.copy(attach, SzykAttachDto.class);
						attachDto.setUndeletable(undeletableMap.get(attach.getId()));
						return attachDto;
					}).sorted(Comparator.comparing(SzykAttachDto::getCreateTime).reversed()).collect(Collectors.toList()));
			}
		}

		// 专业分类
		List<DictMajorDto> majorList = dictMajorService.listByDictStatus(null);
		Map<String, DictMajorDto> majorMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(majorList)) {
			majorMap.putAll(majorList.stream().collect(Collectors.toMap(DictMajorDto::getDictKey, Function.identity())));
		}
		// 项目分类
		List<DictCommonDto> pcList = dictCommonService.listByDictStatus(DictEnum.PC.getCode(), null);
		Map<String, DictCommonDto> pcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pcList)) {
			pcMap.putAll(pcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		// 列支渠道
		List<DictCommonDto> dcList = dictCommonService.listByDictStatus(DictEnum.DC.getCode(), null);
		Map<String, DictCommonDto> dcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dcList)) {
			dcMap.putAll(dcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		// 项目标签
		List<DictLabelDto> labelList = dictLabelService.listByDictStatus(null);
		Map<String, DictLabelDto> labelMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(labelList)) {
			labelMap.putAll(labelList.stream().collect(Collectors.toMap(DictLabelDto::getDictKey, Function.identity())));
		}
		// 审查状态
		List<DictBiz> statusList = dictBizService.getList(DictBizEnum.PROJECT_REVIEW.getCode());
		Map<String, DictBiz> statusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(statusList)) {
			statusMap.putAll(statusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 创建人
		if (!StringUtils.isEmpty(result.getCreateUser())) {
			UserInfo user = userService.userInfo(result.getCreateUser());
			result.setCreateUserName(user == null ? null : user.getUser().getRealName());
		}

		// 专业分类
		if (!ObjectUtils.isEmpty(majorMap) && !ObjectUtils.isEmpty(result.getSpecialtyClassification()) && majorMap.containsKey(result.getSpecialtyClassification())) {
			result.setSpecialtyClassificationName(majorMap.get(result.getSpecialtyClassification()).getDictValue());
		}
		// 项目分类
		if (!ObjectUtils.isEmpty(pcMap) && !ObjectUtils.isEmpty(result.getProjectClassification()) && pcMap.containsKey(result.getProjectClassification())) {
			result.setProjectClassificationName(pcMap.get(result.getProjectClassification()).getDictValue());
		}
		// 列支渠道
		if (!ObjectUtils.isEmpty(dcMap) && !ObjectUtils.isEmpty(result.getDistributionChannel()) && dcMap.containsKey(result.getDistributionChannel())) {
			result.setDistributionChannelName(dcMap.get(result.getDistributionChannel()).getDictValue());
		}
		// v1.5获取项目标签
		// 项目标签
		if (!ObjectUtils.isEmpty(labelMap) && !ObjectUtils.isEmpty(result.getProjectLabel())) {
			List<PlProjectLabel> projectLabelList = new ArrayList();
			List<String> projectLabelByte = Arrays.asList(result.getProjectLabel().split(","));
			for (String key : projectLabelByte) {
				if (labelMap.containsKey(key)) {
					PlProjectLabel plProjectLabel = new PlProjectLabel();
					plProjectLabel.setDictKey(labelMap.get(key).getDictKey());
					plProjectLabel.setDictValue(labelMap.get(key).getDictValue());
					projectLabelList.add(plProjectLabel);
				}
			}
			result.setProjectLabelList(projectLabelList);
		}

		// 审查状态
		if (!ObjectUtils.isEmpty(statusMap) && !ObjectUtils.isEmpty(result.getReviewStatus()) && statusMap.containsKey(result.getReviewStatus())) {
			result.setReviewStatusName(statusMap.get(result.getReviewStatus()).getDictValue());
		}
		// 审查意见
		// 审核未通过，集团看到所有，二级单位看到二级
		if (!ReviewStatusEnum.PASS.getCode().equals(result.getReviewStatus())) {
			if (dept.getUnitLevel().equals(corLenvel)) {
			} else if (dept.getUnitLevel().equals(secLenvel)) {
				result.setCorpApproveRemark(null);
				result.setCorpApproveResult(null);
			} else {
				result.setCorpApproveRemark(null);
				result.setCorpApproveResult(null);
				result.setSecApproveRemark(null);
			}
		}
		// 对审查结论字典赋值
		if (org.apache.commons.lang3.StringUtils.isNotBlank(result.getCorpApproveResult())) {
			List<DictBiz> opinionList = dictBizService.getList(DictBizEnum.PROJECT_APPROVAL_OPINION.getCode());
			Map<String, String> opinionMap = opinionList.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
			result.setCorpApproveResultName(opinionMap.get(result.getCorpApproveResult()));
		}
		// 添加补充材料
		List<SzykAttachDto> supplementList = supplementList(id);
		result.setSupplementFileList(supplementList);

		// 获取最新组织信息
		if (Func.isNotEmpty(result.getCompanyId())) {
			result.setCompanyName(SysCache.getDeptName(result.getCompanyId()));
		}
		if (Func.isNotEmpty(result.getConstructionUnitId())) {
			//建设单位如果是部门的话，需要加上单位名称
			Dept dept1 = SysCache.getDept(result.getConstructionUnitId());
			if (Func.isNotEmpty(dept1)) {
				if (dept1.getUnitId().equals(dept1.getId())) {
					result.setConstructionUnitName(dept1.getDeptName());
				} else {
					result.setConstructionUnitName(SysCache.getDeptName(dept1.getUnitId()) + "/" + dept1.getDeptName());
				}
			}
			//单独处理
			//针对当前登录用户的业务范围是山东能源的做以下展示调整:公司名称是“山东能源”的，建设单位只显示部门就可以，展示的是山能的总部的数据。
			//公司名称是二三四级单位的，建设单位展示到单位级别，不显示部门。
			Dept dtoDept = SysCache.getDept(result.getConstructionUnitId());
			if (DeptScopeUtil.isHeadPerson() && Objects.equals(result.getCompanyName(), "山东能源")) {
				//如果是集团的，不显示部门

				Optional.ofNullable(dtoDept).ifPresent(e -> result.setConstructionUnitName(e.getDeptName()));
			}
			if (DeptScopeUtil.isHeadPerson() && !Objects.equals(result.getCompanyName(), "山东能源")) {
				//公司名称是二三四级单位的，建设单位展示到单位级别，不显示部门。
				if (dtoDept != null) {
					result.setConstructionUnitName(SysCache.getDeptName(dtoDept.getUnitId()));
				}
			}
		}
		if (Func.isNotEmpty(result.getInvestmentSubjectId())) {
			result.setInvestmentSubjectName(SysCache.getDeptName(result.getInvestmentSubjectId()));
		}
		if (Func.isNotEmpty(result.getLeadOrgId())) {
			result.setLeadOrgName(SysCache.getDeptName(result.getLeadOrgId()));
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProjectBaseDto save(ProjectBaseVo vo) {
		Long id = vo.getId();
		ProjectBaseDto oldProjectBase = null;
		if (id != null) {
			oldProjectBase = projectBaseService.fetchById(id);
			Integer year = oldProjectBase.getYear();
			Integer newYear = vo.getYear();
			//年份变更，重新计算批次,只有之前的批次不为空,并且现在的状态是审核中
			if (!Objects.equals(year, newYear) && year != null && Objects.equals(oldProjectBase.getReviewStatus(), ReviewStatusEnum.UNDER_REVIEW.getCode())) {
				Integer batchNo = projectBaseService.getMaxBatchNo(newYear);
				String batchName = "第" + DigitUtil.arabicNumToChineseNum(batchNo) + "批";
				vo.setBatchNo(batchNo.toString());
				vo.setBatchName(batchName);
			}
		}
		ProjectBaseDto result = new ProjectBaseDto();

		if (!StringUtils.isEmpty(vo.getProjectName())) {
			vo.setProjectName(vo.getProjectName().trim());
			// 判断名称是否已经存在
			boolean exist = checkProjectNameExist(vo.getId(), vo.getProjectName(),vo.getYear(), vo.getConstructionUnitId());
			if (exist) {
				throw new ServiceException("同一个建设单位的项目名称在同一个申报年份内不能重复!");
			}
		}

		if (StringUtils.isEmpty(vo.getId())) {
			vo.setApprovalNo(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
			// v1.5默认的补充状态为--
			vo.setSupplementStatus(SupplementStatusEnum.DEFAULT.getCode());
		}
		if (StringUtil.isBlank(vo.getReviewStatus())) {
			vo.setReviewStatus(ReviewStatusEnum.TO_BE_REPORTED.getCode());
		}
		// 增加保存的时候需要保存层级
		Dept dept = deptService.getById(AuthUtil.getDeptId());
		dept = deptService.getById(dept.getUnitId());
		vo.setAncestors(dept.getAncestors() + "," + dept.getId());

		if (StringUtils.isEmpty(vo.getId())) {
			vo.setApproveLevel(dept.getUnitLevel());
			result = projectBaseService.save(vo);
		} else {
			result = projectBaseService.update(vo);
		}

		// 项目介绍
		if (!ObjectUtils.isEmpty(vo.getIntroduceVo())) {
			// 删除项目介绍数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectIntroduceService.deleteByProjectId(result.getId());
			}

			ProjectIntroduceVo introduceVo = vo.getIntroduceVo();
			introduceVo.setProjectId(result.getId());
			projectIntroduceService.save(introduceVo);
		}

		// 删除项目联系人数据
		if (!StringUtils.isEmpty(vo.getId())) {
			projectContactService.deleteByProjectId(result.getId());
		}

		// 项目联系人
		if (!ObjectUtils.isEmpty(vo.getContactVo())) {

			ProjectContactVo contactVo = vo.getContactVo();
			contactVo.setProjectId(result.getId());
			projectContactService.save(contactVo);
		}

		// 删除项目成员数据
		if (!StringUtils.isEmpty(vo.getId())) {
			projectMemberService.deleteByProjectId(result.getId());
		}

		// 项目成员
		if (!CollectionUtils.isEmpty(vo.getMemberList())) {

			for (ProjectMemberVo memberVo : vo.getMemberList()) {
				memberVo.setProjectId(result.getId());
				projectMemberService.save(memberVo);
			}
		}
		// 关联附件信息
		projectLogicFileService.relatedFile(OperationTypeEnum.getOperationType(vo.getId()), result.getId(), vo.getFileList());

		// // 删除附件数据
		// if (!StringUtils.isEmpty(vo.getId())) {
		// 	projectFileService.deleteByProjectId(vo.getId(), Arrays.asList(vo.getId()), ProjectFileEnum.PROJECT_FILE.getCode());
		// }
		//
		// // 附件
		// if (!CollectionUtils.isEmpty(vo.getFileList())) {
		// 	for (Long fileId : vo.getFileList()) {
		// 		ProjectFileVo fileVo = new ProjectFileVo();
		// 		fileVo.setProjectId(result.getId());
		// 		fileVo.setAttachId(fileId);
		// 		fileVo.setBusinessId(result.getId());
		// 		fileVo.setBusinessType(ProjectFileEnum.PROJECT_FILE.getCode());
		//
		// 		projectFileService.save(fileVo);
		// 	}
		// }
		if (vo.getDoLog()) {
			projectVersionService.projectVersionLog(Arrays.asList(result.getId()), Long.valueOf(AuthUtil.getDeptId()),
				AuthUtil.getUserId());
		}
		return result;
	}

	public boolean checkProjectNameExist(Long projectId, String projectName, Integer year, Long constructionUnitId) {
		// 判断项目名称是否重复
		Dept dept = SysCache.getDept(constructionUnitId);
		Dept currentUnit = SysCache.getDept(dept.getUnitId());
		List<ProjectBaseDto> projectBaseDtos = projectBaseService.listByProjectName(currentUnit.getId(), projectName, projectId,year);
		if (CollectionUtil.isNotEmpty(projectBaseDtos)) {
			return true;
		}
		return false;
	}

	@Transactional(rollbackFor = Exception.class)
	public ProjectBaseDto submit(ProjectBaseVo vo) {

		ProjectBaseDto result = new ProjectBaseDto();

		if (!StringUtils.isEmpty(vo.getProjectName())) {
			vo.setProjectName(vo.getProjectName().trim());
		}
		// 校验
		if (!StringUtils.isEmpty(vo.getProjectName())) {
			List<ProjectBasePageDto> list = projectBaseService.listByNoStatus(Arrays.asList(ReviewStatusEnum.TO_BE_REPORTED.getCode(),
				ReviewStatusEnum.NULLIFY.getCode()), vo.getProjectName(), vo.getId(), vo.getConstructionUnitId());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.PROJECT_DATA_EXIST.getMessage());
			}
		}

		if (StringUtils.isEmpty(vo.getId())) {
			vo.setApprovalNo(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
		}
		vo.setReviewStatus(ReviewStatusEnum.UNDER_REVIEW.getCode());
		vo.setSubmitTime(new Date());

		if (StringUtils.isEmpty(vo.getId())) {
			result = projectBaseService.save(vo);
		} else {
			result = projectBaseService.update(vo);
		}

		// 项目介绍
		if (!ObjectUtils.isEmpty(vo.getIntroduceVo())) {
			// 删除项目介绍数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectIntroduceService.deleteByProjectId(result.getId());
			}

			ProjectIntroduceVo introduceVo = vo.getIntroduceVo();
			introduceVo.setProjectId(result.getId());
			projectIntroduceService.save(introduceVo);
		}

		// 项目联系人
		if (!ObjectUtils.isEmpty(vo.getContactVo())) {
			// 删除项目联系人数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectContactService.deleteByProjectId(result.getId());
			}

			ProjectContactVo contactVo = vo.getContactVo();
			contactVo.setProjectId(result.getId());
			projectContactService.save(contactVo);
		}

		// 项目成员
		if (!CollectionUtils.isEmpty(vo.getMemberList())) {
			// 删除项目成员数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectMemberService.deleteByProjectId(result.getId());
			}

			for (ProjectMemberVo memberVo : vo.getMemberList()) {
				memberVo.setProjectId(result.getId());
				projectMemberService.save(memberVo);
			}
		}

		// 删除附件数据
		if (!StringUtils.isEmpty(vo.getId())) {
			projectFileService.deleteByProjectId(vo.getId(), Arrays.asList(vo.getId()), ProjectFileEnum.PROJECT_FILE.getCode());
		}

		// 附件
		if (!CollectionUtils.isEmpty(vo.getFileList())) {
			for (Long fileId : vo.getFileList()) {
				ProjectFileVo fileVo = new ProjectFileVo();
				fileVo.setProjectId(result.getId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(result.getId());
				fileVo.setBusinessType(ProjectFileEnum.PROJECT_FILE.getCode());

				projectFileService.save(fileVo);
			}
			// 自动收集
			baseLogicService.autoCollectKnowledge(vo.getFileList(), result.getId(), result.getApprovalNo());
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> report(ProjectBaseDeleteVo vo) {
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		List<ProjectBasePageDto> list = projectBaseService.listByIds(vo.getIdList());
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}
		List<ProjectBasePageDto> filterList = list.stream().filter(item -> !StringUtils.isEmpty(item.getProjectName())).collect(Collectors.toList());
		Map<String, List<ProjectBasePageDto>> map = new HashMap<>();
		if (!CollectionUtils.isEmpty(filterList)) {
			map.putAll(filterList.stream().collect(Collectors.groupingBy(ProjectBasePageDto::getProjectName)));
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();

			ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
			deleteDto.setName(projectBaseDto.getApprovalNo());
			deleteDto.setResult(true);

			// 校验-建设单位
			if (StringUtils.isEmpty(projectBaseDto.getConstructionUnitId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_1.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-公司名称
			if (StringUtils.isEmpty(projectBaseDto.getCompanyId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_2.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目名称
			if (StringUtils.isEmpty(projectBaseDto.getProjectName())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_3.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-专业分类
			if (StringUtils.isEmpty(projectBaseDto.getSpecialtyClassification())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_15.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目分类
			if (StringUtils.isEmpty(projectBaseDto.getProjectClassification())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_16.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-计划招标时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanTenderDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_17.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-计划开始时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanStartDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_18.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-计划结束时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanCompleteDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_19.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-列支渠道
			if (StringUtils.isEmpty(projectBaseDto.getDistributionChannel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_20.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-申报年份
			if (StringUtils.isEmpty(projectBaseDto.getYear())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_21.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-投资主体
			if (StringUtils.isEmpty(projectBaseDto.getInvestmentSubjectId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_22.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目预计总投资
			if (StringUtils.isEmpty(projectBaseDto.getTotalEstimate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_23.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目本年度投资
			if (StringUtils.isEmpty(projectBaseDto.getCurrentInvestment())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_24.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 项目介绍
			ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
			// 校验-项目现状及存在问题
			if (StringUtils.isEmpty(introduceDto.getProjectIntroduction())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_25.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目必要性
			if (StringUtils.isEmpty(introduceDto.getProjectNecessity())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_26.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-建设内容
			if (StringUtils.isEmpty(introduceDto.getConstructionContent())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_28.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-设备投入明细
			if (StringUtils.isEmpty(introduceDto.getEquipmentInputDetails())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_30.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-信息化智能化系统投入明细
			if (StringUtils.isEmpty(introduceDto.getImInputDetails())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_31.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 联系人
			ProjectContactDto contactDto = projectContactService.getByProjectId(id);
			// 校验-项目负责人
			if (StringUtils.isEmpty(contactDto.getHeadPerson())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_32.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目负责人联系方式
			if (StringUtils.isEmpty(contactDto.getHeadPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_33.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目联系人
			if (StringUtils.isEmpty(contactDto.getConstructionPerson())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_34.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目联系人联系方式
			if (StringUtils.isEmpty(contactDto.getConstructionPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_35.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 项目成员
			List<ProjectMemberDto> memberList = projectMemberService.listByProjectId(id);
			if (CollectionUtils.isEmpty(memberList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_36.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 提交数据校验名称唯一
			if (!ObjectUtils.isEmpty(map) && map.containsKey(projectBaseDto.getProjectName())) {
				List<ProjectBasePageDto> baseList = map.get(projectBaseDto.getProjectName());
				if (!CollectionUtils.isEmpty(baseList) && baseList.size() > 1) {
					deleteDto.setResult(false);
					deleteDto.setMessage(ExceptionEnum.PROJECT_NAME_EXIST.getMessage());
					result.add(deleteDto);
					continue;
				}
			}

			// 项目名称唯一校验
			List<ProjectBasePageDto> basePagelist = projectBaseService.listByNoStatus(Arrays.asList(ReviewStatusEnum.TO_BE_REPORTED.getCode(),
				ReviewStatusEnum.NULLIFY.getCode()), projectBaseDto.getProjectName(), id, projectBaseDto.getConstructionUnitId());
			if (!CollectionUtils.isEmpty(basePagelist)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.PROJECT_DATA_EXIST.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 状态
			if (!projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.TO_BE_REPORTED.getCode())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.PROJECT_TO_BE_REPORTED.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 附件
			List<ProjectFileDto> fileDtoList = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode());
			if (CollectionUtils.isEmpty(fileDtoList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_37.getMessage());
				result.add(deleteDto);
				continue;
			}

			boolean flag = projectBaseService.updateByStatus(id, ReviewStatusEnum.UNDER_REVIEW.getCode(), new Date(), null, null);
			deleteDto.setResult(flag);

			result.add(deleteDto);
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> delete(ProjectBaseDeleteVo vo) {
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();

			ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
			deleteDto.setName(projectBaseDto.getProjectName());
			if (Func.isNotEmpty(projectBaseDto.getConstructionUnitId())) {
				Dept dept = SysCache.getDept(projectBaseDto.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						deleteDto.setConstructionUnitName(dept.getDeptName());
					} else {
						deleteDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}

			// 状态
			if (!projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.TO_BE_REPORTED.getCode())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.PROJECT_TO_BE_REPORTED_DELETE.getMessage());
				result.add(deleteDto);
				continue;
			}

			if (!DeptScopeUtil.canDel(projectBaseDto.getCreateDept(), projectBaseDto.getCreateUser())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(com.snszyk.zbusiness.project.enums.ExceptionEnum.NO_SELF_CANT_DEL.getMessage());
				result.add(deleteDto);
				continue;
			}

			Boolean flag = projectBaseService.deleteById(id);
			if (flag) {
				// 删除钉钉待办
				szykTaskService.delTaskAndMsg(Arrays.asList(ToDoBusinessTypeEnum.PROJECT_REPORT), id);

			}
			deleteDto.setResult(flag);

			// 删除项目数据
			// projectIntroduceService.deleteByProjectId(id);

			// 删除联系人数据
			// projectContactService.deleteByProjectId(id);

			// 删除项目成员数据
			// projectMemberService.deleteByProjectId(id);

			// 删除附件数据
			// projectFileService.deleteByProjectId(id, Arrays.asList(id), ProjectFileEnum.PROJECT_FILE.getCode());

			result.add(deleteDto);
		}

		return result;
	}

	public void export(ProjectBasePageVo vo, HttpServletRequest request, HttpServletResponse response) {

		Dept currentDept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		Dept currentUnit = SysCache.getDept(currentDept.getUnitId());
//		List<Long> deptList = new ArrayList<>();
		// 以单位登录看到单位及权属单位提报的项目
//		if (DeptScopeUtil.isUnitLogin()) {
//		deptList = DeptScopeUtil.verticalDeptList();
		// 以部门登录看到本部门以及下级所有部门申报的项目
//			List<Dept> currentAndLowDept = DeptScopeUtil.verticalDeptList();
//		}
		vo.setSize(-1);
		IPage<ProjectBasePageDto> page = page(vo);
		IPage<ProjectBaseDto> convert = page.convert(e -> BeanUtil.copy(e, ProjectBaseDto.class));
		List<ProjectBaseDto> list = convert.getRecords();
		if (CollectionUtils.isEmpty(page.getRecords())) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		List<ProjectBaseExportDto> result = new ArrayList<>();

		// 专业分类
		List<DictMajorDto> majorList = dictMajorService.listByDictStatus(null);
		Map<String, DictMajorDto> majorMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(majorList)) {
			majorMap.putAll(majorList.stream().collect(Collectors.toMap(DictMajorDto::getDictKey, Function.identity())));
		}
		// 列支渠道
		List<DictCommonDto> dcList = dictCommonService.listByDictStatus(DictEnum.DC.getCode(), null);
		Map<String, DictCommonDto> dcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dcList)) {
			dcMap.putAll(dcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}

		for (int i = 0; i < list.size(); i++) {
			ProjectBaseDto dto = list.get(i);
			ProjectBaseExportDto exportDto = new ProjectBaseExportDto();
			BeanUtil.copyProperties(dto, exportDto);
			// 专业分类
			if (!ObjectUtils.isEmpty(majorMap) && !ObjectUtils.isEmpty(dto.getSpecialtyClassification()) && majorMap.containsKey(dto.getSpecialtyClassification())) {
				exportDto.setSpecialtyClassificationName(majorMap.get(dto.getSpecialtyClassification()).getDictValue());
			}
			// 列支渠道
			if (!ObjectUtils.isEmpty(dcMap) && !ObjectUtils.isEmpty(dto.getDistributionChannel()) && dcMap.containsKey(dto.getDistributionChannel())) {
				exportDto.setDistributionChannelName(dcMap.get(dto.getDistributionChannel()).getDictValue());
			}

			// 项目介绍
			ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(dto.getId());

			// 联系人
			ProjectContactDto contactDto = projectContactService.getByProjectId(dto.getId());

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			exportDto.setNo((String.valueOf(i + 1)));
			exportDto.setProjectNo(StringUtils.isEmpty(exportDto.getProjectNo()) ? "-" : exportDto.getProjectNo());
			exportDto.setCompanyName(StringUtils.isEmpty(exportDto.getCompanyName()) ? "-" : exportDto.getCompanyName());
			//建设单位如果是部门的话，需要加上单位名称
			Dept dept = SysCache.getDept(dto.getConstructionUnitId());
			if (Func.isNotEmpty(dept)) {
				if (dept.getUnitId().equals(dept.getId())) {
					exportDto.setConstructionUnitName(dept.getDeptName());
				} else {
					exportDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
				}
			} else {
				exportDto.setConstructionUnitName("-");
			}
			exportDto.setSpecialtyClassificationName(StringUtils.isEmpty(exportDto.getSpecialtyClassificationName()) ? "-" : exportDto.getSpecialtyClassificationName());
			exportDto.setProjectName(StringUtils.isEmpty(exportDto.getProjectName()) ? "-" : exportDto.getProjectName());
			exportDto.setProjectIntroduction(StringUtils.isEmpty(introduceDto.getProjectIntroduction()) ? "-" : introduceDto.getProjectIntroduction());
			exportDto.setConstructionContent(StringUtils.isEmpty(introduceDto.getConstructionContent()) ? "-" : introduceDto.getConstructionContent());
			exportDto.setDistributionChannelName(StringUtils.isEmpty(exportDto.getDistributionChannelName()) ? "-" : exportDto.getDistributionChannelName());
			exportDto.setTotalEstimate(StringUtils.isEmpty(dto.getTotalEstimate()) ? "-" : String.valueOf(dto.getTotalEstimate()));
			exportDto.setCurrentInvestment(StringUtils.isEmpty(dto.getCurrentInvestment()) ? "-" : String.valueOf(dto.getCurrentInvestment()));
			exportDto.setEquipmentInputDetails(StringUtils.isEmpty(introduceDto.getEquipmentInputDetails()) ? "-" : introduceDto.getEquipmentInputDetails());
			exportDto.setImInputDetails(StringUtils.isEmpty(introduceDto.getImInputDetails()) ? "-" : introduceDto.getImInputDetails());
			exportDto.setPlanTenderDate(StringUtils.isEmpty(dto.getPlanTenderDate()) ? "-" : sdf.format(dto.getPlanTenderDate()));
			exportDto.setPlanCompleteDate(StringUtils.isEmpty(dto.getPlanCompleteDate()) ? "-" : sdf.format(dto.getPlanCompleteDate()));
			exportDto.setHeadPersonAndTel(StringUtils.isEmpty(contactDto.getHeadPerson()) && StringUtils.isEmpty(contactDto.getHeadPersonTel()) ? "-" : contactDto.getHeadPerson() + contactDto.getHeadPersonTel());
			exportDto.setCorpApproveRemark(StringUtils.isEmpty(dto.getCorpApproveResultName()) && StringUtils.isEmpty(dto.getCorpApproveRemark()) ? "-" : dto.getCorpApproveResultName() + "。" + dto.getCorpApproveRemark());
			exportDto.setSecApproveRemark(StringUtils.isEmpty(dto.getSecApproveRemark()) ? "-" : dto.getSecApproveRemark());

			result.add(exportDto);
		}

		// 导出
		ExcelUtil.export(response, "项目申报数据" + DateUtil.time(), "项目申报数据", result, ProjectBaseExportDto.class);
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean saveLabel(ProjectBaseLabelVo vo) {
		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return false;
		}
		List<String> projectLabelList = vo.getProjectLabelList();
		for (Long id : vo.getIdList()) {
			ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
			String projectLabel = null;
			// 不存在数据
			if (StringUtils.isEmpty(projectBaseDto.getProjectLabel())) {
				projectLabel = StringUtil.join(projectLabelList, ",");
				projectBaseService.updateByLabel(id, StringUtil.join(projectLabelList, ","));
			}
			// 存在数据
			if (!StringUtils.isEmpty(projectBaseDto.getProjectLabel())) {
				List<String> list = new ArrayList<>(Arrays.asList(projectBaseDto.getProjectLabel().split(",")));
				for (String label : projectLabelList) {
					if (list.contains(label)) {
						continue;
					}
					list.add(label);
				}
				projectLabel = StringUtil.join(list, ",");
				projectBaseService.updateByLabel(id, StringUtil.join(list, ","));
			}
			// 同步项目库
			if (projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.PASS.getCode()) && !StringUtils.isEmpty(projectLabel)) {
				plBaseService.updateByLabel(id, projectLabel);
			}
		}
		return true;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteLabel(Long id, String labelKey) {
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
		if (projectBaseDto == null) {
			return false;
		}

		// 不存在数据
		if (StringUtils.isEmpty(projectBaseDto.getProjectLabel())) {
			throw new ServiceException(ExceptionEnum.PROJECT_LABEL_NOT_DATA.getMessage());
		}

		List<String> projectLabelList = new ArrayList<>(Arrays.asList(projectBaseDto.getProjectLabel().split(",")));
		projectLabelList.remove(labelKey);

		projectBaseService.updateByLabel(id, StringUtil.join(projectLabelList, ","));

		// 同步项目库
//		if (projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.PASS.getCode())) {
		plBaseService.updateByLabel(id, StringUtil.join(projectLabelList, ","));
//		}

		return true;
	}

	/**
	 * 审核通过
	 * v1.5 添加补充材料字段
	 * 注释1
	 * <p>
	 * 补充材料状态：新增字段，放至填报人后，有几种状态：--、无需提报、待提交、已提交、已归档。此字段与审查结论有关。
	 * <p>
	 * --：审查结论为空时，补充材料状态为--。
	 * <p>
	 * 无需提报：审查结论除原则同意立项，其他状态都为“无需提报”。
	 * <p>
	 * 待提交：审查结论为原则同意立项，补充材料状态为待提交。并且建设单位可看到“上传补充材料”按钮。项目申报集团下发以后，给建设单位此项目的提报人发送待办消息：需提交补充材料，请处理。提交后，此待办消失。
	 * <p>
	 * 已提交：建设单位提交补充材料后，补充材料状态状态置为已提交
	 * <p>
	 * 已归档：建设单位提交后，集团联络员通过查看详情，查看补充材料，确认无误后，进行归档，归档后，补充材料状态置为“已归档”。
	 *
	 * @param id
	 */
	@Transactional(rollbackFor = Exception.class)
	public void auditPass(Long id) {

		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
		// 专业分类
		String specialtyClassification = projectBaseDto.getSpecialtyClassification();
		DictMajorDto dictKey = dictMajorService.getByDictKey(specialtyClassification);
		String abbreviation = "";
		if (dictKey != null) {
			abbreviation = dictKey.getAbbreviation();
		}
		Long companyId = projectBaseDto.getCompanyId();
		Dept company = SysCache.getDept(companyId);
		String companyCode = company.getCompanyCode();
		// 项目数据
		ProjectBaseDto result = projectBaseService.fetchById(id);
		// v1.5 设置补充材料字段
		String supplementStatus = SupplementStatusEnum.DEFAULT.getCode();

		// 原则同意立项为   待提交
		if (Objects.equals(result.getCorpApproveResult(), ProjectApprovalOpinionEnum.PASS.getCode())) {
			supplementStatus = SupplementStatusEnum.TO_DO_REPORT.getCode();

		} else if (StringUtil.isNotBlank(result.getCorpApproveResult())) {
			// 其他的状态为无需提报
			supplementStatus = SupplementStatusEnum.NO_NEED_REPORT.getCode();
		}
		// 更新项目提报数据
		result.setSupplementStatus(supplementStatus);
		result.setReviewStatus(ReviewStatusEnum.PASS.getCode());

		projectBaseService.updateByStatus(id, result.getReviewStatus(), null, null
			, result.getSupplementStatus());

		// 项目介绍
		ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
		result.setIntroduceDto(introduceDto);

		// 联系人
		ProjectContactDto contactDto = projectContactService.getByProjectId(id);
		result.setContactDto(contactDto);

		// 项目成员
		List<ProjectMemberDto> memberList = projectMemberService.listByProjectId(id);
		result.setMemberList(memberList);

		// 附件
		List<ProjectFileDto> fileDtoList = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode());
		if (!CollectionUtils.isEmpty(fileDtoList)) {
			// 附件ID
			List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());
			result.setFileIdList(attachIds);
		}
		// 原则同意立项，同步项目库数据
		if (org.apache.commons.lang3.StringUtils.isBlank(result.getCorpApproveResult())) {
			throw new ServiceException("项目：" + result.getProjectName() + "的集团审查结论为空");
		}
		if (result.getCorpApproveResult().equals(ProjectApprovalOpinionEnum.PASS.getCode())
			|| result.getCorpApproveResult().equals(ProjectApprovalOpinionEnum.ALL_PASS.getCode())) {
			plExternalReferenceService.synchronizeProject(result);
		}

	}


	public ProjectCheckDataDto checkData(ProjectBaseDeleteVo vo) {
		ProjectCheckDataDto dto = new ProjectCheckDataDto();
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			throw new ServiceException(ExceptionEnum.NOT_PARAMS.getMessage());
		}

		List<ProjectBasePageDto> list = projectBaseService.listByIds(vo.getIdList());
		if (CollectionUtils.isEmpty(list)) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}
		List<ProjectBasePageDto> filterList = list.stream().filter(item -> !StringUtils.isEmpty(item.getProjectName())).collect(Collectors.toList());
		Map<String, List<ProjectBasePageDto>> map = new HashMap<>();
		if (!CollectionUtils.isEmpty(filterList)) {
			map.putAll(filterList.stream().collect(Collectors.groupingBy(ProjectBasePageDto::getProjectName)));
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();
			ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
			deleteDto.setName(projectBaseDto.getProjectName());
			if (Func.isNotEmpty(projectBaseDto.getConstructionUnitId())) {
				Dept dept = SysCache.getDept(projectBaseDto.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						deleteDto.setConstructionUnitName(dept.getDeptName());
					} else {
						deleteDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}

			// 校验-建设单位
			if (StringUtils.isEmpty(projectBaseDto.getConstructionUnitId())) {
				dto.setCheckResult(false);
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_1.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-公司名称
			if (StringUtils.isEmpty(projectBaseDto.getCompanyId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_2.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目名称
			if (StringUtils.isEmpty(projectBaseDto.getProjectName())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_3.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-专业分类
			if (StringUtils.isEmpty(projectBaseDto.getSpecialtyClassification())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_15.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目分类
			if (StringUtils.isEmpty(projectBaseDto.getProjectClassification())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_16.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-计划招标时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanTenderDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_17.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-计划开始时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanStartDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_18.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-计划结束时间
			if (StringUtils.isEmpty(projectBaseDto.getPlanCompleteDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_19.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-列支渠道
			if (StringUtils.isEmpty(projectBaseDto.getDistributionChannel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_20.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-申报年份
			if (StringUtils.isEmpty(projectBaseDto.getYear())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_21.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-投资主体
			if (StringUtils.isEmpty(projectBaseDto.getInvestmentSubjectId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_22.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目预计总投资
			if (StringUtils.isEmpty(projectBaseDto.getTotalEstimate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_23.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目本年度投资
			if (StringUtils.isEmpty(projectBaseDto.getCurrentInvestment())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_24.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 项目介绍
			ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
			// 校验-项目现状及存在问题
			if (StringUtils.isEmpty(introduceDto.getProjectIntroduction())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_25.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目必要性
			if (StringUtils.isEmpty(introduceDto.getProjectNecessity())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_26.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-建设内容
			if (StringUtils.isEmpty(introduceDto.getConstructionContent())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_28.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-设备投入明细
			if (StringUtils.isEmpty(introduceDto.getEquipmentInputDetails())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_30.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-信息化智能化系统投入明细
			if (StringUtils.isEmpty(introduceDto.getImInputDetails())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_31.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 联系人
			ProjectContactDto contactDto = projectContactService.getByProjectId(id);
			// 校验-项目负责人
			if (StringUtils.isEmpty(contactDto.getHeadPerson())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_32.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目负责人联系方式
			if (StringUtils.isEmpty(contactDto.getHeadPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_33.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目负责人联系方式 手机号格式
			if (!CommonUtil.isValidPhoneNumber(contactDto.getHeadPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_46.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目联系人
			if (StringUtils.isEmpty(contactDto.getConstructionPerson())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_34.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 校验-项目联系人联系方式
			if (StringUtils.isEmpty(contactDto.getConstructionPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_35.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目联系人联系方式手机号格式
			if (!CommonUtil.isValidPhoneNumber(contactDto.getConstructionPersonTel())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_47.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 项目成员
			List<ProjectMemberDto> memberList = projectMemberService.listByProjectId(id);
			if (CollectionUtils.isEmpty(memberList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_36.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			} else {
				List<ProjectMemberDto> collect = memberList.stream().filter(e -> StringUtil.isBlank(e.getMemberName()) || StringUtil.isBlank(e.getUnit()) ||
					StringUtil.isBlank(e.getWorkDivision()) || StringUtil.isBlank(e.getTitles())).collect(Collectors.toList());
				if (CollectionUtil.isNotEmpty(collect)) {
					deleteDto.setResult(false);
					deleteDto.setMessage("项目成员数据不完整");
					result.add(deleteDto);
					dto.setCheckResult(false);
					continue;
				}

			}

			// 提交数据校验名称唯一
			if (!ObjectUtils.isEmpty(map) && map.containsKey(projectBaseDto.getProjectName())) {
				List<ProjectBasePageDto> baseList = map.get(projectBaseDto.getProjectName());
				if (!CollectionUtils.isEmpty(baseList) && baseList.size() > 1) {
					deleteDto.setResult(false);
					deleteDto.setMessage(ExceptionEnum.PROJECT_NAME_EXIST.getMessage());
					result.add(deleteDto);
					dto.setCheckResult(false);
					continue;
				}
			}

			// 项目名称唯一校验
//			List<ProjectBasePageDto> basePagelist = projectBaseService.listByNoStatus(Arrays.asList(ReviewStatusEnum.TO_BE_REPORTED.getCode(),
//				ReviewStatusEnum.NULLIFY.getCode()), projectBaseDto.getProjectName(), id, projectBaseDto.getConstructionUnitId());
//			if (!CollectionUtils.isEmpty(basePagelist)) {
//				deleteDto.setResult(false);
//				deleteDto.setMessage(ExceptionEnum.PROJECT_DATA_EXIST.getMessage());
//				result.add(deleteDto);
//				dto.setCheckResult(false);
//				continue;
//			}

			// 状态
			if (!(projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.TO_BE_REPORTED.getCode())
				|| projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.CANCEL.getCode())
				|| projectBaseDto.getReviewStatus().equals(ReviewStatusEnum.RETURN.getCode()))
			) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.PROJECT_TO_BE_REPORTED.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 增加当前审批节点是0发起人时才可提报
			if (Func.isNotEmpty(projectBaseDto.getInstanceId())) {
				InstanceDto instanceDto = instanceService.fetchById(projectBaseDto.getInstanceId());
				if (!instanceDto.getCurrFlowSort().equals(CommonConstant.ZERO)) {
					deleteDto.setResult(false);
					deleteDto.setMessage(ExceptionEnum.PROJECT_TO_START_PERSON.getMessage());
					result.add(deleteDto);
					dto.setCheckResult(false);
					continue;
				}
			}

			// 附件
			List<ProjectFileDto> fileDtoList = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode());
			if (CollectionUtils.isEmpty(fileDtoList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_37.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
		}
		dto.setDataList(result);
		return dto;
	}

	@Transactional
	public Boolean createBatchNo(ProjectCreateBatchNoVo vo) {
//		// 更新批次号
//		String batchName = vo.getBatchName();
//		// v1.5获取批次的 数字 2023年第一批
//		batchName = batchName.replaceAll("年第", "");
//		batchName = batchName.replaceAll("批", "");
//		String year = batchName.substring(0, 4);
//		String numStr = batchName.substring(4);
//		// 数字001 002
//		int nowNo = DigitUtil.chineseNumToArabicNum(numStr);
//		// 转化后的编号 202301
//		String batchNo = year + String.format("%03d", nowNo);
//		// 本次已经发送过的人不在重复发送
//		Set<String> haveSendUserSet = new HashSet<>();
//
//		//公司-联络人
//		Map<Long, List<Long>> companyContactMap = new HashMap<>();
//		for (Long id : vo.getIdList()) {
//			ProjectBaseDto projectBaseDto = projectBaseService.detail(id);
//			String oldBatchNo = projectBaseDto.getBatchNo();
//			projectBaseDto.setBatchName(vo.getBatchName());
//			projectBaseDto.setBatchNo(batchNo);
//			projectBaseService.save(BeanUtil.copy(projectBaseDto, ProjectBaseVo.class));
//			// 更新项目库
//			PlBaseDto plBaseDto = plBaseService.fetchById(id);
//			if (ObjectUtil.isNotEmpty(plBaseDto)) {
//				plBaseDto.setBatchNo(batchNo);
//				plBaseDto.setBatchName(projectBaseDto.getBatchName());
//				plBaseService.save(BeanUtil.copy(plBaseDto, PlBaseVo.class));
//			}
//
//
//			if (StringUtil.isNotBlank(oldBatchNo)) {
//				continue;
//			}
//
//			if (!Objects.equals(projectBaseDto.getCorpApproveResult(), ProjectApprovalOpinionEnum.PASS.getCode())) {
//				continue;
//			}
//			// 只有处于补充材料待提交状态的 才发送代办通知
//			if (!Objects.equals(projectBaseDto.getSupplementStatus(), SupplementStatusEnum.TO_DO_REPORT.getCode())) {
//				continue;
//			}
//			List<Long> sendUserList = new ArrayList<>();
//			Long sendDept = null;
//			// v1.5 给建设单位此项目的提报人发送待办消息： 需提交补充材料，请处理。提交后，此待办消失
//			// 二级公司及权属单位的补充材料原来是给发起人发通知由发起人上传，改成发通知给二级公司联络人，由二级公司联络人上传，支持建设单位查看。
//			//如果不是发送给总部的
//			String unitLevel = projectBaseDto.getSupplyLevel();
//			//1.发送的公司和人
//			if (StringUtil.isNotBlank(unitLevel) && !unitLevel.equals(UnitLevelEnum.UNIT_LEVEL_1.getCode().toString())) {
//				// 二级公司
//				sendDept = projectBaseDto.getCompanyId();
//				List<Long> longs = companyContactMap.get(sendDept);
//				if (CollectionUtil.isNotEmpty(longs)) {
//					sendUserList.addAll(longs);
//				} else {
//					// 二级公司联络人
//					sendUserList = userService.getUserByContactAndDept(ContactPersonEnum.PROJECT.getCode(), sendDept).stream().map(UserVO::getId).collect(Collectors.toList());
//					companyContactMap.put(sendDept, sendUserList);
//				}
//			} else {
//				// 发起人
//				sendUserList.add(projectBaseDto.getCreateUser());
//				sendDept = projectBaseDto.getCreateDept();
//			}
//			//2. 总部的
//			if (StringUtil.isNotBlank(unitLevel) && unitLevel.equals(UnitLevelEnum.UNIT_LEVEL_1.getCode().toString())) {
//				if (haveSendUserSet.contains(projectBaseDto.getCreateUser() + ":" + sendDept)) {
//					continue;
//				} else {
////					sendTodoReportTask(projectBaseDto, projectBaseDto.getCreateUser(), sendDept, MsgContentConstant.PROJECT_SUPPLY);
//					//已经发送的部门+人员
//					haveSendUserSet.add(projectBaseDto.getCreateUser() + ":" + sendDept);
//				}
//			}
//			//二级的
//			if (StringUtil.isNotBlank(unitLevel) && !unitLevel.equals(UnitLevelEnum.UNIT_LEVEL_1.getCode().toString())) {
//				for (Long userId : sendUserList) {
//					if (haveSendUserSet.contains(userId + ":" + sendDept)) {
//						continue;
//					} else {
////						sendTodoReportTask(projectBaseDto, userId, sendDept, MsgContentConstant.SECOND_PROJECT_SUPPLY);
//						//已经发送的部门+人员
//						haveSendUserSet.add(userId + ":" + sendDept);
//					}
//				}
//			}
//		}
//
//		ProjectBatchNoDto projectBatchNoDto = (ProjectBatchNoDto) batchNoLogicService.fetchById(Long.valueOf(year));
//		if (ObjectUtils.isEmpty(projectBatchNoDto)) {
//			projectBatchNoDto = new ProjectBatchNoDto();
//			projectBatchNoDto.setId(Long.valueOf(year));
//		}
//		Integer oldNo = projectBatchNoDto.getNowNo();
//		if (ObjectUtil.isEmpty(oldNo) || nowNo > oldNo) {
//			// 如果新的批次号大于数据库中的批次号
//			projectBatchNoDto.setNowNo(nowNo);
//			batchNoLogicService.saveNew(BeanUtil.copy(projectBatchNoDto, ProjectBatchNoVo.class));
//		}

		return true;
	}

	public ProjectRWOrgDto getOrg(Long projectId) {
		ProjectRWOrgDto dto = new ProjectRWOrgDto();
		// 默认集团公司为考核单位
		DeptVO deptVO = deptService.getTopDept();
		// 被考核的组织为二级单位
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(projectId);
		dto.setInitiateOrgId(deptVO.getId());
		dto.setInitiateOrgName(deptVO.getDeptName());
		Long constructionUnitId = projectBaseDto.getConstructionUnitId();
		// 获取对应的二级单位
		DeptVO twoDept = deptService.getTwoDept(constructionUnitId);
		dto.setAssessedOrgId(twoDept.getId());
		dto.setAssessedOrgName(twoDept.getDeptName());

		return dto;
	}


	/**
	 * 查询本部门的和下级部门的提报项目
	 *
	 * @param constructionUnitId
	 * @return
	 */
	public List<ProjectBaseDto> listByCurrentDeptAndLower(Long constructionUnitId) {
		List<Long> deptIdList = deptService.getChildDeptByIds(Arrays.asList(constructionUnitId));
		List<ProjectBaseDto> result = projectBaseService.listByDeptIdList(deptIdList);
		if (CollectionUtil.isNotEmpty(result)) {
			// 获取最新组织信息
			result.forEach(dto -> {
				if (Func.isNotEmpty(dto.getCompanyId())) {
					dto.setCompanyName(SysCache.getDeptName(dto.getCompanyId()));
				}
				if (Func.isNotEmpty(dto.getConstructionUnitId())) {

					//建设单位如果是部门的话，需要加上单位名称
					Dept dept = SysCache.getDept(dto.getConstructionUnitId());
					if (Func.isNotEmpty(dept)) {
						if (dept.getUnitId().equals(dept.getId())) {
							dto.setConstructionUnitName(dept.getDeptName());
						} else {
							dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
						}
					}
				}
				if (Func.isNotEmpty(dto.getInvestmentSubjectId())) {
					dto.setInvestmentSubjectName(SysCache.getDeptName(dto.getInvestmentSubjectId()));
				}
				if (Func.isNotEmpty(dto.getLeadOrgId())) {
					dto.setLeadOrgName(SysCache.getDeptName(dto.getLeadOrgId()));
				}
			});
		}
		return result;

	}

	@Transactional
	public Boolean saveApprovalOpinion(ProjectApprovalOpinionNewVo v) {
		projectBaseService.updateApprovalOpinionBatch(v);
		// 更新流程中的审批
		// businessProjectService.updateApprovalOpinionBatch(v);
		return true;

	}

	public ProjectBaseApprovalOpinionDto getApprovalOpinion(Long businessId) {
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(businessId);

		ProjectBaseApprovalOpinionDto dto = BeanUtil.copyProperties(projectBaseDto, ProjectBaseApprovalOpinionDto.class);
		ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(businessId);
		dto.setConstructionContent(introduceDto.getConstructionContent());
		//获取最新组织信息
		if (Func.isNotEmpty(projectBaseDto.getConstructionUnitId())) {
			//建设单位如果是部门的话，需要加上单位名称
			Dept dept = SysCache.getDept(projectBaseDto.getConstructionUnitId());
			if (Func.isNotEmpty(dept)) {
				if (dept.getUnitId().equals(dept.getId())) {
					dto.setConstructionUnitName(dept.getDeptName());
				} else {
					dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
				}
			}
		}

		return dto;
	}

	public JSONArray createOaData(List<Long> businessIds) {
		List<ProjectBaseOaDto> dtos = projectBaseService.queryDataByIdForOa(businessIds);
		if (CollectionUtil.isNotEmpty(dtos)) {
			dtos.forEach(dto -> {
				dto.setCorpApproveResult(DictBizCache.getValue(DictBizEnum.PROJECT_APPROVAL_OPINION.getCode(), dto.getCorpApproveResult()));
				// 跳转链接赋值
				dto.setProjectUrl(websiteUrl + projectUrl + String.valueOf(dto.getId()) + "?flag=center&audit=&instanceId=" + String.valueOf(dto.getInstanceId()));
				dto.setId(dto.getInstanceId());
				//建设单位如果是部门的话，需要加上单位名称
				Dept dept = SysCache.getDept(dto.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						dto.setConstructionUnitName(dept.getDeptName());
					} else {
						dto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			});
		}

		return JSONArray.parseArray(JSON.toJSONString(dtos));
	}

	/**
	 * 上传补充材料
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean uploadSupplement(ProjectSupplementVo vo) {
		Long projectId = vo.getProjectId();
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(projectId);
		List<Long> fileIds = vo.getFileIds();
		List<ProjectFileVo> list = new ArrayList<>();
		// 删除之前存在的
		projectFileService.deleteByProjectId(projectId, Collections.singletonList(projectId), ProjectFileEnum.PROJECT_SUPPLEMENT.getCode());
		for (Long fileId : fileIds) {
			ProjectFileVo projectFile = new ProjectFileVo();
			projectFile.setProjectId(projectId);
			projectFile.setAttachId(fileId);
			projectFile.setBusinessId(projectId);
			projectFile.setBusinessType(ProjectFileEnum.PROJECT_SUPPLEMENT.getCode());
			list.add(projectFile);
		}
		Boolean b = projectFileService.batchSave(list);
		if (!b) {
			return false;
		}
		// 自动收集
		baseLogicService.autoCollectKnowledge(fileIds, projectId, projectBaseDto.getProjectNo());
		// 补充材料状态状态置为已提交
		boolean updateStatus = projectBaseService.updateSupplementStatus(projectId, SupplementStatusEnum.REPORTED.getCode());
		if (!updateStatus) {
			throw new ServiceException("更新补充材料状态失败!");
		}
		String batchNo = projectBaseDto.getBatchNo();
		// 没有生成批次,不处理待办
		if (StringUtil.isBlank(batchNo)) {
			return true;
		}
		// 上传完成之后补充材料待办完成
		// 判断是否全部上传完成 根据批次 人和部门判断
		//当前是集团还是二级单位
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		if (dept == null) {
			throw new ServiceException("部门不存在");
		}
		Integer unitLevel = dept.getUnitLevel();
		//如果是集团的
		if (Objects.equals(unitLevel, UnitLevelEnum.UNIT_LEVEL_1.getCode())) {
			//集团的
			List<ProjectBaseDto> projectBaseDtos = projectBaseService.listByconstructionUnitAndUser(DeptScopeUtil.getLoginDeptId(), null, batchNo);
			long count = projectBaseDtos.stream().filter(e -> Objects.equals(e.getSupplementStatus(), SupplementStatusEnum.TO_DO_REPORT.getCode())).count();
			if (count == CommonConstant.ZERO) {
				List<SzykTaskDto> levelOneSzykTaskDtos = szykTaskService.listByTypeAndReceiverId(
					ToDoBusinessTypeEnum.PROJECT_REPORT.getCode(), AuthUtil.getUserId(), Long.valueOf(projectBaseDto.getBatchNo()),
					Long.valueOf(AuthUtil.getDeptId()), MsgContentConstant.PROJECT_SUPPLY);
				if (CollectionUtil.isNotEmpty(levelOneSzykTaskDtos)) {
					szykTaskService.finishTask(levelOneSzykTaskDtos.stream().map(BaseCrudSlimDto::getId).collect(Collectors.toList()));
				}
			}
		} else {
			//二级的
			List<ProjectBaseDto> projectBaseDtos = projectBaseService.listByCompanyAndUser(DeptScopeUtil.getLoginDeptId(), null, batchNo);
			long count = projectBaseDtos.stream().filter(e -> Objects.equals(e.getSupplementStatus(), SupplementStatusEnum.TO_DO_REPORT.getCode())).count();
			if (count == CommonConstant.ZERO) {
				List<SzykTaskDto> szykTaskDtos = szykTaskService.listByTypeAndReceiverId(
					ToDoBusinessTypeEnum.PROJECT_REPORT.getCode(), AuthUtil.getUserId(), Long.valueOf(projectBaseDto.getBatchNo()),
					Long.valueOf(AuthUtil.getDeptId()), MsgContentConstant.SECOND_PROJECT_SUPPLY);
				if (CollectionUtil.isNotEmpty(szykTaskDtos)) {
					szykTaskService.finishTask(szykTaskDtos.stream().map(BaseCrudSlimDto::getId).collect(Collectors.toList()));
				}
			}

		}
		return true;
	}

	/**
	 * 获取补充材料列表
	 *
	 * @param projectId
	 * @return
	 */
	public List<SzykAttachDto> supplementList(Long projectId) {
		List<ProjectFileDto> projectFileDtos = projectFileService.listByProject(projectId, projectId, ProjectFileEnum.PROJECT_SUPPLEMENT.getCode());
		if (CollectionUtil.isEmpty(projectFileDtos)) {
			return new ArrayList<>();
		}
		List<Long> collect = projectFileDtos.stream().map(e -> e.getAttachId()).collect(Collectors.toList());
		List<SzykAttachDto> attaches = attachService.listDetailByFileIds(collect);
		attaches.forEach(e -> {
			e.setUndeletable(-1l);
		});
		return attaches;
	}

	/**
	 * 补充材料归档
	 *
	 * @param projectId
	 * @return
	 */
	public Boolean placeOnFile(Long projectId) {
		boolean b = projectBaseService.updateSupplementStatus(projectId, SupplementStatusEnum.PLACE_ON_FILE.getCode());
		if (!b) {
			throw new ServiceException("归档失败!");
		}
		// 同步到项目库的补充材料页签
		List<ProjectFileDto> projectFileDtos = projectFileService.listByProject(projectId, projectId, ProjectFileEnum.PROJECT_SUPPLEMENT.getCode());
		if (CollectionUtil.isEmpty(projectFileDtos)) {
			return true;
		}
		List<PlFileVo> plFileVos = BeanUtil.copy(projectFileDtos, PlFileVo.class);
		plFileVos.forEach(e -> e.setId(null));
		// 删除项目库之前得数据
		plFileService.deleteByProjectId(projectId, Collections.singletonList(projectId), ProjectFileEnum.PROJECT_SUPPLEMENT.getCode());
		Boolean aBoolean = plFileService.saveBatch(plFileVos);
		if (!aBoolean) {
			throw new ServiceException("同步到项目库失败!");
		}

		return true;
	}

	public static void main(String[] args) {
		String a = "2023八";
		String substring = a.substring(4);
		System.out.println(substring);
	}

	public Boolean checkCancel(Long businessId) {
		ProjectBaseDto dto = projectBaseService.fetchById(businessId);
		if (Func.isNotEmpty(dto.getCancelFlag()) && dto.getCancelFlag() && dto.getReviewStatus().equals(ReviewStatusEnum.UNDER_REVIEW.getCode()) && dto.getCreateUser().equals(AuthUtil.getUserId())) {
			return true;
		} else {
			return false;
		}
	}

	public Boolean autoFile(Long id) {
		ProjectBaseDto dto = projectBaseService.fetchById(id);
		List<ProjectFileDto> fileDtoList = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode());
		if (!CollectionUtils.isEmpty(fileDtoList)) {
			// 自动收集
			baseLogicService.autoCollectKnowledge(fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList()), dto.getId(), dto.getProjectNo());
		}
		return true;
	}

	/**
	 * 作废
	 *
	 * @param id
	 * @return
	 */
	public ProjectBaseDto nullify(Long id) {
		// 作废校验
		this.cancelValid(id);

		ProjectBaseVo baseVo = new ProjectBaseVo();
		baseVo.setId(id);
		baseVo.setReviewStatus(ReviewStatusEnum.NULLIFY.getCode());
		baseVo.setNullifyTime(LocalDateTime.now());
		return this.projectBaseService.save(baseVo);
	}

	/**
	 * 作废校验
	 *
	 * @param id
	 */
	private void cancelValid(Long id) {
		ProjectBaseDto projectBaseDto = this.projectBaseService.fetchById(id);
		if (projectBaseDto == null) {
			throw new ServiceException("项目不存在!");
		}
		// 只有退回至发起人才可以作废
		if (!ReviewStatusEnum.RETURN.getCode().equals(projectBaseDto.getReviewStatus())
			|| !FirstProcessEnum.STEP_NAME.getValue().equals(projectBaseDto.getCurrStepName())
			|| !AuthUtil.getUserId().equals(projectBaseDto.getCreateUser())) {
			throw new ServiceException("项目状态不合法!(只有项目状态处于退回状态、项目节点为发起人且发起人为自己才可作废)");
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean copyForTest(ProjectBaseCopyVo vo) {
		Integer num = vo.getNum();
		String projectName = vo.getProjectName();
		Long id = vo.getId();
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
		ProjectBaseVo projectBaseVo = BeanUtil.copy(projectBaseDto, ProjectBaseVo.class);
		ProjectIntroduceDto projectIntroduceDto = projectIntroduceService.getByProjectId(id);
		ProjectIntroduceVo introduceVo = BeanUtil.copy(projectIntroduceDto, ProjectIntroduceVo.class);
		ProjectContactDto projectContactDto = projectContactService.getByProjectId(id);
		ProjectContactVo contactVo = BeanUtil.copy(projectContactDto, ProjectContactVo.class);
		ProjectMemberDto projectMemberDto = projectMemberService.listByProjectId(id).get(0);
		ProjectMemberVo memberVo = BeanUtil.copy(projectMemberDto, ProjectMemberVo.class);
		ProjectFileDto projectFileDto = projectFileService.listByProject(id, id, ProjectFileEnum.PROJECT_FILE.getCode()).get(0);
		ProjectFileVo fileVo = BeanUtil.copy(projectFileDto, ProjectFileVo.class);

		for (int i = 1; i < num + 1; i++) {
			projectBaseVo.setProjectName(projectName + "-" + i);
			projectBaseVo.setId(null);
			projectBaseVo.setReviewStatus(ReviewStatusEnum.TO_BE_REPORTED.getCode());
			projectBaseVo.setSubmitTime(null);
			projectBaseVo.setInstanceId(null);
			projectBaseVo.setCancelFlag(null);
			projectBaseVo.setCurrStepName(null);
			ProjectBaseDto baseDto = projectBaseService.save(projectBaseVo);

			introduceVo.setProjectId(baseDto.getId());
			introduceVo.setId(null);
			projectIntroduceService.save(introduceVo);

			contactVo.setProjectId(baseDto.getId());
			contactVo.setId(null);
			projectContactService.save(contactVo);

			memberVo.setProjectId(baseDto.getId());
			memberVo.setId(null);
			projectMemberService.save(memberVo);

			fileVo.setProjectId(baseDto.getId());
			fileVo.setBusinessId(baseDto.getId());
			fileVo.setId(null);
			projectFileService.save(fileVo);
		}
		return true;
	}

	public Map<String, Object> getLatestYearAndBatchNo() {
		return projectBaseService.getLatestYearAndBatchNo();
	}
}
