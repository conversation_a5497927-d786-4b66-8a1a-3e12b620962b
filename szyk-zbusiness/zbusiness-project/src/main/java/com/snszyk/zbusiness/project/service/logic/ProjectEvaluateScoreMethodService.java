package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.project.dto.ProjectEvaluateScoreMethodDto;
import com.snszyk.zbusiness.project.service.IProjectEvaluateScoreMethodService;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateScoreMethodVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ProjectEvaluateScoreMethodService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectEvaluateScoreMethodService extends BaseCrudLogicService<ProjectEvaluateScoreMethodDto, ProjectEvaluateScoreMethodVo> {

	private final IProjectEvaluateScoreMethodService projectEvaluateScoreMethodService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectEvaluateScoreMethodService;
	}


}
