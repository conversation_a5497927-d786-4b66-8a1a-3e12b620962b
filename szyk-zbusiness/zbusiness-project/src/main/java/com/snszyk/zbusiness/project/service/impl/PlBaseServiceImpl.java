/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.project.dto.PlBaseDto;
import com.snszyk.zbusiness.project.dto.ProjectBasePageDto;
import com.snszyk.zbusiness.project.entity.PlBase;
import com.snszyk.zbusiness.project.mapper.PlBaseMapper;
import com.snszyk.zbusiness.project.service.IPlBaseService;
import com.snszyk.zbusiness.project.service.IProjectBaseService;
import com.snszyk.zbusiness.project.service.IProjectEvaluateService;
import com.snszyk.zbusiness.project.service.IProjectProgressService;
import com.snszyk.zbusiness.project.vo.PlBaseDispatchVo;
import com.snszyk.zbusiness.project.vo.PlBasePageVo;
import com.snszyk.zbusiness.project.vo.PlBaseVo;
import com.snszyk.zbusiness.project.vo.ProjectProgressPageNewVo;
import com.snszyk.zbusiness.stat.dto.BoardProjectInvestmentDto;
import com.snszyk.zbusiness.stat.dto.BoardProjectPhaseDto;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;
import com.snszyk.zbusiness.stat.dto.HomeProjectStatDto;
import com.snszyk.zbusiness.stat.vo.HomeProjectStatVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 项目库基本信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@AllArgsConstructor
@Service
public class PlBaseServiceImpl extends BaseCrudServiceImpl<PlBaseMapper, PlBase, PlBaseDto, PlBaseVo> implements IPlBaseService {

	private final IProjectBaseService projectBaseService;
	private final IProjectProgressService projectProgressService;
	private final IProjectEvaluateService evaluateService;

	@Override
	protected Wrapper<PlBase> beforePage(PlBaseVo vo) {
		if (ObjectUtil.isEmpty(vo)) {
			return Wrappers.emptyWrapper();
		}

		List<String> labelList = new ArrayList<>();
		if (ObjectUtil.isNotEmpty(vo.getProjectLabel())) {
			labelList.addAll(new ArrayList<>(Arrays.asList(vo.getProjectLabel().split(","))));
		}

		LambdaQueryWrapper<PlBase> lambdaQuery = Wrappers.lambdaQuery(PlBase.class);
		lambdaQuery.like(ObjectUtil.isNotEmpty(vo.getProjectNo()), PlBase::getProjectNo, vo.getProjectNo());
		lambdaQuery.like(ObjectUtil.isNotEmpty(vo.getCompanyName()), PlBase::getCompanyName, vo.getCompanyName());
		lambdaQuery.like(ObjectUtil.isNotEmpty(vo.getConstructionUnitName()), PlBase::getConstructionUnitName, vo.getConstructionUnitName());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getProjectClassification()), PlBase::getProjectClassification, vo.getProjectClassification());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getProjectPhase()), PlBase::getProjectPhase, vo.getProjectPhase());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getProjectStatus()), PlBase::getProjectStatus, vo.getProjectStatus());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getReviewStatus()), PlBase::getReviewStatus, vo.getReviewStatus());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getSpecialtyClassification()), PlBase::getSpecialtyClassification, vo.getSpecialtyClassification());
		lambdaQuery.eq(ObjectUtil.isNotEmpty(vo.getConstructionUnitId()), PlBase::getConstructionUnitId, vo.getConstructionUnitId());

		if (!CollectionUtils.isEmpty(labelList)) {
			String str = " ";

			for (String label : labelList) {
				str = str + " project_label like '%" + label + "%' " + " and";
			}

			if (str.contains("and")) {
				str = str.substring(0, str.length() - 3);
			}

			lambdaQuery.apply(str);
		}

		lambdaQuery.orderByDesc(PlBase::getCreateTime, PlBase::getUpdateTime);
		return lambdaQuery;
	}


	@Override
	public List<PlBaseDto> listByIds(List<Long> idList) {
		List<PlBase> list = this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(idList), PlBase::getId, idList)
			.list();
		return BeanUtil.copy(list, PlBaseDto.class);
	}

	@Override
	public Boolean updateBatch(List<PlBasePageVo> v) {
		List<PlBase> baseList = BeanUtil.copy(v, PlBase.class);
		return super.updateBatchById(baseList);
	}

	@Override
	public PlBaseDto getByDictKey(String specialtyClassification, String projectClassification, String projectPhase, String projectLabel) {

		List<PlBase> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(specialtyClassification), PlBase::getSpecialtyClassification, specialtyClassification)
			.eq(ObjectUtil.isNotEmpty(projectClassification), PlBase::getProjectClassification, projectClassification)
			.eq(ObjectUtil.isNotEmpty(projectPhase), PlBase::getProjectPhase, projectPhase)
			.like(ObjectUtil.isNotEmpty(projectLabel), PlBase::getProjectLabel, projectLabel)
			.list();
		if (ObjectUtil.isNotEmpty(list)) {
			return BeanUtil.copy(list.get(0), PlBaseDto.class);
		}
		return null;
	}

	@Override
	public boolean updateByLabel(Long id, String projectLabel) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), PlBase::getId, id)
			.set(PlBase::getProjectLabel, projectLabel)
			.update();

		return result;
	}

	@Override
	public boolean updateByProjectNode(Long id, String projectNode) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), PlBase::getId, id)
			.set(PlBase::getProjectNode, projectNode)
			.update();

		return result;
	}

	@Override
	public boolean updateByAcceptanceStatus(Long id, String acceptanceStatus) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), PlBase::getId, id)
			.set(PlBase::getAcceptanceStatus, acceptanceStatus)
			.update();

		return result;
	}

	@Override
	public boolean updateByProjectPhase(Long id, String projectPhase, String projectPhaseName) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), PlBase::getId, id)
			.set(PlBase::getProjectPhase, projectPhase)
			.set(PlBase::getProjectPhaseName, projectPhaseName)
			.update();

		return result;
	}

	@Override
	public IPage<PlBaseDto> listByPage(Integer year, List<String> corpApproveResultList, BigDecimal minTotalEstimate, BigDecimal maxTotalEstimate, String projectNode, String projectName, String companyName, List<Long> deptIds, String constructionUnitName, long current, long size) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.like(ObjectUtil.isNotEmpty(projectName), PlBase::getProjectName, projectName)
			.like(ObjectUtil.isNotEmpty(companyName), PlBase::getCompanyName, companyName)
			//v1.4根据创建单位判断
			.in(ObjectUtil.isNotEmpty(deptIds), PlBase::getCreateDept, deptIds)
			.like(ObjectUtil.isNotEmpty(constructionUnitName), PlBase::getConstructionUnitName, constructionUnitName)
			.eq(ObjectUtil.isNotEmpty(projectNode), PlBase::getProjectNode, projectNode)
			.orderByDesc(PlBase::getCreateTime);

		Page<PlBase> page = new Page(current, size);
		IPage<PlBase> majorPage = baseMapper.listByPage(page, projectNode, projectName, companyName, deptIds, constructionUnitName, year, corpApproveResultList, minTotalEstimate, maxTotalEstimate);

		return majorPage.convert(PlBase -> BeanUtil.copyProperties(PlBase, PlBaseDto.class));

	}

	@Override
	public List<PlBaseDto> listByYear(List<Long> deptIds, int year) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.in(ObjectUtil.isNotEmpty(deptIds), PlBase::getConstructionUnitId, deptIds)
			.eq(ObjectUtil.isNotEmpty(year), PlBase::getYear, year)
			.orderByDesc(PlBase::getCreateTime);

		List<PlBase> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}


	@Override
	public List<PlBaseDto> listByParam(List<Long> deptIds, int year, PlBaseDispatchVo vo) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.in(ObjectUtil.isNotEmpty(deptIds), PlBase::getConstructionUnitId, deptIds)
			.eq(ObjectUtil.isNotEmpty(year), PlBase::getYear, year)
			.like(StringUtil.isNotBlank(vo.getProjectName()), PlBase::getProjectName, vo.getProjectName())
			.like(StringUtil.isNotBlank(vo.getCompanyName()), PlBase::getCompanyName, vo.getCompanyName())
			.like(StringUtil.isNotBlank(vo.getConstructionUnitName()), PlBase::getConstructionUnitName, vo.getConstructionUnitName())
			.orderByDesc(PlBase::getCreateTime);
		List<PlBase> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}


	@Override
	public List<PlBaseDto> listByYearReduce(List<Long> deptIds, int year, String projectNode) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.in(ObjectUtil.isNotEmpty(deptIds), PlBase::getConstructionUnitId, deptIds)
			.eq(ObjectUtil.isNotEmpty(projectNode), PlBase::getProjectNode, projectNode)
			.lt(ObjectUtil.isNotEmpty(year), PlBase::getYear, year)

			.orderByDesc(PlBase::getCreateTime);

		List<PlBase> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}

	@Override
	public List<PlBaseDto> listByDept(Long deptId) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.eq(ObjectUtils.isNotEmpty(deptId), PlBase::getConstructionUnitId, deptId)
			.orderByDesc(PlBase::getCreateTime);

		List<PlBase> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}

	@Override
	public List<PlBaseDto> listByDept(List<Long> deptIds) {
		LambdaQueryWrapper<PlBase> queryWrapper = Wrappers.<PlBase>query().lambda()
			.in(ObjectUtils.isNotEmpty(deptIds), PlBase::getConstructionUnitId, deptIds)
			.orderByDesc(PlBase::getCreateTime);

		List<PlBase> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}

	@Override
	public List<PlBaseDto> listAll() {
		List<PlBase> list = super.list();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, PlBaseDto.class));
	}

	@Override
	public boolean insert(PlBaseVo baseVo) {
		PlBase base = new PlBase();
		BeanUtil.copyProperties(baseVo, base);
		boolean save = save(base);
		baseMapper.updateDept(base.getId(), baseVo.getCreateDept());
		return save;
	}

	@Override
	public boolean updateByUnit(Long id, String cooperationUnit, String supervisionUnit) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), PlBase::getId, id)
			.set(PlBase::getCooperationUnit, cooperationUnit)
			.set(PlBase::getSupervisionUnit, supervisionUnit)
			.update();

		return result;
	}

	@Override
	public IPage<PlBaseDto> pageList(PlBaseVo vo, Long deptId) {
		Page<PlBaseDto> page = new Page(vo.getCurrent(), vo.getSize());
		//项目标签
		String projectLabel = vo.getProjectLabel();
		List<String> labelList = new ArrayList<>();
		if (StringUtils.isNotBlank(projectLabel)) {
			String[] split = projectLabel.split(",");
			labelList = Arrays.stream(split).collect(Collectors.toList());
		}
		vo.setLabelList(labelList);
		vo.setDeptId(deptId);
		vo.setUserId(AuthUtil.getUserId());
		return baseMapper.pageList(page, vo);

	}

	@Override
	public IPage<ProjectBasePageDto> progressPage(ProjectProgressPageNewVo vo) {
		Page<PlBase> page = new Page(vo.getCurrent(), vo.getSize());
		Page<PlBase> basePage = this.lambdaQuery()
			.like(StringUtils.isNotEmpty(vo.getProjectName()), PlBase::getProjectName, vo.getProjectName())
			.like(StringUtils.isNotEmpty(vo.getProjectNo()), PlBase::getProjectNo, vo.getProjectNo())
			.in(PlBase::getConstructionUnitId, vo.getDeptIds())
			.eq(StringUtil.isNotBlank(vo.getProjectClassification()), PlBase::getProjectClassification, vo.getProjectClassification())
			.orderByDesc(PlBase::getCreateTime)
			.page(page);
		if (CollectionUtil.isEmpty(basePage.getRecords())) {
			return new Page<>();
		}
		return basePage.convert(PlBase -> BeanUtil.copyProperties(PlBase, ProjectBasePageDto.class));
	}

	/**
	 * 驾驶舱项目总数
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public BoardProjectPhaseDto boardProjectPhaseStat(LocalDateTime startDate, LocalDateTime endDate) {
		//项目总数   统计项目库的数量
		Integer count = this.count();
		//立项                  根据项目库的创建日期
		Integer approveCount = this.lambdaQuery()
			.gt(startDate != null, BaseCrudEntity::getCreateTime, startDate)
			.lt(endDate != null, BaseCrudEntity::getCreateTime, endDate).count();
		//验收                  根据验收时间
		Integer acceptCount = projectProgressService.getAcceptCount(startDate, endDate);
		//后评价
		Integer evaluateCount = evaluateService.getFinishedCount(startDate, endDate);
		BoardProjectPhaseDto build = BoardProjectPhaseDto.builder().count(count)
			.acceptCount(acceptCount)
			.approveCount(approveCount)
			.evaluateCount(evaluateCount).build();
		return build;
	}


	/**
	 * 驾驶舱投资估算
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@Override
	public BoardProjectInvestmentDto projectInvestment(LocalDateTime startDate, LocalDateTime endDate) {
		//总投资
		BigDecimal projectInvestmentSum = projectBaseService.projectInvestmentCount(startDate, endDate);
		//根据列支渠道分类统计项目数量
		List<EchartCircleDto> list = projectBaseService.projectInvestment(startDate, endDate);
		return BoardProjectInvestmentDto.builder().channeList(list)
			.projectInvestmentSum(projectInvestmentSum.setScale(2, RoundingMode.HALF_UP)).build();
	}

	@Override
	public Boolean updateContractAmount(Long projectId, BigDecimal contractAmount) {
		return this.lambdaUpdate().eq(PlBase::getId, projectId)
			.set(PlBase::getContractAmount, contractAmount)
			.update();
	}

	@Override
	public Boolean updatePlanFunds(Long projectId, BigDecimal planFunds) {
		return this.lambdaUpdate().eq(PlBase::getId, projectId)
			.set(PlBase::getPlanFunds, planFunds)
			.update();
	}

	@Override
	public void clearProjectNo(Long id) {
		PlBase byId = getById(id);
		if (byId != null) {
			this.lambdaUpdate().eq(PlBase::getId, id).set(PlBase::getProjectNo, null).update();
		}

	}

	/**
	 * 首页项目管理统计
	 *
	 * @param v
	 * @return
	 */
	@Override
	public HomeProjectStatDto homeProjectStat(HomeProjectStatVo v) {
		v.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
		if (v.getUnitRange() == null) {
			//默认只看当前单位的
			v.setUnitRange(CommonConstant.ONE);
		}
		// 项目总数
		Integer count = this.baseMapper.projectCountByYear(v);
		//项目审查的数量
		Integer examineCount = this.baseMapper.projectExamineCountByYear(v);
		//验收的
		Integer haveAcceptCount = this.baseMapper.countAccept(v);
		Integer passCount = this.baseMapper.countPass(v);
		HomeProjectStatDto build = HomeProjectStatDto.builder()
			.acceptCount(haveAcceptCount)
			.count(count)
			.examineCount(examineCount)
			.passCount(passCount).build();
		return build;
	}

	/**
	 * 查询本年度和去年的
	 *
	 * @param year
	 * @param projectNode
	 * @param vo
	 * @return
	 */
	@Override
	public IPage<PlBaseDto> listByYearParam(Long deptId, Integer year, String projectNode, PlBaseDispatchVo vo) {
		Page<PlBase> page = new Page<>(vo.getCurrent(), vo.getSize());
		vo.setDeptId(deptId);
		vo.setYear(year);
		vo.setProjectNode(projectNode);
		IPage<PlBaseDto> resultPage = this.baseMapper.getPlBaseList(page, vo);
		return resultPage;
	}

	/**
	 * 根据业务范围查询项目
	 *
	 * @param deptIds
	 * @return
	 */
	@Override
	public List<PlBaseDto> listByDeptScope(List<Long> deptIds) {
		if (CollectionUtil.isEmpty(deptIds)) {
			return new ArrayList<>();
		}
		List<PlBase> list = this.lambdaQuery().in(PlBase::getCreateDept, deptIds).list();
		return BeanUtil.copy(list, PlBaseDto.class);

	}

	@Override
	public boolean updateFunds(Long id, BigDecimal planFunds, BigDecimal contractAmount, String remark, String supervisionUnit, String cooperationUnit) {
		LambdaUpdateWrapper<PlBase> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(PlBase::getPlanFunds, planFunds)
			.set(PlBase::getContractAmount, contractAmount)
			.set(PlBase::getRemark, remark)
			.set(StringUtil.isNotBlank(supervisionUnit), PlBase::getSupervisionUnit, supervisionUnit)
			.set(StringUtil.isNotBlank(cooperationUnit), PlBase::getCooperationUnit, cooperationUnit)
			.eq(PlBase::getId, id);
		return this.update(updateWrapper);
	}

	@Override
	public PlBaseDto update(PlBaseVo vo) {
		PlBase entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, PlBaseDto.class);
		} else {
			return null;
		}
	}
}
