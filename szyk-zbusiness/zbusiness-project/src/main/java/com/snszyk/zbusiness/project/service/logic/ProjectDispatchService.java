package com.snszyk.zbusiness.project.service.logic;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.constant.RedisConstant;
import com.snszyk.core.crud.dto.BaseCrudSlimDto;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.UserInfo;
import com.snszyk.system.enums.MenuEnum;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.service.IUserService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.task.constant.MsgContentConstant;
import com.snszyk.task.constant.MsgTitleConstant;
import com.snszyk.task.constant.MsgUrlConstant;
import com.snszyk.task.dto.SzykMsgDto;
import com.snszyk.task.dto.SzykMsgGenDto;
import com.snszyk.task.dto.SzykTaskDto;
import com.snszyk.task.dto.SzykTaskGenDto;
import com.snszyk.task.enums.NoticeNextOperationEnum;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.service.ISzykMsgService;
import com.snszyk.task.service.ISzykTaskService;
import com.snszyk.zbusiness.dict.dto.DispatchPeriodDetailDto;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.dict.enums.DispatchPeriodDetailStatusEnum;
import com.snszyk.zbusiness.dict.enums.DispatchPeriodStatusEnum;
import com.snszyk.zbusiness.dict.service.IDictMapService;
import com.snszyk.zbusiness.dict.service.IDispatchPeriodDetailService;
import com.snszyk.zbusiness.dict.service.IDispatchPeriodService;
import com.snszyk.zbusiness.knowledge.service.IBaseLogicService;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.enums.*;
import com.snszyk.zbusiness.project.service.*;
import com.snszyk.zbusiness.project.vo.*;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ProjectDispatchService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@Slf4j
@AllArgsConstructor
@Service
public class ProjectDispatchService extends BaseCrudLogicService<ProjectDispatchDto, ProjectDispatchVo> {

	private final IProjectDispatchService projectDispatchService;

	private final IProjectDispatchDetailService projectDispatchDetailService;

	private final IProjectDispatchLogService projectDispatchLogService;

	private final IProjectFileService projectFileService;

	private final IDispatchPeriodDetailService dispatchPeriodDetailService;

	private final IDispatchPeriodService dispatchPeriodService;

	private final IAttachService attachService;

	private final IDictMapService dictMapService;

	private final IDictBizService dictBizService;

	private final IUserService userService;

	private final IDeptService deptService;

	private final IPlExternalReferenceService plExternalReferenceService;

	private final ISzykTaskService szykTaskService;

	private final ISzykMsgService szykMsgService;

	private final SzykRedis szykRedis;

	private final IBaseLogicService baseLogicService;
	private final IPlBaseService plBaseService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectDispatchService;
	}

	public IPage<ProjectDispatchPageDto> page(ProjectDispatchPageVo vo, SzykUser szykUser) {
		// 查询当前人单位
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		List<Long> deptIdList = Collections.singletonList(dept.getId());
		//调度明细id
		List<Long> detailIdList = new ArrayList<>();
		//非下发机构调度明细
		List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDeptIdList(deptIdList);
		if (!CollectionUtils.isEmpty(detailList)) {
			detailIdList.addAll(detailList.stream().map(ProjectDispatchDetailDto::getDispatchId).collect(Collectors.toList()));
		}
		IPage<ProjectDispatchPageDto> page = projectDispatchService.pageList(vo, detailIdList, deptIdList);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}
		// 状态
		List<DictBiz> statusList = dictBizService.getList(DictBizEnum.DISPATCH_STATUS.getCode());
		Map<String, DictBiz> statusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(statusList)) {
			statusMap.putAll(statusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 项目明细
		List<ProjectDispatchDetailDto> list = projectDispatchDetailService.listByDispatchId((page.getRecords().stream().map(BaseCrudSlimDto::getId).collect(Collectors.toList())));
		Map<Long, List<ProjectDispatchDetailDto>> map = new HashMap<>();
		if (!CollectionUtils.isEmpty(list)) {
			map.putAll(list.stream().collect(Collectors.groupingBy(ProjectDispatchDetailDto::getDispatchId)));
		}
		for (ProjectDispatchPageDto dto : page.getRecords()) {
			// 状态
			if (!ObjectUtils.isEmpty(statusMap) && !ObjectUtils.isEmpty(dto.getDispatchStatus()) && statusMap.containsKey(dto.getDispatchStatus())) {
				dto.setDispatchStatusName(statusMap.get(dto.getDispatchStatus()).getDictValue());
			}

			// 是否是当前所在部门提交
			if (deptIdList.contains(dto.getSendCompanyId())) {
				dto.setFlag(true);
			} else {
				dto.setFlag(false);
			}
			if (!CollectionUtils.isEmpty(map) && map.containsKey(dto.getId())) {
				List<ProjectDispatchDetailDto> dispatchDetailList = map.get(dto.getId());
				List<Long> deptList = dispatchDetailList.stream().map(ProjectDispatchDetailDto::getPresentCompanyId).collect(Collectors.toList());
				for (Long deptId : deptList) {
					if (deptIdList.contains(deptId)) {
						dto.setDeptFlag(true);
						break;
					} else {
						dto.setDeptFlag(false);
					}
				}
			}
			//是否可以删除
			dto.setDelFlag(false);
			if (Objects.equals(dto.getDispatchStatus(), DispatchStatusEnum.REPORT.getCode()) || Objects.equals(dto.getDispatchStatus(), DispatchStatusEnum.ISSUE.getCode())) {
				if (DeptScopeUtil.canDel(dto.getSendCompanyId(), dto.getCreateUser())) {
					dto.setDelFlag(true);
				}
			}
			if (Func.isNotEmpty(dto.getSendCompanyId())) {
				dto.setSendCompanyName(SysCache.getDeptName(dto.getSendCompanyId()));
			}
		}
		return page;
	}

	public ProjectDispatchDto detail(Long id, SzykUser szykUser) {

		ProjectDispatchDto result = projectDispatchService.fetchById(id);
		if (result == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		boolean flag = false;
		Long deptId = Long.valueOf(AuthUtil.getDeptId());
		// 判断当前部门是下发单位
		if (Objects.equals(deptId, result.getSendCompanyId())) {
			flag = true;
		}

		// 调度状态
		List<DictBiz> statusList = dictBizService.getList(DictBizEnum.DISPATCH_STATUS.getCode());
		Map<String, DictBiz> statusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(statusList)) {
			statusMap.putAll(statusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 调度状态
		if (!ObjectUtils.isEmpty(statusMap) && !ObjectUtils.isEmpty(result.getDispatchStatus()) && statusMap.containsKey(result.getDispatchStatus())) {
			result.setDispatchStatusName(statusMap.get(result.getDispatchStatus()).getDictValue());
		}

		// 查询全数据/权限数据
		List<ProjectDispatchDetailDto> list = projectDispatchDetailService.listByDispatchIdAndDeptId(id, flag ? null : Collections.singletonList(deptId));
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		Map<String, String> phaseMap = dictMapService.getProjectPhaseDictMap();

		// 项目调度状态
		List<DictBiz> dispatchStatusList = dictBizService.getList(DictBizEnum.PROJECT_DISPATCH_STATUS.getCode());
		Map<String, DictBiz> dispatchStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dispatchStatusList)) {
			dispatchStatusMap.putAll(dispatchStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 推进进度
		List<DictBiz> pushProgressList = dictBizService.getList(DictBizEnum.PUSH_PROGRESS.getCode());
		Map<String, DictBiz> pushProgressMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pushProgressList)) {
			pushProgressMap.putAll(pushProgressList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		if (result.getDispatchStatus().equals(DispatchStatusEnum.ISSUE.getCode())) {
			result.setBackFlag(false);
		} else {
			result.setBackFlag(true);
		}

		for (ProjectDispatchDetailDto detailDto : list) {
			// 项目阶段
			if (!ObjectUtils.isEmpty(phaseMap) && !ObjectUtils.isEmpty(detailDto.getProjectPhase()) && phaseMap.containsKey(detailDto.getProjectPhase())) {
				detailDto.setProjectPhaseName(phaseMap.get(detailDto.getProjectPhase()));
			}

			// 推进进度
			if (!ObjectUtils.isEmpty(pushProgressMap) && !ObjectUtils.isEmpty(detailDto.getPushProgress()) && pushProgressMap.containsKey(detailDto.getPushProgress())) {
				detailDto.setPushProgressName(pushProgressMap.get(detailDto.getPushProgress()).getDictValue());
			}

			// 项目调度状态
			if (!ObjectUtils.isEmpty(dispatchStatusMap) && !ObjectUtils.isEmpty(detailDto.getProjectDispatchStatus()) && dispatchStatusMap.containsKey(detailDto.getProjectDispatchStatus())) {
				detailDto.setProjectDispatchStatusName(dispatchStatusMap.get(detailDto.getProjectDispatchStatus()).getDictValue());
			}

			// 交付物
			List<ProjectFileDto> fileDtoList = projectFileService.listByProject(detailDto.getProjectId(), detailDto.getId(), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
			if (!CollectionUtils.isEmpty(fileDtoList)) {
				// 附件ID
				List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (!CollectionUtils.isEmpty(attachList)) {
					detailDto.setFileList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
				}
			}

			if (!StringUtils.isEmpty(detailDto.getPresentCompanyId()) && Objects.equals(deptId, detailDto.getPresentCompanyId())) {
				detailDto.setFlag(true);
			} else {
				detailDto.setFlag(false);
			}

//			// 顶级节点不能提报
//			if (!StringUtils.isEmpty(detailDto.getPresentCompanyId()) && result.getSendCompanyId().equals(detailDto.getPresentCompanyId())) {
//				detailDto.setFlag(false);
//			}
			//获取最新组织信息
			if (Func.isNotEmpty(detailDto.getCompanyId())) {
				detailDto.setCompanyName(SysCache.getDeptName(detailDto.getCompanyId()));
			}
			if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
				if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
					Dept dept = SysCache.getDept(detailDto.getConstructionUnitId());
					if (Func.isNotEmpty(dept)) {
						if (dept.getUnitId().equals(dept.getId())) {
							detailDto.setConstructionUnitName(dept.getDeptName());
						} else {
							detailDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
						}
					}
				}
			}
			if (Func.isNotEmpty(detailDto.getOperateCompanyId())) {
				detailDto.setOperateCompanyName(SysCache.getDeptName(detailDto.getOperateCompanyId()));
			}

		}

		result.setList(list);


		List<Long> presentCompanyIdList = list.stream().map(ProjectDispatchDetailDto::getPresentCompanyId).collect(Collectors.toList());
		for (Long presentCompanyId : presentCompanyIdList) {
			if (deptId.equals(presentCompanyId)) {
				result.setDeptFlag(true);
				break;
			} else {
				result.setDeptFlag(false);
			}
		}
		if (Func.isNotEmpty(result.getSendCompanyId())) {
			result.setSendCompanyName(SysCache.getDeptName(result.getSendCompanyId()));
		}

		return result;
	}

	public List<ProjectDispatchDetailDto> projectDetail(ProjectDispatchProjectVo vo, SzykUser szykUser) {

		ProjectDispatchDto result = projectDispatchService.fetchById(vo.getId());
		if (result == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		boolean flag = false;

		// 查询当前人所在部门
		List<Long> deptIdList = userService.queryDeptByUserId(szykUser.getUserId());
		if (CollectionUtils.isEmpty(deptIdList)) {
			return null;
		}

		// 判断当前部门是否是创建人所在部门
		if (deptIdList.contains(result.getSendCompanyId())) {
			flag = true;
		}

		// 查询全数据/权限数据
		List<ProjectDispatchDetailDto> list = projectDispatchDetailService.listByDispatchIdAndDeptId(vo.getId(), flag ? null : deptIdList, vo.getProjectName(), vo.getCompanyName(), vo.getConstructionUnitName());
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		Map<String, String> phaseMap = dictMapService.getProjectPhaseDictMap();

		// 项目调度状态
		List<DictBiz> dispatchStatusList = dictBizService.getList(DictBizEnum.PROJECT_DISPATCH_STATUS.getCode());
		Map<String, DictBiz> dispatchStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dispatchStatusList)) {
			dispatchStatusMap.putAll(dispatchStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 推进进度
		List<DictBiz> pushProgressList = dictBizService.getList(DictBizEnum.PUSH_PROGRESS.getCode());
		Map<String, DictBiz> pushProgressMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pushProgressList)) {
			pushProgressMap.putAll(pushProgressList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		if (result.getDispatchStatus().equals(DispatchStatusEnum.ISSUE.getCode())) {
			result.setBackFlag(false);
		} else {
			result.setBackFlag(true);
		}

		for (ProjectDispatchDetailDto detailDto : list) {
			// 项目阶段
			if (!ObjectUtils.isEmpty(phaseMap) && !ObjectUtils.isEmpty(detailDto.getProjectPhase()) && phaseMap.containsKey(detailDto.getProjectPhase())) {
				detailDto.setProjectPhaseName(phaseMap.get(detailDto.getProjectPhase()));
			}

			// 推进进度
			if (!ObjectUtils.isEmpty(pushProgressMap) && !ObjectUtils.isEmpty(detailDto.getPushProgress()) && pushProgressMap.containsKey(detailDto.getPushProgress())) {
				detailDto.setPushProgressName(pushProgressMap.get(detailDto.getPushProgress()).getDictValue());
			}

			// 项目调度状态
			if (!ObjectUtils.isEmpty(dispatchStatusMap) && !ObjectUtils.isEmpty(detailDto.getProjectDispatchStatus()) && dispatchStatusMap.containsKey(detailDto.getProjectDispatchStatus())) {
				detailDto.setProjectDispatchStatusName(dispatchStatusMap.get(detailDto.getProjectDispatchStatus()).getDictValue());
			}

			// 交付物
			List<ProjectFileDto> fileDtoList = projectFileService.listByProject(detailDto.getProjectId(), detailDto.getId(), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
			if (!CollectionUtils.isEmpty(fileDtoList)) {
				// 附件ID
				List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (!CollectionUtils.isEmpty(attachList)) {
					detailDto.setFileList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
				}
			}

			if (deptIdList.contains(detailDto.getPresentCompanyId())) {
				detailDto.setFlag(true);
			} else {
				detailDto.setFlag(false);
			}

//			// 顶级节点不能提报
//			if (!StringUtils.isEmpty(detailDto.getPresentCompanyId()) && result.getSendCompanyId().equals(detailDto.getPresentCompanyId())) {
//				detailDto.setFlag(false);
//			}
			//获取最新组织信息
			if (Func.isNotEmpty(detailDto.getCompanyId())) {
				detailDto.setCompanyName(SysCache.getDeptName(detailDto.getCompanyId()));
			}
			if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
				if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
					Dept dept = SysCache.getDept(detailDto.getConstructionUnitId());
					if (Func.isNotEmpty(dept)) {
						if (dept.getUnitId().equals(dept.getId())) {
							detailDto.setConstructionUnitName(dept.getDeptName());
						} else {
							detailDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
						}
					}
				}
			}
			if (Func.isNotEmpty(detailDto.getOperateCompanyId())) {
				detailDto.setOperateCompanyName(SysCache.getDeptName(detailDto.getOperateCompanyId()));
			}
		}

		return list;
	}

	public List<ProjectDispatchLogDto> logList(Long id) {
		List<ProjectDispatchLogDto> list = projectDispatchLogService.listByDispatchDetailId(id);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		// 操作
		List<DictBiz> statusList = dictBizService.getList(DictBizEnum.OPERATE.getCode());
		Map<String, DictBiz> statusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(statusList)) {
			statusMap.putAll(statusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		for (ProjectDispatchLogDto logDto : list) {
			UserInfo user = userService.userInfo(logDto.getOperatorId());
			logDto.setAvatar(user == null ? null : user.getUser().getAvatar());

			// 操作
			if (!ObjectUtils.isEmpty(statusMap) && !ObjectUtils.isEmpty(logDto.getOperate()) && statusMap.containsKey(logDto.getOperate())) {
				logDto.setOperateName(statusMap.get(logDto.getOperate()).getDictValue());
			}
			//获取最新组织信息
			if (Func.isNotEmpty(logDto.getOperatorCompanyId())) {
				logDto.setOperatorCompanyName(SysCache.getDeptName(logDto.getOperatorCompanyId()));
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProjectDispatchDto save(ProjectDispatchVo vo) {
		ProjectDispatchDto result = new ProjectDispatchDto();
		if (StringUtils.isEmpty(vo.getId())) {
			vo.setDispatchNo(new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
			vo.setDispatchStatus(DispatchStatusEnum.ISSUE.getCode());
			//如果有调度周期,生成周期的开始和结束时间
			if (vo.getDispatchPeriodDetailId() != null) {
				DispatchPeriodDetailDto detailDto = dispatchPeriodDetailService.fetchById(vo.getDispatchPeriodDetailId());
				vo.setPeriodStartTime(detailDto == null ? null : detailDto.getPeriodStartTime());
				vo.setPeriodEndTime(detailDto == null ? null : detailDto.getPeriodEndTime());
			}
		}
		vo.setDispatchStatus(DispatchStatusEnum.ISSUE.getCode());

		if (StringUtils.isEmpty(vo.getId())) {
			result = projectDispatchService.save(vo);
		} else {
			result = projectDispatchService.update(vo);
		}
		if (!StringUtils.isEmpty(vo.getId())) {
			List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDispatchId(vo.getId());
			if (!CollectionUtils.isEmpty(detailList)) {
				List<Long> ids = detailList.stream().map(ProjectDispatchDetailDto::getId).collect(Collectors.toList());
				// 删除调度日志
				projectDispatchLogService.deleteByDispatchDetailId(ids);

				for (ProjectDispatchDetailDto detailDto : detailList) {
					// 删除交付物数据
					projectFileService.deleteByProjectId(detailDto.getProjectId(), Arrays.asList(detailDto.getId()), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
				}
			}
			// 删除调度明细
			projectDispatchDetailService.deleteByDispatchId(vo.getId());
		}

		for (ProjectDispatchDetailVo detailVo : vo.getList()) {
			detailVo.setDispatchId(result.getId());
			detailVo.setProjectDispatchStatus(ProjectDispatchStatusEnum.ISSUE.getCode());
			detailVo.setPresentCompanyId(detailVo.getConstructionUnitId());
			//获取中间单位
			String ancestors = deptService.getAncestors(detailVo.getConstructionUnitId(), vo.getSendCompanyId());
			if (StringUtils.isEmpty(ancestors)) {
				throw new ServiceException(ExceptionEnum.DEPT_NOT_EXIST_PARENT.getMessage());
			}
			detailVo.setAncestors(ancestors);
			ProjectDispatchDetailDto detailDto = projectDispatchDetailService.save(detailVo);

			if (CollectionUtils.isEmpty(detailVo.getFileList())) {
				continue;
			}
			for (Long fileId : detailVo.getFileList()) {
				ProjectFileVo fileVo = new ProjectFileVo();
				fileVo.setProjectId(detailDto.getProjectId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(detailDto.getId());
				fileVo.setBusinessType(ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
				projectFileService.save(fileVo);
			}
			// 自动收集
			//baseLogicService.autoCollectKnowledge(detailVo.getFileList(), result.getId(), result.getDispatchNo());
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public ProjectDispatchDto submit(ProjectDispatchVo vo, SzykUser szykUser) throws InterruptedException {

		ProjectDispatchDto result = new ProjectDispatchDto();
		//调度周期明细
		DispatchPeriodDetailDto periodDetailDto = dispatchPeriodDetailService.fetchById(vo.getDispatchPeriodDetailId());
		vo.setPeriodStartTime(periodDetailDto == null ? null : periodDetailDto.getPeriodStartTime());
		vo.setPeriodEndTime(periodDetailDto == null ? null : periodDetailDto.getPeriodEndTime());
		vo.setDispatchStatus(DispatchStatusEnum.REPORT.getCode());
		//如果没有调度单号则在下发时添加调度单号
		if (StringUtil.isBlank(vo.getDispatchNo())) {
			vo.setDispatchNo(new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));
		}
		if (StringUtils.isEmpty(vo.getId())) {
			result = projectDispatchService.save(vo);
		} else {
			result = projectDispatchService.update(vo);
		}

		if (!StringUtils.isEmpty(vo.getId())) {
			List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDispatchId(vo.getId());
			if (!CollectionUtils.isEmpty(detailList)) {
				List<Long> ids = detailList.stream().map(ProjectDispatchDetailDto::getId).collect(Collectors.toList());
				// 删除调度日志
				projectDispatchLogService.deleteByDispatchDetailId(ids);

				for (ProjectDispatchDetailDto detailDto : detailList) {
					// 删除交付物数据
					projectFileService.deleteByProjectId(detailDto.getProjectId(), Arrays.asList(detailDto.getId()), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
				}
			}

			// 删除调度明细
			projectDispatchDetailService.deleteByDispatchId(vo.getId());
		}

		for (ProjectDispatchDetailVo detailVo : vo.getList()) {
			detailVo.setDispatchId(result.getId());
			detailVo.setProjectDispatchStatus(ProjectDispatchStatusEnum.REPORT.getCode());
			detailVo.setPresentCompanyId(detailVo.getConstructionUnitId());

			String ancestors = deptService.getAncestors(detailVo.getConstructionUnitId(), vo.getSendCompanyId());
			if (StringUtils.isEmpty(ancestors)) {
				throw new ServiceException(ExceptionEnum.DEPT_NOT_EXIST_PARENT.getMessage());
			}
			detailVo.setAncestors(ancestors);

			ProjectDispatchDetailDto detailDto = projectDispatchDetailService.save(detailVo);

			if (CollectionUtils.isEmpty(detailVo.getFileList())) {
				continue;
			}

			for (Long fileId : detailVo.getFileList()) {
				ProjectFileVo fileVo = new ProjectFileVo();
				fileVo.setProjectId(detailDto.getProjectId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(detailDto.getId());
				fileVo.setBusinessType(ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());

				projectFileService.save(fileVo);
			}
			// 自动收集
			baseLogicService.autoCollectKnowledge(detailVo.getFileList(), detailDto.getId(), detailDto.getProjectNo());
		}

		// 更新状态
//		dispatchPeriodDetailService.updateByDetailStatus(vo.getDispatchPeriodDetailId(), DispatchPeriodDetailStatusEnum.NO.getCode());
		if (periodDetailDto == null) {
			return result;
		}
		List<DispatchPeriodDetailDto> list = dispatchPeriodDetailService.listByPeriodId(periodDetailDto.getDispatchPeriodId());
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}
		List<DispatchPeriodDetailDto> filterList = list.stream().filter(item -> item.getPeriodDetailStatus().equals(DispatchPeriodDetailStatusEnum.YES.getCode())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(filterList)) {
			dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.ALL_CLOSE.getCode());
		}
		if (!CollectionUtils.isEmpty(filterList) && filterList.size() == list.size()) {
			dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.ALL_OPEN.getCode());
		}
		if (!CollectionUtils.isEmpty(filterList) && filterList.size() != list.size()) {
			dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.PART_OPEN.getCode());
		}
		//消息埋点
		SpringUtil.getBean(ProjectDispatchService.class).handleTaskAndMsg(result, BeanUtil.copy(vo.getList(), ProjectDispatchDetailDto.class));
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> issue(ProjectDispatchDeleteVo vo, SzykUser szykUser) throws InterruptedException {
		List<ProjectDeleteDto> result = new ArrayList<>();
		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}
		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();
			result.add(deleteDto);
			ProjectDispatchDto projectDispatchDto = projectDispatchService.fetchById(id);
			deleteDto.setName(projectDispatchDto.getDispatchNo());

			List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDispatchId(id);
			//v1.5错误信息校验
			StringBuilder errorBuilder = new StringBuilder();
			if (CollectionUtils.isEmpty(detailList)) {
				deleteDto.setResult(false);
				errorBuilder.append(ExceptionEnum.DISPATCH_NOT_DETAIL.getMessage()).append("，");
			}
			//调度周期
			if (projectDispatchDto.getDispatchPeriodDetailId() == null) {
				deleteDto.setResult(false);
				errorBuilder.append("调度周期不能为空" + "，");
			}
			//调度截止日期
			if (projectDispatchDto.getDispatchDeadline() == null) {
				deleteDto.setResult(false);
				errorBuilder.append("调度截止日期不能为空" + "，");
			}
			if (!errorBuilder.toString().isEmpty()) {
				String errorMsg = errorBuilder.toString();
				deleteDto.setMessage(errorMsg.substring(0, errorMsg.length() - 1));
				continue;
			}

			projectDispatchService.updateByStatus(id, DispatchStatusEnum.REPORT.getCode());
			projectDispatchDetailService.updateByStatus(id, ProjectDispatchStatusEnum.REPORT.getCode());

			// 更新状态
//			dispatchPeriodDetailService.updateByDetailStatus(projectDispatchDto.getDispatchPeriodDetailId(), DispatchPeriodDetailStatusEnum.NO.getCode());

			DispatchPeriodDetailDto periodDetailDto = dispatchPeriodDetailService.fetchById(projectDispatchDto.getDispatchPeriodDetailId());
			if (periodDetailDto == null) {
				deleteDto.setResult(false);
				deleteDto.setMessage("该调度周期不存在");
				continue;
			}
			List<DispatchPeriodDetailDto> list = dispatchPeriodDetailService.listByPeriodId(periodDetailDto.getDispatchPeriodId());
			if (CollectionUtils.isEmpty(list)) {
				deleteDto.setResult(false);
				deleteDto.setMessage("该调度周期不存在");
				continue;
			}
			List<DispatchPeriodDetailDto> filterList = list.stream().filter(item -> item.getPeriodDetailStatus().equals(DispatchPeriodDetailStatusEnum.YES.getCode())).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(filterList)) {
				dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.ALL_CLOSE.getCode());
			}
			if (!CollectionUtils.isEmpty(filterList) && filterList.size() == list.size()) {
				dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.ALL_OPEN.getCode());
			}
			if (!CollectionUtils.isEmpty(filterList) && filterList.size() != list.size()) {
				dispatchPeriodService.updateByStatus(periodDetailDto.getDispatchPeriodId(), DispatchPeriodStatusEnum.PART_OPEN.getCode());
			}

			deleteDto.setResult(true);
			//消息埋点
			SpringUtil.getBean(ProjectDispatchService.class).handleTaskAndMsg(projectDispatchDto, detailList);
			if (CollectionUtil.isNotEmpty(detailList)) {
				detailList.forEach(detailDto -> {
					List<ProjectFileDto> fileDtoList = projectFileService.listByProject(detailDto.getProjectId(), detailDto.getId(), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
					if (!CollectionUtils.isEmpty(fileDtoList)) {
						// 附件ID
						List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());
						baseLogicService.autoCollectKnowledge(attachIds, detailDto.getId(), detailDto.getProjectNo());
					}
				});
			}

		}
		return result;
	}

	public void handleTaskAndMsg(ProjectDispatchDto projectDispatchDto, List<ProjectDispatchDetailDto> detailList) throws InterruptedException {

		Integer sendCount = 0;
		//待办埋点1 发送给建设单位有项目调度编辑权限的人 待办
		//待办任务
		List<Long> deptIds = detailList.stream().filter(e -> e.getConstructionUnitId() != null).map(e -> e.getConstructionUnitId()).collect(Collectors.toList());
//		List<UserDTO> personListByDeptIds = userService.getContactPersonListByDeptIds(deptIds);
		List<UserDTO> personListByDeptIds = userService.getContactPersonListByDeptMenu(deptIds, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());

		//待办的userIds

		List<Long> todoUserIds = personListByDeptIds.stream().map(e -> e.getId()).collect(Collectors.toList());
		Long id = projectDispatchDto.getId();
		//接收人的set
		Set<Long> receiverIdSet = new HashSet<>();

		for (ProjectDispatchDetailDto detailDto : detailList) {
//			List<UserDTO> userDTOS = userService.getContactPersonListByDeptRole(detailDto.getConstructionUnitId(), Collections.singletonList(RoleEnum.DEFAULT.getName()));
			List<UserDTO> userDTOS = userService.getContactPersonListByDeptMenu(detailDto.getConstructionUnitId(), MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());
			SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
			szykTaskGenDto.setTitle(MsgTitleConstant.ACCEPTANCE_DISPATCH);
			szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
			szykTaskGenDto.setBusinessId(id);//调度id
			szykTaskGenDto.setUrl(String.format(MsgUrlConstant.DISPATCH_SUBMIT, id, false) + "?deptScopeId=" + detailDto.getConstructionUnitId());
			szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_SUBMIT.getCode());
			if (CollectionUtils.isEmpty(userDTOS)) {
				continue;
			}
			//同一个调度,已经发过待办的人不再重新发送
			userDTOS = userDTOS.stream().filter(e -> !receiverIdSet.contains(e.getId())).collect(Collectors.toList());
			for (UserDTO userDTO : userDTOS) {
				receiverIdSet.add(userDTO.getId());
			}
			if (CollectionUtil.isNotEmpty(userDTOS)) {
				String userIds = userDTOS.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.joining(","));
				szykTaskGenDto.setReceiverId(userIds);
				String content = String.format(MsgContentConstant.DISPATCH_ISSUE, projectDispatchDto.getSendCompanyName());
				szykTaskGenDto.setContent(content);
				szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, id));
				szykTaskGenDto.setDeptId(detailDto.getConstructionUnitId());
				//多个项目都有此人做为建设单位联络人,只发送一次
				szykTaskService.genTodoTask(szykTaskGenDto, true);//
				sendCount++;
				if (sendCount == 10) {
					Thread.sleep(1000);
					sendCount = 0;
				}
			}
		}
		//消息埋点2   除建设单位、下发单位之外的下级（所有层级）有项目调度编辑权限的人 发送提醒消息
		Long issueCompanyId = projectDispatchDto.getSendCompanyId();
		//需要发送消息的部门
		List<Long> needSendMessageDeptIds = new ArrayList<>();
		for (ProjectDispatchDetailDto detailDto : detailList) {
			Long constructionUnitId = detailDto.getConstructionUnitId();
			List<Long> deptIdList = deptService.getMiddleDept(issueCompanyId, constructionUnitId);
			if (CollectionUtil.isNotEmpty(deptIdList)) {
				needSendMessageDeptIds.addAll(deptIdList);
			}
		}
		//需要发送消息的人
//		List<UserDTO> needSendMessageUserList = userService.getContactPersonListByDeptIds(needSendMessageDeptIds);
		List<UserDTO> needSendMessageUserList = userService.getContactPersonListByDeptMenu(needSendMessageDeptIds, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());
		//多个人的通知
		if (CollectionUtil.isNotEmpty(needSendMessageUserList)) {
			for (UserDTO userDTO : needSendMessageUserList) {
				if (todoUserIds.contains(userDTO.getId())) {
					continue;
				}
				SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
				szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
				szykMsgDto.setBusinessId(id);
				szykMsgDto.setUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, id) + "?deptScopeId=" + userDTO.getDeptId());
				szykMsgDto.setReceiverId(userDTO.getId());
				szykMsgDto.setDeptId(Long.valueOf(userDTO.getDeptId()));
				szykMsgDto.setContent(String.format(MsgContentConstant.DISPATCH_DETAIL));
				szykMsgService.genMsg(szykMsgDto);
				sendCount++;
				if (sendCount == 10) {
					Thread.sleep(1000);
					sendCount = 0;
				}
			}
		}
//		Duration between = DateUtil.between(new Date(), projectDispatchDto.getDispatchDeadline());
//		long millis = between.toMillis() - 1000 * 60 * 60 * 15;
//		//redis过期监听
//		szykRedis.setEx(RedisConstant.PROJECT_DISPATCH_DEADLINE_PREFIX + id, 1, millis / 1000);
		Date limitDate = projectDispatchDto.getDispatchDeadline();
		//如果是今天
		if (limitDate.compareTo(new Date()) <= 0) {
			return;
		}
		//获取截止日期前一天的9点
		LocalDateTime of = LocalDateTimeUtil.of(limitDate);
		LocalDateTime nineTime = of.minusDays(1L).withHour(9).withMinute(0).withSecond(0);
		//当前时间和 前一天的9点的间隔
		long millis = Duration.between(LocalDateTime.now(), nineTime).toMillis();
		if (millis <= 0) {
			millis = 1000 * 10;
		}

		szykRedis.setEx(RedisConstant.PROJECT_DISPATCH_DEADLINE_PREFIX + id, 1, millis / 1000);
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> delete(ProjectDispatchDeleteVo vo) {
		List<ProjectDeleteDto> result = new ArrayList<>();

		Dept loginDept = DeptScopeUtil.getLoginDept();
		Long loginDeptId = loginDept.getId();
		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();
			ProjectDispatchDto projectDispatchDto = projectDispatchService.fetchById(id);
			deleteDto.setName(projectDispatchDto.getDispatchNo());
			//v1.5仅待下发.待提报待提状态的可以删除
			if (!Objects.equals(DispatchStatusEnum.ISSUE.getCode(), projectDispatchDto.getDispatchStatus()) && !Objects.equals(DispatchStatusEnum.REPORT.getCode(), projectDispatchDto.getDispatchStatus())) {
				deleteDto.setResult(false);
				deleteDto.setMessage("仅待下发,待提报状态的调度可以删除!");
				result.add(deleteDto);
				continue;
			}
			//只能删除自己的
			if (!DeptScopeUtil.canDel(projectDispatchDto.getSendCompanyId(), projectDispatchDto.getCreateUser())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.NO_SELF_CANT_DEL.getMessage());
				result.add(deleteDto);
				continue;
			}
			if (!Objects.equals(loginDeptId, projectDispatchDto.getSendCompanyId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage("非下发单位不能删除此调度信息!");
				result.add(deleteDto);
				continue;
			}
			// 是否
			Boolean flag = projectDispatchService.deleteById(id);
			//删除待办任务
			if (flag) {
				//删除钉钉待办
				szykTaskService.delTaskAndMsg(Arrays.asList(ToDoBusinessTypeEnum.PROJECT_DISPATCH), id);

			}
			deleteDto.setResult(flag);

			List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDispatchId(id);
			if (!CollectionUtils.isEmpty(detailList)) {
				List<Long> ids = detailList.stream().map(ProjectDispatchDetailDto::getId).collect(Collectors.toList());
				// 删除调度日志
				projectDispatchLogService.deleteByDispatchDetailId(ids);

				for (ProjectDispatchDetailDto detailDto : detailList) {
					// 删除交付物数据
					projectFileService.deleteByProjectId(detailDto.getProjectId(), Arrays.asList(detailDto.getId()), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());
				}
			}

			// 删除调度明细
			projectDispatchDetailService.deleteByDispatchId(id);

			result.add(deleteDto);
		}

		return result;
	}

	/**
	 * 提报
	 * @param vo
	 * @param szykUser
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean report(ProjectDispatchReportVo vo, SzykUser szykUser) {
		// 查询当前人所在部门
		Long loginDeptId = Long.valueOf(AuthUtil.getDeptId());
		ProjectDispatchDto dispatchDto = projectDispatchService.fetchById(vo.getId());
		if (dispatchDto == null) {
			return false;
		}

		//v1.5退回待办 使用
		boolean backFlag = false;
		// 判断当前部门是创建人所在部门
		if (Objects.equals(loginDeptId, dispatchDto.getSendCompanyId())) {
			backFlag = true;
		}
		// 查询全数据/权限数据
		List<ProjectDispatchDetailDto> oldList = projectDispatchDetailService.listByDispatchIdAndDeptId(dispatchDto.getId(), backFlag ? null : Collections.singletonList(loginDeptId));
		//之前退回的list
		List<ProjectDispatchDetailDto> oldBackList = oldList.stream().filter(e -> Objects.equals(e.getProjectDispatchStatus(), ProjectDispatchStatusEnum.EXIT.getCode())).collect(Collectors.toList());

		// 查询明细
		List<Long> detailIdList = vo.getList().stream().map(ProjectDispatchDetailVo::getId).collect(Collectors.toList());
		List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByIdList(detailIdList);
		if (CollectionUtils.isEmpty(detailList)) {
			return false;
		}
		Map<Long, ProjectDispatchDetailDto> detailMap = detailList.stream().collect(Collectors.toMap(ProjectDispatchDetailDto::getId, Function.identity()));
		// 校验是否存在提报权限
		for (ProjectDispatchDetailDto detailDto : detailList) {
			if (!Objects.equals(loginDeptId, detailDto.getPresentCompanyId())) {
				throw new ServiceException(detailDto.getProjectNo() + ExceptionEnum.PROJECT_NOT_AUTH.getMessage());
			}
		}

		//+++消息埋点使用 获取上级单位id
		Long upDeptId = null;
		String currentDetpName = null;
		for (ProjectDispatchDetailVo detailVo : vo.getList()) {
			if (!detailMap.containsKey(detailVo.getId())) {
				continue;
			}

			ProjectDispatchDetailDto detailDto = detailMap.get(detailVo.getId());
			BeanUtil.copyProperties(detailVo, detailDto);

			detailDto.setProjectDispatchStatus(ProjectDispatchStatusEnum.SUBMITTED.getCode());
			detailDto.setOperateCompanyId(detailDto.getPresentCompanyId());
			Dept dept = SysCache.getDept(detailDto.getPresentCompanyId());
			detailDto.setOperateCompanyName(dept == null ? null : dept.getDeptName());
			//埋点用 当前的部门名
			if (StringUtil.isBlank(currentDetpName)) {
				Long unitId = dept.getUnitId();
				if (!Objects.equals(unitId, dept.getId())) {
					Dept unit = SysCache.getDept(unitId);
					currentDetpName = unit.getDeptName() + "/" + dept.getDeptName();
				} else {
					currentDetpName = dept.getDeptName();
				}
			}
			List<String> ancestorsList = new ArrayList<>(Arrays.asList(detailDto.getAncestors().split(",")));
//			//获取建设单位最新的祖级列表
//			if (Func.isNotEmpty(detailVo.getConstructionUnitId())) {
//				ancestorsList = unitFromNow(detailDto.getConstructionUnitId());
//			}
			if (CollectionUtils.isEmpty(ancestorsList)) {
				continue;
			}
			int num = 0;
			for (int i = 0; i < ancestorsList.size(); i++) {
				if (ancestorsList.get(i).equals(String.valueOf(detailDto.getPresentCompanyId()))) {
					num = i + 1;
				}
			}
//			// 最后一个单位不需要提报
//			if (num >= ancestorsList.size()) {
//				continue;
//			}

			if (num == 1) {
				detailDto.setSubmitPersonId(szykUser.getUserId());
				detailDto.setSubmitPersonName(szykUser.getNickName());
				detailDto.setSubmitTime(new Date());
			}

			if (num == ancestorsList.size()) {
				num = ancestorsList.size() - 1;
			}
			long deptId = Long.parseLong(ancestorsList.get(num));
			if (upDeptId == null) {
				upDeptId = deptId;
			}
			detailDto.setPresentCompanyId(deptId);

			projectDispatchDetailService.updateById(detailDto);
			//20241205 同步修改项目库的合同金额
			BigDecimal contractAmount = detailDto.getContractAmount();
			plBaseService.updateContractAmount(detailDto.getProjectId(), contractAmount);
			BigDecimal planFunds = detailDto.getPlanFunds();
			//同步修改项目库的计划资金 20250113
			plBaseService.updatePlanFunds(detailDto.getProjectId(), planFunds);
			// 删除交付物数据
			projectFileService.deleteByProjectId(detailDto.getProjectId(), Arrays.asList(detailDto.getId()), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());

			if (!CollectionUtils.isEmpty(detailVo.getFileList())) {
				for (Long fileId : detailVo.getFileList()) {
					ProjectFileVo fileVo = new ProjectFileVo();
					fileVo.setProjectId(detailDto.getProjectId());
					fileVo.setAttachId(fileId);
					fileVo.setBusinessId(detailDto.getId());
					fileVo.setBusinessType(ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());

					projectFileService.save(fileVo);
				}
				// 自动收集
				baseLogicService.autoCollectKnowledge(detailVo.getFileList(), dispatchDto.getId(), dispatchDto.getDispatchNo());
			}
			// 调度记录
			ProjectDispatchLogVo logVo = new ProjectDispatchLogVo();
			logVo.setProjectDispatchDetailId(detailDto.getId());
			logVo.setOperatorId(szykUser.getUserId());
			logVo.setOperatorName(szykUser.getNickName());
			logVo.setOperatorCompanyId(detailDto.getOperateCompanyId());
			logVo.setOperatorCompanyName(SysCache.getDeptName(detailDto.getOperateCompanyId()));

			logVo.setOperate(OperateEnum.REPORT.getCode());
			logVo.setOperateTime(new Date());
			logVo.setRemark(null);

			projectDispatchLogService.save(logVo);
		}

		List<ProjectDispatchDetailDto> detailAllList = projectDispatchDetailService.listByDispatchId(vo.getId());

		List<ProjectDispatchDetailDto> filterList = detailAllList.stream().filter(item -> item.getProjectDispatchStatus().equals(ProjectDispatchStatusEnum.SUBMITTED.getCode())).collect(Collectors.toList());

		Long sendCompanyId = dispatchDto.getSendCompanyId();
		Dept sendCompany = SysCache.getDept(sendCompanyId);
		Long parentId = sendCompany.getParentId();
		if (parentId == 0) {
			parentId = sendCompanyId;
		}
		Long finalParentId = parentId;
		List<ProjectDispatchDetailDto> submitFilterList = filterList.stream().filter(e -> Objects.equals(e.getPresentCompanyId(), sendCompanyId) || Objects.equals(e.getPresentCompanyId(), finalParentId)).collect(Collectors.toList());
		// 全部提报
		if (!CollectionUtils.isEmpty(submitFilterList) && detailAllList.size() == submitFilterList.size()) {
			//下发单位提报||提报给下发单位
			if (Objects.equals(loginDeptId, dispatchDto.getSendCompanyId()) || Objects.equals(upDeptId, dispatchDto.getSendCompanyId())) {
				projectDispatchService.updateByStatus(vo.getId(), DispatchStatusEnum.ALL_REPORT.getCode());
			}
		}
		// 部分提报
		if (!CollectionUtils.isEmpty(submitFilterList) && detailAllList.size() != submitFilterList.size()) {
			//下发单位提报||提报给下发单位
			if (Objects.equals(loginDeptId, dispatchDto.getSendCompanyId()) || Objects.equals(upDeptId, dispatchDto.getSendCompanyId())) {
				projectDispatchService.updateByStatus(vo.getId(), DispatchStatusEnum.PART_REPORT.getCode());
			}
		}
		//埋点1 项目调度到达的待办 完成
//		List<ProjectDispatchDetailDto> myList = projectDispatchDetailService.listByDispatchIdAndDeptId(vo.getId(),
//			Collections.singletonList(loginDeptId));
		List<ProjectDispatchDetailDto> constList = projectDispatchDetailService.listByDispatchIdAndConstId(vo.getId(), loginDeptId);
		long noSubmitCount = constList.stream().filter(e -> !ProjectDispatchStatusEnum.SUBMITTED.getCode().equals(e.getProjectDispatchStatus())).count();
		if (noSubmitCount == 0l) {
			String content = String.format(MsgContentConstant.DISPATCH_ISSUE, dispatchDto.getSendCompanyName());
			szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_DISPATCH, vo.getId(), AuthUtil.getUserId(), Long.valueOf(AuthUtil.getDeptId()), content);
		}
		//下级全部提交的待办完成
		List<ProjectDispatchDetailDto> myList = projectDispatchDetailService.listByDispatchIdAndDeptId(vo.getId(), Collections.singletonList(loginDeptId));
		Long lowerNoSubmitCount = myList.stream().filter(e -> !ProjectDispatchStatusEnum.SUBMITTED.getCode().equals(e.getProjectDispatchStatus())).count();
		if (lowerNoSubmitCount == 0l) {
			if (upDeptId != null) {
				Long finalUpDeptId1 = upDeptId;
				long count = myList.stream().filter(e -> !finalUpDeptId1.equals(e.getPresentCompanyId())).count();
				if (count == 0) {
					szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_DISPATCH, vo.getId(), AuthUtil.getUserId(), Long.valueOf(AuthUtil.getDeptId()), MsgContentConstant.DISPATCH_ALL_REPORT);
				}
			}
		}
		if (CollectionUtil.isNotEmpty(constList)) {
			List<ProjectDispatchDetailDto> submitList = constList.stream().filter(e -> ProjectDispatchStatusEnum.SUBMITTED.getCode().equals(e.getProjectDispatchStatus()) && Long.valueOf(AuthUtil.getDeptId()).equals(e.getOperateCompanyId())).collect(Collectors.toList());
			if (constList.size() == submitList.size()) {
				List<SzykTaskDto> szykTaskList = szykTaskService.listByTypeAndReceiverId(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode(), AuthUtil.getUserId(), vo.getId(), Long.valueOf(AuthUtil.getDeptId()));
				szykTaskService.finishTask(szykTaskList.stream().map(e -> e.getId()).collect(Collectors.toList()));
			}
		}

		//埋点2 给上级单位的联络人通知
		if (upDeptId != null) {
			Dept dept = SysCache.getDept(upDeptId);
			boolean canSend = true;
			if (dept != null) {
				Integer sendMsgLevel = dept.getUnitLevel();
				Dept issDept = SysCache.getDept(dispatchDto.getSendCompanyId());
				if (issDept != null) {
					Integer issLevel = issDept.getUnitLevel();
					if (sendMsgLevel != null && issLevel != null) {
						if (sendMsgLevel < issLevel) {
							canSend = false;
						}
					}
				}
			}
			//多个人的通知
//			List<UserDTO> userDTOList = userService.getContactPersonListByDeptRole(upDeptId, Collections.singletonList(RoleEnum.DEFAULT.getName()));
			List<UserDTO> userDTOList = userService.getContactPersonListByDeptMenu(upDeptId, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());

			if (CollectionUtil.isNotEmpty(userDTOList) && canSend) {
				for (UserDTO userDTO : userDTOList) {
					SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
					szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
					szykMsgDto.setBusinessId(vo.getId());
					szykMsgDto.setUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, vo.getId()) + "?deptScopeId=" + upDeptId);
					szykMsgDto.setReceiverId(userDTO.getId());
					szykMsgDto.setContent(String.format(MsgContentConstant.DISPATCH_REPORT, currentDetpName));
					szykMsgDto.setDeptId(upDeptId);
					szykMsgService.genMsg(szykMsgDto);
				}
			}
		}
		// 埋点3.全部提报给上级单位联络人发待办通知
		Long finalUpDeptId = upDeptId;
		String upDeptIdStr = String.valueOf(finalUpDeptId);
		List<ProjectDispatchDetailDto> upAllFilterList = detailAllList.stream().filter(e -> e.getAncestors() != null && e.getAncestors().contains(upDeptIdStr) && !upDeptIdStr.equals(e.getAncestors().split(",")[0])).collect(Collectors.toList());

		List<ProjectDispatchDetailDto> upSubmitFilterList = upAllFilterList.stream().filter(e -> e.getProjectDispatchStatus().equals(ProjectDispatchStatusEnum.SUBMITTED.getCode()) && String.valueOf(finalUpDeptId).equals(String.valueOf(e.getPresentCompanyId()))).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(upSubmitFilterList) && upAllFilterList.size() == upSubmitFilterList.size()) {
			if (upDeptId != null) {
//				List<UserDTO> userDTOList = userService.getContactPersonListByDeptRole(upDeptId, Collections.singletonList(RoleEnum.DEFAULT.getName()));

				List<UserDTO> userDTOList = userService.getContactPersonListByDeptMenu(upDeptId, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());

				//是否下发单位提报
				boolean flag = false;
				if (String.valueOf(upDeptId).equals(String.valueOf(dispatchDto.getSendCompanyId()))) {
					flag = true;
				}
				if (CollectionUtil.isNotEmpty(userDTOList)) {
					if (flag) {
						//下发单位接受消息
						List<SzykMsgGenDto> msgList = new ArrayList<>();
						for (UserDTO userDTO : userDTOList) {
							SzykMsgGenDto szykTaskDto = new SzykMsgGenDto();
							szykTaskDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
							szykTaskDto.setBusinessId(vo.getId());
							szykTaskDto.setTitle(MsgTitleConstant.ACCEPTANCE_DISPATCH);
							szykTaskDto.setUrl(String.format(MsgUrlConstant.DISPATCH_SUBMIT, vo.getId(), flag) + "?deptScopeId=" + upDeptId);
							szykTaskDto.setReceiverId(userDTO.getId());
							szykTaskDto.setDeptId(upDeptId);
							szykTaskDto.setNextOperation(NoticeNextOperationEnum.TO_SUBMIT.getCode());
							String content = String.format("项目调度：" + MsgContentConstant.DISPATCH_ALL_REPORT);
							szykTaskDto.setContent(content);
							msgList.add(szykTaskDto);
						}
						szykMsgService.genMsg(msgList);
					} else {
						//非下发单位接受待办
						SzykTaskGenDto szykTaskDto = new SzykTaskGenDto();
						szykTaskDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
						szykTaskDto.setBusinessId(vo.getId());
						szykTaskDto.setTitle(MsgTitleConstant.ACCEPTANCE_DISPATCH);
						szykTaskDto.setUrl(String.format(MsgUrlConstant.DISPATCH_SUBMIT, vo.getId(), flag) + "?deptScopeId=" + upDeptId);
						String userIds = userDTOList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.joining(","));
						szykTaskDto.setReceiverId(userIds);
						szykTaskDto.setNextOperation(NoticeNextOperationEnum.TO_SUBMIT.getCode());
						String content = String.format(MsgContentConstant.DISPATCH_ALL_REPORT);
						szykTaskDto.setContent(content);
						szykTaskDto.setDeptId(upDeptId);
						szykTaskDto.setDetailUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, vo.getId()));
						szykTaskService.genTodoTask(szykTaskDto, true);
					}

				}
			}
		}

		//v1.5 提报后退回的代办完成
		List<ProjectDispatchDetailVo> list = vo.getList();
		List<ProjectDispatchDetailVo> submitBackList = list.stream().filter(e -> Objects.equals(e.getProjectDispatchStatus(), ProjectDispatchStatusEnum.EXIT.getCode())).collect(Collectors.toList());
		if (submitBackList.size() == oldBackList.size()) {
			List<SzykTaskDto> szykTaskDtos = szykTaskService.listByTypeAndReceiverId(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode(), AuthUtil.getUserId(), dispatchDto.getId(), String.format(MsgContentConstant.DISPATCH_BACK), Long.valueOf(AuthUtil.getDeptId()));
			if (CollectionUtil.isNotEmpty(szykTaskDtos)) {
				List<Long> taskIds = szykTaskDtos.stream().map(e -> e.getId()).collect(Collectors.toList());
				szykTaskService.finishTask(taskIds);
			}
		}
		return true;
	}

	private List<String> unitFromNow(Long constructionUnitId) {
		List<String> list = new LinkedList<>();
		Dept dept = SysCache.getDept(constructionUnitId);
		list.add(String.valueOf(dept.getId()));
		//增加上级
		do {
			if (Func.isNotEmpty(dept.getParentId())) {
				Dept parentDept = SysCache.getDept(dept.getParentId());
				if (Func.isEmpty(parentDept)) {
					break;
				}
				list.add(String.valueOf(parentDept.getId()));
				dept = parentDept;
			} else {
				break;
			}
		} while (true);
		return list;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> back(ProjectDispatchBackVo vo, SzykUser szykUser) {

		Dept currentDept = DeptScopeUtil.getLoginDept();
		Long currentDeptId = currentDept.getId();
		List<ProjectDeleteDto> resultList = new ArrayList<>();

		ProjectDispatchDto dispatchDto = projectDispatchService.fetchById(vo.getId());
		if (dispatchDto == null) {
			throw new ServiceException("当前调度不存在!");
		}
		Long sendCompanyId = dispatchDto.getSendCompanyId();
		Dept sendCompany = SysCache.getDept(sendCompanyId);
		Long parentId = sendCompany.getParentId();
		// 查询明细
		List<Long> detailIdList = vo.getList().stream().map(ProjectDispatchDetailVo::getId).collect(Collectors.toList());
		List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByIdList(detailIdList);
		if (CollectionUtils.isEmpty(detailList)) {
			throw new ServiceException("调度明细不存在!");
		}

		Map<Long, ProjectDispatchDetailDto> detailMap = detailList.stream().collect(Collectors.toMap(ProjectDispatchDetailDto::getId, Function.identity()));

		//遍历多个项目
		//已经发送过待办的人,不再重复发送    用户Id:业务范围
		Set<String> haveSendMsgUserSet = new HashSet<>();
		for (ProjectDispatchDetailVo detailVo : vo.getList()) {
			if (!detailMap.containsKey(detailVo.getId())) {
				continue;
			}
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();
			resultList.add(deleteDto);
			deleteDto.setResult(true);
			deleteDto.setName(detailVo.getProjectName());
			if (Func.isNotEmpty(detailVo.getConstructionUnitId())) {
				Dept dept = SysCache.getDept(detailVo.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						deleteDto.setConstructionUnitName(dept.getDeptName());
					} else {
						deleteDto.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}
			//校验是否建设单位回退给自己
			if (Objects.equals(detailVo.getConstructionUnitId(), currentDeptId)) {
				deleteDto.setMessage("建设单位的调度不能退回给自己!");
				deleteDto.setResult(false);
				continue;
			}

			ProjectDispatchDetailDto detailDto = detailMap.get(detailVo.getId());
			BeanUtil.copyProperties(detailVo, detailDto);

			detailDto.setProjectDispatchStatus(ProjectDispatchStatusEnum.EXIT.getCode());
			detailDto.setOperateCompanyId(detailDto.getPresentCompanyId());

			Dept dept = deptService.getById(detailDto.getPresentCompanyId());
			detailDto.setOperateCompanyName(dept == null ? null : dept.getDeptName());

			detailDto.setPresentCompanyId(detailDto.getConstructionUnitId());

			projectDispatchDetailService.updateById(detailDto);

			// 删除交付物数据
			projectFileService.deleteByProjectId(detailDto.getProjectId(), Arrays.asList(detailDto.getId()), ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());

			if (!CollectionUtils.isEmpty(detailVo.getFileList())) {
				for (Long fileId : detailVo.getFileList()) {
					ProjectFileVo fileVo = new ProjectFileVo();
					fileVo.setProjectId(detailDto.getProjectId());
					fileVo.setAttachId(fileId);
					fileVo.setBusinessId(detailDto.getId());
					fileVo.setBusinessType(ProjectFileEnum.DISPATCH_DELIVERABLES.getCode());

					projectFileService.save(fileVo);
				}
				// 自动收集
				//baseLogicService.autoCollectKnowledge(detailVo.getFileList(), dispatchDto.getId(), dispatchDto.getDispatchNo());
			}

			// 调度记录
			ProjectDispatchLogVo logVo = new ProjectDispatchLogVo();
			logVo.setProjectDispatchDetailId(detailDto.getId());
			logVo.setOperatorId(szykUser.getUserId());
			logVo.setOperatorName(szykUser.getNickName());
			logVo.setOperatorCompanyId(detailDto.getOperateCompanyId());
			logVo.setOperatorCompanyName(SysCache.getDeptName(detailDto.getOperateCompanyId()));

			logVo.setOperate(OperateEnum.RETURN.getCode());
			logVo.setOperateTime(new Date());
			logVo.setRemark(vo.getRemark());
			projectDispatchLogService.save(logVo);
			//埋点1 调度记录被退回 发送通知及给  除建设单位联络人、下发单位联络人之外的下级（所有层级）联络人
			Long issueCompanyId = dispatchDto.getSendCompanyId();
			Long constructionUnitId = detailDto.getConstructionUnitId();
			List<Long> sendMeageDeptIdList = deptService.getMiddleDept(issueCompanyId, constructionUnitId);
			//需要发送消息的人
			List<UserDTO> needSendMessageUserList = userService.getContactPersonListByDeptMenu(sendMeageDeptIdList, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());
			//多个人的通知
			if (CollectionUtil.isNotEmpty(needSendMessageUserList)) {
				//去除自己
				needSendMessageUserList = needSendMessageUserList.stream().filter(e -> !szykUser.getUserId().equals(e.getId()) && !haveSendMsgUserSet.contains(e.getId() + ":" + e.getDeptId())).collect(Collectors.toList());
				for (UserDTO userDTO : needSendMessageUserList) {
					SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
					szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
					szykMsgDto.setBusinessId(vo.getId());
					szykMsgDto.setUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, vo.getId()) + "?deptScopeId=" + userDTO.getDeptId());
					szykMsgDto.setReceiverId(userDTO.getId());
					szykMsgDto.setDeptId(Long.valueOf(userDTO.getDeptId()));
					szykMsgDto.setContent(String.format(MsgContentConstant.DISPATCH_BACK_KNOW, SysCache.getDeptName(detailDto.getConstructionUnitId())));
					szykMsgDto.setTitle(MsgTitleConstant.ACCEPTANCE_DISPATCH);
					szykMsgDto.setDeptId(Long.valueOf(userDTO.getDeptId()));
					szykMsgService.genMsg(szykMsgDto);
					//同一个调度发送过一次不再重复发送
					haveSendMsgUserSet.add(userDTO.getId() + ":" + userDTO.getDeptId());
				}
			}

		}

		List<ProjectDispatchDetailDto> detailAllList = projectDispatchDetailService.listByDispatchId(vo.getId());
		List<ProjectDispatchDetailDto> filterList = detailAllList.stream().filter(item ->
				item.getProjectDispatchStatus().equals(ProjectDispatchStatusEnum.SUBMITTED.getCode())
					&&
					(String.valueOf(dispatchDto.getSendCompanyId()).equals(String.valueOf(item.getPresentCompanyId()))
						|| String.valueOf(parentId).equals(String.valueOf(item.getPresentCompanyId()))))
			.collect(Collectors.toList());

		// 待提报
		projectDispatchService.updateByStatus(vo.getId(), DispatchStatusEnum.REPORT.getCode());
		// 全部提报
		if (!CollectionUtils.isEmpty(filterList) && detailAllList.size() == filterList.size()) {
			projectDispatchService.updateByStatus(vo.getId(), DispatchStatusEnum.ALL_REPORT.getCode());
		}
		// 部分提报
		if (!CollectionUtils.isEmpty(filterList) && detailAllList.size() != filterList.size() && filterList.size() > 0) {
			projectDispatchService.updateByStatus(vo.getId(), DispatchStatusEnum.PART_REPORT.getCode());
		}

		//埋点2 调度记录被退回 发送待办任务给  建设单位联络人
		Set<Long> haveSendUnitSet = new HashSet<>();
		for (ProjectDispatchDetailDto detailDto : detailList) {
			Long constructionUnitId = detailDto.getConstructionUnitId();
			//退回给自己的不发送待办
			if (Objects.equals(detailDto.getConstructionUnitId(), currentDeptId)) {
				continue;
			}
			if (constructionUnitId == null) {
				continue;
			}
			// V1.4同一个建设单位的不重复退回
			if (haveSendUnitSet.contains(constructionUnitId)) {
				continue;
			}
			List<UserDTO> userDTOS = userService.getContactPersonListByDeptMenu(constructionUnitId, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());
			SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
			szykTaskGenDto.setTitle(MsgTitleConstant.DISPATCH_BACK);
			szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
			szykTaskGenDto.setBusinessId(vo.getId());
			szykTaskGenDto.setDeptId(constructionUnitId);
			szykTaskGenDto.setUrl(String.format(MsgUrlConstant.DISPATCH_SUBMIT, vo.getId(), false) + "?deptScopeId=" + constructionUnitId);
			szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_SUBMIT.getCode());
			szykTaskGenDto.setReceiverId(userDTOS.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.joining(",")));
			szykTaskGenDto.setContent(String.format(MsgContentConstant.DISPATCH_BACK));
			szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.DISPATCH_DETAIL, vo.getId()));
			szykTaskService.genTodoTask(szykTaskGenDto, true);
			haveSendUnitSet.add(constructionUnitId);
		}
		//埋点3 修改全部提交的url
		List<SzykMsgDto> szykMsgDtos = szykMsgService.listByParam(AuthUtil.getUserId(), vo.getId(), "项目调度：" + MsgContentConstant.DISPATCH_ALL_REPORT, Long.valueOf(AuthUtil.getDeptId()));
		if (CollectionUtil.isNotEmpty(szykMsgDtos)) {
			List<Long> msgIds = szykMsgDtos.stream().map(e -> e.getId()).collect(Collectors.toList());
			szykMsgService.updateUrl(msgIds, String.format(MsgUrlConstant.DISPATCH_DETAIL, vo.getId()) + "?deptScopeId=" + Long.valueOf(AuthUtil.getDeptId()));
		}

		return resultList;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean file(Long id) {

		boolean result = projectDispatchService.updateByStatus(id, DispatchStatusEnum.FILE.getCode());

		projectDispatchDetailService.updateByStatus(id, ProjectDispatchStatusEnum.FILE.getCode());

		ProjectDispatchDto dispatchDto = projectDispatchService.fetchById(id);

		// 查询明细数据
		List<ProjectDispatchDetailDto> list = projectDispatchDetailService.listByDispatchId(id);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		dispatchDto.setList(list);

		// 同步项目库
		plExternalReferenceService.synchronizeProjectDispatch(dispatchDto);
		//v1.4修改全部消息的的url为查看  调度只有编辑页和详情页
		List<SzykMsgDto> szykMsgDtos = szykMsgService.listByBusinessIdAndType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode(), id);
		if (CollectionUtil.isNotEmpty(szykMsgDtos)) {
			String detailUrl = String.format(MsgUrlConstant.DISPATCH_DETAIL, id);
			szykMsgService.updateUrl(szykMsgDtos.stream().map(BaseCrudSlimDto::getId).collect(Collectors.toList()), detailUrl);
		}
		//v1.4修改待办的为已办  退回的待办   全部提交的待办  项目调度到达的待办
		List<SzykTaskDto> szykTaskDtos = szykTaskService.listByTypeAndBusinessId(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode(), id);
		if (CollectionUtil.isNotEmpty(szykTaskDtos)) {
			List<Long> taskIds = szykTaskDtos.stream().filter(e -> CommonConstant.ZERO.equals(e.getTaskStatus())).map(e -> e.getId()).collect(Collectors.toList());
			szykTaskService.finishTask(taskIds);
		}

		return result;
	}

	public void export(ProjectDispatchExportVo vo, HttpServletResponse response, OutputStream out, SzykUser szykUser) {

		if (CollectionUtils.isEmpty(vo.getIdList())) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		List<ProjectDispatchDto> dispatchList = projectDispatchService.listByIdList(vo.getIdList());
		if (CollectionUtils.isEmpty(dispatchList)) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		// 字典数据
		Map<String, String> phaseMap = dictMapService.getProjectPhaseDictMap();

		// 项目调度状态
		List<DictBiz> dispatchStatusList = dictBizService.getList(DictBizEnum.PROJECT_DISPATCH_STATUS.getCode());
		Map<String, DictBiz> dispatchStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dispatchStatusList)) {
			dispatchStatusMap.putAll(dispatchStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 推进进度
		List<DictBiz> pushProgressList = dictBizService.getList(DictBizEnum.PUSH_PROGRESS.getCode());
		Map<String, DictBiz> pushProgressMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pushProgressList)) {
			pushProgressMap.putAll(pushProgressList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		List<ProjectDispatchExportDto> result = new ArrayList<>();

		for (ProjectDispatchDto dispatchDto : dispatchList) {

			ProjectDispatchExportDto exportDto = new ProjectDispatchExportDto();
			BeanUtil.copyProperties(dispatchDto, exportDto);

			boolean flag = false;

			// 查询当前人所在部门
			List<Long> deptIdList = userService.queryDeptByUserId(szykUser.getUserId());
			if (CollectionUtils.isEmpty(deptIdList)) {
				continue;
			}

			// 判断当前部门是否是创建人所在部门
			if (deptIdList.contains(dispatchDto.getSendCompanyId())) {
				flag = true;
			}

			// 查询全数据/权限数据
			List<ProjectDispatchDetailDto> list = projectDispatchDetailService.listByDispatchIdAndDeptId(dispatchDto.getId(), flag ? null : deptIdList, null, null, null);
			if (CollectionUtils.isEmpty(list)) {
				continue;
			}

			List<ProjectDispatchDetailExportDto> exportList = new ArrayList<>();

			for (ProjectDispatchDetailDto detailDto : list) {

				ProjectDispatchDetailExportDto exportDto1 = new ProjectDispatchDetailExportDto();
				BeanUtil.copyProperties(detailDto, exportDto1);

				//更新最新组织信息
				if (Func.isNotEmpty(detailDto.getCompanyId())) {
					exportDto1.setCompanyName(SysCache.getDeptName(detailDto.getCompanyId()));
				}
				if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
					if (Func.isNotEmpty(detailDto.getConstructionUnitId())) {
						Dept dept = SysCache.getDept(detailDto.getConstructionUnitId());
						if (Func.isNotEmpty(dept)) {
							if (dept.getUnitId().equals(dept.getId())) {
								exportDto1.setConstructionUnitName(dept.getDeptName());
							} else {
								exportDto1.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
							}
						}
					}
				}
				if (Func.isNotEmpty(detailDto.getOperateCompanyId())) {
					exportDto1.setOperateCompanyName(SysCache.getDeptName(detailDto.getOperateCompanyId()));
				}

				// 项目阶段
				if (!ObjectUtils.isEmpty(phaseMap) && !ObjectUtils.isEmpty(detailDto.getProjectPhase()) && phaseMap.containsKey(detailDto.getProjectPhase())) {
					detailDto.setProjectPhaseName(phaseMap.get(detailDto.getProjectPhase()));
				}

				// 推进进度
				if (!ObjectUtils.isEmpty(pushProgressMap) && !ObjectUtils.isEmpty(detailDto.getPushProgress()) && pushProgressMap.containsKey(detailDto.getPushProgress())) {
					detailDto.setPushProgressName(pushProgressMap.get(detailDto.getPushProgress()).getDictValue());
				}

				// 项目调度状态
				if (!ObjectUtils.isEmpty(dispatchStatusMap) && !ObjectUtils.isEmpty(detailDto.getProjectDispatchStatus()) && dispatchStatusMap.containsKey(detailDto.getProjectDispatchStatus())) {
					detailDto.setProjectDispatchStatusName(dispatchStatusMap.get(detailDto.getProjectDispatchStatus()).getDictValue());
				}

				BeanUtil.copyProperties(detailDto, exportDto1);

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				exportDto1.setSubmitTime(StringUtils.isEmpty(detailDto.getSubmitTime()) ? null : sdf.format(detailDto.getSubmitTime()));

				exportList.add(exportDto1);
			}
			exportDto.setList(exportList);

			//获取最新组织名称
			exportDto.setSendCompanyName(SysCache.getDeptName(dispatchDto.getSendCompanyId()));

			result.add(exportDto);
		}

		// 设置响应头信息
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Content-disposition", "attachment;filename=chatEduExport.xlsx");
		// 使用EasyExcel进行导出
		ExcelWriter excelWriter = null;
		try {
			excelWriter = EasyExcel.write(response.getOutputStream(), ProjectDispatchDetailExportDto.class).build();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}

		// 循环写入多个Sheet
		for (int i = 0; i < result.size(); i++) {
			ProjectDispatchExportDto projectDispatchExportDto = result.get(i);
			List<ProjectDispatchDetailExportDto> list = projectDispatchExportDto.getList();
			WriteSheet writeSheet = EasyExcel.writerSheet(i, projectDispatchExportDto.getSendCompanyName() + "_" + projectDispatchExportDto.getPeriodName()).build();
			excelWriter.write(list, writeSheet);
		}


		excelWriter.finish();


	}

	/**
	 * 发送调度deadline通知
	 *
	 * @param dispatchId
	 */
	public void sendMessage(String dispatchId) {
		ProjectDispatchDto projectDispatchDto = projectDispatchService.fetchById(Long.valueOf(dispatchId));

		if (ProjectDispatchStatusEnum.FILE.getCode().equals(projectDispatchDto.getDispatchStatus())) {
			return;

		}
		List<ProjectDispatchDetailDto> detailDtos = projectDispatchDetailService.listByDispatchId(Long.valueOf(dispatchId));
		if (CollectionUtil.isEmpty(detailDtos)) {
			return;
		}
		List<Long> needSendMessageDeptList = new ArrayList<>();
		//遍历各个项目
		for (ProjectDispatchDetailDto dto : detailDtos) {
			//单位层级
			String ancestors = dto.getAncestors();
			//下一个单位
			Long presentCompanyId = dto.getPresentCompanyId();
			//获取下一个单位到 下发单位之间的单位list
			if (StringUtils.isEmpty(ancestors)) {
				return;
			}
			String[] split = ancestors.split(",");

			boolean middle = false;
			for (String deptId : split) {
				if (String.valueOf(projectDispatchDto.getSendCompanyId()).equals(deptId)) {
					middle = false;
					continue;
				}
				if (String.valueOf(presentCompanyId).equals(deptId)) {
					middle = true;
					needSendMessageDeptList.add(Long.valueOf(deptId));
					continue;
				}

				if (middle) {
					needSendMessageDeptList.add(Long.valueOf(deptId));
				}
			}
		}
		if (CollectionUtil.isEmpty(needSendMessageDeptList)) {
			return;
		}
//		List<UserDTO> userDTOList = userService.getContactPersonListByDeptIds(needSendMessageDeptList);
		List<UserDTO> userDTOList = userService.getContactPersonListByDeptMenu(needSendMessageDeptList, MenuEnum.PROJECT_DISPATCHING_EDIT.getCode());

		List<SzykMsgGenDto> szykMsgGenDtoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(userDTOList)) {
			for (UserDTO userDTO : userDTOList) {
				SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
				szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_DISPATCH.getCode());
				szykMsgDto.setBusinessId(projectDispatchDto.getId());
				szykMsgDto.setUrl(String.format(MsgUrlConstant.DISPATCH_SUBMIT, projectDispatchDto.getId(), false) + "?deptScopeId=" + userDTO.getDeptId());
				szykMsgDto.setDeptId(Long.valueOf(userDTO.getDeptId()));
				szykMsgDto.setReceiverId(userDTO.getId());
				szykMsgDto.setContent(String.format(MsgContentConstant.PROJECT_DISPATCH_DEADLINE, DateUtil.format(projectDispatchDto.getDispatchDeadline(), "yyyy-MM-dd")));
				boolean b = szykMsgService.canSend(szykMsgDto);
				if (b) {
					szykMsgGenDtoList.add(szykMsgDto);
				}
			}
		}
		szykMsgService.genMsg(szykMsgGenDtoList);

	}

	public void detailExport(ProjectDispatchDetailDto vo, HttpServletResponse response) {
		if (vo.getId() == null) {
			return;
		}
		ProjectDispatchDto detail = detail(vo.getId(), null);
		List<ProjectDispatchDetailDto> list = detail.getList();
		List<ProjectDispatchDetailExport> resultList = BeanUtil.copy(list, ProjectDispatchDetailExport.class);

		if (StringUtil.isNotBlank(vo.getCompanyName())) {
			resultList = resultList.stream().filter(e -> e.getCompanyName() != null && e.getCompanyName().contains(vo.getCompanyName())).collect(Collectors.toList());
		}

		if (StringUtil.isNotBlank(vo.getConstructionUnitName())) {
			resultList = resultList.stream().filter(e -> e.getConstructionUnitName() != null && e.getConstructionUnitName().contains(vo.getConstructionUnitName())).collect(Collectors.toList());
		}
		if (StringUtil.isNotBlank(vo.getProjectName())) {
			resultList = resultList.stream().filter(e -> e.getProjectName() != null && e.getProjectName().contains(vo.getProjectName())).collect(Collectors.toList());
		}

		for (ProjectDispatchDetailExport export : resultList) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			export.setSubmitTimeStr(StringUtils.isEmpty(export.getSubmitTime()) ? null : sdf.format(export.getSubmitTime()));

		}

		// 导出
		ExcelUtil.export(response, "调度明细" + DateUtil.time(), "调度明细", resultList, ProjectDispatchDetailExport.class);
	}

	/**
	 * 获取下发机构的参数
	 *
	 * @return
	 */
	public List<DeptDTO> getIssueDeptParam() {
		List<Long> dispatchId = new ArrayList<>();
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		List<Long> deptIdList = Collections.singletonList(dept.getId());
		List<ProjectDispatchDetailDto> detailList = projectDispatchDetailService.listByDeptIdList(deptIdList);
		if (!CollectionUtils.isEmpty(detailList)) {
			dispatchId.addAll(detailList.stream().map(ProjectDispatchDetailDto::getDispatchId).collect(Collectors.toList()));
		}
		List<Long> issDeptIdList = projectDispatchService.getIssueDeptParam(dispatchId, dept.getId());
		if (CollectionUtil.isEmpty(issDeptIdList)) {
			return new ArrayList<>();
		}
		List<DeptDTO> deptDTOS = deptService.listByDeptIdList(issDeptIdList);
		return deptDTOS;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean deptUnit() {
		List<ProjectDispatchDetailDto> tempList = projectDispatchDetailService.getTempList();
		if (Func.isNotEmpty(tempList)) {
			for (ProjectDispatchDetailDto projectDispatchDetailDto : tempList) {
				String jsonString = JSON.toJSONString(projectDispatchDetailDto);
				log.info("处理的项目调度detail:" + jsonString);
				Long presentCompanyId = projectDispatchDetailDto.getPresentCompanyId();
				Dept dept = SysCache.getDept(presentCompanyId);
				Long unitId = dept.getUnitId();
				Boolean b = projectDispatchDetailService.updatePresentCompanyId(projectDispatchDetailDto.getId(), unitId);
				if(b){
					log.info("处理成功");
				}else{
					throw new ServiceException(jsonString);
				}

			}
		}
		return true;

	}
}
