/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 项目基本信息
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("project_base")
@EqualsAndHashCode(callSuper = false)
public class ProjectBase extends BaseCrudEntity {

	/**
	 * 立项编号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String approvalNo;

	/**
	 * 专家评审信息
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String expertReview;
	/**
	 * 项目编号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectNo;
	/**
	 * 项目名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectName;
	/**
	 * 公司id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 公司名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 建设单位id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer year;

	/**
	 * 批次
	 */
	private String batchNo;

	/**
	 * 批次
	 */
	private String batchName;

	/**
	 * 专业分类，字典sc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String specialtyClassification;
	/**
	 * 项目分类，字典pc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectClassification;
	/**
	 * 列支渠道，字典dc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String distributionChannel;
	/**
	 * 投资主体id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long investmentSubjectId;
	/**
	 * 投资主体名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String investmentSubjectName;
	/**
	 * 项目预计总投资
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal totalEstimate;
	/**
	 * 项目本年度投资
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal currentInvestment;

	/**
	 * 计划资金
	 */
	private BigDecimal planFunds;

	/**
	 * 合同金额
	 */
	private BigDecimal contractAmount;
	/**
	 * 牵头部门id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long leadOrgId;
	/**
	 * 牵头部门名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String leadOrgName;
	/**
	 * 计划招标日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date planTenderDate;
	/**
	 * 计划开始日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date planStartDate;
	/**
	 * 计划上线日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date planCompleteDate;
	/**
	 * 项目标签，字典pl
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectLabel;
	/**
	 * 审查状态，字典review_status
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String reviewStatus;
	/**
	 * 复审状态
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String retrialStatus;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 提报时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date submitTime;

	private Long instanceId;

	/**
	 * 集团审查结论
	 */
	private String corpApproveResult;

	/**
	 * 集团审查意见
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String corpApproveRemark;

	/**
	 * 二级审查意见
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String secApproveRemark;

	private String ancestors;

	private Integer approveLevel;
	/**
	 * 补充材料状态  0.-- 1.无需提报 2.待提交 3.已提交 4.已归档
	 */
	private String  supplementStatus;


	private String currStepName;


	private Boolean cancelFlag = false;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime supplementTime;

	@ApiModelProperty(value = "作废时间")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime nullifyTime;

}
