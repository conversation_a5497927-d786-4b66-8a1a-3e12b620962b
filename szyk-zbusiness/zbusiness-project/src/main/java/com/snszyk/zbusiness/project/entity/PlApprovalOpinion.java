/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库-项目审批意见实体类
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlApprovalOpinion extends BaseCrudEntity {

	/**
	* 项目id
	*/
	private Long projectId;
	/**
	* 审批节点
	*/
	private Integer flowSort;
	/**
	* 流程id
	*/
	private Long instanceId;
	/**
	* 审核部门id
	*/
	private Long approveDeptId;
	/**
	* 审核部门名称
	*/
	private String approveDeptName;
	/**
	* 审核人id
	*/
	private String approveUserId;
	/**
	* 审核人姓名
	*/
	private String approveUserName;
	/**
	* 项目审批意见，数据字典
	*/
	private String approveResult;
	/**
	* 审核意见
	*/
	private String approveRemark;
	/**
	* 审批时间
	*/
	private LocalDateTime approveTime;


}
