<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.ProjectLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="projectLabelResultMap" type="com.snszyk.zbusiness.project.entity.ProjectLabel">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="user_id" property="userId"/>
        <result column="project_id" property="projectId"/>
        <result column="project_label" property="projectLabel"/>
        <result column="project_label_name" property="projectLabelName"/>
    </resultMap>


    <select id="selectProjectLabelPage" resultMap="projectLabelResultMap">
        select * from project_label where is_deleted = 0
    </select>

</mapper>
