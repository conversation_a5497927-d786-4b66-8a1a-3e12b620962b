package com.snszyk.zbusiness.project.designpattern.strategy;

import com.snszyk.core.tool.utils.ObjectUtil;

/**
 * <AUTHOR>
 */
public class DictContext {
	DictTypeStrategy dictTypeStrategy;

	public DictContext(DictTypeStrategy dictTypeStrategy) {
		this.dictTypeStrategy = dictTypeStrategy;
	}

	public Boolean handle(String dictKey) {
		if (ObjectUtil.isEmpty(dictTypeStrategy)) {
			return false;
		}
		return dictTypeStrategy.isReference(dictKey);
	}

}
