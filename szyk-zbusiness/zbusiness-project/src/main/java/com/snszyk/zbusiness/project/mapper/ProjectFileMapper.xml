<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.ProjectFileMapper">
    <select id="listByProjects" resultType="com.snszyk.zbusiness.project.dto.ProjectFileDto">

        select
        a.id,
        a.project_id,
        a.attach_id,
        a.business_id,
        a.business_type,
        a.undeletable,
        a.create_user,
        a.create_time,
        a.update_user,
        a.update_time,
        a.is_deleted,
        b.name,
        b.original_name,
        b.extension,
        b.attach_size,
        b.link,
        b.domain,
        c.company_id,
        d.dept_code as orgCode,
        d.dept_name as orgName
        from
        project_file a
        left join szyk_attach b on a.attach_id = b.id
        left  join project_base c on a.project_id = c.id
        left join szyk_dept d on c.construction_unit_id = d.id
        where
        a.project_id in
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        and a.business_type = #{businessType}
        and a.is_deleted = 0
    </select>
    <select id="listLatestByProjects" resultType="com.snszyk.zbusiness.project.dto.ProjectFileDto">
        select
        a.id,
        a.project_id,
        a.attach_id,
        a.business_id,
        a.business_type,
        a.undeletable,
        a.create_user,
        a.create_time,
        a.update_user,
        a.update_time,
        a.is_deleted,
        b.name,
        b.original_name,
        b.extension,
        b.attach_size,
        b.link,
        b.domain,
        c.company_id,
        d.dept_code as orgCode,
        f.dept_name as companyName,
        f.dept_code as companyCode,
        IF(d.dept_category in (2,4), concat(e.dept_name,'_',d.dept_name), d.dept_name) as orgName,
        c.project_name
        from
        project_file a
        left join szyk_attach b on a.attach_id = b.id
        left join project_base c on a.project_id = c.id
        left join szyk_dept d on c.construction_unit_id = d.id
        left join szyk_dept e on e.id =d.unit_id and e.is_deleted=0 and e.dept_status=1
        left join szyk_dept f on f.id =c.company_id and f.is_deleted=0 and f.dept_status=1

        where
        a.project_id in
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        and a.business_type = #{businessType}
        and a.is_deleted = 0
        and b.id in (
        select
        max(b1.id)
        from
        project_file a1
        left join szyk_attach b1 on a1.attach_id = b1.id
        where a1.is_deleted=0 and a1.business_type=#{businessType} and a1.project_id in
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by a1.project_id
        )

    </select>

</mapper>
