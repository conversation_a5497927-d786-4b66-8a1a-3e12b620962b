/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.project.dto.ProjectDeadlineDto;
import com.snszyk.zbusiness.project.entity.ProjectDeadline;
import com.snszyk.zbusiness.project.vo.ProjectDeadlinePageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 申报截止日期设置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
public interface ProjectDeadlineMapper extends BaseMapper<ProjectDeadline> {

    IPage<ProjectDeadlineDto> pageList(@Param("v") ProjectDeadlinePageVo v);

    ProjectDeadlineDto detail(Long id);

}
