package com.snszyk.zbusiness.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.project.dto.ProjectBaseDto;
import com.snszyk.zbusiness.project.dto.ProjectBaseOaDto;
import com.snszyk.zbusiness.project.entity.ProjectBase;
import com.snszyk.zbusiness.project.vo.ProjectBasePageVo;
import com.snszyk.zbusiness.stat.dto.BoardCompanyExamineDto;
import com.snszyk.zbusiness.stat.dto.BoardExamineDto;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

public interface ProjectBaseMapper extends BaseMapper<ProjectBase> {

	@Select("SELECT * FROM `project_base` WHERE  id = #{id}")
	ProjectBase fetchByIdIgnoreDelete(Long id);

	List<ProjectBaseOaDto> queryDataByIdForOa(@Param("ids") List<Long> businessIds);

	IPage<ProjectBaseDto> pageList(Page<ProjectBaseDto> page, @Param("vo") ProjectBasePageVo vo);

	/**
	 * 根据列支渠道分类统计项目数量
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<EchartCircleDto> projectInvestment(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

	/*
	驾驶舱项目审查数量
	 */
	BoardExamineDto projectExamine(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate,
								   @Param("parentUnitId") Long parentUnitId);

	/**
	 * 驾驶舱项目审查数量分布
	 *
	 * @param startDate
	 * @param endDate
	 * @param list
	 * @return
	 */
	List<BoardCompanyExamineDto> companyExamine(@Param("startDate") LocalDateTime startDate,
												@Param("endDate") LocalDateTime endDate, @Param("list") List<Long> list);

	List<ProjectBaseDto> listByInstanceIds(@Param("instanceIds") List<Long> instanceIds);

	ProjectBaseDto detail(@Param("id") Long id);

	List<String> selectMaxBatchNo(Integer year);

	ProjectBaseDto getByInstanceId(@Param("instanceId") Long instanceId);

	List<ProjectBaseDto> getToDoList();

	/**
	 * 查询reviewStatus=1的记录中最新的年份
	 * @return 最新的年份
	 */
	@Select("SELECT MAX(year) FROM project_base WHERE is_deleted=0 AND review_status=1")
	Integer selectLatestYear();
}
