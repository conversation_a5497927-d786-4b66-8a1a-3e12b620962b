package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.project.dto.ProjectMemberDto;
import com.snszyk.zbusiness.project.service.IProjectMemberService;
import com.snszyk.zbusiness.project.vo.ProjectMemberVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ProjectMemberService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectMemberService extends BaseCrudLogicService<ProjectMemberDto, ProjectMemberVo> {

	private final IProjectMemberService projectMemberService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectMemberService;
	}


}
