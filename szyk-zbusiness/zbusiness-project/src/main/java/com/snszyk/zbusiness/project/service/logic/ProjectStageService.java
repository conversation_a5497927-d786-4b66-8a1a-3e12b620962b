package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.project.dto.ProjectStageDto;
import com.snszyk.zbusiness.project.service.IProjectStageService;
import com.snszyk.zbusiness.project.vo.ProjectStageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ProjectStageService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectStageService extends BaseCrudLogicService<ProjectStageDto, ProjectStageVo> {

	private final IProjectStageService projectStageService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.projectStageService;
	}


}
