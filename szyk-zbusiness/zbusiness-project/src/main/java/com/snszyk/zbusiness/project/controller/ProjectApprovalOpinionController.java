/*
 *      Copyright (c) 2018-2028
 *//*
package com.snszyk.zbusiness.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.ProjectApprovalOpinionDto;
import com.snszyk.zbusiness.project.service.logic.ProjectApprovalOpinionLogicService;
import com.snszyk.zbusiness.project.vo.ProjectApprovalOpinionEditVo;
import com.snszyk.zbusiness.project.vo.ProjectApprovalOpinionVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


*//**
 * 项目审批意见 控制器
 *
 * <AUTHOR>
 * @since 2023-04-04
 *//*
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-project/approvalopinion")
@Api(value = "项目审查意见", tags = "项目审查意见")
public class ProjectApprovalOpinionController extends BaseCrudController {


    private final ProjectApprovalOpinionLogicService projectApprovalOpinionLogicService;

    @Override
    protected BaseCrudLogicService fetchBaseLogicService() {
        return projectApprovalOpinionLogicService;
    }

    *//**
     * 保存
     *//*
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "保存", notes = "ProjectApprovalOpinionVo")
    public R<Boolean> save(@RequestBody ProjectApprovalOpinionEditVo v) {
        Boolean result = projectApprovalOpinionLogicService.saveData(v);
        return R.data(result);
    }


    *//**
     * 根据ID获取数据
     *//*
    @GetMapping("/fetchById")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "流程id", notes = "id")
    public R<ProjectApprovalOpinionDto> fetchByIdInstanceId(Long id) {
		ProjectApprovalOpinionDto dto = projectApprovalOpinionLogicService.fetchByIdInstanceId(id);
        return R.data(dto);
    }



}*/
