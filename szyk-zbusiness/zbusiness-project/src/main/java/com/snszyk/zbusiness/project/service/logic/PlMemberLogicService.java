/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.zbusiness.project.dto.PlMemberDto;
import com.snszyk.zbusiness.project.vo.PlMemberVo;
import com.snszyk.zbusiness.project.service.IPlMemberService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 项目库项目成员表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@AllArgsConstructor
@Service
public class PlMemberLogicService extends BaseCrudLogicService<PlMemberDto, PlMemberVo> {

    private final IPlMemberService plMemberService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.plMemberService;
    }
}
