package com.snszyk.zbusiness.project.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.dto.DictCommonDto;
import com.snszyk.zbusiness.dict.dto.DictPhaseDto;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.enums.EnableTypeEnum;
import com.snszyk.zbusiness.dict.service.IDictCommonService;
import com.snszyk.zbusiness.dict.service.IDictPhaseService;
import com.snszyk.zbusiness.knowledge.service.IBaseLogicService;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.enums.*;
import com.snszyk.zbusiness.project.service.*;
import com.snszyk.zbusiness.project.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目进度服务类
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectProgressNewService {

	private final IProjectBaseService projectBaseService;

	private final IProjectProgressService projectProgressService;

	private final IProjectAcceptService projectAcceptService;

	private final IProjectFileService projectFileService;

	private final IProjectStageService projectStageService;

	private final IDictBizService dictBizService;

	private final IAttachService attachService;

	private final IDictCommonService dictCommonService;

	private final IDictPhaseService dictPhaseService;

	private final IPlExternalReferenceService plExternalReferenceService;

	private final IPlBaseService plBaseService;


	private final IPlContactService plContactService;
	private final IProjectIntroduceService projectIntroduceService;

	private final IBaseLogicService baseLogicService;

	private final IProjectContactService projectContactService;

	public IPage<ProjectBasePageDto> page(ProjectProgressPageNewVo vo) {
		//更新进度时时，可选择的项目范围依据登录组织判断，如果以部门登录则可更新本部门的项目进度，如果以单位登录，则可更新单位所有的项目进度。
		List<Long> deptIds = new ArrayList<>();
		deptIds.add(DeptScopeUtil.getLoginDeptId());
		//如果是单位登录
		if (DeptScopeUtil.isUnitLogin()) {
			List<Dept> currentUnitAll = DeptScopeUtil.getCurrentUnitAll();
			deptIds.addAll(currentUnitAll.stream().map(e -> e.getId()).collect(Collectors.toList()));
		}

		vo.setDeptIds(deptIds);
		//查询项目申报中，审核通过且原则同意的数据
		IPage<ProjectBasePageDto> page = plBaseService.progressPage(vo);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		// 项目分类
		List<DictCommonDto> pcList = dictCommonService.listByDictStatus(DictEnum.PC.getCode(), null);
		Map<String, DictCommonDto> pcMap = new HashMap<>();
		if (!org.springframework.util.CollectionUtils.isEmpty(pcList)) {
			pcMap.putAll(pcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		//投资主体
		List<Long> ids = page.getRecords().stream().map(e -> e.getId()).collect(Collectors.toList());
		List<ProjectBasePageDto> projectBasePageDtos = projectBaseService.listByIds(ids);
		Map<Long, ProjectBasePageDto> projectBaseIdMap = projectBasePageDtos.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));

		for (ProjectBasePageDto record : page.getRecords()) {
			DictCommonDto dictCommonDto = pcMap.get(record.getProjectClassification());
			if (dictCommonDto != null) {
				record.setProjectClassificationName(dictCommonDto.getDictValue());
			}
			//投资主体
			ProjectBasePageDto projectBasePageDto = projectBaseIdMap.get(record.getId());
			if (projectBasePageDto != null) {
				record.setInvestmentSubjectId(projectBasePageDto.getInvestmentSubjectId());
				record.setInvestmentSubjectName(SysCache.getDeptName(projectBasePageDto.getInvestmentSubjectId()));
			}
			//获取最新组织信息
			if (Func.isNotEmpty(record.getCompanyId())) {
				record.setCompanyName(SysCache.getDeptName(record.getCompanyId()));
			}
			if (Func.isNotEmpty(record.getConstructionUnitId())) {
				Dept dept = SysCache.getDept(record.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						record.setConstructionUnitName(dept.getDeptName());
					} else {
						record.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}
			if (Func.isNotEmpty(record.getInvestmentSubjectId())) {
				record.setInvestmentSubjectName(SysCache.getDeptName(record.getInvestmentSubjectId()));
			}

		}
		return page;
	}

	public ProjectProgressDto detail(Long id) {

		PlBaseDto plBaseDto = plBaseService.fetchById(id);
		if (plBaseDto == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		PlContactDto contactDto = plContactService.getByProjectId(id);
		ProjectProgressDto result=null;
		if (contactDto != null) {
			 result = BeanUtil.copy(contactDto, ProjectProgressDto.class);
		} else {
			result = new ProjectProgressDto();
		}
		BeanUtil.copy(plBaseDto, result);
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
		if (projectBaseDto != null) {
			result.setInvestorId(projectBaseDto.getInvestmentSubjectId());
			result.setInvestorName(SysCache.getDeptName(projectBaseDto.getInvestmentSubjectId()));
		}
		// 项目分类
		if (!StringUtils.isEmpty(result.getProjectClassification())) {
			DictCommonDto dictCommonDto = dictCommonService.getByDictKey(DictEnum.PC.getCode(), result.getProjectClassification());
			result.setProjectClassificationName(dictCommonDto == null ? null : dictCommonDto.getDictValue());
		}

		// 状态
		if (!StringUtils.isEmpty(result.getProgressStatus())) {
			String statusName = dictBizService.getValue(DictBizEnum.PROGRESS_STATUS.getCode(), result.getProgressStatus());
			result.setProgressStatus(statusName);
		}

		// 项目阶段数据
		List<ProjectStageDto> stageList = projectStageService.listByProgressId(result.getId());

		// 项目阶段字典数据
		List<DictPhaseDto> phaseList = dictPhaseService.listAll();
		Map<String, DictPhaseDto> phaseMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(phaseList)) {
			phaseMap.putAll(phaseList.stream().collect(Collectors.toMap(DictPhaseDto::getDictKey, Function.identity())));
		}

		// 阶段状态
		List<DictBiz> stageStatusList = dictBizService.getList(DictBizEnum.STAGE_STATUS.getCode());
		Map<String, DictBiz> stageStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(stageStatusList)) {
			stageStatusMap.putAll(stageStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		if (!CollectionUtils.isEmpty(stageList)) {
			for (ProjectStageDto stageDto : stageList) {
				// 必要节点
				if (!StringUtils.isEmpty(stageDto.getStageKey()) && !ObjectUtils.isEmpty(phaseMap) && phaseMap.containsKey(stageDto.getStageKey())) {
					stageDto.setNecessaryType(phaseMap.get(stageDto.getStageKey()).getNecessaryType());
				}

				// 阶段状态
				if (!ObjectUtils.isEmpty(stageStatusMap) && !ObjectUtils.isEmpty(stageDto.getStageStatus()) && stageStatusMap.containsKey(stageDto.getStageStatus())) {
					stageDto.setStageStatusName(stageStatusMap.get(stageDto.getStageStatus()).getDictValue());
				}

				List<ProjectFileDto> fileDtoList = projectFileService.listByProject(stageDto.getProjectId(), stageDto.getId(), ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());
				if (CollectionUtils.isEmpty(fileDtoList)) {
					continue;
				}
				// 附件ID
				List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (CollectionUtils.isEmpty(attachList)) {
					continue;
				}

				stageDto.setFileList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
			}
		}

		result.setStageList(stageList);

		//v1.5建设内容
		// 项目介绍
		ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
		if (introduceDto != null) {
			String constructionContent = introduceDto.getConstructionContent();
			result.setConstructionContent(constructionContent);
		}
		//获取最新组织信息
		if (Func.isNotEmpty(result.getCompanyId())) {
			result.setCompanyName(SysCache.getDeptName(result.getCompanyId()));
		}
		if (Func.isNotEmpty(result.getConstructionUnitId())) {
			Dept dept = SysCache.getDept(result.getConstructionUnitId());
			if (Func.isNotEmpty(dept)) {
				if (dept.getUnitId().equals(dept.getId())) {
					result.setConstructionUnitName(dept.getDeptName());
				} else {
					result.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
				}
			}
		}
		if (Func.isNotEmpty(result.getInvestorId())) {
			result.setInvestorName(SysCache.getDeptName(result.getInvestorId()));
		}
		return result;
	}

	public ProjectProgressDto detailEdit(Long id) {
		PlBaseDto plBaseDto = plBaseService.fetchById(id);
		if (plBaseDto == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}
		PlContactDto contactDto = plContactService.getByProjectId(id);
		ProjectProgressDto result = BeanUtil.copy(contactDto, ProjectProgressDto.class);
		BeanUtil.copy(plBaseDto, result);
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(id);
		if (projectBaseDto != null) {
			result.setInvestorId(projectBaseDto.getInvestmentSubjectId());
			result.setInvestorName(SysCache.getDeptName(projectBaseDto.getInvestmentSubjectId()));
		}

		// 项目分类
		if (!StringUtils.isEmpty(result.getProjectClassification())) {
			DictCommonDto dictCommonDto = dictCommonService.getByDictKey(DictEnum.PC.getCode(), result.getProjectClassification());
			result.setProjectClassificationName(dictCommonDto == null ? null : dictCommonDto.getDictValue());
		}

		// 状态
		if (!StringUtils.isEmpty(result.getProgressStatus())) {
			String statusName = dictBizService.getValue(DictBizEnum.PROGRESS_STATUS.getCode(), result.getProgressStatus());
			result.setProgressStatus(statusName);
		}

		// 项目阶段数据
		List<DictPhaseDto> phaseList = dictPhaseService.listAll();
		Map<String, DictPhaseDto> phaseMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(phaseList)) {
			phaseMap.putAll(phaseList.stream().collect(Collectors.toMap(DictPhaseDto::getDictKey, Function.identity())));
		}

		// 阶段状态
		List<DictBiz> stageStatusList = dictBizService.getList(DictBizEnum.STAGE_STATUS.getCode());
		Map<String, DictBiz> stageStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(stageStatusList)) {
			stageStatusMap.putAll(stageStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		//v1.5  项目验收申请通过后，项目进度提报中的项目验收阶段以及之前前的阶段不能再更新
		ProjectProgressVo projectProgressVo = new ProjectProgressVo();

		projectProgressVo.setProjectId(id);
		projectProgressVo.setProgressStatus(ProgressStatusEnum.PASS.getCode());
		List<ProjectProgressDto> projectProgressDtoList = projectProgressService.listByProjectIdStatus(id, ProgressStatusEnum.PASS.getCode());
		boolean haveAccept = false;

		if (CollectionUtil.isNotEmpty(projectProgressDtoList)) {
			haveAccept = true;
		}
		List<ProjectStageDto> stageList = projectStageService.listByProgressId(result.getId());
		if (CollectionUtil.isEmpty(stageList)) {
			stageList = new LinkedList<>();
			//查询数据字典
			List<DictPhaseDto> dictPhaseDtos = dictPhaseService.listByStatusOrValue(null, EnableTypeEnum.YES.getCode(), EnableTypeEnum.YES.getCode());
			if (CollectionUtil.isNotEmpty(dictPhaseDtos)) {
				for (DictPhaseDto dto : dictPhaseDtos) {
					ProjectStageDto stageDto = new ProjectStageDto();
					stageDto.setStageName(dto.getDictValue());
					stageDto.setStageKey(dto.getDictKey());
					stageDto.setStageSort(dto.getSort());
					stageDto.setNecessaryType(dto.getNecessaryType());
					stageDto.setOutputDocument(dto.getOutputDocument());
					stageList.add(stageDto);
				}
			}
		}
		if (CollectionUtil.isNotEmpty(stageList)) {
			Optional<ProjectStageDto>
				first = stageList.stream().filter(e -> ProjectStageEnum.PROJECT_ACCEPT.getDictKey().equals(e.getStageKey())).findFirst();
			Integer stageSort = null;
			if (first.isPresent()) {
				ProjectStageDto projectStageDto = first.get();
				stageSort = projectStageDto.getStageSort();
			}
			for (ProjectStageDto stageDto : stageList) {
				stageDto.setCanEdit(CommonConstant.ONE);
				//v1.5 验收通过后验收阶段和之前得阶段不可以修改
				if (haveAccept && stageSort != null && stageDto.getStageSort() != null && stageDto.getStageSort() <= stageSort) {
					stageDto.setCanEdit(CommonConstant.ZERO);
				}
				if (!StringUtils.isEmpty(stageDto.getStageKey()) && !ObjectUtils.isEmpty(phaseMap) && phaseMap.containsKey(stageDto.getStageKey())) {
					stageDto.setStageName(phaseMap.get(stageDto.getStageKey()).getDictValue());
					stageDto.setStageSort(phaseMap.get(stageDto.getStageKey()).getSort());
					stageDto.setNecessaryType(phaseMap.get(stageDto.getStageKey()).getNecessaryType());
				}

				// 阶段状态
				if (!ObjectUtils.isEmpty(stageStatusMap) && !ObjectUtils.isEmpty(stageDto.getStageStatus()) && stageStatusMap.containsKey(stageDto.getStageStatus())) {
					stageDto.setStageStatusName(stageStatusMap.get(stageDto.getStageStatus()).getDictValue());
				}
				List<ProjectFileDto> fileDtoList = new ArrayList<>();
				if (stageDto.getProgressId() != null && stageDto.getId() != null) {
					fileDtoList = projectFileService.listByProject(stageDto.getProjectId(), stageDto.getId(), ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());
				}

				if (CollectionUtils.isEmpty(fileDtoList)) {
					continue;
				}
				// 附件ID
				List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (CollectionUtils.isEmpty(attachList)) {
					continue;
				}

				stageDto.setFileList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
			}
		}
		// 重新排序
		stageList.stream().sorted(Comparator.comparing(ProjectStageDto::getStageSort));

		result.setStageList(stageList);
		result.setProjectId(id);

		//v1.5建设内容
		// 项目介绍
		ProjectIntroduceDto introduceDto = projectIntroduceService.getByProjectId(id);
		if (introduceDto != null) {
			String constructionContent = introduceDto.getConstructionContent();
			result.setConstructionContent(constructionContent);
		}
		//获取最新组织信息
		if (Func.isNotEmpty(result.getCompanyId())) {
			result.setCompanyName(SysCache.getDeptName(result.getCompanyId()));
		}
		if (Func.isNotEmpty(result.getConstructionUnitId())) {
			if (Func.isNotEmpty(result.getConstructionUnitId())) {
				Dept dept = SysCache.getDept(result.getConstructionUnitId());
				if (Func.isNotEmpty(dept)) {
					if (dept.getUnitId().equals(dept.getId())) {
						result.setConstructionUnitName(dept.getDeptName());
					} else {
						result.setConstructionUnitName(SysCache.getDeptName(dept.getUnitId()) + "/" + dept.getDeptName());
					}
				}
			}
		}
		if (Func.isNotEmpty(result.getInvestorId())) {
			result.setInvestorName(SysCache.getDeptName(result.getInvestorId()));
		}
		//获取最新的progressId
		result.setProgressId(projectProgressService.getLastedByProjectId(result.getProjectId()));
		return result;
	}


	@Transactional(rollbackFor = Exception.class)
	public ProjectProgressDto save(ProjectProgressNewVo vo) {

		ProjectProgressDto result = new ProjectProgressDto();
		//projectBaseService.updateFunds(vo);
		PlBaseDto plBaseDto = plBaseService.fetchById(vo.getId());
		plBaseService.updateFunds(vo.getId(), vo.getPlanFunds(), vo.getContractAmount(), vo.getRemark(), vo.getSupervisionUnit(), vo.getCooperationUnit());
		PlContactVo plContactVo = BeanUtil.copy(vo, PlContactVo.class);
		plContactVo.setProjectId(vo.getId());

		plContactService.updateContact(vo.getId(), plContactVo);
		projectContactService.updateContact(vo.getId(), vo.getHeadPerson(), vo.getHeadPersonTel(), vo.getConstructionPerson(), vo.getConstructionPersonTel());
		// 项目阶段
		if (!CollectionUtils.isEmpty(vo.getStageList())) {
			// 删除项目阶段数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectStageService.deleteByProgressId(vo.getId());
			}

			// 删除项目阶段中附件
			List<Long> stageIdList = vo.getStageList().stream().map(ProjectStageVo::getId).collect(Collectors.toList());
			if (!StringUtils.isEmpty(vo.getId()) && !CollectionUtils.isEmpty(stageIdList)) {
				projectFileService.deleteByProjectId(vo.getId(), stageIdList, ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());
			}

			for (ProjectStageVo stageVo : vo.getStageList()) {
				stageVo.setProgressId(vo.getId());
				stageVo.setProjectId(vo.getId());

				projectStageService.save(stageVo);

				// 项目附件
				if (CollectionUtils.isEmpty(stageVo.getFileList())) {
					continue;
				}
				for (Long fileId : stageVo.getFileList()) {
					ProjectFileVo fileVo = new ProjectFileVo();
					fileVo.setProjectId(vo.getId());
					fileVo.setAttachId(fileId);
					fileVo.setBusinessId(stageVo.getId());
					fileVo.setBusinessType(ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());

					projectFileService.save(fileVo);
				}
				// 自动收集
				baseLogicService.autoCollectKnowledge(stageVo.getFileList(), plBaseDto.getId(), plBaseDto.getProjectNo());
			}
		}
		auditPass(vo.getId());
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public ProjectProgressDto submit(ProjectProgressVo vo, SzykUser szykUser) {

		ProjectProgressDto result = new ProjectProgressDto();

		// 校验
		if (!StringUtils.isEmpty(vo.getProjectId())) {
			List<ProjectProgressDto> list = projectProgressService.listByNoStatus(ProgressStatusEnum.PASS.getCode(), vo.getProjectId(), vo.getId(), vo.getProgressType());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.PROGRESS_DATA_EXIST_PROJECT.getMessage());
			}
		}

		if (StringUtils.isEmpty(vo.getId())) {
			vo.setProgressNo(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
		} else {
			vo.setProgressNo(projectProgressService.fetchById(vo.getId()).getProgressNo());
		}
		vo.setProgressStatus(ProgressStatusEnum.UNDER_REVIEW.getCode());
		vo.setSubmitDate(new Date());
		vo.setSubmitPersonId(szykUser.getUserId());
		vo.setSubmitPersonName(szykUser.getNickName());

		if (StringUtils.isEmpty(vo.getId())) {
			result = projectProgressService.save(vo);
		} else {
			result = projectProgressService.update(vo);
		}

		// 项目阶段
		if (!CollectionUtils.isEmpty(vo.getStageList())) {
			// 删除项目阶段数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectStageService.deleteByProgressId(vo.getId());
			}

			// 删除项目阶段中附件
			List<Long> stageIdList = vo.getStageList().stream().map(ProjectStageVo::getId).collect(Collectors.toList());
			if (!StringUtils.isEmpty(vo.getId()) && !CollectionUtils.isEmpty(stageIdList)) {
				projectFileService.deleteByProjectId(result.getProjectId(), stageIdList, ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());
			}

			for (ProjectStageVo stageVo : vo.getStageList()) {
				stageVo.setProgressId(result.getId());
				stageVo.setProjectId(result.getProjectId());

				// 实际开始时间 && 实际结束时间
				if (StringUtils.isEmpty(stageVo.getActualStartTime()) && StringUtils.isEmpty(stageVo.getActualCompleteTime())) {
					stageVo.setStageStatus(StageStatusEnum.STAGE_STATUS_0.getDictKey());
				}

				// 计划开始时间 && 实际开始时间
				if (!StringUtils.isEmpty(stageVo.getPlanStartTime()) && !StringUtils.isEmpty(stageVo.getActualStartTime())) {
					if (stageVo.getActualStartTime().getTime() > stageVo.getPlanStartTime().getTime()) {
						stageVo.setStageStatus(StageStatusEnum.STAGE_STATUS_3.getDictKey());
					}
					if (stageVo.getActualStartTime().getTime() <= stageVo.getPlanStartTime().getTime()) {
						stageVo.setStageStatus(StageStatusEnum.STAGE_STATUS_1.getDictKey());
					}
				}

				// 计划结束时间 && 实际结束时间
				if (!StringUtils.isEmpty(stageVo.getPlanCompleteTime()) && !StringUtils.isEmpty(stageVo.getActualCompleteTime())) {
					if (stageVo.getActualCompleteTime().getTime() > stageVo.getPlanCompleteTime().getTime()) {
						stageVo.setStageStatus(StageStatusEnum.STAGE_STATUS_4.getDictKey());
					}
					if (stageVo.getActualCompleteTime().getTime() <= stageVo.getPlanCompleteTime().getTime()) {
						stageVo.setStageStatus(StageStatusEnum.STAGE_STATUS_2.getDictKey());
					}
				}

				projectStageService.save(stageVo);

				// 项目附件
				if (CollectionUtils.isEmpty(stageVo.getFileList())) {
					continue;
				}
				for (Long fileId : stageVo.getFileList()) {
					ProjectFileVo fileVo = new ProjectFileVo();
					fileVo.setProjectId(result.getProjectId());
					fileVo.setAttachId(fileId);
					fileVo.setBusinessId(stageVo.getId());
					fileVo.setBusinessType(ProjectFileEnum.PROGRESS_DELIVERABLES.getCode());

					projectFileService.save(fileVo);
				}
				// 自动收集
				baseLogicService.autoCollectKnowledge(stageVo.getFileList(), result.getProjectId(), result.getProjectNo());
			}
		}

		// 项目验收
		if (!ObjectUtils.isEmpty(vo.getAcceptVo())) {
			// 删除项目阶段数据
			if (!StringUtils.isEmpty(vo.getId())) {
				projectAcceptService.deleteByProgressId(vo.getId());
			}

			ProjectAcceptVo acceptVo = vo.getAcceptVo();
			acceptVo.setProgressId(result.getId());
			acceptVo.setProjectId(result.getProjectId());

			projectAcceptService.save(acceptVo);
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> report(ProjectProgressDeleteVo vo, SzykUser szykUser) {
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();

			ProjectProgressDto progressDto = projectProgressService.fetchById(id);
			deleteDto.setName(progressDto.getProgressNo());

			// 校验-建设单位
			if (StringUtils.isEmpty(progressDto.getConstructionUnitId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_1.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-公司名称
			if (StringUtils.isEmpty(progressDto.getCompanyId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_2.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目名称
			if (StringUtils.isEmpty(progressDto.getProjectId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_3.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-计划资金
			if (StringUtils.isEmpty(progressDto.getPlanFunds())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_4.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-合同金额
			if (StringUtils.isEmpty(progressDto.getContractAmount())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_5.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-投资主体
			if (StringUtils.isEmpty(progressDto.getInvestorId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_6.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 项目阶段
			List<ProjectStageDto> stageList = projectStageService.listByProgressId(progressDto.getId());

			// 校验-项目阶段
			if (CollectionUtils.isEmpty(stageList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_7.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目阶段-计划开始时间
			List<ProjectStageDto> startList = stageList.stream().filter(item -> StringUtils.isEmpty(item.getPlanStartTime())).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(startList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_8.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目阶段-计划结束时间
			List<ProjectStageDto> completeList = stageList.stream().filter(item -> StringUtils.isEmpty(item.getPlanCompleteTime())).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(completeList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_9.getMessage());
				result.add(deleteDto);
				continue;
			}

			// 项目验收
			ProjectAcceptDto acceptDto = projectAcceptService.getByProgressId(progressDto.getId());

			// 校验-项目验收
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && ObjectUtils.isEmpty(acceptDto)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_10.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目验收-验收时间
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getAcceptDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_11.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目验收-集团信息化资源使用情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getResourceUse())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_12.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目验收-项目完成情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getCompleteResult())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_13.getMessage());
				result.add(deleteDto);
				continue;
			}
			// 校验-项目验收-项目运行情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getProjectOperation())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_14.getMessage());
				result.add(deleteDto);
				continue;
			}

			boolean flag = projectProgressService.updateByStatus(id, ProgressStatusEnum.UNDER_REVIEW.getCode(), new Date(), szykUser.getUserId(), szykUser.getNickName());
			deleteDto.setResult(flag);

			result.add(deleteDto);
		}

		return result;
	}

	public ProjectCheckDataDto checkData(ProjectBaseDeleteVo vo) {
		ProjectCheckDataDto dto = new ProjectCheckDataDto();
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			throw new ServiceException(ExceptionEnum.NOT_PARAMS.getMessage());
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();

			ProjectProgressDto progressDto = projectProgressService.fetchById(id);
			deleteDto.setName(progressDto.getProgressNo());

			// 状态
			if (!(progressDto.getProgressStatus().equals(ProgressStatusEnum.TO_BE_REPORTED.getCode())
				|| progressDto.getProgressStatus().equals(ProgressStatusEnum.CANCEL.getCode())
				|| progressDto.getProgressStatus().equals(ProgressStatusEnum.RETURN.getCode()))
			) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.PROJECT_TO_BE_REPORTED.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 校验-建设单位
			if (StringUtils.isEmpty(progressDto.getConstructionUnitId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_1.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-公司名称
			if (StringUtils.isEmpty(progressDto.getCompanyId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_2.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目名称
			if (StringUtils.isEmpty(progressDto.getProjectId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_3.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-计划资金
			if (StringUtils.isEmpty(progressDto.getPlanFunds())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_4.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-合同金额
			if (StringUtils.isEmpty(progressDto.getContractAmount())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_5.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-投资主体
			if (StringUtils.isEmpty(progressDto.getInvestorId())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_6.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 项目阶段
			List<ProjectStageDto> stageList = projectStageService.listByProgressId(progressDto.getId());

			// 校验-项目阶段
			if (CollectionUtils.isEmpty(stageList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_7.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目阶段-计划开始时间
			List<ProjectStageDto> startList = stageList.stream().filter(item -> StringUtils.isEmpty(item.getPlanStartTime())).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(startList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_8.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目阶段-计划结束时间
			List<ProjectStageDto> completeList = stageList.stream().filter(item -> StringUtils.isEmpty(item.getPlanCompleteTime())).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(completeList)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_9.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}

			// 项目验收
			ProjectAcceptDto acceptDto = projectAcceptService.getByProgressId(progressDto.getId());

			// 校验-项目验收
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && ObjectUtils.isEmpty(acceptDto)) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_10.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目验收-验收时间
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getAcceptDate())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_11.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目验收-集团信息化资源使用情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getResourceUse())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_12.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目验收-项目完成情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getCompleteResult())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_13.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
			// 校验-项目验收-项目运行情况
			if (progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_ACCEPTED.getCode()) && StringUtils.isEmpty(acceptDto.getProjectOperation())) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.REPORT_CHECK_ERROR_14.getMessage());
				result.add(deleteDto);
				dto.setCheckResult(false);
				continue;
			}
		}
		dto.setDataList(result);
		return dto;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<ProjectDeleteDto> delete(ProjectProgressDeleteVo vo) {
		List<ProjectDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			ProjectDeleteDto deleteDto = new ProjectDeleteDto();

			ProjectProgressDto progressDto = projectProgressService.fetchById(id);
			deleteDto.setName(progressDto.getProgressNo());

			// 校验
			if (!(progressDto.getProgressStatus().equals(ProgressStatusEnum.TO_BE_REPORTED.getCode())
				|| progressDto.getProgressStatus().equals(ProgressStatusEnum.CANCEL.getCode())
				|| progressDto.getProgressStatus().equals(ProgressStatusEnum.RETURN.getCode()))
			) {
				deleteDto.setResult(false);
				deleteDto.setMessage(ExceptionEnum.DATA_NOT_DELETE.getMessage());
				result.add(deleteDto);
				continue;
			}

			Boolean flag = projectProgressService.deleteById(id);
			deleteDto.setResult(flag);

			// 删除阶段数据
			//projectStageService.deleteByProgressId(id);

			// 删除验收数据
			//projectAcceptService.deleteByProgressId(id);

			// 删除附件数据
			/*List<ProjectStageDto> stageList = projectStageService.listByProgressId(progressDto.getId());
			if (!CollectionUtils.isEmpty(stageList)) {
				List<Long> stageIdList = stageList.stream().map(ProjectStageDto::getId).collect(Collectors.toList());
				projectFileService.deleteByProjectId(id, stageIdList, progressDto.getProgressType().equals(ProgressTypeEnum.PROJECT_PROGRESS.getCode()) ? ProjectFileEnum.PROGRESS_DELIVERABLES.getCode() : ProjectFileEnum.ACCEPT_DELIVERABLES.getCode());
			}*/

			result.add(deleteDto);
		}

		return result;
	}


	/**
	 * 审核通过
	 *
	 * @param id
	 */
	@Transactional(rollbackFor = Exception.class)
	public void auditPass(Long id) {

		//projectProgressService.updateByStatus(id, ProgressStatusEnum.PASS.getCode(), null, null, null);


		ProjectProgressDto result = new ProjectProgressDto();
		result.setProjectId(id);
		result.setProgressType(ProgressTypeEnum.PROJECT_PROGRESS.getCode());


		// 项目阶段数据
		List<ProjectStageDto> stageList = projectStageService.listByProgressId(id);

		for (ProjectStageDto stageDto : stageList) {
			List<ProjectFileDto> fileDtoList = projectFileService.listByProject(stageDto.getProjectId(), stageDto.getId(), result.getProgressType().equals(ProgressTypeEnum.PROJECT_PROGRESS.getCode()) ? ProjectFileEnum.PROGRESS_DELIVERABLES.getCode() : ProjectFileEnum.ACCEPT_DELIVERABLES.getCode());
			if (CollectionUtils.isEmpty(fileDtoList)) {
				continue;
			}

			// 附件ID
			List<Long> attachIds = fileDtoList.stream().map(ProjectFileDto::getAttachId).collect(Collectors.toList());
			stageDto.setFileIdList(attachIds);
		}

		result.setStageList(stageList);

		// 同步项目库数据
		plExternalReferenceService.synchronizeProjectStage(result);
	}


}
