/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.logic;

import cn.hutool.core.util.ObjectUtil;
import com.snszyk.common.utils.DigitUtil;
import com.snszyk.zbusiness.project.dto.ProjectBatchNoDto;
import com.snszyk.zbusiness.project.vo.ProjectBatchNoVo;
import com.snszyk.zbusiness.project.service.IProjectBatchNoService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * 项目批次号 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@AllArgsConstructor
@Service
public class ProjectBatchNoLogicService extends BaseCrudLogicService<ProjectBatchNoDto, ProjectBatchNoVo> {

    private final IProjectBatchNoService projectBatchNoService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.projectBatchNoService;
    }


	public String getNewBatch(Integer year) {
		ProjectBatchNoDto projectBatchNoDto = projectBatchNoService.fetchById(Long.valueOf(year));
		Integer no = 1;
		if(ObjectUtil.isNotEmpty(projectBatchNoDto)){
			no = projectBatchNoDto.getNowNo() + 1;
		}
		String batchNo = DigitUtil.arabicNumToChineseNum(no);
		return createBatchNo(year,batchNo);
	}

	private String createBatchNo(Integer year, String batchNo) {
    	return year + "年第" + batchNo + "批";
	}

	public Boolean saveNew(ProjectBatchNoVo vo) {
		return projectBatchNoService.saveNew(vo);
	}
}
