/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目文档
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("project_file")
@EqualsAndHashCode(callSuper = false)
public class ProjectFile extends BaseCrudEntity {

	/**
	 * 项目id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	 * 附件id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long attachId;
	/**
	 * 业务id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long businessId;
	/**
	 * 业务类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String businessType;
	/**
	 * 不可删除的
	 */
	private Long undeletable;

}
