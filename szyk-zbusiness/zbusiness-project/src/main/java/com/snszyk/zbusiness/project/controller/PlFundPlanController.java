/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.controller;

import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.datascope.annotation.DataAuth;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.PlFundPlanDto;
import com.snszyk.zbusiness.project.service.IPlBaseService;
import com.snszyk.zbusiness.project.service.logic.PlFundPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 项目库项目资金计划 控制器
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-project/fund_plan")
@Api(value = "项目库项目资金计划表", tags = "项目库项目资金计划")
public class PlFundPlanController extends BaseCrudController {

	private final PlFundPlanService plFundPlanService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return plFundPlanService;
	}

	@GetMapping("/detail")
	@ApiOperation(value = "详情")
	@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IPlBaseService.class )
	public R<PlFundPlanDto> detail(@ApiParam(value = "projectId", required = true) @RequestParam Long projectId) {
		PlFundPlanDto baseCrudDto = plFundPlanService.detail(projectId);
		return R.data(baseCrudDto);
	}

}
