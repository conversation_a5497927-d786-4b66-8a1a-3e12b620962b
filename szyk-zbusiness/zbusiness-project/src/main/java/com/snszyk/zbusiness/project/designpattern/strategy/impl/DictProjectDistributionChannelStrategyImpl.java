package com.snszyk.zbusiness.project.designpattern.strategy.impl;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.project.designpattern.factory.DictTypeFactory;
import com.snszyk.zbusiness.project.designpattern.strategy.DictTypeStrategy;
import com.snszyk.zbusiness.project.service.IProjectBaseService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class DictProjectDistributionChannelStrategyImpl implements DictTypeStrategy, InitializingBean {
	@Resource
	private IProjectBaseService baseService;

	@Override
	public Boolean isReference(String dictKey) {
		return ObjectUtil.isNotEmpty(baseService.getByDictKey(null, null, dictKey));
	}

	@Override
	public void afterPropertiesSet()  {
		DictTypeFactory.register(DictEnum.DC.getCode(), this);
	}
}
