package com.snszyk.zbusiness.project.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.system.cache.SysCache;
import com.snszyk.zbusiness.dict.dto.DictCommonDto;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.enums.EnableTypeEnum;
import com.snszyk.zbusiness.dict.service.IDictCommonService;
import com.snszyk.zbusiness.project.dto.PlFundPlanDto;
import com.snszyk.zbusiness.project.enums.ExceptionEnum;
import com.snszyk.zbusiness.project.service.IPlFundPlanService;
import com.snszyk.zbusiness.project.vo.PlFundPlanVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PlFundPlanService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class PlFundPlanService extends BaseCrudLogicService<PlFundPlanDto, PlFundPlanVo> {

	private final IPlFundPlanService plFundPlanService;

	private final IDictCommonService dictCommonService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.plFundPlanService;
	}

	public PlFundPlanDto detail(Long projectId) {

		PlFundPlanDto result = plFundPlanService.getByProjectId(projectId);

		if (result == null) {
			throw new ServiceException(ExceptionEnum.DING_DATA_NO_EXIST.getMessage());
		}

		// 列支渠道
		List<DictCommonDto> dcList = dictCommonService.listByDictStatus(DictEnum.DC.getCode(), null);
		Map<String, DictCommonDto> dcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dcList)) {
			dcMap.putAll(dcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}

		// 列支渠道
		if (!ObjectUtils.isEmpty(dcMap) && !ObjectUtils.isEmpty(result.getDistributionChannel()) && dcMap.containsKey(result.getDistributionChannel())) {
			result.setDistributionChannelName(dcMap.get(result.getDistributionChannel()).getDictValue());
		}
		//增加主体名称查询
		if (!ObjectUtils.isEmpty(result.getInvestmentSubjectId())) {
			result.setInvestmentSubjectName(SysCache.getDeptName(result.getInvestmentSubjectId()));
		}

		return result;
	}

}
