/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.project.dto.ProjectBaseDto;
import com.snszyk.zbusiness.project.dto.ProjectStageDto;
import com.snszyk.zbusiness.project.entity.ProjectStage;
import com.snszyk.zbusiness.project.mapper.ProjectStageMapper;
import com.snszyk.zbusiness.project.service.IProjectStageService;
import com.snszyk.zbusiness.project.vo.ProjectStageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * ProjectStageServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectStageServiceImpl extends BaseCrudServiceImpl<ProjectStageMapper, ProjectStage, ProjectStageDto, ProjectStageVo> implements IProjectStageService {

	@Override
	public List<ProjectStageDto> listByProgressId(Long progressId) {
		LambdaQueryWrapper<ProjectStage> queryWrapper = Wrappers.<ProjectStage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(progressId), ProjectStage::getProgressId, progressId);

		List<ProjectStage> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectStageDto.class));
	}

	@Override
	public List<ProjectStageDto> listByProgressProjectId(Long progressId, Long projectId) {
		if (progressId == null || projectId == null) {
			return new ArrayList<>();
		}
		LambdaQueryWrapper<ProjectStage> queryWrapper = Wrappers.<ProjectStage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(progressId), ProjectStage::getProgressId, progressId).eq(ProjectStage::getProjectId, projectId);
		List<ProjectStage> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectStageDto.class));
	}

	@Override
	public int deleteByProgressId(Long progressId) {
		LambdaQueryWrapper<ProjectStage> queryWrapper = Wrappers.<ProjectStage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(progressId), ProjectStage::getProgressId, progressId);

		return baseMapper.delete(queryWrapper);
	}

	@Override
	public List<ProjectStageDto> listByProgressIdIgnoreDelete(Long progressId) {

		List<ProjectStage> list = baseMapper.selectListIgnoreDelete(progressId);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectStageDto.class));
	}

	@Override
	public List<ProjectStageDto> listByPlanDate(LocalDate planDate) {
		List<ProjectStage> list = super.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(planDate), ProjectStage::getPlanStartTime, planDate)
			.or()
			.eq(ObjectUtil.isNotEmpty(planDate), ProjectStage::getPlanCompleteTime, planDate)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, ProjectStageDto.class);
	}
}
