/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 *项目后评价
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("project_evaluate")
@EqualsAndHashCode(callSuper = false)
public class ProjectEvaluate extends BaseCrudEntity {

	/**
	 * 项目id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	 * 项目编号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectNo;
	/**
	 * 项目名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectName;
	/**
	 * 公司id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 公司名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 建设单位id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer year;
	/**
	 * 专业分类，字典sc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String specialtyClassification;
	/**
	 * 项目分类，字典pc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectClassification;
	/**
	 * 项目阶段，字典pp
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectPhase;
	/**
	 * 项目阶段名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectPhaseName;
	/**
	 * 评价小组id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long evaluateGroupId;
	/**
	 * 评价小组名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String evaluateGroupName;
	/**
	 * 方案id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long schemeId;
	/**
	 * 方案名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String schemeName;
	/**
	 * 评价状态，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String evaluateStatus;
	/**
	 * 总分
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal totalScore;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 发起组织id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long initiateCompanyId;
	/**
	 * 发起组织名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String initiateCompanyName;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer sendMember;

	/**
	 * 满分
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal fullScore;

	/**
	 * 项目类型
	 */
	private String projectType;
}
