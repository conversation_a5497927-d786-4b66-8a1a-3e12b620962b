/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.project.dto.ProjectDispatchLogDto;
import com.snszyk.zbusiness.project.entity.ProjectDispatchLog;
import com.snszyk.zbusiness.project.mapper.ProjectDispatchLogMapper;
import com.snszyk.zbusiness.project.service.IProjectDispatchLogService;
import com.snszyk.zbusiness.project.vo.ProjectDispatchLogVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * ProjectDispatchLogServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectDispatchLogServiceImpl extends BaseCrudServiceImpl<ProjectDispatchLogMapper, ProjectDispatchLog, ProjectDispatchLogDto, ProjectDispatchLogVo> implements IProjectDispatchLogService {

	@Override
	public List<ProjectDispatchLogDto> listByDispatchDetailId(Long projectDispatchDetailId) {
		LambdaQueryWrapper<ProjectDispatchLog> queryWrapper = Wrappers.<ProjectDispatchLog>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectDispatchDetailId), ProjectDispatchLog::getProjectDispatchDetailId, projectDispatchDetailId)
			.orderByDesc(ProjectDispatchLog::getOperateTime);

		List<ProjectDispatchLog> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectDispatchLogDto.class));
	}

	@Override
	public int deleteByDispatchDetailId(List<Long> dispatchDetailIds) {
		LambdaQueryWrapper<ProjectDispatchLog> queryWrapper = Wrappers.<ProjectDispatchLog>query().lambda()
			.in(CollectionUtils.isNotEmpty(dispatchDetailIds), ProjectDispatchLog::getProjectDispatchDetailId, dispatchDetailIds);

		return baseMapper.delete(queryWrapper);
	}
}
