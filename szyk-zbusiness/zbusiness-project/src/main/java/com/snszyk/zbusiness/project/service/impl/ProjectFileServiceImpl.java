/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.project.dto.ProjectFileDto;
import com.snszyk.zbusiness.project.entity.ProjectFile;
import com.snszyk.zbusiness.project.mapper.ProjectFileMapper;
import com.snszyk.zbusiness.project.service.IProjectFileService;
import com.snszyk.zbusiness.project.vo.ProjectFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ProjectFileServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class ProjectFileServiceImpl extends BaseCrudServiceImpl<ProjectFileMapper, ProjectFile, ProjectFileDto, ProjectFileVo> implements IProjectFileService {


	@Override
	public Boolean updateUndeletable(Long projectId, Long businessId, String businessType, Long undeletable) {
		return this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectFile::getProjectId, projectId)
			.eq(ObjectUtils.isNotEmpty(businessId), ProjectFile::getBusinessId, businessId)
			.eq(ObjectUtils.isNotEmpty(businessType), ProjectFile::getBusinessType, businessType)
			.set(ObjectUtils.isNotEmpty(undeletable), ProjectFile::getUndeletable, undeletable)
			.update();
	}

	@Override
	public List<ProjectFileDto> listByProject(Long projectId, Long businessId, String businessType) {
		LambdaQueryWrapper<ProjectFile> queryWrapper = Wrappers.<ProjectFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectFile::getProjectId, projectId)
			.eq(ObjectUtils.isNotEmpty(businessId), ProjectFile::getBusinessId, businessId)
			.eq(ObjectUtils.isNotEmpty(businessType), ProjectFile::getBusinessType, businessType);

		List<ProjectFile> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}

		return Objects.requireNonNull(BeanUtil.copy(list, ProjectFileDto.class));
	}

	@Override
	public List<ProjectFileDto> listLatestByProjects(List<Long> projectIds, String businessType) {
		if (CollectionUtil.isEmpty(projectIds)) {
			return new ArrayList<>();
		}
		return this.baseMapper.listLatestByProjects(projectIds, businessType);

	}
	@Override
	public List<ProjectFileDto> listByProjects(List<Long> projectIds, String businessType) {
		if (CollectionUtil.isEmpty(projectIds)) {
			return new ArrayList<>();
		}
		return this.baseMapper.listByProjects(projectIds, businessType);

	}

	@Override
	public int deleteByProjectId(Long projectId, List<Long> businessIdList, String businessType) {
		LambdaQueryWrapper<ProjectFile> queryWrapper = Wrappers.<ProjectFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectFile::getProjectId, projectId)
			.in(CollectionUtils.isNotEmpty(businessIdList), ProjectFile::getBusinessId, businessIdList)
			.eq(ObjectUtils.isNotEmpty(businessType), ProjectFile::getBusinessType, businessType);

		return baseMapper.delete(queryWrapper);
	}

	@Override
	public int deleteByProjectId(Long projectId, List<Long> businessIdList, String businessType, List<Long> attachIds) {
		LambdaQueryWrapper<ProjectFile> queryWrapper = Wrappers.<ProjectFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(projectId), ProjectFile::getProjectId, projectId)
			.in(CollectionUtils.isNotEmpty(businessIdList), ProjectFile::getBusinessId, businessIdList)
			.in(ProjectFile::getAttachId, attachIds)
			.eq(ObjectUtils.isNotEmpty(businessType), ProjectFile::getBusinessType, businessType);

		return baseMapper.delete(queryWrapper);
	}

	@Override
	public Boolean batchSave(List<ProjectFileVo> list) {
		if (CollectionUtil.isEmpty(list)) {
			return false;
		}
		return this.saveBatch(BeanUtil.copy(list, ProjectFile.class));
	}

	@Override
	public boolean deleteByIds(List<Long> deleteFileIds) {
		int i = this.baseMapper.deleteBatchIds(deleteFileIds);
		return i > 0;
	}
}
