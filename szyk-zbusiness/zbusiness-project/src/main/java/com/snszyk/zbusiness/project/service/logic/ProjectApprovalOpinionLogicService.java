/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service.logic;

import cn.hutool.core.bean.BeanUtil;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.zbusiness.flow.dto.InstanceDto;
import com.snszyk.zbusiness.flow.dto.InstanceProcessDto;
import com.snszyk.zbusiness.flow.enums.CompleteEventEnum;
import com.snszyk.zbusiness.flow.service.IInstanceProcessService;
import com.snszyk.zbusiness.flow.service.IInstanceService;
import com.snszyk.zbusiness.flow.vo.InstanceProcessVo;
import com.snszyk.zbusiness.flow.vo.InstanceVo;
import com.snszyk.zbusiness.project.dto.ProjectApprovalOpinionDto;
import com.snszyk.zbusiness.project.dto.ProjectBaseDto;
import com.snszyk.zbusiness.project.entity.ProjectApprovalOpinion;
import com.snszyk.zbusiness.project.service.IProjectBaseService;
import com.snszyk.zbusiness.project.vo.ProjectApprovalOpinionEditVo;
import com.snszyk.zbusiness.project.vo.ProjectApprovalOpinionVo;
import com.snszyk.zbusiness.project.service.IProjectApprovalOpinionService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * 项目审批意见 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@AllArgsConstructor
@Service
public class ProjectApprovalOpinionLogicService extends BaseCrudLogicService<ProjectApprovalOpinionDto, ProjectApprovalOpinionVo> {

    private final IProjectApprovalOpinionService projectApprovalOpinionService;

    private final IInstanceService instanceService;

    private final IInstanceProcessService instanceProcessService;




    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.projectApprovalOpinionService;
    }

    @Transactional
    public Boolean saveData(ProjectApprovalOpinionEditVo v) {
		List<ProjectApprovalOpinionVo> list = new LinkedList<>();
		LocalDateTime nowDate = LocalDateTime.now();
		SzykUser user = AuthUtil.getUser();
		ProjectApprovalOpinionVo vo = BeanUtil.copyProperties(v,ProjectApprovalOpinionVo.class);
    	//首先遍历数据
		v.getInstanceList().forEach(instanceId ->{
			//获取流程数据
			InstanceDto instanceDto = instanceService.fetchById(instanceId);
			//删除当前节点已存在的审查数据
			projectApprovalOpinionService.deleteByProjectId(instanceDto.getId(),instanceDto.getCurrFlowSort());
			//组装数据
			vo.setProjectId(instanceDto.getBusinessId());
			vo.setInstanceId(instanceId);
			vo.setApproveUserId(String.valueOf(user.getUserId()));
			vo.setApproveUserName(user.getNickName());
			vo.setApproveDeptId(instanceDto.getCurrDeptId());
			vo.setApproveDeptName(instanceDto.getCurrDeptName());
			vo.setApproveTime(nowDate);
			vo.setFlowSort(instanceDto.getCurrFlowSort());
			vo.setId(null);
			projectApprovalOpinionService.save(vo);
			//审批流更新完成情况
			instanceDto.setCurCompleteEvent(CompleteEventEnum.YES.getCode());
			instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));
			//更新节点信息
			InstanceProcessDto instanceProcessDto = instanceProcessService.fetchById(instanceDto.getCurrProcessId());
			instanceProcessDto.setCompleteEvent(CompleteEventEnum.YES.getCode());
			instanceProcessService.save(BeanUtil.copyProperties(instanceProcessDto, InstanceProcessVo.class));
		});
		return true;
    }

	public ProjectApprovalOpinionDto fetchByIdInstanceId(Long id) {
    	//获取流程当前节点
		InstanceDto instanceDto = instanceService.fetchById(id);
		return projectApprovalOpinionService.fetchByIdInstanceId(id,instanceDto.getCurrFlowSort());
	}
}
