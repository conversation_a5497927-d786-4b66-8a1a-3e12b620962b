/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.mapper;

import com.snszyk.zbusiness.project.entity.PlStage;
import com.snszyk.zbusiness.project.vo.PlStageVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 项目库项目阶段表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
public interface PlStageMapper extends BaseMapper<PlStage> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param plStage
	 * @return
	 */
	List<PlStageVo> selectPlStagePage(IPage page, PlStageVo plStage);

}
