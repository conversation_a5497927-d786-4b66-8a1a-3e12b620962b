package com.snszyk.zbusiness.project.service.logic;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDeptService;
import com.snszyk.zbusiness.flow.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.flow.service.ApprovalOperateService;
import com.snszyk.zbusiness.project.dto.ProjectProgressDto;
import com.snszyk.zbusiness.project.enums.ProgressStatusEnum;
import com.snszyk.zbusiness.project.service.IProjectProgressService;
import com.snszyk.zbusiness.project.vo.ProjectProgressVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @atuthor
 * @date 2023/3/28
 * @apiNote
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjectTwoAcceptApprovalOperate implements ApprovalOperateService {

	private final ProjectProgressService projectProgressService;
	private final IProjectProgressService iprojectProgressService;
	private final IDeptService deptService;



	@Override
	public String bpmBusinessType() {
		return BusinessTypeEnum.TWO_PROJECT_CHECK.getCode();
	}

	@Override
	public Boolean intiInstance(Long businessId, Long instanceId, Map<String,Object> params,Boolean createFlag) {
		SzykUser szykUser = AuthUtil.getUser();
		//维护流程id以及修改审批状态
		ProjectProgressDto projectProgressDto = iprojectProgressService.fetchById(businessId);
		projectProgressDto.setProgressStatus(ProgressStatusEnum.UNDER_REVIEW.getCode());
		projectProgressDto.setInstanceId(instanceId);
		projectProgressDto.setSubmitPersonId(szykUser.getUserId());
		projectProgressDto.setSubmitPersonName(szykUser.getNickName());
		projectProgressDto.setSubmitDate(new Date());
		projectProgressDto.setCancelFlag(true);
		iprojectProgressService.save(BeanUtil.copyProperties(projectProgressDto, ProjectProgressVo.class));
		projectProgressService.autoFile(businessId);
		return true;
	}

	@Override
	public Boolean processDone(Long businessId, Long instanceId, Map<String, Object> params) {
		//有人审核后就不能撤回了
		ProjectProgressDto projectProgressDto = iprojectProgressService.fetchById(businessId);
		projectProgressDto.setCancelFlag(false);
		iprojectProgressService.save(BeanUtil.copyProperties(projectProgressDto, ProjectProgressVo.class));
		return true;
	}

	@Override
	public String getDescription(Long businessId) {
		ProjectProgressDto projectProgressDto = iprojectProgressService.fetchById(businessId);
		return projectProgressDto.getProjectName();
	}


	@Override
	public Boolean updateLevel(Long businessId, Integer Level) {

		return iprojectProgressService.updateLevel(businessId,Level);
	}


	@Override
	public Boolean doApproval(Long businessId) {
		//审核通过执行的操作
		projectProgressService.auditPass(businessId);
		return true;
	}

	@Override
	public Boolean doReturn(Long businessId) {
		//变更状态
		ProjectProgressDto projectProgressDto = iprojectProgressService.fetchById(businessId);
		projectProgressDto.setProgressStatus(ProgressStatusEnum.RETURN.getCode());
		iprojectProgressService.save(BeanUtil.copyProperties(projectProgressDto, ProjectProgressVo.class));
		return true;
	}

	@Override
	public Boolean doCancel(Long businessId) {
		ProjectProgressDto projectProgressDto = iprojectProgressService.fetchById(businessId);
		Dept dept = deptService.getById(projectProgressDto.getCreateDept());
		dept = deptService.getById(dept.getUnitId());
		//变更状态
		return iprojectProgressService.cancelInstance(businessId,dept.getUnitLevel());
	}

	@Override
	public JSONArray createOaData(List<Long> businessId) {
		return null;
	}

	@Override
	public Boolean checkCancel(Long businessId) {
		return projectProgressService.checkCancel(businessId);
	}

	@Override
	public Boolean handleBusiness(Long businessId) {
		return null;
	}


}
