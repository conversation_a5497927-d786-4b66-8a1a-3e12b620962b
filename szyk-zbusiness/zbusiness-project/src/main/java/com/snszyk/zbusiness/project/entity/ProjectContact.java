/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目联系人
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("project_contact")
@EqualsAndHashCode(callSuper = false)
public class ProjectContact extends BaseCrudEntity {

	/**
	 * 项目id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectId;
	/**
	 * 项目负责人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String headPerson;
	/**
	 * 负责人联系方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String headPersonTel;
	/**
	 * 项目联系人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String constructionPerson;
	/**
	 * 项目联系人联系方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String constructionPersonTel;

}
