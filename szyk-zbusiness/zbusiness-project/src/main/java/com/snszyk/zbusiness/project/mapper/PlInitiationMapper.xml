<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.PlInitiationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="plInitiationResultMap" type="com.snszyk.zbusiness.project.entity.PlInitiation">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="project_id" property="projectId"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_complete_date" property="planCompleteDate"/>
        <result column="project_introduction" property="projectIntroduction"/>
        <result column="project_necessity" property="projectNecessity"/>
        <result column="construction_content" property="constructionContent"/>
        <result column="equipment_input_details" property="equipmentInputDetails"/>
        <result column="im_input_details" property="imInputDetails"/>
        <result column="review_comments" property="reviewComments"/>
    </resultMap>


    <select id="selectPlInitiationPage" resultMap="plInitiationResultMap">
        select * from pl_initiation where is_deleted = 0
    </select>

</mapper>
