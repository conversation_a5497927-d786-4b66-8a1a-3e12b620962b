///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.project.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.snszyk.core.crud.controller.BaseCrudController;
//import com.snszyk.core.crud.dto.BaseCrudDto;
//import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.project.service.logic.ProjectBaseHistoryLogicService;
//import com.snszyk.zbusiness.project.vo.ProjectBaseHistoryVo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//
///**
// * 项目申报内容历史记录 控制器
// *
// * <AUTHOR>
// * @since 2024-04-19
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("zbusiness-project/projectbasehistory")
//@Api(value = "项目申报内容历史记录", tags = "项目申报内容历史记录接口")
//public class ProjectBaseHistoryController extends BaseCrudController {
//
//	// private final IProjectBaseHistoryService projectBaseHistoryService;
//
//    private final ProjectBaseHistoryLogicService projectBaseHistoryLogicService;
//
//    @Override
//    protected BaseCrudLogicService fetchBaseLogicService() {
//        return projectBaseHistoryLogicService;
//    }
//
//    /**
//     * 保存
//     */
//    @PostMapping("/save")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "保存", notes = "ProjectBaseHistoryVo")
//    public R<BaseCrudDto> save(@RequestBody ProjectBaseHistoryVo v) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 分页
//     */
//    @GetMapping("/page")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "分页", notes = "ProjectBaseHistoryVo")
//    public R<IPage<BaseCrudDto>> page(ProjectBaseHistoryVo v) {
//        IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
//        return R.data(pageQueryResult);
//    }
//
//    /**
//    * 列表
//    */
//    @GetMapping("/list")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "列表", notes = "ProjectBaseHistoryVo")
//    public R<List<BaseCrudDto>> list(ProjectBaseHistoryVo v) {
//        List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
//        return R.data(listQueryResult);
//    }
//
//    /**
//     * 获取单条
//     */
//    @GetMapping("/fetchOne")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "获取单条数据", notes = "ProjectBaseHistoryVo")
//    public R<BaseCrudDto> fetchOne(ProjectBaseHistoryVo v) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 根据ID获取数据
//     */
//    @Override
//    @GetMapping("/fetchById")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "根据ID获取数据", notes = "id")
//    public R<BaseCrudDto> fetchById(Long id) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 删除
//     */
//    @Override
//    @PostMapping("/deleteById")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "删除", notes = "id")
//    public R<Boolean> deleteById(Long id) {
//        Boolean result = this.fetchBaseLogicService().deleteById(id);
//        return R.data(result);
//    }
//
//}
