/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.crud.annotation.OptionDept;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.handler.AutoFillHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 项目库基本信息表实体类
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlBase extends BaseCrudEntity {

	/**
	 * 项目编号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectNo;
	/**
	 * 项目名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectName;

	/**
	 * 批次
	 */
	private String batchNo;
	/**
	 * 批次
	 */
	private String batchName;

	/**
	 * 公司id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 公司名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 建设单位id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer year;
	/**
	 * 专业分类，字典sc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String specialtyClassification;
	/**
	 * 项目分类，字典pc
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectClassification;
	/**
	 * 项目阶段，字段pp
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectPhase;
	/**
	 * 项目阶段
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectPhaseName;
	/**
	 * 牵头部门id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long leadOrgId;
	/**
	 * 牵头部门名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String leadOrgName;
	/**
	 * 合作单位
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String cooperationUnit;
	/**
	 * 监理单位
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String supervisionUnit;
	/**
	 * 项目状态，字典project_status
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectStatus;
	/**
	 * 审查状态，字典review_status
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String reviewStatus;
	/**
	 * 验收状态，字典acceptance_status
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String acceptanceStatus;
	/**
	 * 项目标签，字典pl
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectLabel;
	/**
	 * 项目当前节点
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String projectNode;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;

	private String corpApproveResult;

	/**
	 * 集团审查意见
	 */
	private String corpApproveRemark;

	/**
	 * 二级审查意见
	 */
	private String secApproveRemark;

	/**
	 * 计划资金
	 */
	private BigDecimal planFunds;

	/**
	 * 合同金额
	 */
	private BigDecimal contractAmount;


	@ApiModelProperty("创建人")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long createUser;

	@ApiModelProperty("创建部门")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@OptionDept(value = AutoFillHandler.class, override = false)
	private Long createDept;
	/**
	 * 项目预计总投资
	 */
	@TableField(exist = false)
	private BigDecimal totalEstimate;


}
