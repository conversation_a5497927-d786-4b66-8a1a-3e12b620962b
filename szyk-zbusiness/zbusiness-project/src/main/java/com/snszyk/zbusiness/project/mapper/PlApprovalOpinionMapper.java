/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.mapper;

import com.snszyk.zbusiness.project.entity.PlApprovalOpinion;
import com.snszyk.zbusiness.project.vo.PlApprovalOpinionVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 项目库-项目审批意见 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface PlApprovalOpinionMapper extends BaseMapper<PlApprovalOpinion> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param plApprovalOpinion
	 * @return
	 */
	List<PlApprovalOpinionVo> selectPlApprovalOpinionPage(IPage page, PlApprovalOpinionVo plApprovalOpinion);

}
