<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.ProjectBaseHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="projectBaseHistoryResultMap" type="com.snszyk.zbusiness.project.entity.ProjectBaseHistory">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="project_id" property="projectId"/>
        <result column="approval_no" property="approvalNo"/>
        <result column="project_no" property="projectNo"/>
        <result column="project_name" property="projectName"/>
        <result column="company_id" property="companyId"/>
        <result column="company_name" property="companyName"/>
        <result column="construction_unit_id" property="constructionUnitId"/>
        <result column="construction_unit_name" property="constructionUnitName"/>
        <result column="year" property="year"/>
        <result column="batch_no" property="batchNo"/>
        <result column="batch_name" property="batchName"/>
        <result column="specialty_classification" property="specialtyClassification"/>
        <result column="project_classification" property="projectClassification"/>
        <result column="distribution_channel" property="distributionChannel"/>
        <result column="investment_subject_id" property="investmentSubjectId"/>
        <result column="investment_subject_name" property="investmentSubjectName"/>
        <result column="total_estimate" property="totalEstimate"/>
        <result column="current_investment" property="currentInvestment"/>
        <result column="lead_org_id" property="leadOrgId"/>
        <result column="lead_org_name" property="leadOrgName"/>
        <result column="plan_tender_date" property="planTenderDate"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_complete_date" property="planCompleteDate"/>
        <result column="plan_funds" property="planFunds"/>
        <result column="contract_amount" property="contractAmount"/>
        <result column="corp_approve_result" property="corpApproveResult"/>
        <result column="corp_approve_remark" property="corpApproveRemark"/>
        <result column="sec_approve_remark" property="secApproveRemark"/>
        <result column="project_introduction" property="projectIntroduction"/>
        <result column="project_necessity" property="projectNecessity"/>
        <result column="construction_content" property="constructionContent"/>
        <result column="equipment_input_details" property="equipmentInputDetails"/>
        <result column="im_input_details" property="imInputDetails"/>
        <result column="head_person" property="headPerson"/>
        <result column="head_person_tel" property="headPersonTel"/>
        <result column="construction_person" property="constructionPerson"/>
        <result column="construction_person_tel" property="constructionPersonTel"/>
        <result column="project_member" property="projectMember"/>
        <result column="file_id" property="fileId"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectProjectBaseHistoryPage" resultMap="projectBaseHistoryResultMap">
        select * from project_base_history where is_deleted = 0
    </select>

</mapper>
