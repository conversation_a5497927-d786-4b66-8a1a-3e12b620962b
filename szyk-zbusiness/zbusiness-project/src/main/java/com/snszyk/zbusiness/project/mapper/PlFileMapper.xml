<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.project.mapper.PlFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="plFileResultMap" type="com.snszyk.zbusiness.project.entity.PlFile">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="project_id" property="projectId"/>
        <result column="attach_id" property="attachId"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
    </resultMap>


    <select id="selectPlFilePage" resultMap="plFileResultMap">
        select * from pl_file where is_deleted = 0
    </select>

</mapper>
