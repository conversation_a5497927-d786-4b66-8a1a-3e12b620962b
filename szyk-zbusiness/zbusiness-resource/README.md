# SZYK项目操作记录系统

## 项目概述

本项目为SZYK项目实现了一个通用的操作记录追踪系统，主要用于记录用户对互联网资产管理模块的操作行为，支持字段级别的变更追踪和跨模块扩展。

## 系统特性

### 核心功能
- ✅ **自动操作记录**：基于AOP注解自动记录用户操作
- ✅ **字段级变更追踪**：详细记录每个字段的变更前后值
- ✅ **异步处理**：支持异步记录，不影响业务性能
- ✅ **灵活配置**：支持多种配置选项和SpEL表达式
- ✅ **扩展性强**：支持跨模块使用，易于扩展新业务类型

### 技术特性
- ✅ **数据库设计**：主表+扩展表设计，支持灵活字段扩展
- ✅ **Spring AOP**：基于切面编程实现自动记录
- ✅ **MyBatis-Plus**：使用MyBatis-Plus进行数据访问
- ✅ **异步支持**：使用Spring @Async支持异步处理
- ✅ **SpEL表达式**：支持灵活的参数提取和业务逻辑

## 项目结构

```
szyk-zbusiness/zbusiness-resource/
├── src/main/java/com/snszyk/zbusiness/resource/
│   ├── annotation/           # 注解定义
│   │   └── OperationRecord.java
│   ├── aspect/              # AOP切面
│   │   └── OperationRecordAspect.java
│   ├── controller/          # 控制器
│   │   ├── SysOperationRecordController.java
│   │   └── RsInternetControllerExample.java
│   ├── dto/                 # 数据传输对象
│   │   ├── SysOperationRecordDto.java
│   │   ├── SysOperationRecordExtensionDto.java
│   │   └── FieldChangeDto.java
│   ├── entity/              # 实体类
│   │   ├── SysOperationRecord.java
│   │   └── SysOperationRecordExtension.java
│   ├── enums/               # 枚举定义
│   │   ├── BusinessTypeEnum.java
│   │   └── OperationTypeEnum.java
│   ├── mapper/              # 数据访问层
│   │   ├── SysOperationRecordMapper.java
│   │   └── SysOperationRecordExtensionMapper.java
│   ├── service/             # 服务接口
│   │   ├── ISysOperationRecordService.java
│   │   └── ISysOperationRecordExtensionService.java
│   ├── service/impl/        # 服务实现
│   │   ├── SysOperationRecordServiceImpl.java
│   │   └── SysOperationRecordExtensionServiceImpl.java
│   ├── util/                # 工具类
│   │   └── OperationRecordUtil.java
│   ├── vo/                  # 视图对象
│   │   ├── SysOperationRecordVo.java
│   │   ├── SysOperationRecordPageVo.java
│   │   └── SysOperationRecordExtensionVo.java
│   └── config/              # 配置类
│       └── OperationRecordConfig.java
├── src/main/resources/
│   ├── mapper/              # MyBatis映射文件
│   │   ├── SysOperationRecordMapper.xml
│   │   └── SysOperationRecordExtensionMapper.xml
│   ├── sql/                 # 数据库脚本
│   │   └── operation_record_ddl.sql
│   └── docs/                # 文档
│       └── 操作记录系统使用说明.md
└── src/test/java/           # 测试用例
    └── com/snszyk/zbusiness/resource/service/
        └── SysOperationRecordServiceTest.java
```

## 数据库设计

### 主表：sys_operation_record
存储操作记录的基本信息，包括操作人、操作时间、业务信息等。

### 扩展表：sys_operation_record_extension
存储字段级别的变更详情，支持灵活的字段扩展。

## 快速开始

### 1. 执行数据库脚本
```sql
-- 执行 src/main/resources/sql/operation_record_ddl.sql
```

### 2. 在Controller中添加注解
```java
@PostMapping("/save")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName"
)
public R<RsInternetDto> save(@RequestBody RsInternetVo vo) {
    return R.data(service.save(vo));
}
```

### 3. 查询操作记录
```java
// 分页查询
@PostMapping("/sys/operation-record/page")
public R<IPage<SysOperationRecordDto>> page(@RequestBody SysOperationRecordPageVo vo);

// 查询详情（包含字段变更）
@GetMapping("/sys/operation-record/detail/{id}")
public R<SysOperationRecordDto> detail(@PathVariable Long id);
```

## 使用示例

### 注解方式（推荐）
```java
// 新增操作
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName"
)

// 更新操作
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.UPDATE,
    businessIdExpression = "#vo.id",
    businessNameExpression = "#result.data.systemName"
)

// 删除操作
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.DELETE,
    businessIdExpression = "#id"
)
```

### 工具类方式
```java
@Autowired
private OperationRecordUtil operationRecordUtil;

// 记录新增
operationRecordUtil.recordCreate(BusinessTypeEnum.INTERNET_ASSET, id, name, newData);

// 记录更新
operationRecordUtil.recordUpdate(BusinessTypeEnum.INTERNET_ASSET, id, name, oldData, newData);

// 记录删除
operationRecordUtil.recordDelete(BusinessTypeEnum.INTERNET_ASSET, id, name, oldData);
```

## 扩展指南

### 添加新的业务类型
1. 在`BusinessTypeEnum`中添加新枚举值
2. 在`SysOperationRecordServiceImpl`中添加字段标签映射
3. 在`getBusinessData`方法中添加数据获取逻辑

### 添加新的操作类型
1. 在`OperationTypeEnum`中添加新枚举值
2. 根据需要调整业务逻辑

## 配置说明

### 异步配置
系统默认使用异步记录，可以通过以下方式配置：
```java
@OperationRecord(async = false)  // 关闭异步
```

### 字段变更记录
```java
@OperationRecord(recordFieldChanges = false)  // 关闭字段变更记录
```

### SpEL表达式
支持从方法参数和返回结果中提取业务信息：
```java
businessIdExpression = "#vo.id"              // 从参数获取
businessIdExpression = "#result.data.id"     // 从返回结果获取
```

## 性能优化

1. **异步处理**：默认使用异步记录，避免影响业务性能
2. **批量操作**：大批量操作建议关闭字段变更记录
3. **索引优化**：数据库表已创建必要的索引
4. **数据归档**：建议定期归档历史数据

## 注意事项

1. **权限控制**：操作记录会记录当前登录用户信息
2. **数据安全**：敏感信息可以通过配置排除记录
3. **异常处理**：操作记录失败不会影响业务操作
4. **事务一致性**：支持事务回滚时同步回滚操作记录

## 测试

运行测试用例：
```bash
mvn test -Dtest=SysOperationRecordServiceTest
```

## 文档

详细使用说明请参考：[操作记录系统使用说明](src/main/resources/docs/操作记录系统使用说明.md)

## 版本历史

- **v1.0.0** (2024-07-04)
  - 初始版本发布
  - 支持基本的操作记录功能
  - 支持字段级变更追踪
  - 支持异步处理
  - 支持跨模块扩展

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

Copyright (c) 2018-2028 SZYK Project
