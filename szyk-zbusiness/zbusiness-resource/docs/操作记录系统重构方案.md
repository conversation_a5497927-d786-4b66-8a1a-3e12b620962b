# 操作记录系统重构方案

## 问题分析

### 1. 模块位置不合理
当前操作记录系统放在 `zbusiness-resource` 模块中，存在以下问题：
- 这是一个通用功能，不应该局限于资源管理模块
- 其他业务模块（zbusiness-project、zbusiness-person等）无法直接使用
- 违反了模块职责单一原则
- 增加了模块间的不必要依赖

### 2. 魔法值问题
代码中使用了硬编码的字符串 `"RESOURCE"`，应该使用枚举值。

## 重构方案

### 方案一：移动到 szyk-system 模块（推荐）

#### 优势
- `szyk-system` 是系统基础模块，适合放置通用功能
- 已有 `BusinessModuleEnum` 在 `system-api` 中定义
- 系统级通用功能的标准位置
- 所有业务模块都可以直接使用

#### 移动计划

**第一步：移动通用组件到 system-api**
```
szyk-system/system-api/src/main/java/com/snszyk/system/
├── annotation/
│   └── OperationRecord.java
├── dto/
│   ├── SysOperationRecordDto.java
│   ├── SysOperationRecordExtensionDto.java
│   └── FieldChangeDto.java
├── entity/
│   ├── SysOperationRecord.java
│   └── SysOperationRecordExtension.java
├── enums/
│   ├── BusinessTypeEnum.java        # 扩展现有枚举
│   └── OperationTypeEnum.java       # 扩展现有枚举
├── service/
│   └── ISysOperationRecordService.java
└── vo/
    ├── SysOperationRecordVo.java
    ├── SysOperationRecordPageVo.java
    └── SysOperationRecordExtensionVo.java
```

**第二步：移动实现类到 system-rest**
```
szyk-system/system-rest/src/main/java/com/snszyk/system/
├── aspect/
│   └── OperationRecordAspect.java
├── controller/
│   └── SysOperationRecordController.java
├── mapper/
│   ├── SysOperationRecordMapper.java
│   └── SysOperationRecordExtensionMapper.java
├── service/
│   └── impl/
│       ├── SysOperationRecordServiceImpl.java
│       └── SysOperationRecordExtensionServiceImpl.java
└── service/
    └── logic/
        └── impl/
            └── SysOperationRecordLogicServiceImpl.java
```

**第三步：移动资源文件**
```
szyk-system/system-rest/src/main/resources/
├── mapper/
│   ├── SysOperationRecordMapper.xml
│   └── SysOperationRecordExtensionMapper.xml
└── sql/
    └── sys_operation_record.sql
```

### 方案二：移动到 zbusiness-api 模块

#### 优势
- `zbusiness-api` 是公共API模块，包含DTO、接口定义
- 可以被所有业务子模块共享使用
- 符合当前的模块架构设计

#### 移动计划

**第一步：移动接口和DTO到 zbusiness-api**
```
szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/common/
├── annotation/
├── dto/
├── entity/
├── enums/
├── service/
└── vo/
```

**第二步：实现类保留在各业务模块或创建新的通用模块**

## 推荐方案详细实施

### 选择方案一：移动到 szyk-system 模块

#### 实施步骤

**步骤1：扩展现有枚举**

在 `system-api` 中扩展 `BusinessModuleEnum`：
```java
public enum BusinessModuleEnum {
    // 现有枚举...
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产"),
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产"),
    SOFTWARE_ASSET("SOFTWARE_ASSET", "软件资产"),
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统"),
    PROJECT_MANAGEMENT("PROJECT_MANAGEMENT", "项目管理"),
    PERSONNEL_MANAGEMENT("PERSONNEL_MANAGEMENT", "人员管理");
}
```

**步骤2：创建通用的操作类型枚举**

在 `system-api` 中创建 `OperationTypeEnum`：
```java
public enum OperationTypeEnum {
    CREATE("CREATE", "新增"),
    UPDATE("UPDATE", "更新"),
    DELETE("DELETE", "删除"),
    QUERY("QUERY", "查询"),
    IMPORT("IMPORT", "导入"),
    EXPORT("EXPORT", "导出"),
    AUDIT("AUDIT", "审核"),
    ENABLE("ENABLE", "启用"),
    DISABLE("DISABLE", "停用");
}
```

**步骤3：移动核心文件**

1. 移动注解和DTO到 `system-api`
2. 移动实现类到 `system-rest`
3. 更新包名和导入语句
4. 更新配置文件

**步骤4：修复魔法值问题**

```java
// 修改前
recordVo.setBusinessModule("RESOURCE");

// 修改后
recordVo.setBusinessModule(BusinessModuleEnum.INTERNET_ASSET.getCode());
```

**步骤5：更新业务模块使用方式**

在各业务模块中：
```java
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    description = "新增互联网资产"
)
```

#### 依赖关系调整

**system-api 模块**
- 添加操作记录相关的接口、DTO、枚举
- 不增加新的外部依赖

**system-rest 模块**
- 添加操作记录的实现类
- 依赖 system-api

**业务模块**
- 依赖 system-api（获取注解和接口）
- 运行时依赖 system-rest（获取实现）

#### 配置调整

**application.yml**
```yaml
# 操作记录配置
operation-record:
  enabled: true
  async: true
  default-business-module: SYSTEM
```

**Spring配置**
```java
@ComponentScan(basePackages = {
    "com.snszyk.system.aspect",  // 扫描操作记录切面
    "com.snszyk.system.service"  // 扫描操作记录服务
})
```

## 迁移检查清单

### 文件移动清单
- [ ] 移动注解类到 system-api
- [ ] 移动DTO类到 system-api  
- [ ] 移动实体类到 system-api
- [ ] 移动枚举类到 system-api
- [ ] 移动服务接口到 system-api
- [ ] 移动VO类到 system-api
- [ ] 移动切面类到 system-rest
- [ ] 移动控制器到 system-rest
- [ ] 移动Mapper接口到 system-rest
- [ ] 移动服务实现到 system-rest
- [ ] 移动Mapper XML到 system-rest
- [ ] 移动SQL脚本到 system-rest

### 代码修改清单
- [ ] 更新所有包名引用
- [ ] 修复魔法值问题
- [ ] 更新import语句
- [ ] 更新Spring配置
- [ ] 更新Maven依赖
- [ ] 更新文档引用

### 测试验证清单
- [ ] 编译验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 功能测试
- [ ] 性能测试

## 风险评估

### 低风险
- 文件移动操作
- 包名修改
- 魔法值修复

### 中风险
- 依赖关系调整
- Spring配置修改
- 现有业务模块适配

### 高风险
- 数据库表结构变更（如果需要）
- 现有数据迁移（如果需要）

## 回滚方案

如果迁移过程中出现问题：
1. 保留原有代码的备份
2. 使用Git版本控制进行回滚
3. 分步骤迁移，每步都可以独立回滚
4. 保持向后兼容性

## 后续优化

迁移完成后的优化建议：
1. 统一各业务模块的操作记录使用方式
2. 完善操作记录的查询和统计功能
3. 添加操作记录的数据归档功能
4. 优化异步处理性能
5. 添加操作记录的监控和告警
