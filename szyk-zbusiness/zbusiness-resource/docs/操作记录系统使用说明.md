# 操作记录系统使用说明

## 概述

操作记录系统是一个基于AOP的自动化操作日志记录系统，专门为SZYK项目的互联网资产管理模块设计，支持自动记录用户的增删改操作，并提供详细的字段变更追踪功能。

## 系统架构

### 核心组件

1. **@OperationRecord注解** - 标记需要记录操作的方法
2. **OperationRecordAspect** - AOP切面，自动拦截并记录操作
3. **SysOperationRecord实体** - 操作记录数据模型
4. **SysOperationRecordService** - 操作记录业务服务
5. **OperationRecordUtil** - 手动记录操作的工具类

### 数据库表结构

```sql
CREATE TABLE `sys_operation_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
  `business_id` bigint(20) NOT NULL COMMENT '业务数据ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务数据名称',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint(20) DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(200) DEFAULT NULL COMMENT '操作人部门名称',
  `org_id` bigint(20) DEFAULT NULL COMMENT '主管单位ID',
  `org_name` varchar(200) DEFAULT NULL COMMENT '主管单位名称',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_uri` varchar(500) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `field_changes` text COMMENT '字段变更详情(JSON格式)',
  `before_data` text COMMENT '操作前数据(JSON格式)',
  `after_data` text COMMENT '操作后数据(JSON格式)',
  `is_deleted` int(2) NOT NULL DEFAULT '0' COMMENT '是否已删除',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_operator_time` (`operator_id`, `operation_time`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作记录表';
```

## 使用方式

### 1. 注解方式（推荐）

在Controller方法上添加`@OperationRecord`注解：

```java
@PostMapping("/save")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    description = "新增互联网资产",
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName",
    recordFieldChanges = true,
    async = true
)
public R<RsInternetDto> saveOrUpdate(@RequestBody RsInternetVo vo) {
    // 业务逻辑
    RsInternetDto result = rsInternetLogicService.saveOrUpdate(vo);
    return R.data(result);
}
```

### 2. 手动记录方式

使用`OperationRecordUtil`工具类：

```java
@Autowired
private OperationRecordUtil operationRecordUtil;

public void someBusinessMethod() {
    // 业务逻辑
    
    // 手动记录操作
    operationRecordUtil.recordOperation(
        BusinessTypeEnum.INTERNET_ASSET,
        OperationTypeEnum.UPDATE,
        businessId,
        "手动更新操作",
        "更新了某些重要信息",
        fieldChanges
    );
}
```

## 注解参数说明

### @OperationRecord注解参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| businessType | BusinessTypeEnum | 是 | 业务类型 | BusinessTypeEnum.INTERNET_ASSET |
| operationType | OperationTypeEnum | 是 | 操作类型 | OperationTypeEnum.CREATE |
| description | String | 否 | 操作描述 | "新增互联网资产" |
| businessIdExpression | String | 是 | 业务ID的SpEL表达式 | "#result.data.id" |
| businessNameExpression | String | 否 | 业务名称的SpEL表达式 | "#result.data.systemName" |
| recordFieldChanges | boolean | 否 | 是否记录字段变更 | true |
| async | boolean | 否 | 是否异步执行 | true |

### SpEL表达式说明

系统支持以下SpEL表达式变量：

- `#result` - 方法返回值
- `#参数名` - 方法参数（如 #vo, #id 等）
- `#args[0]` - 第一个参数
- `#args[1]` - 第二个参数

常用表达式示例：
- `#result.data.id` - 获取返回结果中的ID
- `#vo.id` - 获取参数vo中的id字段
- `#id` - 直接获取名为id的参数

## 枚举定义

### BusinessTypeEnum - 业务类型

```java
public enum BusinessTypeEnum {
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产");
    
    private final String code;
    private final String desc;
}
```

### OperationTypeEnum - 操作类型

```java
public enum OperationTypeEnum {
    CREATE("CREATE", "新增"),
    UPDATE("UPDATE", "修改"),
    DELETE("DELETE", "删除"),
    QUERY("QUERY", "查询"),
    EXPORT("EXPORT", "导出"),
    IMPORT("IMPORT", "导入");
    
    private final String code;
    private final String desc;
}
```

## 查询接口

### 分页查询操作记录

```http
POST /zbusiness/operation-record/page
Content-Type: application/json

{
    "current": 1,
    "size": 10,
    "businessType": "INTERNET_ASSET",
    "operationType": "CREATE",
    "operatorDeptId": 123,
    "systemName": "测试系统",
    "startOperationTime": "2024-01-01 00:00:00",
    "endOperationTime": "2024-12-31 23:59:59"
}
```

### 查询操作记录详情

```http
GET /zbusiness/operation-record/detail/{id}
```

## 字段变更追踪

当`recordFieldChanges=true`时，系统会自动对比操作前后的数据变化，记录具体的字段变更信息：

```json
{
    "fieldChanges": [
        {
            "fieldName": "systemName",
            "fieldLabel": "系统名称",
            "oldValue": "旧系统名称",
            "newValue": "新系统名称"
        },
        {
            "fieldName": "status",
            "fieldLabel": "状态",
            "oldValue": "1",
            "newValue": "2"
        }
    ]
}
```

## 性能优化

### 异步处理

建议在注解中设置`async=true`，启用异步处理模式，避免影响主业务流程性能：

```java
@OperationRecord(
    // ... 其他参数
    async = true  // 启用异步处理
)
```

### 批量操作优化

对于批量删除等操作，可以设置`recordFieldChanges=false`减少性能开销：

```java
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.DELETE,
    description = "批量删除互联网资产",
    businessIdExpression = "#vo.idList[0]",
    businessNameExpression = "批量删除",
    recordFieldChanges = false,  // 批量操作不记录字段变更
    async = true
)
```

## 扩展指南

### 添加新的业务类型

1. 在`BusinessTypeEnum`中添加新的业务类型
2. 在`SysOperationRecordServiceImpl.getBusinessData()`方法中添加对应的数据获取逻辑
3. 在相应的Controller中添加`@OperationRecord`注解

### 自定义字段变更检测

可以通过实现自定义的字段变更检测逻辑，在`OperationRecordAspect.detectFieldChanges()`方法中进行扩展。

## 注意事项

1. **权限控制**：操作记录查询遵循部门权限范围，用户只能查看本部门及下级部门的操作记录
2. **数据安全**：敏感字段（如密码）不会被记录到操作日志中
3. **存储空间**：建议定期清理历史操作记录，避免数据表过大影响性能
4. **异常处理**：操作记录失败不会影响主业务流程的执行
5. **SpEL表达式**：确保SpEL表达式的正确性，避免运行时异常

## 常见问题

### Q: 为什么操作记录没有生成？
A: 检查以下几点：
- 确认方法上有`@OperationRecord`注解
- 确认SpEL表达式语法正确
- 检查是否有异常被捕获但未处理

### Q: 如何查看字段变更详情？
A: 在查询操作记录详情接口中，`fieldChanges`字段包含了详细的变更信息。

### Q: 如何提高操作记录的性能？
A: 建议使用异步模式（`async=true`），对于不需要字段变更追踪的操作设置`recordFieldChanges=false`。
