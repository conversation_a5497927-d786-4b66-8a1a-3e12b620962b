# 主管单位信息获取验证示例

## 问题背景

原来的操作记录系统中，主管单位ID是从当前操作人的部门信息中获取的：

```java
// 错误的做法
Dept loginDept = DeptScopeUtil.getLoginDept();
if (loginDept.getUnitId() != null) {
    recordVo.setOrgId(loginDept.getUnitId()); // 使用操作人的单位ID
}
```

这种做法是错误的，因为操作的资产可能属于不同的单位，应该从业务参数中获取。

## 修复后的逻辑

现在从业务参数中获取主管单位信息：

```java
// 正确的做法
private void setOrgInfoFromBusinessParam(SysOperationRecordVo recordVo, ProceedingJoinPoint joinPoint) {
    Object[] args = joinPoint.getArgs();
    if (args != null && args.length > 0) {
        Object firstArg = args[0];
        
        // 1. 优先从orgId字段获取
        java.lang.reflect.Field orgIdField = firstArg.getClass().getDeclaredField("orgId");
        orgIdField.setAccessible(true);
        Object orgIdValue = orgIdField.get(firstArg);
        
        if (orgIdValue instanceof Long) {
            Long orgId = (Long) orgIdValue;
            recordVo.setOrgId(orgId); // 使用业务参数中的单位ID
            
            // 通过orgId获取组织名称
            Dept orgDept = SysCache.getDept(orgId);
            if (orgDept != null) {
                recordVo.setOrgName(orgDept.getDeptName());
            }
        }
    }
}
```

## 验证场景

### 场景1：新增互联网资产

**请求参数**：
```json
POST /internet/save
{
    "systemName": "新系统",
    "orgId": 1001,
    "orgName": "某分公司",
    "internetAddress": "http://example.com"
}
```

**期望的操作记录**：
- 操作类型：CREATE
- 操作描述：新增互联网资产
- 主管单位ID：1001（从业务参数获取）
- 主管单位名称：某分公司（通过orgId查询获取）

### 场景2：修改互联网资产

**请求参数**：
```json
POST /internet/save
{
    "id": 123,
    "systemName": "修改后的系统",
    "orgId": 1002,
    "orgName": "另一个分公司",
    "internetAddress": "http://example.com"
}
```

**期望的操作记录**：
- 操作类型：UPDATE
- 操作描述：修改互联网资产
- 主管单位ID：1002（从业务参数获取）
- 主管单位名称：另一个分公司（通过orgId查询获取）

### 场景3：跨单位操作

假设当前操作人属于"集团总部"（unitId=1000），但操作的是"分公司A"（orgId=1001）的资产：

**请求参数**：
```json
POST /internet/save
{
    "id": 456,
    "systemName": "分公司A的系统",
    "orgId": 1001,
    "orgName": "分公司A"
}
```

**修复前的错误记录**：
- 主管单位ID：1000（错误：使用了操作人的单位ID）
- 主管单位名称：集团总部（错误）

**修复后的正确记录**：
- 主管单位ID：1001（正确：使用了业务参数中的单位ID）
- 主管单位名称：分公司A（正确）

## 容错机制验证

### 场景4：业务参数中没有orgId字段

如果业务参数中没有`orgId`字段，会尝试获取`orgName`字段：

```java
// 只有orgName字段的情况
{
    "systemName": "某系统",
    "orgName": "某单位"
}
```

**处理结果**：
- 主管单位ID：null
- 主管单位名称：某单位（从orgName字段获取）

### 场景5：业务参数中既没有orgId也没有orgName

如果业务参数中既没有`orgId`也没有`orgName`字段，会使用当前操作人的单位信息作为兜底：

```java
// 没有组织相关字段的情况
{
    "systemName": "某系统"
}
```

**处理结果**：
- 主管单位ID：1000（使用操作人的单位ID作为兜底）
- 主管单位名称：集团总部（使用操作人的单位名称作为兜底）

## 日志验证

在debug模式下，可以通过日志验证获取逻辑：

```
DEBUG - 从业务参数中获取主管单位信息成功，orgId: 1001, orgName: 分公司A
DEBUG - 业务参数中没有orgId字段: java.lang.NoSuchFieldException: orgId
DEBUG - 从业务参数中获取主管单位名称成功: 某单位
DEBUG - 使用当前操作人单位信息作为兜底，orgId: 1000, orgName: 集团总部
```

## 测试建议

### 1. 单元测试

创建单元测试验证反射获取字段的逻辑：

```java
@Test
public void testGetOrgInfoFromBusinessParam() {
    // 测试有orgId字段的情况
    RsInternetVo vo1 = new RsInternetVo();
    vo1.setOrgId(1001L);
    
    // 测试只有orgName字段的情况
    RsInternetVo vo2 = new RsInternetVo();
    vo2.setOrgName("某单位");
    
    // 测试没有组织字段的情况
    RsInternetVo vo3 = new RsInternetVo();
}
```

### 2. 集成测试

通过实际的HTTP请求验证操作记录是否正确：

```java
@Test
public void testOperationRecordWithCorrectOrgInfo() {
    // 发送新增请求
    // 验证数据库中的操作记录
    // 检查orgId和orgName是否正确
}
```

### 3. 手工测试

1. 登录系统
2. 操作不同单位的互联网资产
3. 查看操作记录表中的主管单位信息是否正确
4. 验证跨单位操作的记录是否准确

## 预期效果

修复后，操作记录中的主管单位信息将准确反映被操作资产的归属单位，而不是操作人的所属单位，这样可以：

1. **准确追踪**：正确记录每个资产的操作历史
2. **权限审计**：准确反映跨单位操作的情况
3. **数据分析**：为各单位的资产操作统计提供准确数据
4. **合规要求**：满足资产管理的合规性要求
