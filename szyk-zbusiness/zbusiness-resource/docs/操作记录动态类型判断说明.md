# 操作记录动态类型判断说明

## 问题背景

在SZYK项目中，`/internet/save`接口是一个保存和修改一体的接口，通过参数中的`id`字段来判断是新增操作还是修改操作：
- 如果`vo.getId() == null`，则为新增操作
- 如果`vo.getId() != null`，则为修改操作

原来的`@OperationRecord`注解中固定指定`operationType = OperationTypeEnum.CREATE`是不合适的，需要动态判断操作类型。

## 解决方案

### 1. 修改OperationRecordAspect切面

在`OperationRecordAspect`中添加了动态判断操作类型的逻辑：

```java
/**
 * 动态判断操作类型
 * 
 * @param annotation 注解信息
 * @param joinPoint 切点信息
 * @param result 方法返回结果
 * @return 操作类型代码
 */
private String determineOperationType(OperationRecord annotation, ProceedingJoinPoint joinPoint, Object result) {
    try {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        
        // 对于saveOrUpdate类型的方法，通过参数中的ID判断是新增还是修改
        if (args != null && args.length > 0) {
            Object firstArg = args[0];
            if (firstArg != null) {
                // 通过反射获取ID字段
                try {
                    java.lang.reflect.Field idField = firstArg.getClass().getDeclaredField("id");
                    idField.setAccessible(true);
                    Object idValue = idField.get(firstArg);
                    
                    if (idValue != null) {
                        return OperationTypeEnum.UPDATE.getCode();
                    } else {
                        return OperationTypeEnum.CREATE.getCode();
                    }
                } catch (Exception e) {
                    log.debug("无法获取参数ID字段，使用默认操作类型: {}", e.getMessage());
                }
            }
        }
        
        // 如果无法判断，返回注解中指定的操作类型
        return annotation.operationType().getCode();
    } catch (Exception e) {
        log.warn("判断操作类型失败，使用默认操作类型: {}", e.getMessage());
        return annotation.operationType().getCode();
    }
}
```

### 2. 动态生成操作描述

根据动态判断的操作类型，自动生成合适的操作描述：

```java
/**
 * 根据操作类型确定描述
 * 
 * @param annotation 注解信息
 * @param operationType 操作类型
 * @return 操作描述
 */
private String determineDescription(OperationRecord annotation, String operationType) {
    String description = annotation.description();
    
    // 如果注解中没有指定描述，根据操作类型生成默认描述
    if (StringUtil.isBlank(description) || "保存互联网资产".equals(description)) {
        if (OperationTypeEnum.CREATE.getCode().equals(operationType)) {
            return "新增" + annotation.businessType().getName();
        } else if (OperationTypeEnum.UPDATE.getCode().equals(operationType)) {
            return "修改" + annotation.businessType().getName();
        }
    }
    
    return description;
}
```

### 3. 修改Controller注解

将Controller中的注解修改为通用描述，让切面动态判断：

```java
@PostMapping("/save")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE, // 这里会被动态覆盖
    description = "保存互联网资产",
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName",
    recordFieldChanges = true,
    async = true
)
public R<RsInternetDto> saveOrUpdate(@RequestBody RsInternetVo v) {
    // 业务逻辑
}
```

## 工作原理

### 1. 判断流程

1. **获取方法参数**：从`ProceedingJoinPoint`中获取方法的第一个参数
2. **反射获取ID字段**：通过反射获取参数对象的`id`字段值
3. **判断操作类型**：
   - 如果`id == null`，判断为新增操作（CREATE）
   - 如果`id != null`，判断为修改操作（UPDATE）
4. **生成描述**：根据操作类型自动生成"新增互联网资产"或"修改互联网资产"

### 2. 容错机制

- 如果反射获取ID字段失败，使用注解中指定的默认操作类型
- 如果整个判断过程出现异常，记录警告日志并使用默认操作类型
- 确保操作记录功能不会因为类型判断失败而影响主业务流程

## 使用示例

### 新增操作

```javascript
// 前端请求
POST /internet/save
{
    "systemName": "新系统",
    "systemUrl": "http://example.com"
    // 注意：没有id字段
}
```

**操作记录结果**：
- 操作类型：CREATE
- 操作描述：新增互联网资产

### 修改操作

```javascript
// 前端请求
POST /internet/save
{
    "id": 123,
    "systemName": "修改后的系统名称",
    "systemUrl": "http://example.com"
}
```

**操作记录结果**：
- 操作类型：UPDATE
- 操作描述：修改互联网资产

## 扩展性

这种动态判断机制可以扩展到其他类似的保存/修改一体接口：

1. **设备管理**：`RsEquipmentController.saveOrUpdate()`
2. **软件管理**：`RsSoftwareController.saveOrUpdate()`
3. **系统管理**：`RsSystemController.saveOrUpdate()`

只需要在相应的Controller方法上添加`@OperationRecord`注解，切面会自动进行动态类型判断。

## 主管单位信息获取

### 问题说明

原来的实现中，主管单位ID是从当前操作人的部门信息中获取的，这是不正确的。主管单位信息应该从业务参数中获取，因为操作的资产可能属于不同的单位。

### 解决方案

添加了`setOrgInfoFromBusinessParam`方法，从业务参数中获取主管单位信息：

```java
/**
 * 从业务参数中设置主管单位信息
 */
private void setOrgInfoFromBusinessParam(SysOperationRecordVo recordVo, ProceedingJoinPoint joinPoint) {
    // 1. 尝试从业务参数的orgId字段获取
    // 2. 如果没有orgId，尝试获取orgName字段
    // 3. 如果都获取不到，使用当前操作人的单位信息作为兜底
}
```

### 获取逻辑

1. **优先级1**：从业务参数的`orgId`字段获取，然后通过`SysCache.getDept(orgId)`获取单位名称
2. **优先级2**：如果没有`orgId`字段，尝试从`orgName`字段直接获取单位名称
3. **兜底方案**：如果业务参数中都没有，使用当前操作人的单位信息

### 适用场景

- **互联网资产管理**：`RsInternetVo`中的`orgId`和`orgName`字段
- **设备管理**：类似的组织字段
- **系统管理**：类似的组织字段

## 注意事项

1. **字段命名**：要求参数对象必须有名为`id`的字段（用于操作类型判断）
2. **组织字段**：优先使用`orgId`字段，其次是`orgName`字段
3. **字段访问**：所有字段可以是private，切面会通过反射设置可访问性
4. **性能影响**：反射操作有一定性能开销，但在异步模式下对主业务影响很小
5. **日志记录**：判断过程中的异常会记录到日志中，便于排查问题
6. **容错机制**：如果从业务参数获取失败，会使用当前操作人的单位信息作为兜底

## 测试验证

建议进行以下测试来验证功能：

1. **新增测试**：不传id参数，验证操作类型为CREATE
2. **修改测试**：传入有效id参数，验证操作类型为UPDATE
3. **异常测试**：传入无效参数，验证容错机制是否正常工作
4. **性能测试**：验证动态判断不会显著影响接口性能
