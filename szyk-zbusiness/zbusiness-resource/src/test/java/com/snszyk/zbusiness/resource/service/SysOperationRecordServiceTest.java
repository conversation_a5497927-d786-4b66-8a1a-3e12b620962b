/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 操作记录服务测试类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SysOperationRecordServiceTest {

    @Autowired
    private ISysOperationRecordService operationRecordService;

    @Test
    public void testSaveOperationRecord() {
        // 准备测试数据
        SysOperationRecordVo vo = new SysOperationRecordVo();
        vo.setBusinessModule("RESOURCE");
        vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
        vo.setBusinessId(1L);
        vo.setBusinessName("测试系统");
        vo.setOperationType(OperationTypeEnum.CREATE.getCode());
        vo.setOperationDesc("新增互联网资产");
        vo.setOperationTime(LocalDateTime.now());
        vo.setOperatorId(1L);
        vo.setOperatorName("测试用户");
        vo.setOperatorDeptId(1L);
        vo.setOperatorDeptName("测试部门");
        vo.setOrgId(1L);
        vo.setOrgName("测试单位");

        // 执行保存
        SysOperationRecordDto saved = operationRecordService.save(vo);

        // 验证结果
        assertNotNull(saved);
        assertNotNull(saved.getId());
        assertEquals(vo.getBusinessType(), saved.getBusinessType());
        assertEquals(vo.getOperationType(), saved.getOperationType());
        assertEquals(vo.getBusinessName(), saved.getBusinessName());
    }

    @Test
    public void testPageQuery() {
        // 准备查询条件
        SysOperationRecordPageVo vo = new SysOperationRecordPageVo();
        vo.setCurrent(1L);
        vo.setSize(10L);
        vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
        vo.setOperationType(OperationTypeEnum.CREATE.getCode());

        // 执行查询
        IPage<SysOperationRecordDto> result = operationRecordService.pageList(vo);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCurrent() >= 0);
        assertTrue(result.getSize() > 0);
    }

    @Test
    public void testCompareFields() {
        // 准备测试数据
        RsInternetDto oldData = new RsInternetDto();
        oldData.setId(1L);
        oldData.setSystemName("旧系统名称");
        oldData.setInternetType(1);
        oldData.setResourceStatus(1);

        RsInternetDto newData = new RsInternetDto();
        newData.setId(1L);
        newData.setSystemName("新系统名称");
        newData.setInternetType(2);
        newData.setResourceStatus(1);

        // 执行字段比较
        List<FieldChangeDto> changes = operationRecordService.compareFields(
            BusinessTypeEnum.INTERNET_ASSET.getCode(), oldData, newData);

        // 验证结果
        assertNotNull(changes);
        assertTrue(changes.size() > 0);

        // 验证系统名称变更
        FieldChangeDto systemNameChange = changes.stream()
            .filter(change -> "systemName".equals(change.getFieldName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(systemNameChange);
        assertEquals("旧系统名称", systemNameChange.getOldValue());
        assertEquals("新系统名称", systemNameChange.getNewValue());
        assertTrue(systemNameChange.getHasChanged());

        // 验证资源类型变更
        FieldChangeDto typeChange = changes.stream()
            .filter(change -> "internetType".equals(change.getFieldName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(typeChange);
        assertEquals("1", typeChange.getOldValue());
        assertEquals("2", typeChange.getNewValue());
        assertTrue(typeChange.getHasChanged());
    }

    @Test
    public void testCompareFieldsForCreate() {
        // 准备测试数据（新增操作，只有新数据）
        RsInternetDto newData = new RsInternetDto();
        newData.setId(1L);
        newData.setSystemName("新系统");
        newData.setInternetType(1);

        // 执行字段比较
        List<FieldChangeDto> changes = operationRecordService.compareFields(
            BusinessTypeEnum.INTERNET_ASSET.getCode(), null, newData);

        // 验证结果
        assertNotNull(changes);
        assertTrue(changes.size() > 0);

        // 验证所有字段都是新增
        changes.forEach(change -> {
            assertNull(change.getOldValue());
            assertNotNull(change.getNewValue());
        });
    }

    @Test
    public void testCompareFieldsForDelete() {
        // 准备测试数据（删除操作，只有旧数据）
        RsInternetDto oldData = new RsInternetDto();
        oldData.setId(1L);
        oldData.setSystemName("要删除的系统");
        oldData.setInternetType(1);

        // 执行字段比较
        List<FieldChangeDto> changes = operationRecordService.compareFields(
            BusinessTypeEnum.INTERNET_ASSET.getCode(), oldData, null);

        // 验证结果
        assertNotNull(changes);
        assertTrue(changes.size() > 0);

        // 验证所有字段都是删除
        changes.forEach(change -> {
            assertNotNull(change.getOldValue());
            assertNull(change.getNewValue());
        });
    }

    @Test
    public void testGetDetailWithChanges() {
        // 先保存一条操作记录
        SysOperationRecordVo vo = new SysOperationRecordVo();
        vo.setBusinessModule("RESOURCE");
        vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
        vo.setBusinessId(1L);
        vo.setBusinessName("测试系统");
        vo.setOperationType(OperationTypeEnum.UPDATE.getCode());
        vo.setOperationDesc("更新互联网资产");
        vo.setOperationTime(LocalDateTime.now());
        vo.setOperatorId(1L);
        vo.setOperatorName("测试用户");

        SysOperationRecordDto saved = operationRecordService.save(vo);

        // 模拟保存字段变更
        FieldChangeDto fieldChange = new FieldChangeDto("systemName", "系统名称", "旧名称", "新名称");
        operationRecordService.saveFieldChanges(saved.getId(), 
            BusinessTypeEnum.INTERNET_ASSET.getCode(), List.of(fieldChange));

        // 获取详情
        SysOperationRecordDto detail = operationRecordService.getDetailWithChanges(saved.getId());

        // 验证结果
        assertNotNull(detail);
        assertEquals(saved.getId(), detail.getId());
        assertNotNull(detail.getFieldChanges());
        assertTrue(detail.getFieldChanges().size() > 0);
    }

    @Test
    public void testAsyncSave() {
        // 准备测试数据
        SysOperationRecordVo vo = new SysOperationRecordVo();
        vo.setBusinessModule("RESOURCE");
        vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
        vo.setBusinessId(1L);
        vo.setBusinessName("异步测试系统");
        vo.setOperationType(OperationTypeEnum.CREATE.getCode());
        vo.setOperationDesc("异步新增互联网资产");
        vo.setOperationTime(LocalDateTime.now());
        vo.setOperatorId(1L);
        vo.setOperatorName("测试用户");

        // 执行异步保存（这里只是调用，实际验证需要等待异步完成）
        assertDoesNotThrow(() -> {
            operationRecordService.saveAsync(vo);
        });
    }
}
