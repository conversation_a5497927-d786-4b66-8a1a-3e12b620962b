package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 字段语义处理器测试
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FieldSemanticProcessorTest {

    @Autowired
    private FieldSemanticProcessor fieldSemanticProcessor;

    @Test
    public void testGetFieldSemanticName() {
        // 测试互联网资产字段语义名称
        String semanticName = fieldSemanticProcessor.getFieldSemanticName(
            BusinessTypeEnum.INTERNET_ASSET, "systemName");
        assertEquals("系统名称", semanticName);

        semanticName = fieldSemanticProcessor.getFieldSemanticName(
            BusinessTypeEnum.INTERNET_ASSET, "resourceStatus");
        assertEquals("资源状态", semanticName);
    }

    @Test
    public void testConvertFieldValueToDisplay() {
        // 测试布尔值转换
        String displayValue = fieldSemanticProcessor.convertFieldValueToDisplay(
            BusinessTypeEnum.INTERNET_ASSET, "isPublicCloud", "true");
        assertEquals("是", displayValue);

        displayValue = fieldSemanticProcessor.convertFieldValueToDisplay(
            BusinessTypeEnum.INTERNET_ASSET, "isPublicCloud", "false");
        assertEquals("否", displayValue);

        // 测试等保级别转换
        displayValue = fieldSemanticProcessor.convertFieldValueToDisplay(
            BusinessTypeEnum.INTERNET_ASSET, "securityLevel", "3");
        assertEquals("三级", displayValue);
    }

    @Test
    public void testProcessChangeDetailsDisplay() {
        // 创建测试数据
        List<RsOperationRecordDetailDto> detailList = Arrays.asList(
            createDetailDto("systemName", "旧系统", "新系统"),
            createDetailDto("isPublicCloud", "false", "true"),
            createDetailDto("securityLevel", "2", "3")
        );

        // 处理显示转换
        fieldSemanticProcessor.processChangeDetailsDisplay(
            BusinessTypeEnum.INTERNET_ASSET, detailList);

        // 验证结果
        RsOperationRecordDetailDto systemNameDetail = detailList.get(0);
        assertEquals("系统名称", systemNameDetail.getFieldLabel());
        assertEquals("旧系统", systemNameDetail.getOldDisplayValue());
        assertEquals("新系统", systemNameDetail.getNewDisplayValue());

        RsOperationRecordDetailDto cloudDetail = detailList.get(1);
        assertEquals("是否公有云", cloudDetail.getFieldLabel());
        assertEquals("否", cloudDetail.getOldDisplayValue());
        assertEquals("是", cloudDetail.getNewDisplayValue());

        RsOperationRecordDetailDto securityDetail = detailList.get(2);
        assertEquals("等保级别", securityDetail.getFieldLabel());
        assertEquals("二级", securityDetail.getOldDisplayValue());
        assertEquals("三级", securityDetail.getNewDisplayValue());
    }

    @Test
    public void testIsSensitiveField() {
        // 测试敏感字段识别
        boolean isSensitive = fieldSemanticProcessor.isSensitiveField(
            BusinessTypeEnum.INTERNET_ASSET, "contactPhone");
        assertTrue(isSensitive);

        isSensitive = fieldSemanticProcessor.isSensitiveField(
            BusinessTypeEnum.INTERNET_ASSET, "systemName");
        assertFalse(isSensitive);
    }

    @Test
    public void testGetFieldGroup() {
        // 测试字段分组
        String group = fieldSemanticProcessor.getFieldGroup(
            BusinessTypeEnum.INTERNET_ASSET, "systemName");
        assertEquals("基本信息", group);

        group = fieldSemanticProcessor.getFieldGroup(
            BusinessTypeEnum.INTERNET_ASSET, "contactPerson");
        assertEquals("联系信息", group);

        group = fieldSemanticProcessor.getFieldGroup(
            BusinessTypeEnum.INTERNET_ASSET, "securityLevel");
        assertEquals("安全信息", group);
    }

    private RsOperationRecordDetailDto createDetailDto(String fieldName, String oldValue, String newValue) {
        RsOperationRecordDetailDto dto = new RsOperationRecordDetailDto();
        dto.setFieldName(fieldName);
        dto.setOldValue(oldValue);
        dto.setNewValue(newValue);
        return dto;
    }
}
