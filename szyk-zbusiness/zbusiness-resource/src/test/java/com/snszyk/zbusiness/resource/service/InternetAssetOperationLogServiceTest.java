/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.service.IInternetAssetOperationLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 互联网资产操作日志服务测试
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@SpringBootTest
public class InternetAssetOperationLogServiceTest {

    @Autowired
    private IInternetAssetOperationLogService operationLogService;

    @Test
    public void testCreateOperationLog() {
        // 创建测试数据
        RsInternetDto oldData = new RsInternetDto();
        oldData.setId(1L);
        oldData.setSystemName("测试系统");
        oldData.setOrgId(1L);
        oldData.setOrgName("测试组织");
        oldData.setInternetType("WEB");
        oldData.setApplicationType("BUSINESS");

        RsInternetDto newData = new RsInternetDto();
        newData.setId(1L);
        newData.setSystemName("测试系统-修改");
        newData.setOrgId(1L);
        newData.setOrgName("测试组织");
        newData.setInternetType("API");
        newData.setApplicationType("BUSINESS");

        // 测试创建操作日志
        boolean result = operationLogService.createOperationLog(
            1L,
            "测试系统",
            "UPDATE",
            "测试更新互联网资产",
            oldData,
            newData,
            1L,
            "测试组织"
        );

        System.out.println("创建操作日志结果: " + result);
    }

    @Test
    public void testCreateDeleteLog() {
        // 创建删除日志测试
        RsInternetDto data = new RsInternetDto();
        data.setId(2L);
        data.setSystemName("待删除系统");
        data.setOrgId(1L);
        data.setOrgName("测试组织");

        boolean result = operationLogService.createOperationLog(
            2L,
            "待删除系统",
            "DELETE",
            "测试删除互联网资产",
            data,
            null,
            1L,
            "测试组织"
        );

        System.out.println("创建删除日志结果: " + result);
    }
}
