package com.snszyk.zbusiness.resource.service;

import com.alibaba.fastjson.JSON;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.entity.RsBusinessFieldDefinition;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordTemplate;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.plugin.BusinessModulePluginManager;
import com.snszyk.zbusiness.resource.service.logic.RsOperationRecordLogicService;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作记录系统集成测试
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class OperationRecordSystemTest {

    @Autowired
    private RsOperationRecordLogicService operationRecordLogicService;

    @Autowired
    private DynamicFieldConfigService dynamicFieldConfigService;

    @Autowired
    private ExtensionFieldStorageService extensionFieldStorageService;

    @Autowired
    private OperationRecordTemplateService templateService;

    @Autowired
    private BusinessModulePluginManager pluginManager;

    @Test
    public void testCompleteOperationRecordFlow() {
        log.info("开始测试完整的操作记录流程");

        // 1. 测试动态字段配置
        testDynamicFieldConfig();

        // 2. 测试模板功能
        testTemplateManagement();

        // 3. 测试插件系统
        testPluginSystem();

        // 4. 测试扩展字段存储
        testExtensionFieldStorage();

        // 5. 测试完整的操作记录创建流程
        testCompleteRecordCreation();

        log.info("完整的操作记录流程测试完成");
    }

    /**
     * 测试动态字段配置
     */
    private void testDynamicFieldConfig() {
        log.info("测试动态字段配置");

        // 获取互联网资产的字段配置
        List<RsBusinessFieldDefinition> fieldDefinitions = dynamicFieldConfigService
            .getFieldDefinitions(BusinessTypeEnum.INTERNET_ASSET);

        assert !fieldDefinitions.isEmpty() : "字段定义不应为空";
        log.info("获取到 {} 个字段定义", fieldDefinitions.size());

        // 测试字段分组
        Map<String, List<RsBusinessFieldDefinition>> groupedFields = dynamicFieldConfigService
            .getGroupedFieldDefinitions(BusinessTypeEnum.INTERNET_ASSET);

        assert !groupedFields.isEmpty() : "分组字段不应为空";
        log.info("字段分组: {}", groupedFields.keySet());

        // 测试字段映射
        Map<String, String> fieldMapping = dynamicFieldConfigService
            .getFieldMapping(BusinessTypeEnum.INTERNET_ASSET);

        assert !fieldMapping.isEmpty() : "字段映射不应为空";
        log.info("字段映射数量: {}", fieldMapping.size());
    }

    /**
     * 测试模板管理
     */
    private void testTemplateManagement() {
        log.info("测试模板管理");

        // 获取默认模板
        RsOperationRecordTemplate defaultTemplate = templateService
            .getDefaultTemplate(BusinessTypeEnum.INTERNET_ASSET);

        if (defaultTemplate != null) {
            log.info("默认模板: {}", defaultTemplate.getTemplateName());

            // 测试模板配置解析
            Map<String, Object> templateConfig = templateService.parseTemplateConfig(defaultTemplate);
            assert !templateConfig.isEmpty() : "模板配置不应为空";
            log.info("模板配置: {}", JSON.toJSONString(templateConfig, true));

            // 测试模板应用
            Map<String, Object> testData = createTestOperationData();
            Map<String, Object> appliedData = templateService.applyTemplate(defaultTemplate, testData);
            log.info("应用模板后的数据: {}", JSON.toJSONString(appliedData, true));
        }

        // 获取所有模板
        List<RsOperationRecordTemplate> templates = templateService
            .getTemplates(BusinessTypeEnum.INTERNET_ASSET);
        log.info("获取到 {} 个模板", templates.size());
    }

    /**
     * 测试插件系统
     */
    private void testPluginSystem() {
        log.info("测试插件系统");

        // 测试插件注册
        boolean hasPlugin = pluginManager.hasPlugin(BusinessTypeEnum.INTERNET_ASSET);
        log.info("互联网资产模块是否有插件: {}", hasPlugin);

        if (hasPlugin) {
            // 测试插件预处理
            Map<String, Object> oldData = createTestOperationData();
            Map<String, Object> newData = createTestOperationData();
            newData.put("systemName", "更新后的系统名称");

            Map<String, Object> preprocessedData = pluginManager.executePreprocess(
                BusinessTypeEnum.INTERNET_ASSET, 1L, OperationTypeEnum.UPDATE.getCode(), oldData, newData
            );
            log.info("插件预处理结果: {}", JSON.toJSONString(preprocessedData, true));

            // 测试业务数据验证
            Map<String, String> validationErrors = pluginManager.executeBusinessDataValidation(
                BusinessTypeEnum.INTERNET_ASSET, 1L, OperationTypeEnum.UPDATE.getCode(), preprocessedData
            );
            log.info("业务数据验证结果: {}", validationErrors);

            // 测试自定义字段值转换
            String convertedValue = pluginManager.executeCustomFieldValueConversion(
                BusinessTypeEnum.INTERNET_ASSET, "systemName", "测试系统"
            );
            log.info("自定义字段值转换结果: {}", convertedValue);
        }
    }

    /**
     * 测试扩展字段存储
     */
    private void testExtensionFieldStorage() {
        log.info("测试扩展字段存储");

        // 创建测试扩展数据
        Map<String, Object> extensionData = new HashMap<>();
        extensionData.put("customField1", "自定义值1");
        extensionData.put("customField2", 123);
        extensionData.put("customField3", true);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("operationType", OperationTypeEnum.CREATE.getCode());
        metadata.put("businessType", BusinessTypeEnum.INTERNET_ASSET.getCode());
        metadata.put("createTime", System.currentTimeMillis());

        // 保存扩展数据
        boolean saved = extensionFieldStorageService.saveExtensionData(
            999L, BusinessTypeEnum.INTERNET_ASSET, extensionData, metadata
        );
        assert saved : "扩展数据保存应该成功";
        log.info("扩展数据保存成功");

        // 获取扩展数据
        Map<String, Object> retrievedData = extensionFieldStorageService
            .getExtensionData(999L, BusinessTypeEnum.INTERNET_ASSET);
        assert !retrievedData.isEmpty() : "获取的扩展数据不应为空";
        log.info("获取的扩展数据: {}", JSON.toJSONString(retrievedData, true));

        // 格式化显示值
        Map<String, String> displayValues = extensionFieldStorageService
            .formatDisplayValues(BusinessTypeEnum.INTERNET_ASSET, retrievedData);
        log.info("格式化显示值: {}", JSON.toJSONString(displayValues, true));

        // 查询扩展数据
        Map<String, Object> queryConditions = new HashMap<>();
        queryConditions.put("customField1", "自定义值1");

        List<Long> recordIds = extensionFieldStorageService.queryRecordIds(
            BusinessTypeEnum.INTERNET_ASSET, queryConditions
        );
        assert !recordIds.isEmpty() : "查询结果不应为空";
        log.info("查询到的记录ID: {}", recordIds);
    }

    /**
     * 测试完整的操作记录创建流程
     */
    private void testCompleteRecordCreation() {
        log.info("测试完整的操作记录创建流程");

        // 创建操作记录VO
        RsOperationRecordVo vo = new RsOperationRecordVo();
        vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
        vo.setBusinessId(1L);
        vo.setBusinessName("测试互联网资产");
        vo.setOperationType(OperationTypeEnum.UPDATE.getCode());
        vo.setOperationDescription("测试操作记录创建");

        // 设置变更数据
        Map<String, Object> oldData = createTestOperationData();
        Map<String, Object> newData = createTestOperationData();
        newData.put("systemName", "更新后的系统名称");
        newData.put("_ext_customField", "扩展字段值"); // 扩展字段

        vo.setOldData(JSON.toJSONString(oldData));
        vo.setNewData(JSON.toJSONString(newData));
        vo.setChangeFields("systemName,internetAddress");

        // 创建操作记录
        boolean created = operationRecordLogicService.createOperationRecord(vo);
        assert created : "操作记录创建应该成功";
        log.info("操作记录创建成功");

        // 如果有记录ID，测试获取增强详情
        if (vo.getId() != null) {
            RsOperationRecordDto enhancedDetail = operationRecordLogicService.getEnhancedDetail(vo.getId());
            assert enhancedDetail != null : "增强详情不应为空";
            log.info("增强详情: {}", JSON.toJSONString(enhancedDetail, true));

            // 验证扩展数据
            if (enhancedDetail.getExtensionData() != null) {
                log.info("扩展数据: {}", JSON.toJSONString(enhancedDetail.getExtensionData(), true));
            }

            if (enhancedDetail.getExtensionDisplayValues() != null) {
                log.info("扩展显示值: {}", JSON.toJSONString(enhancedDetail.getExtensionDisplayValues(), true));
            }
        }
    }

    /**
     * 创建测试操作数据
     */
    private Map<String, Object> createTestOperationData() {
        Map<String, Object> data = new HashMap<>();
        data.put("systemName", "测试系统");
        data.put("internetAddress", "http://test.example.com");
        data.put("systemType", "WEB");
        data.put("importance", "HIGH");
        data.put("status", "ACTIVE");
        return data;
    }
}
