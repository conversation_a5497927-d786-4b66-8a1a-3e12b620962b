# SZYK项目操作记录系统使用说明

## 1. 系统概述

操作记录系统是为SZYK项目设计的通用操作日志追踪系统，主要用于记录用户对业务数据的增删改操作，支持字段级别的变更追踪。

### 1.1 主要功能

- **自动记录操作日志**：通过AOP注解自动记录用户操作
- **字段级变更追踪**：详细记录字段变更前后的值
- **异步处理**：支持异步记录，不影响业务性能
- **灵活配置**：支持多种配置选项，适应不同业务场景
- **扩展性强**：支持跨模块使用，易于扩展新的业务类型

### 1.2 技术架构

- **数据存储**：主表 + 扩展表设计，支持灵活的字段扩展
- **AOP切面**：基于Spring AOP实现自动记录
- **异步处理**：使用Spring @Async支持异步记录
- **SpEL表达式**：支持灵活的参数提取

## 2. 快速开始

### 2.1 基本使用

在Controller方法上添加`@OperationRecord`注解：

```java
@PostMapping("/save")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    description = "新增互联网资产",
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName"
)
public R<RsInternetDto> save(@RequestBody RsInternetVo vo) {
    RsInternetDto saved = rsInternetLogicService.saveOrUpdate(vo);
    return R.data(saved);
}
```

### 2.2 手动记录

对于无法使用注解的场景，可以使用工具类：

```java
@Autowired
private OperationRecordUtil operationRecordUtil;

public void someMethod() {
    // 记录新增操作
    operationRecordUtil.recordCreate(
        BusinessTypeEnum.INTERNET_ASSET, 
        assetId, 
        "系统名称", 
        newData
    );
    
    // 记录更新操作
    operationRecordUtil.recordUpdate(
        BusinessTypeEnum.INTERNET_ASSET, 
        assetId, 
        "系统名称", 
        oldData, 
        newData
    );
}
```

## 3. 注解详细说明

### 3.1 @OperationRecord注解参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| businessType | BusinessTypeEnum | 是 | 业务类型 |
| operationType | OperationTypeEnum | 是 | 操作类型 |
| description | String | 否 | 操作描述 |
| businessIdExpression | String | 否 | 业务ID的SpEL表达式 |
| businessNameExpression | String | 否 | 业务名称的SpEL表达式 |
| recordFieldChanges | boolean | 否 | 是否记录字段变更，默认true |
| async | boolean | 否 | 是否异步执行，默认true |
| logParams | boolean | 否 | 是否记录请求参数，默认true |
| logResult | boolean | 否 | 是否记录返回结果，默认false |
| logException | boolean | 否 | 是否记录异常信息，默认true |

### 3.2 SpEL表达式示例

```java
// 从方法参数获取
businessIdExpression = "#vo.id"
businessNameExpression = "#vo.systemName"

// 从返回结果获取
businessIdExpression = "#result.data.id"
businessNameExpression = "#result.data.systemName"

// 复杂表达式
businessNameExpression = "'批量删除' + #ids.size() + '条记录'"
```

## 4. 业务类型和操作类型

### 4.1 业务类型枚举

```java
public enum BusinessTypeEnum {
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产台账"),
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备台账"),
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统台账"),
    SOFTWARE_ASSET("SOFTWARE_ASSET", "软件台账"),
    SERVER_ROOM("SERVER_ROOM", "机房台账");
}
```

### 4.2 操作类型枚举

```java
public enum OperationTypeEnum {
    CREATE("CREATE", "新增"),
    UPDATE("UPDATE", "更新"),
    DELETE("DELETE", "删除"),
    IMPORT("IMPORT", "导入"),
    EXPORT("EXPORT", "导出"),
    AUDIT("AUDIT", "审核"),
    ENABLE("ENABLE", "启用"),
    DISABLE("DISABLE", "停用");
}
```

## 5. 使用场景示例

### 5.1 新增操作

```java
@PostMapping("/save")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.CREATE,
    businessIdExpression = "#result.data.id",
    businessNameExpression = "#result.data.systemName"
)
public R<RsInternetDto> save(@RequestBody RsInternetVo vo) {
    return R.data(service.save(vo));
}
```

### 5.2 更新操作

```java
@PostMapping("/update")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.UPDATE,
    businessIdExpression = "#vo.id",
    businessNameExpression = "#result.data.systemName"
)
public R<RsInternetDto> update(@RequestBody RsInternetVo vo) {
    return R.data(service.update(vo));
}
```

### 5.3 删除操作

```java
@DeleteMapping("/delete/{id}")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.DELETE,
    businessIdExpression = "#id"
)
public R<Boolean> delete(@PathVariable Long id) {
    return R.data(service.deleteById(id));
}
```

### 5.4 批量操作

```java
@PostMapping("/batch-delete")
@OperationRecord(
    businessType = BusinessTypeEnum.INTERNET_ASSET,
    operationType = OperationTypeEnum.DELETE,
    businessNameExpression = "'批量删除' + #ids.size() + '条记录'",
    recordFieldChanges = false
)
public R<Boolean> batchDelete(@RequestBody List<Long> ids) {
    return R.data(service.deleteByIds(ids));
}
```

## 6. 查询操作记录

### 6.1 分页查询

```java
@PostMapping("/sys/operation-record/page")
public R<IPage<SysOperationRecordDto>> page(@RequestBody SysOperationRecordPageVo vo) {
    IPage<SysOperationRecordDto> pages = operationRecordService.pageList(vo);
    return R.data(pages);
}
```

### 6.2 查询详情（包含字段变更）

```java
@GetMapping("/sys/operation-record/detail/{id}")
public R<SysOperationRecordDto> detail(@PathVariable Long id) {
    SysOperationRecordDto detail = operationRecordService.getDetailWithChanges(id);
    return R.data(detail);
}
```

## 7. 扩展新的业务类型

### 7.1 添加业务类型枚举

在`BusinessTypeEnum`中添加新的业务类型：

```java
NEW_BUSINESS("NEW_BUSINESS", "新业务类型")
```

### 7.2 扩展字段标签映射

在`SysOperationRecordServiceImpl`中添加字段标签映射：

```java
private Map<String, String> getNewBusinessFieldLabels() {
    Map<String, String> labels = new HashMap<>();
    labels.put("fieldName1", "字段标签1");
    labels.put("fieldName2", "字段标签2");
    return labels;
}
```

### 7.3 扩展业务数据获取

在`getBusinessData`方法中添加新业务类型的数据获取逻辑：

```java
case NEW_BUSINESS:
    return newBusinessService.getById(businessId);
```

## 8. 注意事项

### 8.1 性能考虑

- 默认使用异步记录，避免影响业务性能
- 大批量操作建议关闭字段变更记录
- 查询操作通常不需要记录日志

### 8.2 数据安全

- 敏感字段可以通过配置排除记录
- 支持数据脱敏处理
- 操作记录数据建议定期归档

### 8.3 错误处理

- 操作记录失败不会影响业务操作
- 异常信息会记录到日志中
- 支持重试机制

## 9. 常见问题

### 9.1 SpEL表达式解析失败

**问题**：SpEL表达式无法正确解析参数

**解决**：
- 检查表达式语法是否正确
- 确认参数名称是否匹配
- 使用调试模式查看可用变量

### 9.2 字段变更记录不准确

**问题**：字段变更记录的前后值不正确

**解决**：
- 确认业务数据获取逻辑正确
- 检查字段标签映射是否完整
- 验证对象比较逻辑

### 9.3 异步记录失败

**问题**：异步记录操作日志失败

**解决**：
- 检查异步配置是否正确
- 确认数据库连接是否正常
- 查看异步线程池配置

## 10. 最佳实践

1. **合理使用异步**：对于高频操作，建议使用异步记录
2. **精确的SpEL表达式**：使用准确的表达式提取业务信息
3. **适当的日志级别**：根据业务重要性选择是否记录
4. **定期清理**：建立操作记录的清理策略
5. **监控告警**：对操作记录系统建立监控机制
