-- 操作记录表
CREATE TABLE `sys_operation_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_module` varchar(50) NOT NULL COMMENT '业务模块',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `business_id` bigint(20) DEFAULT NULL COMMENT '业务数据ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务数据名称',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint(20) DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(200) DEFAULT NULL COMMENT '操作人部门名称',
  `org_id` bigint(20) DEFAULT NULL COMMENT '主管单位ID',
  `org_name` varchar(200) DEFAULT NULL COMMENT '主管单位名称',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `request_uri` varchar(500) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(20) DEFAULT NULL COMMENT '请求方法',
  `old_data` longtext COMMENT '操作前数据',
  `new_data` longtext COMMENT '操作后数据',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录表';

-- 操作记录扩展字段表
CREATE TABLE `sys_operation_record_extension` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `record_id` bigint(20) NOT NULL COMMENT '操作记录ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(200) DEFAULT NULL COMMENT '字段标签',
  `old_value` text COMMENT '变更前值',
  `new_value` text COMMENT '变更后值',
  `old_display_value` text COMMENT '变更前显示值',
  `new_display_value` text COMMENT '变更后显示值',
  `field_type` varchar(50) DEFAULT NULL COMMENT '字段类型',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_field_name` (`field_name`),
  CONSTRAINT `fk_extension_record_id` FOREIGN KEY (`record_id`) REFERENCES `sys_operation_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录扩展字段表';

-- 创建索引优化查询性能
CREATE INDEX `idx_operation_record_composite` ON `sys_operation_record` (`business_type`, `operation_time`, `org_id`);
CREATE INDEX `idx_extension_composite` ON `sys_operation_record_extension` (`record_id`, `business_type`, `field_name`);
