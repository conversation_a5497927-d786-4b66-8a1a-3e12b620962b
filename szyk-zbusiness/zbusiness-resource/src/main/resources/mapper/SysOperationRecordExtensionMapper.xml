<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.SysOperationRecordExtensionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.snszyk.zbusiness.resource.entity.SysOperationRecordExtension">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="business_type" property="businessType" />
        <result column="field_name" property="fieldName" />
        <result column="field_label" property="fieldLabel" />
        <result column="old_value" property="oldValue" />
        <result column="new_value" property="newValue" />
        <result column="old_display_value" property="oldDisplayValue" />
        <result column="new_display_value" property="newDisplayValue" />
        <result column="field_type" property="fieldType" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- DTO结果映射 -->
    <resultMap id="DtoResultMap" type="com.snszyk.zbusiness.resource.dto.SysOperationRecordExtensionDto">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="business_type" property="businessType" />
        <result column="field_name" property="fieldName" />
        <result column="field_label" property="fieldLabel" />
        <result column="old_value" property="oldValue" />
        <result column="new_value" property="newValue" />
        <result column="old_display_value" property="oldDisplayValue" />
        <result column="new_display_value" property="newDisplayValue" />
        <result column="field_type" property="fieldType" />
        <result column="create_user" property="createUser" />
        <result column="create_dept" property="createDept" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, business_type, field_name, field_label, old_value, new_value, 
        old_display_value, new_display_value, field_type, create_user, create_dept, 
        create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 根据记录ID查询扩展字段 -->
    <select id="selectByRecordId" resultMap="DtoResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM sys_operation_record_extension
        WHERE record_id = #{recordId} 
          AND is_deleted = 0
        ORDER BY id ASC
    </select>

    <!-- 根据记录ID删除扩展字段 -->
    <delete id="deleteByRecordId">
        UPDATE sys_operation_record_extension 
        SET is_deleted = 1, update_time = NOW()
        WHERE record_id = #{recordId} AND is_deleted = 0
    </delete>

</mapper>
