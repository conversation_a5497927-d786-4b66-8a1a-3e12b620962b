package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录详情实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_operation_record_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录详情实体", description = "操作记录详情实体")
public class RsOperationRecordDetail extends BaseCrudEntity {

    /**
     * 操作记录ID
     */
    @ApiModelProperty(value = "操作记录ID")
    private Long recordId;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段中文名称
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldLabel;

    /**
     * 变更前值
     */
    @ApiModelProperty(value = "变更前值")
    private String oldValue;

    /**
     * 变更后值
     */
    @ApiModelProperty(value = "变更后值")
    private String newValue;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;
}
