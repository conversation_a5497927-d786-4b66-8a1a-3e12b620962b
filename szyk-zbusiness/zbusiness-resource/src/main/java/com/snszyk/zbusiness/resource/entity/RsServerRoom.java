/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 数据中心机房台账
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rs_serverroom")
@EqualsAndHashCode(callSuper = false)
public class RsServerRoom extends BaseCrudEntity {


	/**
	 * 数据中心机房名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String roomName;
	/**
	 * 机房编号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String roomNo;
	/**
	 * 机房类型,数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String roomType;
	/**
	 * 机房面积
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal roomArea;
	/**
	 * 机房等级，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String roomLevel;
	/**
	 * 机房定位，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String roomUse;
	/**
	 * 机房承重
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal roomBear;
	/**
	 * 所在楼层
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String floorNo;
	/**
	 * 楼层高度
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal floorHeight;
	/**
	 * PUE水平
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal pueLevel;
	/**
	 * 负责人姓名
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String headPersonName;
	/**
	 * 负责人联系方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String headPersonTel;

	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;

	/**
	 * 所属组织id
	 */

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long orgId;
	/**
	 * 所属组织全路径id
	 */

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String fullOrgId;
	/**
	 * 所属组织名称
	 */

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String orgName;
	/**
	 * 所属组织全路径名称
	 */

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String fullOrgName;

	/**
	 * 是否配备动环监控系统,0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer deSystem;
	/**
	 * 动环系统品牌
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deBrand;

	/**
	 * 动环系统型号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deModel;

	/**
	 * 提交状态，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String submitStatus;
	/**
	 * 资源状态，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String resourceStatus;

	/**
	 * 已用空间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String usedSpace;
	/**
	 * 机房总空间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String totalSpace;

}
