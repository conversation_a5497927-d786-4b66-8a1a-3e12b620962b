/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.resource.dto.RsSystemServerDto;
import com.snszyk.zbusiness.resource.service.IRsSystemServerService;
import com.snszyk.zbusiness.resource.vo.RsSystemServerVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 信息系统-服务器信息 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@AllArgsConstructor
@Service
public class RsSystemServerLogicService extends BaseCrudLogicService<RsSystemServerDto, RsSystemServerVo> {

    private final IRsSystemServerService rsSystemServerService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.rsSystemServerService;
    }
}
