package com.snszyk.zbusiness.resource.service.logic;

import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.UserInfo;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.service.IUserService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.knowledge.service.IBaseLogicService;
import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import com.snszyk.zbusiness.resource.dto.*;
import com.snszyk.zbusiness.resource.enums.ResourceExceptionEnum;
import com.snszyk.zbusiness.resource.enums.RsSoftwareFileEnum;
import com.snszyk.zbusiness.resource.enums.SoftwareStatusEnum;
import com.snszyk.zbusiness.resource.service.IRsSoftwareFileService;
import com.snszyk.zbusiness.resource.service.IRsSoftwareService;
import com.snszyk.zbusiness.resource.service.IRsSoftwareUseSoftwareService;
import com.snszyk.zbusiness.resource.service.IRsSupplierService;
import com.snszyk.zbusiness.resource.util.PaddingUtil;
import com.snszyk.zbusiness.resource.vo.*;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.snszyk.zbusiness.dict.enums.ExceptionEnum.SOFTWARE_SAVE_CHECK;
import static com.snszyk.zbusiness.resource.service.logic.RsEquipmentService.isValidDateFormat;

/**
 * RsSoftwareService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
@Slf4j
public class RsSoftwareService extends BaseCrudLogicService<RsSoftwareDto, RsSoftwareVo> {

	private final IRsSoftwareService rsSoftwareService;

	private final IRsSoftwareFileService rsSoftwareFileService;

	private final IRsSoftwareUseSoftwareService rsSoftwareUseSoftwareService;

	private final IDeptService deptService;

	private final IDictBizService dictBizService;

	private final IAttachService attachService;

	private final IUserService userService;
	private final IRsSupplierService supplierService;
	private final IBaseLogicService baseLogicService;

	@Resource
	private PaddingUtil paddingUtil;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rsSoftwareService;
	}

	public IPage<RsSoftwarePageDto> page(RsSoftwarePageVo vo) {
		Long orgId = vo.getOrgId();
		if (orgId == null) {
			vo.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
		}
		IPage<RsSoftwarePageDto> page = rsSoftwareService.pageList(vo);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}
		// 软件分类
		List<DictBiz> softwareClassifyList = dictBizService.getList(DictBizEnum.SOFTWARE_CLASSIFY.getCode());
		Map<String, DictBiz> softwareClassifyMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareClassifyList)) {
			softwareClassifyMap.putAll(softwareClassifyList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件类型
		List<DictBiz> softwareTypeList = dictBizService.getList(DictBizEnum.SOFTWARE_TYPE.getCode());
		Map<String, DictBiz> softwareTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareTypeList)) {
			softwareTypeMap.putAll(softwareTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 许可类型
		List<DictBiz> licenseTypeList = dictBizService.getList(DictBizEnum.LICENSE_TYPE.getCode());
		Map<String, DictBiz> licenseTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(licenseTypeList)) {
			licenseTypeMap.putAll(licenseTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件状态
		List<DictBiz> softwareStatusList = dictBizService.getList(DictBizEnum.SOFTWARE_STATUS.getCode());
		Map<String, DictBiz> softwareStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareStatusList)) {
			softwareStatusMap.putAll(softwareStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		for (RsSoftwarePageDto dto : page.getRecords()) {

			dto.setFullOrgName(deptService.handleFullName(dto.getOrgId()));
			// 软件分类
			if (!ObjectUtils.isEmpty(softwareClassifyMap) && !ObjectUtils.isEmpty(dto.getSoftwareClassify()) && softwareClassifyMap.containsKey(dto.getSoftwareClassify())) {
				dto.setSoftwareClassifyName(softwareClassifyMap.get(dto.getSoftwareClassify()).getDictValue());
			}
			// 软件类型
			if (!ObjectUtils.isEmpty(softwareTypeMap) && !ObjectUtils.isEmpty(dto.getSoftwareType()) && softwareTypeMap.containsKey(dto.getSoftwareType())) {
				dto.setSoftwareTypeName(softwareTypeMap.get(dto.getSoftwareType()).getDictValue());
			}
			// 许可类型
			if (!ObjectUtils.isEmpty(licenseTypeMap) && !ObjectUtils.isEmpty(dto.getLicenseType()) && licenseTypeMap.containsKey(dto.getLicenseType())) {
				dto.setLicenseTypeName(licenseTypeMap.get(dto.getLicenseType()).getDictValue());
			}
			// 软件状态
			if (!ObjectUtils.isEmpty(softwareStatusMap) && !ObjectUtils.isEmpty(dto.getSoftwareStatus()) && softwareStatusMap.containsKey(dto.getSoftwareStatus())) {
				dto.setSoftwareStatusName(softwareStatusMap.get(dto.getSoftwareStatus()).getDictValue());
			}
			// 合同
			List<RsSoftwareFileDto> contractList = rsSoftwareFileService.listBySoftware(dto.getId(), dto.getId(), RsSoftwareFileEnum.CONTRACT.getCode());
			if (!CollectionUtils.isEmpty(contractList)) {
				// 附件ID
				List<Long> attachIds = contractList.stream().map(RsSoftwareFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (!CollectionUtils.isEmpty(attachList)) {
					dto.setContractList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
				}
			}
			// 授权证书
			List<RsSoftwareFileDto> certificateList = rsSoftwareFileService.listBySoftware(dto.getId(), dto.getId(), RsSoftwareFileEnum.CERTIFICATE.getCode());
			if (!CollectionUtils.isEmpty(certificateList)) {
				// 附件ID
				List<Long> attachIds = certificateList.stream().map(RsSoftwareFileDto::getAttachId).collect(Collectors.toList());

				List<Attach> attachList = attachService.listByFileIds(attachIds);
				if (!CollectionUtils.isEmpty(attachList)) {
					dto.setCertificateList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
				}
			}
		}

		return page;
	}

	public RsSoftwareDto detail(Long id) {

		RsSoftwareDto result = rsSoftwareService.fetchById(id);
		if (result == null) {
			return null;
		}

		// 软件分类
		List<DictBiz> softwareClassifyList = dictBizService.getList(DictBizEnum.SOFTWARE_CLASSIFY.getCode());
		Map<String, DictBiz> softwareClassifyMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareClassifyList)) {
			softwareClassifyMap.putAll(softwareClassifyList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件类型
		List<DictBiz> softwareTypeList = dictBizService.getList(DictBizEnum.SOFTWARE_TYPE.getCode());
		Map<String, DictBiz> softwareTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareTypeList)) {
			softwareTypeMap.putAll(softwareTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 许可类型
		List<DictBiz> licenseTypeList = dictBizService.getList(DictBizEnum.LICENSE_TYPE.getCode());
		Map<String, DictBiz> licenseTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(licenseTypeList)) {
			licenseTypeMap.putAll(licenseTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件状态
		List<DictBiz> softwareStatusList = dictBizService.getList(DictBizEnum.SOFTWARE_STATUS.getCode());
		Map<String, DictBiz> softwareStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareStatusList)) {
			softwareStatusMap.putAll(softwareStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件分类
		if (!ObjectUtils.isEmpty(softwareClassifyMap) && !ObjectUtils.isEmpty(result.getSoftwareClassify()) && softwareClassifyMap.containsKey(result.getSoftwareClassify())) {
			result.setSoftwareClassifyName(softwareClassifyMap.get(result.getSoftwareClassify()).getDictValue());
		}
		// 软件类型
		if (!ObjectUtils.isEmpty(softwareTypeMap) && !ObjectUtils.isEmpty(result.getSoftwareType()) && softwareTypeMap.containsKey(result.getSoftwareType())) {
			result.setSoftwareTypeName(softwareTypeMap.get(result.getSoftwareType()).getDictValue());
		}
		// 许可类型
		if (!ObjectUtils.isEmpty(licenseTypeMap) && !ObjectUtils.isEmpty(result.getLicenseType()) && licenseTypeMap.containsKey(result.getLicenseType())) {
			result.setLicenseTypeName(licenseTypeMap.get(result.getLicenseType()).getDictValue());
		}
		// 软件状态
		if (!ObjectUtils.isEmpty(softwareStatusMap) && !ObjectUtils.isEmpty(result.getSoftwareStatus()) && softwareStatusMap.containsKey(result.getSoftwareStatus())) {
			result.setSoftwareStatusName(softwareStatusMap.get(result.getSoftwareStatus()).getDictValue());
		}
		// 合同
		List<RsSoftwareFileDto> contractList = rsSoftwareFileService.listBySoftware(id, id, RsSoftwareFileEnum.CONTRACT.getCode());
		if (!CollectionUtils.isEmpty(contractList)) {
			// 附件ID
			List<Long> attachIds = contractList.stream().map(RsSoftwareFileDto::getAttachId).collect(Collectors.toList());

			List<Attach> attachList = attachService.listByFileIds(attachIds);
			if (!CollectionUtils.isEmpty(attachList)) {
				result.setContractList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
			}
		}
		// 授权证书
		List<RsSoftwareFileDto> certificateList = rsSoftwareFileService.listBySoftware(id, id, RsSoftwareFileEnum.CERTIFICATE.getCode());
		if (!CollectionUtils.isEmpty(certificateList)) {
			// 附件ID
			List<Long> attachIds = certificateList.stream().map(RsSoftwareFileDto::getAttachId).collect(Collectors.toList());

			List<Attach> attachList = attachService.listByFileIds(attachIds);
			if (!CollectionUtils.isEmpty(attachList)) {
				result.setCertificateList(Objects.requireNonNull(BeanUtil.copy(attachList, SzykAttachDto.class)));
			}
		}

		// 创建人
		if (!StringUtils.isEmpty(result.getCreateUser())) {
			UserInfo user = userService.userInfo(result.getCreateUser());
			result.setCreateUserName(user == null ? null : user.getUser().getRealName());
		}

		// 更新人
		if (!StringUtils.isEmpty(result.getUpdateUser())) {
			UserInfo user = userService.userInfo(result.getUpdateUser());
			result.setUpdateUserName(user == null ? null : user.getUser().getRealName());
		}
		//v1.6组织全程
		result.setFullOrgName(deptService.handleFullName(result.getOrgId()));
		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public RsSoftwareDto saveOrUpdate(RsSoftwareVo vo) {

		//所属的组织id
		Long orgId = vo.getOrgId();
		if (orgId == null) {
			Dept loginDept = DeptScopeUtil.getLoginDept();
			orgId = loginDept.getId();
		}
		vo.setOrgId(orgId);
		Dept dept = SysCache.getDept(orgId);
		vo.setOrgName(dept.getDeptName());
		vo.setFullOrgId(dept.getAncestors() + "," + dept.getId());
		vo.setFullOrgName(dept.getAncestorName() + "-" + dept.getDeptName());
		RsSoftwareDto result = new RsSoftwareDto();

		vo.setSoftwareName(vo.getSoftwareName().trim());
		vo.setSoftwareVersion(vo.getSoftwareVersion().trim());
		//所在的单位
		Long unitId = dept.getUnitId();
		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<RsSoftwareDto> list = rsSoftwareService.listByCheck(unitId, vo.getSoftwareName(), vo.getSoftwareVersion(), vo.getSupplierId(), null);
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(SOFTWARE_SAVE_CHECK.getMessage());
			}
			vo.setSoftwareNo(paddingUtil.softwareKey(vo.getSoftwareClassify()));
			vo.setSoftwareStatus(SoftwareStatusEnum.IN_USE.getCode());
			result = rsSoftwareService.save(vo);
		} else {
			// 更新
			List<RsSoftwareDto> list = rsSoftwareService.listByCheck(unitId, vo.getSoftwareName(), vo.getSoftwareVersion(), vo.getSupplierId(), vo.getId());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(SOFTWARE_SAVE_CHECK.getMessage());
			}
			result = rsSoftwareService.update(vo);
		}
		// 删除附件数据
		if (!StringUtils.isEmpty(vo.getId())) {
			// 合同
			rsSoftwareFileService.deleteBySoftwareId(vo.getId(), Arrays.asList(vo.getId()), RsSoftwareFileEnum.CONTRACT.getCode());
			// 授权证书
			rsSoftwareFileService.deleteBySoftwareId(vo.getId(), Arrays.asList(vo.getId()), RsSoftwareFileEnum.CERTIFICATE.getCode());
		}

		// 合同
		if (!CollectionUtils.isEmpty(vo.getContractList())) {
			for (Long fileId : vo.getContractList()) {
				RsSoftwareFileVo fileVo = new RsSoftwareFileVo();
				fileVo.setSoftwareId(result.getId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(result.getId());
				fileVo.setBusinessType(RsSoftwareFileEnum.CONTRACT.getCode());

				rsSoftwareFileService.save(fileVo);
			}
			// 自动收集
			baseLogicService.autoCollectKnowledge(vo.getContractList(), result.getId(), result.getSoftwareNo());
		}

		// 授权证书
		if (!CollectionUtils.isEmpty(vo.getCertificateList())) {
			for (Long fileId : vo.getCertificateList()) {
				RsSoftwareFileVo fileVo = new RsSoftwareFileVo();
				fileVo.setSoftwareId(result.getId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(result.getId());
				fileVo.setBusinessType(RsSoftwareFileEnum.CERTIFICATE.getCode());

				rsSoftwareFileService.save(fileVo);
			}
			// 自动收集
			baseLogicService.autoCollectKnowledge(vo.getCertificateList(), result.getId(), result.getSoftwareNo());
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(RsSoftwareStatusVo vo) {

		return rsSoftwareService.updateByStatus(vo.getId(), vo.getSoftwareStatus());
	}

	@Transactional(rollbackFor = Exception.class)
	public List<RsSoftwareDeleteDto> delete(RsSoftwareDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			RsSoftwareDeleteDto dto = new RsSoftwareDeleteDto();

			RsSoftwareDto rsSoftwareDto = rsSoftwareService.fetchById(id);
			dto.setName(rsSoftwareDto.getSoftwareNo());

			// 校验
			List<RsSoftwareUseSoftwareDto> list = rsSoftwareUseSoftwareService.listBySoftwareId(rsSoftwareDto.getId());
			if (!CollectionUtils.isEmpty(list)) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.SOFTWARE_USR_SOFTWARE_EXIST.getMessage());
				result.add(dto);
				continue;
			}
			Boolean flag = rsSoftwareService.deleteById(id);
			dto.setResult(flag);
			// 删除合同
			rsSoftwareFileService.deleteBySoftwareId(rsSoftwareDto.getId(), Arrays.asList(rsSoftwareDto.getId()), RsSoftwareFileEnum.CONTRACT.getCode());
			// 删除授权证书
			rsSoftwareFileService.deleteBySoftwareId(rsSoftwareDto.getId(), Arrays.asList(rsSoftwareDto.getId()), RsSoftwareFileEnum.CERTIFICATE.getCode());
			result.add(dto);
		}

		return result;
	}

	public void export(RsSoftwarePageVo vo, HttpServletRequest request, HttpServletResponse response) {
		Long orgId = vo.getOrgId();
		if (orgId == null) {
			vo.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
		}
		vo.setSize(-1);
		IPage<RsSoftwarePageDto> iPage = rsSoftwareService.pageList(vo);
		List<RsSoftwarePageDto> list = iPage.getRecords();
		if (CollectionUtils.isEmpty(list)) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		// 软件分类
		List<DictBiz> softwareClassifyList = dictBizService.getList(DictBizEnum.SOFTWARE_CLASSIFY.getCode());
		Map<String, DictBiz> softwareClassifyMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareClassifyList)) {
			softwareClassifyMap.putAll(softwareClassifyList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件类型
		List<DictBiz> softwareTypeList = dictBizService.getList(DictBizEnum.SOFTWARE_TYPE.getCode());
		Map<String, DictBiz> softwareTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareTypeList)) {
			softwareTypeMap.putAll(softwareTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 许可类型
		List<DictBiz> licenseTypeList = dictBizService.getList(DictBizEnum.LICENSE_TYPE.getCode());
		Map<String, DictBiz> licenseTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(licenseTypeList)) {
			licenseTypeMap.putAll(licenseTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 软件状态
		List<DictBiz> softwareStatusList = dictBizService.getList(DictBizEnum.SOFTWARE_STATUS.getCode());
		Map<String, DictBiz> softwareStatusMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(softwareStatusList)) {
			softwareStatusMap.putAll(softwareStatusList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		List<RsSoftwareExportDto> result = new ArrayList();

		for (RsSoftwarePageDto dto : list) {
			RsSoftwareExportDto exportDto = new RsSoftwareExportDto();

			// 软件分类
			if (!ObjectUtils.isEmpty(softwareClassifyMap) && !ObjectUtils.isEmpty(dto.getSoftwareClassify()) && softwareClassifyMap.containsKey(dto.getSoftwareClassify())) {
				dto.setSoftwareClassifyName(softwareClassifyMap.get(dto.getSoftwareClassify()).getDictValue());
			}
			// 软件类型
			if (!ObjectUtils.isEmpty(softwareTypeMap) && !ObjectUtils.isEmpty(dto.getSoftwareType()) && softwareTypeMap.containsKey(dto.getSoftwareType())) {
				dto.setSoftwareTypeName(softwareTypeMap.get(dto.getSoftwareType()).getDictValue());
			}
			// 许可类型
			if (!ObjectUtils.isEmpty(licenseTypeMap) && !ObjectUtils.isEmpty(dto.getLicenseType()) && licenseTypeMap.containsKey(dto.getLicenseType())) {
				dto.setLicenseTypeName(licenseTypeMap.get(dto.getLicenseType()).getDictValue());
			}
			// 软件状态
			if (!ObjectUtils.isEmpty(softwareStatusMap) && !ObjectUtils.isEmpty(dto.getSoftwareStatus()) && softwareStatusMap.containsKey(dto.getSoftwareStatus())) {
				dto.setSoftwareStatusName(softwareStatusMap.get(dto.getSoftwareStatus()).getDictValue());
			}

			BeanUtil.copyProperties(dto, exportDto);

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			exportDto.setPurchaseTime(StringUtils.isEmpty(dto.getPurchaseTime()) ? null : sdf.format(dto.getPurchaseTime()));
			exportDto.setLicenseTime(!StringUtils.isEmpty(dto.getLicenseStartTime()) && !StringUtils.isEmpty(dto.getLicenseEndTime()) ? sdf.format(dto.getLicenseStartTime()) + "-" + sdf.format(dto.getLicenseEndTime()) : null);
			exportDto.setCreateTime(StringUtils.isEmpty(dto.getCreateTime()) ? null : sdfs.format(dto.getCreateTime()));

			result.add(exportDto);
		}

		// 导出
		ExcelUtil.export(response, "正版软件台账" + DateUtil.time(), "正版软件台账数据", result, RsSoftwareExportDto.class);
	}

	/**
	 * 正版软件台账导入
	 * @param orgId
	 * @param file
	 * @return
	 */
	@Transactional
	public List<RsSoftwareImportErrorDto> importData(Long orgId, MultipartFile file) {
		if (orgId == null) {
			orgId = Long.valueOf(AuthUtil.getDeptId());
		}
		Dept dept = SysCache.getDept(orgId);
		Long unitId = dept.getUnitId();
		String orgName = dept.getDeptName();
		String fullOrgId = dept.getAncestors() + "," + dept.getId();
		String fullOrgName = dept.getAncestorName() + "-" + dept.getDeptName();
		//读取文件
		InputStream inputStream = null;
		try {
			inputStream = file.getInputStream();
		} catch (IOException e) {
			throw new RuntimeException(e);
		} finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

		}
		List<RsSoftwareImportErrorDto> dtos = new LinkedList<>();
		List<RsSoftwareVo> vos = new LinkedList<>();
		//重复的校验
		List<String> repeatCheckStr = new LinkedList<>();
		//获取软甲分类字典
		Map<String, String> softwareClassifyMap = DictBizCache.getList(DictBizEnum.SOFTWARE_CLASSIFY.getCode()).stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Map<String, String> softwareTypeMap = DictBizCache.getList(DictBizEnum.SOFTWARE_TYPE.getCode()).stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Map<String, String> softwareStatusMap = DictBizCache.getList(DictBizEnum.SOFTWARE_STATUS.getCode()).stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Map<String, String> licenseTypeMap = DictBizCache.getList(DictBizEnum.LICENSE_TYPE.getCode()).stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey));
		Dept finalDept = dept;
		Long finalOrgId = orgId;
		EasyExcel.read(inputStream, RsSoftwareImportVo.class, new ReadListener<RsSoftwareImportVo>() {
				@Override
				public void onException(Exception exception, AnalysisContext context) throws Exception {
					int index = context.readRowHolder().getRowIndex() + 1;
					if (exception instanceof ExcelDataConvertException) {
						ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;

						dtos.add(RsSoftwareImportErrorDto.builder().rowIndex(index).errorMsg("第" + (excelDataConvertException.getColumnIndex() + 1) + "列数据格式异常：" + excelDataConvertException.getCellData().getStringValue()).build());
					}
				}

				@Override
				public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
					ReadListener.super.invokeHead(headMap, context);
				}

				@Override
				public void invoke(RsSoftwareImportVo rsSoftwareImportVo, AnalysisContext analysisContext) {
					int index = analysisContext.readRowHolder().getRowIndex() + 1;
					//先验证为空校验
					List<String> msgList = verifyEmpty(rsSoftwareImportVo);
					if (CollectionUtils.isEmpty(msgList)) {
						msgList = new LinkedList<>();
					}

					if (StringUtil.isNotBlank(rsSoftwareImportVo.getPurchaseTimeStr())) {
						String s = rsSoftwareImportVo.getPurchaseTimeStr().replaceAll("[./]", "-");
						boolean validDateFormat = isValidDateFormat(s);
						if (!validDateFormat) {
							msgList.add(ResourceExceptionEnum.WRONG_PURCHASE_TIME.getMessage());
						} else {
							DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
							try {
								Date date = format.parse(s);
								rsSoftwareImportVo.setPurchaseTime(date);
							} catch (ParseException e) {
								throw new RuntimeException(e);
							}
						}
					}
					String softwareClassify = softwareClassifyMap.get(rsSoftwareImportVo.getSoftwareClassifyName());
					if (StringUtil.isBlank(softwareClassify)) {
						msgList.add(ResourceExceptionEnum.NO_SOFTWARE_CLASSIFY.getMessage());
					}
					String softwareType = softwareTypeMap.get(rsSoftwareImportVo.getSoftwareTypeName());
					if (StringUtil.isBlank(softwareType)) {
						msgList.add(ResourceExceptionEnum.NO_SOFTWARE_TYPE.getMessage());
					}
					String softwareStatus = softwareStatusMap.get(rsSoftwareImportVo.getSoftwareStatusName());
					if (StringUtil.isBlank(softwareStatus)) {
						msgList.add(ResourceExceptionEnum.NO_SOFTWARE_STATUS.getMessage());
					}
					String licenseType = licenseTypeMap.get(rsSoftwareImportVo.getLicenseTypeName());
					if (StringUtil.isBlank(licenseType)) {
						msgList.add(ResourceExceptionEnum.NO_LICENSE_TYPE.getMessage());
					}
					if (Objects.equals(rsSoftwareImportVo.getLicenseTypeName(), "数量授权")) {
						if (Func.isEmpty(rsSoftwareImportVo.getLicenseQuantity())) {
							msgList.add(ResourceExceptionEnum.WRONG_LICENSE_QUANTITY.getMessage());
						}
					}
					String[] timeArray = null;
					if (org.apache.commons.lang3.StringUtils.isNotBlank(rsSoftwareImportVo.getLicenseTime())) {
						//非永久有效
						if (rsSoftwareImportVo.getLicenseTime().contains("至")) {
							rsSoftwareImportVo.setPriceValidity(false);
							timeArray = rsSoftwareImportVo.getLicenseTime().split("至");
							if (Func.isEmpty(timeArray) || timeArray.length != 2
								|| Func.isEmpty(DateUtil.parse(timeArray[0], "yyyy-MM-dd"))
								|| Func.isEmpty(DateUtil.parse(timeArray[1], "yyyy-MM-dd"))
							) {
								msgList.add(ResourceExceptionEnum.WRONG_LIMIT_TIME.getMessage());
							}
						} else if (rsSoftwareImportVo.getLicenseTime().contains("长期")) {
							rsSoftwareImportVo.setPriceValidity(true);
						} else {
							msgList.add(ResourceExceptionEnum.WRONG_LIMIT_TIME.getMessage());
						}
					}
					List<RsSupplierDto> rsSupplierDtos = supplierService.listByName(rsSoftwareImportVo.getSupplierName());
					if (CollectionUtil.isEmpty(rsSupplierDtos)) {
						msgList.add(ResourceExceptionEnum.WRONG_SUPPLIER_NAME.getMessage());
					}
					if (org.apache.commons.lang3.StringUtils.isNotEmpty(rsSoftwareImportVo.getSoftwareNo())) {
						List<RsSoftwareDto> list = rsSoftwareService.listByNo(rsSoftwareImportVo.getSoftwareNo());
						if (!CollectionUtils.isEmpty(list)) {
							//如果查询出来的编号的公司不是上传公司的
							if (!finalDept.getId().equals(list.get(0).getOrgId())) {
								msgList.add("编号：" + rsSoftwareImportVo.getSoftwareNo() + "的软件非本组织无权限维护！");
							}
						}
					}
					//导入的中是否有重复的
					String onlyName = rsSoftwareImportVo.getSoftwareName() + rsSoftwareImportVo.getSoftwareVersion()
						+ rsSoftwareImportVo.getSupplierName();
					if (repeatCheckStr.contains(onlyName)) {
						msgList.add(SOFTWARE_SAVE_CHECK.getMessage());
					} else {
						repeatCheckStr.add(rsSoftwareImportVo.getSoftwareName() + rsSoftwareImportVo.getSoftwareVersion() + rsSoftwareImportVo.getSupplierName());
						if (Func.isNotEmpty(rsSupplierDtos)) {
							Long supplierId = rsSupplierDtos.get(0).getId();
							rsSoftwareImportVo.getSoftwareName();
							Long oldId = null;
							//是更新还是新增
							if (!StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareNo())) {
								List<RsSoftwareDto> list = rsSoftwareService.listByNo(rsSoftwareImportVo.getSoftwareNo());
								if (CollectionUtil.isNotEmpty(list)) {
									RsSoftwareDto rsSoftwareDto = list.get(0);
									oldId = rsSoftwareDto.getId();
								}
							}
							List<RsSoftwareDto> rsSoftwareDtos = rsSoftwareService.listByCheck(unitId, rsSoftwareImportVo.getSoftwareName(), rsSoftwareImportVo.getSoftwareVersion(), supplierId, oldId);
							if (CollectionUtil.isNotEmpty(rsSoftwareDtos)) {
								msgList.add(SOFTWARE_SAVE_CHECK.getMessage());
							}
						}
					}

					//校验没有问题就赋值
					if (CollectionUtils.isEmpty(msgList)) {
						RsSoftwareVo rsSoftwareVo = BeanUtil.copyProperties(rsSoftwareImportVo, RsSoftwareVo.class);
						rsSoftwareVo.setOrgId(finalOrgId);
						rsSoftwareVo.setOrgName(orgName);
						rsSoftwareVo.setFullOrgId(fullOrgId);
						rsSoftwareVo.setFullOrgName(fullOrgName);
						rsSoftwareVo.setSoftwareClassify(softwareClassify);
						rsSoftwareVo.setSoftwareType(softwareType);
						rsSoftwareVo.setSoftwareStatus(softwareStatus);
						rsSoftwareVo.setLicenseType(licenseType);
						if (!rsSoftwareVo.getPriceValidity() && timeArray != null && timeArray.length == 2) {
							rsSoftwareVo.setLicenseStartTime(DateUtil.parse(timeArray[0], "yyyy-MM-dd"));
							rsSoftwareVo.setLicenseEndTime(DateUtil.parse(timeArray[1], "yyyy-MM-dd"));
						}
						//匹配供应商信息
						rsSoftwareVo.setSupplierId(rsSupplierDtos.get(0).getId());
						rsSoftwareVo.setSupplierNo(rsSupplierDtos.get(0).getSupplierNo());
						vos.add(rsSoftwareVo);
					}
					if (CollectionUtil.isNotEmpty(msgList)) {
						dtos.add(RsSoftwareImportErrorDto.builder()
							.rowIndex(index)
							.name(rsSoftwareImportVo.getSoftwareName())
							.errorMsg(msgList.stream()
								.collect(Collectors.joining(",")))
							.build());
					}
				}

				@Override
				public void extra(CellExtra extra, AnalysisContext context) {
					ReadListener.super.extra(extra, context);
				}

				@Override
				public void doAfterAllAnalysed(AnalysisContext analysisContext) {

				}

				@Override
				public boolean hasNext(AnalysisContext context) {
					return ReadListener.super.hasNext(context);
				}
			}).

			sheet().

			doRead();
		if (CollectionUtil.isEmpty(dtos)) {
			if (CollectionUtil.isEmpty(vos)) {
				throw new ServiceException("导入数据为空");
			}
			if (CollectionUtil.isNotEmpty(vos)) {
				vos.forEach(vo -> {
					saveDate(vo);
				});
			}
		}
		return dtos;
	}

	private List<String> verifyEmpty(RsSoftwareImportVo rsSoftwareImportVo) {
		List<String> msg = new LinkedList<>();
		if (StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareName())) {
			msg.add(ResourceExceptionEnum.EMPTY_SOFTWARE_NAME.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareVersion())) {
			msg.add(ResourceExceptionEnum.EMPTY_SOFTWARE_VERSION.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareClassifyName())) {
			msg.add(ResourceExceptionEnum.EMPTY_SOFTWARE_CLASSIFY.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareTypeName())) {
			msg.add(ResourceExceptionEnum.EMPTY_SOFTWARE_TYPE.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getSoftwareStatusName())) {
			msg.add(ResourceExceptionEnum.EMPTY_SOFTWARE_STATUS.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getPurchaseTimeStr())) {
			msg.add(ResourceExceptionEnum.EMPTY_PURCHASE_TIME.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getPurchaseAmount())) {
			msg.add(ResourceExceptionEnum.EMPTY_PURCHASE_AMOUNT.getMessage());
		}
//		if (StringUtils.isEmpty(rsSoftwareImportVo.getSupplierName())) {
//			msg.add(ResourceExceptionEnum.EMPTY_SUPPLIER_NAME.getMessage());
//		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getLicenseTime())) {
			msg.add(ResourceExceptionEnum.EMPTY_LICENSE_LIMIT.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getLicenseTypeName())) {
			msg.add(ResourceExceptionEnum.EMPTY_LICENSE_TYPE.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getHeadPersonName())) {
			msg.add(ResourceExceptionEnum.EMPTY_HEAD_PERSON.getMessage());
		}
		if (StringUtils.isEmpty(rsSoftwareImportVo.getHeadPersonTel())) {
			msg.add(ResourceExceptionEnum.EMPTY_HEAD_PERSON_TEL.getMessage());
		}
		return msg;
	}

	public void exportTemplate(HttpServletResponse response) {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
		String fileName = null;
		try {
			fileName = URLEncoder.encode("正版软件台账导入模板", "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
		try {
			InputStream inputStream = getClass().getResourceAsStream("/excel/software.xlsx");
			IoUtil.copy(inputStream, response.getOutputStream());
			response.flushBuffer();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private RsSoftwareDto saveDate(RsSoftwareVo vo) {

		RsSoftwareDto result = new RsSoftwareDto();
		if (!StringUtils.isEmpty(vo.getSoftwareName())) {
			vo.setSoftwareName(vo.getSoftwareName().trim());
		}
		if (!StringUtils.isEmpty(vo.getSoftwareVersion())) {
			vo.setSoftwareVersion(vo.getSoftwareVersion().trim());
		}
		if (StringUtils.isEmpty(vo.getSoftwareNo())) {
			//新增
			vo.setSoftwareNo(paddingUtil.softwareKey(vo.getSoftwareClassify()));
			result = rsSoftwareService.save(vo);
		}
		if (!StringUtils.isEmpty(vo.getSoftwareNo())) {
			//修改
			List<RsSoftwareDto> list = rsSoftwareService.listByNo(vo.getSoftwareNo());
			if (CollectionUtils.isEmpty(list)) {
				//还是新增
				//替换编号
				//vo.setSoftwareNo(paddingUtil.softwareKey(vo.getSoftwareClassify()));
				result = rsSoftwareService.save(vo);
			}
			if (!CollectionUtils.isEmpty(list)) {
				RsSoftwareVo softwareVo = BeanUtil.copyProperties(list.get(0), RsSoftwareVo.class);
				cn.hutool.core.bean.BeanUtil.copyProperties(vo, softwareVo, CopyOptions.create().setIgnoreNullValue(true));
				result = rsSoftwareService.update(softwareVo);
			}
		}

		// 删除附件数据
		if (!StringUtils.isEmpty(vo.getId())) {
			// 合同
			rsSoftwareFileService.deleteBySoftwareId(vo.getId(), Arrays.asList(vo.getId()), RsSoftwareFileEnum.CONTRACT.getCode());

			// 授权证书
			rsSoftwareFileService.deleteBySoftwareId(vo.getId(), Arrays.asList(vo.getId()), RsSoftwareFileEnum.CERTIFICATE.getCode());
		}

		// 合同
		if (!CollectionUtils.isEmpty(vo.getContractList())) {
			for (Long fileId : vo.getContractList()) {
				RsSoftwareFileVo fileVo = new RsSoftwareFileVo();
				fileVo.setSoftwareId(result.getId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(result.getId());
				fileVo.setBusinessType(RsSoftwareFileEnum.CONTRACT.getCode());

				rsSoftwareFileService.save(fileVo);
			}
		}

		// 授权证书
		if (!CollectionUtils.isEmpty(vo.getCertificateList())) {
			for (Long fileId : vo.getCertificateList()) {
				RsSoftwareFileVo fileVo = new RsSoftwareFileVo();
				fileVo.setSoftwareId(result.getId());
				fileVo.setAttachId(fileId);
				fileVo.setBusinessId(result.getId());
				fileVo.setBusinessType(RsSoftwareFileEnum.CERTIFICATE.getCode());

				rsSoftwareFileService.save(fileVo);
			}
		}

		return result;
	}


}
