package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 布尔值字段转换器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
public class BooleanFieldValueConverter implements FieldValueConverter {

    private static final List<String> BOOLEAN_FIELDS = Arrays.asList(
        "isPublicCloud", "isActive", "isEnabled", "isDeleted", "isValid"
    );

    @Override
    public String convert(String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        switch (fieldValue.toLowerCase()) {
            case "true":
            case "1":
                return "是";
            case "false":
            case "0":
                return "否";
            default:
                return fieldValue;
        }
    }

    @Override
    public boolean supports(String fieldName) {
        return BOOLEAN_FIELDS.contains(fieldName) || 
               fieldName.startsWith("is") || 
               fieldName.startsWith("has") ||
               fieldName.startsWith("can");
    }

    @Override
    public int getPriority() {
        return 100;
    }
}
