package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDto;
import com.snszyk.zbusiness.resource.entity.RsSoftware;
import com.snszyk.zbusiness.resource.vo.RsSoftwarePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RsSoftwareMapper extends BaseMapper<RsSoftware> {
	/**
	 * 分页查询
	 * @param page
	 * @param vo
	 * @return
	 */
    IPage<RsSoftwareDto> pageList( Page<RsSoftware> page, @Param("vo") RsSoftwarePageVo vo);

	/**
	 * 判断是否存在重复
	 *
	 * @param unitId
	 * @param softwareName
	 * @param softwareVersion
	 * @param supplierId
	 * @param neId
	 * @return
	 */
	List<RsSoftware> listByCheck(@Param("unitId") Long unitId, @Param("softwareName") String softwareName, @Param("softwareVersion") String softwareVersion,
								 @Param("supplierId") Long supplierId, @Param("neId") Long neId);
}
