/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordExtensionDto;
import com.snszyk.zbusiness.resource.entity.SysOperationRecordExtension;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作记录扩展字段Mapper接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Repository
public interface SysOperationRecordExtensionMapper extends BaseMapper<SysOperationRecordExtension> {

    /**
     * 根据记录ID查询扩展字段
     *
     * @param recordId 记录ID
     * @return 扩展字段列表
     */
    List<SysOperationRecordExtensionDto> selectByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据记录ID删除扩展字段
     *
     * @param recordId 记录ID
     * @return 删除数量
     */
    int deleteByRecordId(@Param("recordId") Long recordId);
}
