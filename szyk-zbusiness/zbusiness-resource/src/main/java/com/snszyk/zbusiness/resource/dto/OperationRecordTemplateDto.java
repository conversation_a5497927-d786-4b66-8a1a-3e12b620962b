package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 操作记录模板数据传输对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录模板DTO", description = "操作记录模板数据传输对象")
public class OperationRecordTemplateDto extends BaseCrudDto {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板配置
     */
    @ApiModelProperty(value = "模板配置")
    private String templateConfig;

    /**
     * 是否默认模板
     */
    @ApiModelProperty(value = "是否默认模板")
    private Boolean isDefault;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String description;

    /**
     * 解析后的模板配置
     */
    @ApiModelProperty(value = "解析后的模板配置")
    private Map<String, Object> parsedConfig;

    /**
     * 字段配置列表
     */
    @ApiModelProperty(value = "字段配置列表")
    private List<Map<String, Object>> fieldConfigs;

    /**
     * 模板状态
     */
    @ApiModelProperty(value = "模板状态")
    private Integer status;

    /**
     * 模板状态名称
     */
    @ApiModelProperty(value = "模板状态名称")
    private String statusName;
}
