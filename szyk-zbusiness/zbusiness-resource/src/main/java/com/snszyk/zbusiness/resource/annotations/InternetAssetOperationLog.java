/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 互联网资产操作日志注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface InternetAssetOperationLog {

    /**
     * 操作类型
     */
    String operationType() default "UPDATE";

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 业务ID字段名
     * 用于从方法参数中获取业务数据ID
     */
    String businessIdField() default "id";

    /**
     * 业务名称字段名
     * 用于从方法参数中获取业务数据名称
     */
    String businessNameField() default "systemName";

    /**
     * 组织ID字段名
     * 用于从方法参数中获取组织ID
     */
    String orgIdField() default "orgId";

    /**
     * 组织名称字段名
     * 用于从方法参数中获取组织名称
     */
    String orgNameField() default "orgName";

    /**
     * 是否记录变更详情
     */
    boolean recordDetails() default true;
}
