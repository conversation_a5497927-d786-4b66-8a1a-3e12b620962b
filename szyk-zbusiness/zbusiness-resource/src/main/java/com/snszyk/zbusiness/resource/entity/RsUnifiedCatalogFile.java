/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 统谈分签文档表实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RsUnifiedCatalogFile extends BaseCrudEntity {

	/**
	 * 业务id
	 */
	private Long businessId;
	/**
	 * 业务类型
	 */
	private String businessType;
	/**
	 * 附件id
	 */
	private Long attachId;


}
