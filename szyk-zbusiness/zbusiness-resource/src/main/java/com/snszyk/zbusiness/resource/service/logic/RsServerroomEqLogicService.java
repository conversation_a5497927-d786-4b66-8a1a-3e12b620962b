/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.resource.dto.RsServerroomEqDto;
import com.snszyk.zbusiness.resource.service.IRsServerroomEqService;
import com.snszyk.zbusiness.resource.vo.RsServerroomEqVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 数据中心机房关联设备明细 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@AllArgsConstructor
@Service
public class RsServerroomEqLogicService extends BaseCrudLogicService<RsServerroomEqDto, RsServerroomEqVo> {

	private final IRsServerroomEqService rsServerroomEqService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rsServerroomEqService;
	}
}
