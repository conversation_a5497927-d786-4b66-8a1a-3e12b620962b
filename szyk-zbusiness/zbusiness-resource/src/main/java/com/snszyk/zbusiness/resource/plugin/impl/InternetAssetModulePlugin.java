package com.snszyk.zbusiness.resource.plugin.impl;

import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.plugin.AbstractBusinessModulePlugin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 互联网资产模块插件实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Component
public class InternetAssetModulePlugin extends AbstractBusinessModulePlugin {

    @Override
    public List<BusinessTypeEnum> getSupportedBusinessTypes() {
        return Arrays.asList(BusinessTypeEnum.INTERNET_ASSET);
    }

    @Override
    public String getPluginName() {
        return "InternetAssetModulePlugin";
    }

    @Override
    public String getPluginDescription() {
        return "互联网资产模块操作记录插件，提供互联网资产特定的操作记录功能";
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public Map<String, Object> preprocessOperationData(BusinessTypeEnum businessType, Long businessId, String operationType,
                                                      Map<String, Object> oldData, Map<String, Object> newData) {
        if (!supports(businessType)) {
            return super.preprocessOperationData(businessType, businessId, operationType, oldData, newData);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("oldData", oldData);
        result.put("newData", newData);

        // 互联网资产特定的预处理逻辑
        if ("CREATE".equals(operationType)) {
            // 创建时记录创建时间戳
            Map<String, Object> enhancedNewData = new HashMap<>(newData);
            enhancedNewData.put("_createTimestamp", System.currentTimeMillis());
            result.put("newData", enhancedNewData);
        } else if ("UPDATE".equals(operationType)) {
            // 更新时记录更新时间戳
            Map<String, Object> enhancedNewData = new HashMap<>(newData);
            enhancedNewData.put("_updateTimestamp", System.currentTimeMillis());
            result.put("newData", enhancedNewData);
        }

        return result;
    }

    @Override
    public void postprocessOperationRecord(BusinessTypeEnum businessType, Long businessId, Long recordId, String operationType) {
        if (!supports(businessType)) {
            return;
        }

        // 互联网资产特定的后处理逻辑
        log.info("互联网资产 {} 执行 {} 操作后处理，记录ID: {}", businessId, operationType, recordId);

        // 可以在这里执行额外的业务逻辑，比如：
        // 1. 发送通知
        // 2. 更新相关统计数据
        // 3. 触发其他业务流程
    }

    @Override
    public String customFieldValueConversion(BusinessTypeEnum businessType, String fieldName, String fieldValue) {
        if (!supports(businessType) || StringUtil.isBlank(fieldValue)) {
            return null;
        }

        // 互联网资产特定的字段值转换
        switch (fieldName) {
            case "internetType":
                return convertInternetType(fieldValue);
            case "applicationType":
                return convertApplicationType(fieldValue);
            case "publicCloudSupplier":
                return convertCloudSupplier(fieldValue);
            default:
                return null; // 使用默认转换逻辑
        }
    }

    @Override
    public String customOperationDescription(BusinessTypeEnum businessType, String operationType, String businessName, List<String> changeFields) {
        if (!supports(businessType)) {
            return null;
        }

        // 互联网资产特定的操作描述生成
        StringBuilder desc = new StringBuilder();
        
        switch (operationType) {
            case "CREATE":
                desc.append("新增互联网资产：").append(businessName);
                break;
            case "UPDATE":
                desc.append("修改互联网资产：").append(businessName);
                if (changeFields != null && !changeFields.isEmpty()) {
                    desc.append("，变更内容：");
                    List<String> fieldLabels = new ArrayList<>();
                    for (String field : changeFields) {
                        fieldLabels.add(getFieldDisplayName(field));
                    }
                    desc.append(String.join("、", fieldLabels));
                }
                break;
            case "DELETE":
                desc.append("删除互联网资产：").append(businessName);
                break;
            case "STATUS_CHANGE":
                desc.append("变更互联网资产状态：").append(businessName);
                break;
            default:
                return null; // 使用默认描述生成逻辑
        }
        
        return desc.toString();
    }

    @Override
    public Map<String, String> validateBusinessData(BusinessTypeEnum businessType, Long businessId, String operationType, Map<String, Object> data) {
        if (!supports(businessType)) {
            return new HashMap<>();
        }

        Map<String, String> errors = new HashMap<>();

        // 互联网资产特定的验证逻辑
        if ("CREATE".equals(operationType) || "UPDATE".equals(operationType)) {
            // 验证必填字段
            List<String> requiredFields = Arrays.asList("systemName", "internetAddress", "internetType", "applicationType");
            errors.putAll(validateRequiredFields(data, requiredFields));

            // 验证字段长度
            Map<String, Integer> fieldLengthMap = new HashMap<>();
            fieldLengthMap.put("systemName", 200);
            fieldLengthMap.put("internetAddress", 500);
            fieldLengthMap.put("contactPerson", 100);
            fieldLengthMap.put("contactPhone", 50);
            errors.putAll(validateFieldLength(data, fieldLengthMap));

            // 验证互联网地址格式
            Object internetAddress = data.get("internetAddress");
            if (internetAddress != null && !isValidInternetAddress(internetAddress.toString())) {
                errors.put("internetAddress", "互联网地址格式不正确");
            }

            // 验证联系电话格式
            Object contactPhone = data.get("contactPhone");
            if (contactPhone != null && StringUtil.isNotBlank(contactPhone.toString()) && 
                !isValidPhoneNumber(contactPhone.toString())) {
                errors.put("contactPhone", "联系电话格式不正确");
            }
        }

        return errors;
    }

    @Override
    public List<String> getSensitiveFields(BusinessTypeEnum businessType) {
        if (!supports(businessType)) {
            return Collections.emptyList();
        }
        return Arrays.asList("contactPhone", "networkAddress");
    }

    @Override
    public List<String> getIndexFields(BusinessTypeEnum businessType) {
        if (!supports(businessType)) {
            return Collections.emptyList();
        }
        return Arrays.asList("systemName", "internetAddress", "internetType", "applicationType", "resourceStatus");
    }

    @Override
    protected String getFieldDisplayName(String fieldName) {
        switch (fieldName) {
            case "systemName": return "系统名称";
            case "internetAddress": return "互联网地址";
            case "networkAddress": return "内网地址";
            case "domainName": return "域名";
            case "internetType": return "互联网类型";
            case "applicationType": return "应用类型";
            case "contactPerson": return "联系人";
            case "contactPhone": return "联系电话";
            case "securityLevel": return "等保级别";
            case "resourceStatus": return "资源状态";
            case "isPublicCloud": return "是否公有云";
            case "publicCloudSupplier": return "公有云供应商";
            default: return fieldName;
        }
    }

    /**
     * 转换互联网类型
     */
    private String convertInternetType(String value) {
        switch (value) {
            case "1": return "网站";
            case "2": return "应用系统";
            case "3": return "API接口";
            case "4": return "其他";
            default: return value;
        }
    }

    /**
     * 转换应用类型
     */
    private String convertApplicationType(String value) {
        switch (value) {
            case "1": return "门户网站";
            case "2": return "业务系统";
            case "3": return "管理系统";
            case "4": return "移动应用";
            case "5": return "微服务";
            default: return value;
        }
    }

    /**
     * 转换云供应商
     */
    private String convertCloudSupplier(String value) {
        switch (value) {
            case "1": return "阿里云";
            case "2": return "腾讯云";
            case "3": return "华为云";
            case "4": return "百度云";
            case "5": return "AWS";
            case "6": return "Azure";
            case "7": return "其他";
            default: return value;
        }
    }

    /**
     * 验证互联网地址格式
     */
    private boolean isValidInternetAddress(String address) {
        // 简单的URL格式验证
        return address.matches("^(https?://)?(([\\w\\-]+\\.)+[a-zA-Z]{2,})(:[0-9]+)?(/.*)?$");
    }

    /**
     * 验证电话号码格式
     */
    private boolean isValidPhoneNumber(String phone) {
        // 简单的电话号码格式验证
        return phone.matches("^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$|^400-?\\d{3}-?\\d{4}$");
    }
}
