package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 扩展字段存储服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface ExtensionFieldStorageService {

    /**
     * 保存扩展字段数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     * @param extensionData 扩展数据
     * @param metadata 元数据
     */
    void saveExtensionData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> extensionData, Map<String, Object> metadata);

    /**
     * 获取扩展字段数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     * @return 扩展数据
     */
    Map<String, Object> getExtensionData(Long recordId, BusinessTypeEnum businessType);

    /**
     * 获取元数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     * @return 元数据
     */
    Map<String, Object> getMetadata(Long recordId, BusinessTypeEnum businessType);

    /**
     * 更新扩展字段数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     * @param extensionData 扩展数据
     * @param metadata 元数据
     */
    void updateExtensionData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> extensionData, Map<String, Object> metadata);

    /**
     * 删除扩展字段数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     */
    void deleteExtensionData(Long recordId, BusinessTypeEnum businessType);

    /**
     * 创建索引数据
     *
     * @param recordId 操作记录ID
     * @param businessType 业务类型
     * @param indexData 索引数据
     */
    void createIndexData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> indexData);

    /**
     * 查询索引数据
     *
     * @param businessType 业务类型
     * @param indexKey 索引键
     * @param indexValue 索引值
     * @return 记录ID列表
     */
    List<Long> queryByIndex(BusinessTypeEnum businessType, String indexKey, String indexValue);

    /**
     * 删除索引数据
     *
     * @param recordId 操作记录ID
     */
    void deleteIndexData(Long recordId);

    /**
     * 验证扩展字段数据
     *
     * @param businessType 业务类型
     * @param extensionData 扩展数据
     * @return 验证结果
     */
    Map<String, String> validateExtensionData(BusinessTypeEnum businessType, Map<String, Object> extensionData);

    /**
     * 格式化扩展字段显示值
     *
     * @param businessType 业务类型
     * @param extensionData 扩展数据
     * @return 格式化后的显示数据
     */
    Map<String, String> formatDisplayValues(BusinessTypeEnum businessType, Map<String, Object> extensionData);

    /**
     * 获取业务类型的扩展字段模式
     *
     * @param businessType 业务类型
     * @return 字段模式定义
     */
    Map<String, Object> getFieldSchema(BusinessTypeEnum businessType);
}
