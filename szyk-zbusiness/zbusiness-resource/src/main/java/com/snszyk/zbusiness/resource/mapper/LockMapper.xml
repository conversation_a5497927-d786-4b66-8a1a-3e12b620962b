<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.LockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="lockResultMap" type="com.snszyk.zbusiness.resource.entity.Lock">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="target_resource" property="targetResource"/>
        <result column="lock_status" property="lockStatus"/>
        <result column="second_lock_stauts" property="secondLockStatus"/>
        <result column="lock_type" property="lockType"/>
        <result column="auto_lock_time" property="autoLockTime"/>
    </resultMap>


    <select id="selectLockPage" resultMap="lockResultMap">
        select * from rs_lock where is_deleted = 0
    </select>

</mapper>
