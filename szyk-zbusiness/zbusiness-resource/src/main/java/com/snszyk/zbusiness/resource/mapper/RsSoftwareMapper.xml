<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.RsSoftwareMapper">


<!--    分页查询-->
    <select id="pageList" resultType="com.snszyk.zbusiness.resource.dto.RsSoftwareDto">
        select t.id, t.org_id, t.org_name, t.full_org_id, t.full_org_name, t.software_classify, t.software_no,
        t.software_name, t.software_type, t.software_version, t.purchase_time, t.purchase_amount, t.supplier_id,
        t.supplier_name, t.supplier_no, t.license_type, t.license_quantity, t.serial_number, t.license_start_time,
        t.license_end_time, t.head_person_id, t.head_person_name, t.head_person_tel, t.software_status,
        t.software_remark, t.create_user, t.create_dept, t.create_time, t.update_user, t.update_time, t.status,
        t.is_deleted ,t.price_validity,u.real_name as "updateUserName" from rs_software t
        left join szyk_dept sd on sd.id =t.org_id and sd.is_deleted=0
        left join szyk_user u on t.update_user = u.id and u.is_deleted=0

        left join szyk_dept udept on sd.unit_id = udept.id
        where t.is_deleted=0
        <if test="vo.softwareType!=null and vo.softwareType !=''">
            and t.software_type = #{vo.softwareType}
        </if>
        <if test="vo.licenseType!=null and vo.licenseType !=''">
            and t.license_type = #{vo.licenseType}
        </if>
        <if test="vo.purchaseStartDate!=null and vo.purchaseEndDate!=null">
            and t.purchase_time between #{vo.purchaseStartDate} and #{vo.purchaseEndDate}
        </if>
        <if test="vo.softwareNo!=null and vo.softwareNo !=''">
        and t.software_no like concat('%',#{vo.softwareNo},'%')
        </if>
        <if test="vo.softwareName!=null and vo.softwareName !=''">
            and t.software_name like concat('%',#{vo.softwareName},'%')
        </if>

        <if test="vo.softwareClassify!=null and vo.softwareClassify !=''">
            and t.software_classify = #{vo.softwareClassify}
        </if>
        <if test="vo.softwareStatus!=null and vo.softwareStatus !=''">
            and t.software_status = #{vo.softwareStatus}
        </if>
        <if test="vo.unitRange ==1 ">
            AND t.org_id = #{vo.orgId}
        </if>
        <if test="vo.unitRange ==2 ">
            AND (
            sd.full_org_id like concat('%',#{vo.orgId},'%')
            or t.org_id=#{vo.orgId}
            )
        </if>

        order by udept.sort asc,sd.sort asc,sd.unit_level asc, sd.id asc, t.create_time desc
    </select>
<!--    判断是否存在重复-->
    <select id="listByCheck" resultType="com.snszyk.zbusiness.resource.entity.RsSoftware">

        select t.id,
               t.org_id,
               t.org_name,
               t.full_org_id,
               t.full_org_name,
               t.software_classify,
               t.software_no,
               t.software_name,
               t.software_type,
               t.software_version,
               t.purchase_time,
               t.purchase_amount,
               t.supplier_id,
               t.supplier_name,
               t.supplier_no,
               t.license_type,
               t.license_quantity,
               t.serial_number,
               t.license_start_time,
               t.license_end_time,
               t.head_person_id,
               t.head_person_name,
               t.head_person_tel,
               t.software_status,
               t.software_remark,
               t.create_user,
               t.create_dept,
               t.create_time,
               t.update_user,
               t.update_time,
               t.status,
               t.is_deleted
        from rs_software t
                 left join szyk_dept t1 on t1.is_deleted = 0 and t1.id = t.org_id
        where t.is_deleted = 0
          and t1.unit_id = #{unitId}
          and t.software_name = #{softwareName}
          and t.software_version = #{softwareVersion}
        and t.supplier_id=#{supplierId}
        <if test="neId !=null">
            and t.id !=#{neId}
        </if>
    </select>
</mapper>
