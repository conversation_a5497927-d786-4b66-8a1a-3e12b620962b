package com.snszyk.zbusiness.resource.config;

import java.util.List;
import java.util.Map;

/**
 * 业务字段配置接口
 * 用于定义不同业务类型的字段配置
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface BusinessFieldConfig {

    /**
     * 获取字段标签映射
     *
     * @return 字段名 -> 中文标签的映射
     */
    Map<String, String> getFieldLabelMap();

    /**
     * 获取字段类型映射
     *
     * @return 字段名 -> 字段类型的映射
     */
    Map<String, String> getFieldTypeMap();

    /**
     * 获取可查询字段列表
     *
     * @return 支持查询的字段名列表
     */
    List<String> getQueryableFields();

    /**
     * 获取默认名称字段
     *
     * @return 默认名称字段名
     */
    String getDefaultNameField();

    /**
     * 获取默认组织字段
     *
     * @return 默认组织字段名
     */
    String getDefaultOrgField();

    /**
     * 获取忽略字段列表
     *
     * @return 在变更记录中需要忽略的字段列表
     */
    List<String> getIgnoreFields();

    /**
     * 获取字段分组映射
     * 用于将字段按业务逻辑分组显示
     *
     * @return 字段分组映射 (字段名 -> 分组名)
     */
    Map<String, String> getFieldGroupMap();

    /**
     * 获取敏感字段列表
     * 这些字段在显示时需要特殊处理（如脱敏）
     *
     * @return 敏感字段列表
     */
    List<String> getSensitiveFields();

    /**
     * 获取字典字段映射
     * 用于标识哪些字段需要进行字典转换
     *
     * @return 字典字段映射 (字段名 -> 字典代码)
     */
    Map<String, String> getDictFieldMap();
}
