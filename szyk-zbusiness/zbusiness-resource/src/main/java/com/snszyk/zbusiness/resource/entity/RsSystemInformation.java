/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信息系统信息表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rs_system_information")
@EqualsAndHashCode(callSuper = false)
public class RsSystemInformation extends BaseCrudEntity {

	/**
	 * 系统id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long systemId;
	/**
	 * 应用提供商id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long supplierId;
	/**
	 * 应用提供商名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String supplierName;
	/**
	 * APP/软件版本号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String versionNo;
	/**
	 * 服务提供方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String serviceMode;
	/**
	 * 部署的OS版本
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osVersion;
	/**
	 * 部署的DB版本
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dbVersion;

	/**
	 * 操作系统类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osType;

	/**
	 * 数据库类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dbType;

	/**
	 * 中间件版本
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String middlewareVersion;
	/**
	 * 部署网络域/分区
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deployNetwork;
	/**
	 * 安全级别
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String securityLevel;
	/**
	 * 部署方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deployMode;
	/**
	 * 部署服务器规模
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer serverScale;
	/**
	 * 数据存储容量
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer memoryCapacity;
	/**
	 * 存储年增长量
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer memoryAnnualGrowth;
	/**
	 * 运维方id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long operationSupplyId;
	/**
	 * 运维方名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String operationSupplyName;
	/**
	 * 联系人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String contactName;
	/**
	 * 联系方式
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String contactPhone;

	//v1.5新增字段
	/**
	 * 是否有小程序,0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Boolean hasApplet;
	/**
	 * 小程序类型及名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String appletDesc;
	/**
	 * 部署位置
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deployPlace;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;

	/**
	 *是否自研：0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Boolean selfDevelop;

	/**
	 *'供应商商联系人'
	 */
	private String developUser;

	/**
	 *'供应商联系方式'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String developPhone;

	/**
	 *'开发语言，数据字典'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String developLanguage;

	/**
	 *是否自行运维：0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Boolean selfOperation;

	/**
	 *'运维方联系人'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String operationUser;

	/**
	 *'运维方联系方式'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String operationPhone;

	/**
	 *'系统是否规划上收，数据字典'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String planCollect;
}
