/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsSystemAuthDto;
import com.snszyk.zbusiness.resource.vo.RsSystemAuthVo;

import java.util.List;

/**
 * 信息系统授权 服务类
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface IRsSystemAuthService extends IBaseCrudService<RsSystemAuthDto, RsSystemAuthVo> {

	Boolean removeBySystemId(Long id);

	Boolean saveBatch(List<RsSystemAuthVo> collect);

	List<RsSystemAuthDto> queryBySystemId(Long id);
}
