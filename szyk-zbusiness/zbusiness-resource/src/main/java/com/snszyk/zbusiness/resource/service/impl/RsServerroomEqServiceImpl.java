/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.RsServerRoomDto;
import com.snszyk.zbusiness.resource.dto.RsServerroomEqDto;
import com.snszyk.zbusiness.resource.entity.RsServerroomEq;
import com.snszyk.zbusiness.resource.mapper.RsServerroomEqMapper;
import com.snszyk.zbusiness.resource.service.IRsServerroomEqService;
import com.snszyk.zbusiness.resource.vo.RsServerRoomCabinetVo;
import com.snszyk.zbusiness.resource.vo.RsServerroomEqVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据中心机房关联设备明细 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@AllArgsConstructor
@Service
public class RsServerroomEqServiceImpl extends BaseCrudServiceImpl<RsServerroomEqMapper, RsServerroomEq, RsServerroomEqDto, RsServerroomEqVo> implements IRsServerroomEqService {


	@Override
	public List<RsServerroomEqDto> listByRoomIdAndType(RsServerRoomCabinetVo vo) {
		return this.baseMapper.listByRoomIdAndType(vo);
	}

	@Override
	public List<RsServerroomEqDto> listByRoomId(Long roomId) {
		List<RsServerroomEq> list = this.lambdaQuery().eq(RsServerroomEq::getRoomId,roomId).orderByAsc(RsServerroomEq::getId).list();
		return BeanUtil.copy(list,RsServerroomEqDto.class);
	}

	/**
	 * 查询设备关联的数据中心机房
	 * @param equipmentId
	 * @return
	 */
	@Override
	public RsServerRoomDto listByEqId(Long equipmentId) {
		return this.baseMapper.listByEqId(equipmentId);
	}

	@Override
	public boolean batchSave(List<RsServerroomEqVo> eqVoList) {
		List<RsServerroomEq> eqList = BeanUtil.copy(eqVoList, RsServerroomEq.class);
		return this.saveBatch(eqList);
	}

	/**
	 * 根据机房id和设备ids删除
	 * @param roomId
	 * @param needDeleteEqList
	 * @return
	 */
	@Override
	public boolean removeByRoomAndEqId(Long roomId, List<Long> needDeleteEqList) {
		if(roomId==null|| CollectionUtil.isEmpty(needDeleteEqList)){
			return false;
		}
        return this.lambdaUpdate().eq(RsServerroomEq::getRoomId, roomId)
			.in(RsServerroomEq::getEquipmentId, needDeleteEqList).remove();
	}
}
