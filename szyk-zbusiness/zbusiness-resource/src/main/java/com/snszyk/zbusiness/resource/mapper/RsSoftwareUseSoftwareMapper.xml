<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.RsSoftwareUseSoftwareMapper">

    <select id="listAll" resultType="com.snszyk.zbusiness.resource.entity.RsSoftwareUseSoftware">
        select t.id,
               t.use_computer_id,
               t.software_id,
               t.install_date,
               t1.supplier_id,
               t1.supplier_name,
               t1.price_validity
        from rs_software_use_software t
                 left join rs_software t1 on t1.id = t.software_id and t1.is_deleted = 0
        where t.is_deleted = 0
    </select>

    <select id="listByUseComputerId" resultType="com.snszyk.zbusiness.resource.entity.RsSoftwareUseSoftware">
        select
             t.* from rs_software_use_software t where t.is_deleted = 0 and t.use_computer_id = #{useComputerId}
    </select>
</mapper>
