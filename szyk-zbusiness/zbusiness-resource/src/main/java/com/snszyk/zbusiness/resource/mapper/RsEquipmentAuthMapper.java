/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.mapper;

import com.snszyk.zbusiness.resource.entity.RsEquipmentAuth;
import com.snszyk.zbusiness.resource.vo.RsEquipmentAuthVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 设备台账授权 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface RsEquipmentAuthMapper extends BaseMapper<RsEquipmentAuth> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rsEquipmentAuth
	 * @return
	 */
	List<RsEquipmentAuthVo> selectRsEquipmentAuthPage(IPage page, RsEquipmentAuthVo rsEquipmentAuth);

}
