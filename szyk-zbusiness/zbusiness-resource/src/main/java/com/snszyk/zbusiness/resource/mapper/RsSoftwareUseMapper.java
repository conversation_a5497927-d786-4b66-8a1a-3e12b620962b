package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.resource.dto.RsSoftwareUseDto;
import com.snszyk.zbusiness.resource.entity.RsSoftwareUse;
import com.snszyk.zbusiness.resource.vo.RsSoftwareUsePageVo;
import org.apache.ibatis.annotations.Param;

public interface RsSoftwareUseMapper extends BaseMapper<RsSoftwareUse> {

	/**
	 * 自定义分页
	 * @param page

	 * @return
	 */
	IPage<RsSoftwareUseDto> pageList( Page<RsSoftwareUse> page, @Param("vo") RsSoftwareUsePageVo vo);
}
