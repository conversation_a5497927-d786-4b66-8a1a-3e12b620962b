/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.annotation.OperationRecord;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.service.logic.RsInternetLogicService;
import com.snszyk.zbusiness.resource.vo.RsInternetPageVo;
import com.snszyk.zbusiness.resource.vo.RsInternetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 互联网资源台账控制器 - 集成操作记录示例
 * 
 * 此文件展示如何在现有Controller中集成操作记录功能
 * 实际使用时，应该修改原有的RsInternetController
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/internet-example")
@Api(value = "互联网资源台账示例", tags = "互联网资源台账示例接口")
public class RsInternetControllerExample {

    private final RsInternetLogicService rsInternetLogicService;

    /**
     * 保存互联网资产
     * 
     * 使用@OperationRecord注解自动记录操作日志
     * - businessType: 指定业务类型为互联网资产
     * - operationType: 指定操作类型为新增
     * - businessIdExpression: 从返回结果中提取业务ID
     * - businessNameExpression: 从返回结果中提取业务名称（系统名称）
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "保存", notes = "保存互联网资产信息")
    @OperationRecord(
        businessType = BusinessTypeEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.CREATE,
        description = "新增互联网资产",
        businessIdExpression = "#result.data.id",
        businessNameExpression = "#result.data.systemName",
        recordFieldChanges = true,
        async = true
    )
    public R<RsInternetDto> save(@RequestBody RsInternetVo vo) {
        RsInternetDto saved = rsInternetLogicService.saveOrUpdate(vo);
        return R.data(saved);
    }

    /**
     * 更新互联网资产
     * 
     * 对于更新操作，系统会自动比较更新前后的字段变化
     * - businessIdExpression: 从请求参数中获取业务ID
     * - businessNameExpression: 从返回结果中获取业务名称
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "更新", notes = "更新互联网资产信息")
    @OperationRecord(
        businessType = BusinessTypeEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.UPDATE,
        description = "更新互联网资产",
        businessIdExpression = "#vo.id",
        businessNameExpression = "#result.data.systemName",
        recordFieldChanges = true,
        async = true
    )
    public R<RsInternetDto> update(@RequestBody RsInternetVo vo) {
        RsInternetDto updated = rsInternetLogicService.saveOrUpdate(vo);
        return R.data(updated);
    }

    /**
     * 删除互联网资产
     * 
     * 删除操作会记录被删除的数据信息
     * - businessIdExpression: 从请求参数中获取业务ID
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "删除", notes = "删除互联网资产")
    @OperationRecord(
        businessType = BusinessTypeEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.DELETE,
        description = "删除互联网资产",
        businessIdExpression = "#id",
        recordFieldChanges = true,
        async = true
    )
    public R<Boolean> delete(@PathVariable Long id) {
        Boolean result = rsInternetLogicService.deleteById(id);
        return R.data(result);
    }

    /**
     * 查询详情
     * 
     * 查询操作通常不需要记录操作日志，但如果需要可以添加
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "详情", notes = "获取互联网资产详情")
    public R<RsInternetDto> detail(@PathVariable Long id) {
        RsInternetDto detail = rsInternetLogicService.detail(id);
        return R.data(detail);
    }

    /**
     * 分页查询
     * 
     * 分页查询通常不需要记录操作日志
     */
    @PostMapping("/page")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "分页", notes = "分页查询互联网资产")
    public R<IPage<RsInternetDto>> page(@RequestBody RsInternetPageVo vo) {
        IPage<RsInternetDto> pages = rsInternetLogicService.pageList(vo);
        return R.data(pages);
    }

    /**
     * 批量删除
     * 
     * 批量操作的示例，可以记录批量操作的日志
     */
    @PostMapping("/batch-delete")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "批量删除", notes = "批量删除互联网资产")
    @OperationRecord(
        businessType = BusinessTypeEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.DELETE,
        description = "批量删除互联网资产",
        businessNameExpression = "'批量删除' + #ids.size() + '条记录'",
        recordFieldChanges = false,
        async = true
    )
    public R<Boolean> batchDelete(@RequestBody java.util.List<Long> ids) {
        Boolean result = rsInternetLogicService.deleteByIds(ids);
        return R.data(result);
    }

    /**
     * 启用/停用资产
     * 
     * 状态变更操作的示例
     */
    @PostMapping("/toggle-status/{id}")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "启用/停用", notes = "启用或停用互联网资产")
    @OperationRecord(
        businessType = BusinessTypeEnum.INTERNET_ASSET,
        operationType = OperationTypeEnum.UPDATE,
        description = "变更互联网资产状态",
        businessIdExpression = "#id",
        businessNameExpression = "#result.data.systemName",
        recordFieldChanges = true,
        async = true
    )
    public R<RsInternetDto> toggleStatus(@PathVariable Long id, @RequestParam Integer status) {
        RsInternetDto result = rsInternetLogicService.updateStatus(id, status);
        return R.data(result);
    }
}
