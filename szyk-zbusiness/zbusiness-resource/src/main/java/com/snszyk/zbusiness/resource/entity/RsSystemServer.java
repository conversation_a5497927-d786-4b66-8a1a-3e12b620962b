/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信息系统-服务器信息实体类
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RsSystemServer extends BaseCrudEntity {

	/**
	* 系统id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long systemId;
	/**
	* 设备id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long equipmentId;
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String cpuCore;
	/**
	* 服务器角色
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String severRole;
	/**
	* 机器名
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String machineName;
	/**
	* 规划IP
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String planIp;
	/**
	* 内存(G)
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String memory;
	/**
	* 系统盘(G)
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String systemDisk;
	/**
	* 外挂硬盘(G)
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String externalHardDrive;
	/**
	* Web服务器类型
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String middlewareInformation;
	/**
	* 数据库及版本号
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String databaseInformation;
	/**
	* 操作系统类型
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osType;
	/**
	* 操作系统名称及版本号
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osInformation;

	/**
	 * 服务器类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String serverType;

	/**
	 *'部署载体'
	 */

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deployType;

	/**'部署平台'
	 *
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String deployPlatform;

	/**
	 *Web服务器类型，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String webServerType;
	/**
	 * 'Web服务器版本号'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String webServerVersion;

	/**
	 *'其他组件，数据字典'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String otherMiddleware;

	/**
	 *'数据库类型'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String databaseType;

	/**
	 *'数据库版本号'
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String databaseVersion;

	/**
	 *操作系统名称，数据字典
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osName;

	/**
	 *操作系统版本号
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String osVersion;
}
