package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.entity.RsOperationRecordTemplate;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 操作记录模板服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface OperationRecordTemplateService {

    /**
     * 获取业务类型的默认模板
     *
     * @param businessType 业务类型
     * @return 默认模板
     */
    RsOperationRecordTemplate getDefaultTemplate(BusinessTypeEnum businessType);

    /**
     * 获取业务类型的所有模板
     *
     * @param businessType 业务类型
     * @return 模板列表
     */
    List<RsOperationRecordTemplate> getTemplates(BusinessTypeEnum businessType);

    /**
     * 根据模板名称获取模板
     *
     * @param businessType 业务类型
     * @param templateName 模板名称
     * @return 模板
     */
    RsOperationRecordTemplate getTemplateByName(BusinessTypeEnum businessType, String templateName);

    /**
     * 创建模板
     *
     * @param template 模板信息
     * @return 是否成功
     */
    boolean createTemplate(RsOperationRecordTemplate template);

    /**
     * 更新模板
     *
     * @param template 模板信息
     * @return 是否成功
     */
    boolean updateTemplate(RsOperationRecordTemplate template);

    /**
     * 删除模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean deleteTemplate(Long templateId);

    /**
     * 设置默认模板
     *
     * @param businessType 业务类型
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean setDefaultTemplate(BusinessTypeEnum businessType, Long templateId);

    /**
     * 解析模板配置
     *
     * @param template 模板
     * @return 解析后的配置
     */
    Map<String, Object> parseTemplateConfig(RsOperationRecordTemplate template);

    /**
     * 验证模板配置
     *
     * @param businessType 业务类型
     * @param templateConfig 模板配置
     * @return 验证结果
     */
    Map<String, String> validateTemplateConfig(BusinessTypeEnum businessType, String templateConfig);

    /**
     * 应用模板配置到操作记录
     *
     * @param template 模板
     * @param operationData 操作数据
     * @return 应用模板后的数据
     */
    Map<String, Object> applyTemplate(RsOperationRecordTemplate template, Map<String, Object> operationData);

    /**
     * 获取模板的字段配置
     *
     * @param template 模板
     * @return 字段配置列表
     */
    List<Map<String, Object>> getTemplateFieldConfigs(RsOperationRecordTemplate template);

    /**
     * 复制模板
     *
     * @param sourceTemplateId 源模板ID
     * @param newTemplateName 新模板名称
     * @param newDescription 新模板描述
     * @return 新模板
     */
    RsOperationRecordTemplate copyTemplate(Long sourceTemplateId, String newTemplateName, String newDescription);

    /**
     * 导出模板配置
     *
     * @param templateId 模板ID
     * @return 模板配置JSON
     */
    String exportTemplateConfig(Long templateId);

    /**
     * 导入模板配置
     *
     * @param businessType 业务类型
     * @param templateName 模板名称
     * @param configJson 配置JSON
     * @return 导入的模板
     */
    RsOperationRecordTemplate importTemplateConfig(BusinessTypeEnum businessType, String templateName, String configJson);
}
