/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.entity.RsInternet;
import com.snszyk.zbusiness.resource.mapper.RsInternetMapper;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import com.snszyk.zbusiness.resource.vo.LockUnitVo;
import com.snszyk.zbusiness.resource.vo.RsInternetPageVo;
import com.snszyk.zbusiness.resource.vo.RsInternetStatusVo;
import com.snszyk.zbusiness.resource.vo.RsInternetVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 互联网资源台账 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@AllArgsConstructor
@Service
public class RsInternetServiceImpl extends BaseCrudServiceImpl<RsInternetMapper, RsInternet, RsInternetDto, RsInternetVo> implements IRsInternetService {


	@Override
	public Page<RsInternetDto> pageList(RsInternetPageVo v) {

		return this.baseMapper.pageList(new Page<>(v.getCurrent(), v.getSize()), v);
	}

	@Override
	public Boolean removeByName(String systemName) {
		return this.lambdaUpdate().eq(RsInternet::getSystemName, systemName).remove();
	}

	@Override
	public Boolean saveBatch(List<RsInternetVo> cachedDataList) {
		List<RsInternet> copy = BeanUtil.copy(cachedDataList, RsInternet.class);
		return saveBatch(copy);
	}

	@Override
	public List<RsInternetDto> listByIds(List<Long> internetIds) {
		if (CollectionUtil.isEmpty(internetIds)) {
			return new ArrayList<>();
		}
		List<RsInternet> list = this.lambdaQuery().in(BaseCrudEntity::getId, internetIds).list();
		return BeanUtil.copy(list, RsInternetDto.class);
	}

	@Override
	public boolean updateNUllAssociation(Long id) {
		return this.lambdaUpdate().eq(RsInternet::getId, id).set(RsInternet::getAssociationNo, null)
			.set(RsInternet::getAssociationId, null)
			.update();
	}

	@Override
	public List<RsInternetDto> listBy(List<LockUnitVo> unitVoList) {
		return this.baseMapper.listBy(unitVoList);
	}

    @Override
    public List<RsInternetDto> listAuthList() {
		List<RsInternet> list = this.lambdaQuery().isNotNull(RsInternet::getAuthOrg).list();
		return BeanUtil.copy(list, RsInternetDto.class);
    }

    @Override
	public Boolean updateResourceStatus(RsInternetStatusVo vo) {
		return this.lambdaUpdate().eq(BaseCrudEntity::getId, vo.getId())
			.set(RsInternet::getResourceStatus, vo.getResourceStatus())
			.update();
	}

	@Override
	public List<RsInternetDto> listByAssociation(String associationType, Long associationId) {
		List<RsInternet> list = this.lambdaQuery().eq(RsInternet::getAssociationType, associationType)
			.eq(RsInternet::getAssociationId, associationId)
			.orderByDesc(BaseCrudEntity::getCreateTime)
			.list();
		return BeanUtil.copy(list, RsInternetDto.class);
	}
}
