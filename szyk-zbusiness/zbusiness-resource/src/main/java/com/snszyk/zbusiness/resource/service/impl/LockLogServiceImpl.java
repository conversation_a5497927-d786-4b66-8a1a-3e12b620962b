/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.resource.dto.LockLogDto;
import com.snszyk.zbusiness.resource.entity.LockLog;
import com.snszyk.zbusiness.resource.enums.OperateActionEnum;
import com.snszyk.zbusiness.resource.mapper.LockLogMapper;
import com.snszyk.zbusiness.resource.service.ILockLogService;
import com.snszyk.zbusiness.resource.vo.LockLogVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资源锁定操作日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@AllArgsConstructor
@Service
public class LockLogServiceImpl extends BaseCrudServiceImpl<LockLogMapper, LockLog, LockLogDto, LockLogVo> implements ILockLogService {


	@Override
	public Boolean saveOrUpdateBatch(List<LockLogVo> lockLogVoList) {
		return super.saveOrUpdateBatch(BeanUtil.copy(lockLogVoList, LockLog.class));
	}

	@Override
	public LockLogDto getLastOne(String targetResource, Long orgId, String operateActionCode) {

		List<LockLog> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(targetResource), LockLog::getTargetResource, targetResource)
			.like(ObjectUtil.isNotEmpty(orgId), LockLog::getTargetOrgId, orgId)
			.eq(ObjectUtil.isNotEmpty(operateActionCode), LockLog::getOperateAction, operateActionCode)
			.orderByDesc(LockLog::getCreateTime)
			.list();
		return ObjectUtil.isEmpty(list) ? null : BeanUtil.copy(list.get(0), LockLogDto.class);
	}

	@Override
	public List<LockLogDto> listBy(List<Long> orgIdList, String targetResource, OperateActionEnum operateCode) {
		return this.baseMapper.listBy(orgIdList, targetResource, operateCode.getCode());
	}

}
