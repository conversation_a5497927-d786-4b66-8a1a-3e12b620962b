package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.service.FieldSemanticProcessor;
import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 默认字段语义处理器实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
public class DefaultFieldSemanticProcessor implements FieldSemanticProcessor {

    private final ApplicationContext applicationContext;
    private final List<FieldValueConverter> fieldValueConverters;

    @Override
    public String getFieldSemanticName(BusinessTypeEnum businessType, String fieldName) {
        if (StringUtil.isBlank(fieldName)) {
            return fieldName;
        }

        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Map<String, String> fieldLabelMap = fieldConfig.getFieldLabelMap();
            return fieldLabelMap.getOrDefault(fieldName, fieldName);
        } catch (Exception e) {
            return fieldName;
        }
    }

    @Override
    public String convertFieldValueToDisplay(BusinessTypeEnum businessType, String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        // 设置当前业务类型到ThreadLocal，供业务感知的转换器使用
        BusinessAwareDictFieldValueConverter.setCurrentBusinessType(businessType);

        try {
            // 按优先级排序转换器
            List<FieldValueConverter> sortedConverters = fieldValueConverters.stream()
                .sorted(Comparator.comparingInt(FieldValueConverter::getPriority))
                .collect(Collectors.toList());

            // 使用字段值转换器进行转换
            for (FieldValueConverter converter : sortedConverters) {
                if (converter.supports(fieldName)) {
                    return converter.convert(fieldName, fieldValue);
                }
            }

            // 如果没有专门的转换器，返回原值
            return fieldValue.trim().isEmpty() ? "--" : fieldValue;
        } finally {
            // 清除ThreadLocal
            BusinessAwareDictFieldValueConverter.clearCurrentBusinessType();
        }
    }

    @Override
    public void processChangeDetailsDisplay(BusinessTypeEnum businessType, List<RsOperationRecordDetailDto> detailList) {
        if (CollectionUtil.isEmpty(detailList)) {
            return;
        }

        for (RsOperationRecordDetailDto detail : detailList) {
            // 设置字段的语义名称
            String semanticName = getFieldSemanticName(businessType, detail.getFieldName());
            detail.setFieldLabel(semanticName);

            // 转换显示值
            String oldDisplayValue = convertFieldValueToDisplay(businessType, detail.getFieldName(), detail.getOldValue());
            String newDisplayValue = convertFieldValueToDisplay(businessType, detail.getFieldName(), detail.getNewValue());

            detail.setOldDisplayValue(oldDisplayValue);
            detail.setNewDisplayValue(newDisplayValue);
        }
    }

    @Override
    public boolean isSensitiveField(BusinessTypeEnum businessType, String fieldName) {
        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            List<String> sensitiveFields = fieldConfig.getSensitiveFields();
            return sensitiveFields.contains(fieldName);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String getFieldDataType(BusinessTypeEnum businessType, String fieldName) {
        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Map<String, String> fieldTypeMap = fieldConfig.getFieldTypeMap();
            return fieldTypeMap.getOrDefault(fieldName, "String");
        } catch (Exception e) {
            return "String";
        }
    }

    @Override
    public String getFieldGroup(BusinessTypeEnum businessType, String fieldName) {
        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Map<String, String> fieldGroupMap = fieldConfig.getFieldGroupMap();
            return fieldGroupMap.getOrDefault(fieldName, "基本信息");
        } catch (Exception e) {
            // 如果配置获取失败，使用默认推断逻辑
            if (fieldName.contains("org") || fieldName.contains("dept")) {
                return "组织信息";
            } else if (fieldName.contains("contact") || fieldName.contains("person")) {
                return "联系信息";
            } else if (fieldName.contains("security") || fieldName.contains("level")) {
                return "安全信息";
            } else if (fieldName.contains("status") || fieldName.contains("state")) {
                return "状态信息";
            } else {
                return "基本信息";
            }
        }
    }

    /**
     * 获取字段配置
     */
    private BusinessFieldConfig getFieldConfig(BusinessTypeEnum businessType) {
        try {
            String configClassName = businessType.getFieldConfigClassName();
            return applicationContext.getBean(configClassName, BusinessFieldConfig.class);
        } catch (Exception e) {
            return applicationContext.getBean("defaultFieldConfig", BusinessFieldConfig.class);
        }
    }
}
