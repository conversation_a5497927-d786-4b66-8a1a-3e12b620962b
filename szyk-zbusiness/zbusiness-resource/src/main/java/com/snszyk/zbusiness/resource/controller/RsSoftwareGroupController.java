package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDeleteDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupPageDto;
import com.snszyk.zbusiness.resource.service.IRsSoftwareGroupService;
import com.snszyk.zbusiness.resource.service.IRsSoftwareService;
import com.snszyk.zbusiness.resource.service.logic.RsSoftwareGroupService;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupPageVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupStatusVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 软件正版化工作领导小组 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/software-group")
@Api(value = "软件正版化工作领导小组", tags = "软件正版化工作领导小组接口")
@Slf4j
public class RsSoftwareGroupController extends BaseCrudController {

	private final RsSoftwareGroupService rsSoftwareGroupService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsSoftwareGroupService;
	}

	@PostMapping("/page")
	@ApiOperation(value = "分页")
	public R<IPage<RsSoftwareGroupPageDto>> page(@RequestBody RsSoftwareGroupPageVo vo) {
		IPage<RsSoftwareGroupPageDto> pageQueryResult = rsSoftwareGroupService.page(vo);
		return R.data(pageQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IRsSoftwareGroupService.class,paramName = "orgId" )
	public R<RsSoftwareGroupDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		RsSoftwareGroupDto baseCrudDto = rsSoftwareGroupService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存/编辑")
	public R<RsSoftwareGroupDto> save(@RequestBody RsSoftwareGroupVo vo) {
		RsSoftwareGroupDto baseCrudDto = rsSoftwareGroupService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody RsSoftwareGroupStatusVo vo) {
		Boolean result = rsSoftwareGroupService.status(vo);
		return R.data(result);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsSoftwareGroupDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = rsSoftwareGroupService.delete(vo);
		return R.data(result);
	}

}
