/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsSoftwareUseDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareUsePageDto;
import com.snszyk.zbusiness.resource.entity.RsSoftwareUse;
import com.snszyk.zbusiness.resource.mapper.RsSoftwareUseMapper;
import com.snszyk.zbusiness.resource.service.IRsSoftwareUseService;
import com.snszyk.zbusiness.resource.vo.RsSoftwareUsePageVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareUseVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * RsSoftwareUseServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSoftwareUseServiceImpl extends BaseCrudServiceImpl<RsSoftwareUseMapper, RsSoftwareUse, RsSoftwareUseDto, RsSoftwareUseVo> implements IRsSoftwareUseService {

	private RsSoftwareUseMapper rsSoftwareUseMapper;

	@Override
	public IPage<RsSoftwareUsePageDto> pageList(RsSoftwareUsePageVo vo) {
		IPage<RsSoftwareUseDto> page = rsSoftwareUseMapper
			.pageList(new Page<>(vo.getCurrent(), vo.getSize()), vo);
		if (page == null) {
			return null;
		}
		return page.convert(RsSoftwareUse -> BeanUtil.copyProperties(RsSoftwareUse, RsSoftwareUsePageDto.class));
	}

	@Override
	public RsSoftwareUseDto update(RsSoftwareUseVo vo) {
		RsSoftwareUse entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, RsSoftwareUseDto.class);
		} else {
			return null;
		}
	}
}
