/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.aspect;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.annotations.InternetAssetOperationLog;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.service.IInternetAssetOperationLogService;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 互联网资产操作日志切面
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Aspect
@Component
@AllArgsConstructor
@Slf4j
public class InternetAssetOperationLogAspect {

    private final IInternetAssetOperationLogService operationLogService;
    private final IRsInternetService rsInternetService;

    @Pointcut("@annotation(com.snszyk.zbusiness.resource.annotations.InternetAssetOperationLog)")
    public void operationLogPointcut() {
    }

    @Around("operationLogPointcut() && @annotation(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, InternetAssetOperationLog operationLog) throws Throwable {
        Object result = null;
        Object oldData = null;
        Exception exception = null;

        try {
            // 获取操作前数据
            oldData = getOldData(joinPoint, operationLog);

            // 执行目标方法
            result = joinPoint.proceed();

            // 记录操作日志
            recordOperationLog(joinPoint, operationLog, oldData, result, null);

        } catch (Exception e) {
            exception = e;
            // 记录异常操作日志
            recordOperationLog(joinPoint, operationLog, oldData, result, e);
            throw e;
        }

        return result;
    }

    /**
     * 获取操作前数据
     */
    private Object getOldData(ProceedingJoinPoint joinPoint, InternetAssetOperationLog annotation) {
        if ("DELETE".equals(annotation.operationType()) || !annotation.recordDetails()) {
            return null;
        }

        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object firstArg = args[0];
                Long businessId = getFieldValue(firstArg, annotation.businessIdField(), Long.class);
                if (businessId != null) {
                    return rsInternetService.getById(businessId);
                }
            }
        } catch (Exception e) {
            log.warn("获取操作前数据失败", e);
        }
        return null;
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, InternetAssetOperationLog annotation,
                                   Object oldData, Object result, Exception exception) {
        try {
            // 获取业务信息
            Object[] args = joinPoint.getArgs();
            if (args.length == 0) {
                return;
            }

            Object firstArg = args[0];
            Long businessId = getFieldValue(firstArg, annotation.businessIdField(), Long.class);
            String businessName = getFieldValue(firstArg, annotation.businessNameField(), String.class);
            Long orgId = getFieldValue(firstArg, annotation.orgIdField(), Long.class);
            String orgName = getFieldValue(firstArg, annotation.orgNameField(), String.class);

            // 如果从参数中获取不到，尝试从结果中获取
            if (result instanceof RsInternetDto) {
                RsInternetDto dto = (RsInternetDto) result;
                if (businessId == null) businessId = dto.getId();
                if (StringUtil.isBlank(businessName)) businessName = dto.getSystemName();
                if (orgId == null) orgId = dto.getOrgId();
                if (StringUtil.isBlank(orgName)) orgName = dto.getOrgName();
            }

            // 获取操作后数据
            Object newData = getNewData(annotation, result, businessId);

            // 构建操作描述
            String description = buildDescription(annotation, businessName, exception);

            // 创建操作日志
            operationLogService.createOperationLog(
                businessId, businessName, annotation.operationType(),
                description, oldData, newData, orgId, orgName
            );

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 获取操作后数据
     */
    private Object getNewData(InternetAssetOperationLog annotation, Object result, Long businessId) {
        if ("DELETE".equals(annotation.operationType())) {
            return null;
        }

        // 如果返回结果包含完整数据，直接使用
        if (result instanceof RsInternetDto) {
            return result;
        }

        // 否则重新查询
        if (businessId != null) {
            try {
                return rsInternetService.getById(businessId);
            } catch (Exception e) {
                log.warn("获取操作后数据失败", e);
            }
        }
        return null;
    }

    /**
     * 构建操作描述
     */
    private String buildDescription(InternetAssetOperationLog annotation, String businessName, Exception exception) {
        StringBuilder desc = new StringBuilder();
        
        if (StringUtil.isNotBlank(annotation.description())) {
            desc.append(annotation.description());
        } else {
            switch (annotation.operationType()) {
                case "CREATE":
                    desc.append("创建互联网资产");
                    break;
                case "UPDATE":
                    desc.append("修改互联网资产");
                    break;
                case "DELETE":
                    desc.append("删除互联网资产");
                    break;
                default:
                    desc.append("操作互联网资产");
            }
        }

        if (StringUtil.isNotBlank(businessName)) {
            desc.append("：").append(businessName);
        }

        if (exception != null) {
            desc.append("（操作失败）");
        }

        return desc.toString();
    }

    /**
     * 获取字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> targetType) {
        if (obj == null || StringUtil.isBlank(fieldName)) {
            return null;
        }

        try {
            // 先尝试直接获取字段
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            
            if (value != null && targetType.isAssignableFrom(value.getClass())) {
                return (T) value;
            }
        } catch (Exception e) {
            // 如果直接获取失败，尝试通过getter方法
            try {
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                Object value = obj.getClass().getMethod(getterName).invoke(obj);
                
                if (value != null && targetType.isAssignableFrom(value.getClass())) {
                    return (T) value;
                }
            } catch (Exception ex) {
                log.debug("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), fieldName);
            }
        }
        
        return null;
    }
}
