package com.snszyk.zbusiness.resource.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.resource.dto.RsSoftwareFileDto;
import com.snszyk.zbusiness.resource.service.IRsSoftwareFileService;
import com.snszyk.zbusiness.resource.vo.RsSoftwareFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * RsSoftwareFileService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSoftwareFileService extends BaseCrudLogicService<RsSoftwareFileDto, RsSoftwareFileVo> {

	private final IRsSoftwareFileService rsSoftwareFileService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rsSoftwareFileService;
	}


}
