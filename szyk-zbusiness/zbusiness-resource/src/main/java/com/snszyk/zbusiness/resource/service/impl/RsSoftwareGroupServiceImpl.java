/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupPageDto;
import com.snszyk.zbusiness.resource.entity.RsSoftwareGroup;
import com.snszyk.zbusiness.resource.mapper.RsSoftwareGroupMapper;
import com.snszyk.zbusiness.resource.service.IRsSoftwareGroupService;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupPageVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * RsSoftwareGroupServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSoftwareGroupServiceImpl extends BaseCrudServiceImpl<RsSoftwareGroupMapper, RsSoftwareGroup, RsSoftwareGroupDto, RsSoftwareGroupVo> implements IRsSoftwareGroupService {

	private final RsSoftwareGroupMapper rsSoftwareGroupMapper;

	@Override
	public IPage<RsSoftwareGroupPageDto> pageList(RsSoftwareGroupPageVo vo) {

		IPage<RsSoftwareGroupDto> page = rsSoftwareGroupMapper
			.pageList(new Page<>(vo.getCurrent(), vo.getSize()),vo);
		if (page == null) {
			return null;
		}

		return page.convert(RsSoftwareGroup -> BeanUtil.copyProperties(RsSoftwareGroup, RsSoftwareGroupPageDto.class));
	}

	@Override
	public boolean updateByStatus(Long id, String groupStatus) {
		boolean result = this.lambdaUpdate()
			.eq(ObjectUtils.isNotEmpty(id), RsSoftwareGroup::getId, id)
			.set(StringUtils.isNotEmpty(groupStatus), RsSoftwareGroup::getGroupStatus, groupStatus)
			.update();

		return result;
	}

	@Override
	public RsSoftwareGroupDto update(RsSoftwareGroupVo vo) {
		RsSoftwareGroup entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, RsSoftwareGroupDto.class);
		} else {
			return null;
		}
	}
}
