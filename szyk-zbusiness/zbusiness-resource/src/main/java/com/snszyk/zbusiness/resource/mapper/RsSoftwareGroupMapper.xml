<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.RsSoftwareGroupMapper">


    <select id="pageList" resultType="com.snszyk.zbusiness.resource.dto.RsSoftwareGroupDto">
        SELECT
        rsg.id, rsg.org_id, rsg.org_name, rsg.full_org_id, rsg.full_org_name, rsg.form_person_id, rsg.form_person_name,
        rsg.phone_no, rsg.form_date, rsg.group_status, rsg.remark, rsg.create_user, rsg.create_dept, rsg.create_time,
        rsg.update_user, rsg.update_time, rsg.status, rsg.is_deleted
        FROM
        `rs_software_group` rsg
        left join szyk_dept sd1 on sd1.id=rsg.org_id and sd1.is_deleted=0
        left join szyk_dept udept on sd1.unit_id = udept.id
        WHERE rsg.is_deleted = 0
        <if test="vo.groupStatus !=null and vo.groupStatus !='' ">
            AND rsg.group_status = #{vo.groupStatus}
        </if>
        <if test="vo.unitRange ==1 ">
            AND rsg.org_id = #{vo.orgId}
        </if>
        <if test="vo.unitRange ==2 ">
            AND (
            sd1.full_org_id like concat('%',#{vo.orgId},'%')
            or rsg.org_id=#{vo.orgId}
            )
        </if>
        ORDER BY
        udept.sort asc,sd1.sort asc,sd1.unit_level asc, sd1.id asc,  rsg.create_time DESC
    </select>

</mapper>
