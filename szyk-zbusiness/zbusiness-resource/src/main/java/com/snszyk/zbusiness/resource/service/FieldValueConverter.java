package com.snszyk.zbusiness.resource.service;

/**
 * 字段值转换器接口
 * 用于将原始字段值转换为用户友好的显示值
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface FieldValueConverter {

    /**
     * 转换字段值
     * 
     * @param fieldName 字段名
     * @param fieldValue 原始字段值
     * @return 转换后的显示值
     */
    String convert(String fieldName, String fieldValue);

    /**
     * 判断是否支持该字段的转换
     * 
     * @param fieldName 字段名
     * @return 是否支持
     */
    boolean supports(String fieldName);

    /**
     * 获取转换器的优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    int getPriority();
}
