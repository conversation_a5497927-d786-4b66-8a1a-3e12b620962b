/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.entity.OperationLog;
import com.snszyk.system.service.IOperationLogService;
import com.snszyk.zbusiness.resource.enums.BusinessModuleEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.dto.FieldChangeDetailDto;
import com.snszyk.zbusiness.resource.dto.InternetAssetOperationLogDto;
import com.snszyk.zbusiness.resource.service.IInternetAssetOperationLogService;
import com.snszyk.zbusiness.resource.vo.InternetAssetOperationLogVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 互联网资产操作日志服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
@Slf4j
public class InternetAssetOperationLogServiceImpl implements IInternetAssetOperationLogService {

    private final IOperationLogService operationLogService;

    /**
     * 互联网资产字段映射
     */
    private static final Map<String, String> FIELD_LABEL_MAP = new HashMap<>();

    static {
        FIELD_LABEL_MAP.put("systemName", "系统名称");
        FIELD_LABEL_MAP.put("orgName", "主管单位");
        FIELD_LABEL_MAP.put("internetAddress", "互联网地址");
        FIELD_LABEL_MAP.put("networkAddress", "内网地址");
        FIELD_LABEL_MAP.put("securityLevel", "等保级别");
        FIELD_LABEL_MAP.put("contactPerson", "负责人");
        FIELD_LABEL_MAP.put("contactPhone", "联系方式");
        FIELD_LABEL_MAP.put("domainName", "域名或URL");
        FIELD_LABEL_MAP.put("internetType", "资源类型");
        FIELD_LABEL_MAP.put("applicationType", "应用类型");
        FIELD_LABEL_MAP.put("resourceStatus", "资源状态");
        FIELD_LABEL_MAP.put("isPublicCloud", "是否公有云部署");
        FIELD_LABEL_MAP.put("publicCloudSupplier", "公有云供应商");
        FIELD_LABEL_MAP.put("remark", "备注");
    }

    @Override
    public IPage<InternetAssetOperationLogDto> page(InternetAssetOperationLogVo vo) {
        IPage<OperationLog> page = new Page<>(vo.getCurrent(), vo.getSize());
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();

        // 固定查询互联网资产模块
        queryWrapper.eq(OperationLog::getBusinessModule, BusinessModuleEnum.INTERNET_ASSET.getCode());

        // 构建查询条件
        if (vo.getOrgId() != null) {
            queryWrapper.eq(OperationLog::getOrgId, vo.getOrgId());
        }

        if (StringUtil.isNotBlank(vo.getSystemName())) {
            queryWrapper.like(OperationLog::getBusinessName, vo.getSystemName());
        }

        if (StringUtil.isNotBlank(vo.getOperationType())) {
            queryWrapper.eq(OperationLog::getOperationType, vo.getOperationType());
        }

        if (vo.getStartUpdateTime() != null) {
            queryWrapper.ge(OperationLog::getOperationTime, vo.getStartUpdateTime());
        }

        if (vo.getEndUpdateTime() != null) {
            queryWrapper.le(OperationLog::getOperationTime, vo.getEndUpdateTime());
        }

        // 按操作时间倒序
        queryWrapper.orderByDesc(OperationLog::getOperationTime);

        IPage<OperationLog> operationLogPage = operationLogService.page(page, queryWrapper);

        // 转换为DTO
        return operationLogPage.convert(this::convertToDto);
    }

    @Override
    public List<InternetAssetOperationLogDto> listByBusinessId(Long businessId) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationLog::getBusinessModule, BusinessModuleEnum.INTERNET_ASSET.getCode())
                   .eq(OperationLog::getBusinessId, businessId)
                   .orderByDesc(OperationLog::getOperationTime);

        List<OperationLog> operationLogs = operationLogService.list(queryWrapper);
        return operationLogs.stream()
                           .map(this::convertToDto)
                           .collect(Collectors.toList());
    }

    @Override
    public InternetAssetOperationLogDto getDetail(Long id) {
        OperationLog operationLog = operationLogService.getById(id);
        if (operationLog == null) {
            return null;
        }

        InternetAssetOperationLogDto dto = convertToDto(operationLog);

        // 解析变更详情
        if (StringUtil.isNotBlank(operationLog.getChangeFields()) &&
            StringUtil.isNotBlank(operationLog.getOldData()) &&
            StringUtil.isNotBlank(operationLog.getNewData())) {

            List<FieldChangeDetailDto> changeDetails = parseChangeDetails(
                operationLog.getChangeFields(),
                operationLog.getOldData(),
                operationLog.getNewData()
            );
            dto.setChangeDetails(changeDetails);
        }

        return dto;
    }

    @Override
    public List<FieldChangeDetailDto> getChangeDetails(Long id) {
        OperationLog operationLog = operationLogService.getById(id);
        if (operationLog == null || StringUtil.isBlank(operationLog.getChangeFields())) {
            return Collections.emptyList();
        }

        return parseChangeDetails(
            operationLog.getChangeFields(),
            operationLog.getOldData(),
            operationLog.getNewData()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOperationLog(Long businessId, String businessName, String operationType,
                                    String description, Object oldData, Object newData,
                                    Long orgId, String orgName) {
        try {
            OperationLog operationLog = new OperationLog();

            // 设置基本信息
            operationLog.setBusinessModule(BusinessModuleEnum.INTERNET_ASSET.getCode());
            operationLog.setOperationType(operationType);
            operationLog.setOperationDescription(description);
            operationLog.setBusinessId(businessId);
            operationLog.setBusinessName(businessName);
            operationLog.setOrgId(orgId);
            operationLog.setOrgName(orgName);

            // 设置操作人信息
            operationLog.setOperatorId(AuthUtil.getUserId());
            operationLog.setOperatorName(AuthUtil.getUserName());
            operationLog.setOperatorDeptId(Long.valueOf(AuthUtil.getDeptId()));
            operationLog.setOperatorDeptName(AuthUtil.getDeptName());

            // 设置操作时间
            operationLog.setOperationTime(LocalDateTime.now());

            // 设置数据变更信息
            if (oldData != null) {
                operationLog.setOldData(JSON.toJSONString(oldData));
            }
            if (newData != null) {
                operationLog.setNewData(JSON.toJSONString(newData));
            }

            // 计算变更字段
            if (oldData != null && newData != null) {
                String changeFields = calculateChangeFields(oldData, newData);
                operationLog.setChangeFields(changeFields);
            }

            return operationLogService.save(operationLog);
        } catch (Exception e) {
            log.error("创建操作日志失败", e);
            return false;
        }
    }

    /**
     * 转换为DTO
     */
    private InternetAssetOperationLogDto convertToDto(OperationLog operationLog) {
        InternetAssetOperationLogDto dto = BeanUtil.copy(operationLog, InternetAssetOperationLogDto.class);

        // 设置枚举名称
        if (StringUtil.isNotBlank(operationLog.getBusinessModule())) {
            BusinessModuleEnum businessModule = BusinessModuleEnum.getByCode(operationLog.getBusinessModule());
            if (businessModule != null) {
                dto.setBusinessModuleName(businessModule.getName());
            }
        }

        if (StringUtil.isNotBlank(operationLog.getOperationType())) {
            OperationTypeEnum operationType = OperationTypeEnum.getByCode(operationLog.getOperationType());
            if (operationType != null) {
                dto.setOperationTypeName(operationType.getName());
            }
        }

        return dto;
    }

    /**
     * 解析变更详情
     */
    private List<FieldChangeDetailDto> parseChangeDetails(String changeFields, String oldDataStr, String newDataStr) {
        List<FieldChangeDetailDto> details = new ArrayList<>();

        try {
            String[] fields = changeFields.split(",");
            JSONObject oldData = JSON.parseObject(oldDataStr);
            JSONObject newData = JSON.parseObject(newDataStr);

            for (String field : fields) {
                field = field.trim();
                if (StringUtil.isBlank(field)) {
                    continue;
                }

                String fieldLabel = FIELD_LABEL_MAP.getOrDefault(field, field);
                String oldValue = oldData.getString(field);
                String newValue = newData.getString(field);

                // 只记录有变化的字段
                if (!Objects.equals(oldValue, newValue)) {
                    FieldChangeDetailDto detail = new FieldChangeDetailDto(
                        field, fieldLabel,
                        oldValue != null ? oldValue : "--",
                        newValue != null ? newValue : "--"
                    );
                    details.add(detail);
                }
            }
        } catch (Exception e) {
            log.error("解析变更详情失败", e);
        }

        return details;
    }

    /**
     * 计算变更字段
     */
    private String calculateChangeFields(Object oldData, Object newData) {
        try {
            JSONObject oldJson = JSON.parseObject(JSON.toJSONString(oldData));
            JSONObject newJson = JSON.parseObject(JSON.toJSONString(newData));

            Set<String> changedFields = new HashSet<>();

            // 检查所有字段
            Set<String> allFields = new HashSet<>();
            allFields.addAll(oldJson.keySet());
            allFields.addAll(newJson.keySet());

            for (String field : allFields) {
                Object oldValue = oldJson.get(field);
                Object newValue = newJson.get(field);

                if (!Objects.equals(oldValue, newValue)) {
                    changedFields.add(field);
                }
            }

            return String.join(",", changedFields);
        } catch (Exception e) {
            log.error("计算变更字段失败", e);
            return "";
        }
    }
}
