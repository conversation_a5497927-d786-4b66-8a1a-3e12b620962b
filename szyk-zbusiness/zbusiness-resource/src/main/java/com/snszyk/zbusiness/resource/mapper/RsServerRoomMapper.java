package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.resource.dto.RsServerRoomDto;
import com.snszyk.zbusiness.resource.entity.RsServerRoom;
import com.snszyk.zbusiness.resource.entity.RsSoftwareGroup;
import com.snszyk.zbusiness.resource.vo.RsServerRoomPageVo;
import com.snszyk.zbusiness.stat.dto.HeadOwnerResourceStatDto;
import com.snszyk.zbusiness.stat.vo.HomeProjectStatVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RsServerRoomMapper extends BaseMapper<RsServerRoom> {


	/**
	 * 自定义分页
	 *
	 * @param page

	 * @return
	 */
	IPage<RsServerRoomDto> pageList(Page<RsSoftwareGroup> page, @Param("vo") RsServerRoomPageVo vo);

	/**
	 * 列表
	 *
	 * @param companyIdList
	 * @param companyName
	 * @param roomName
	 * @param roomLevel
	 * @param roomUse
	 * @param roomStatus
	 * @return
	 */
	List<RsServerRoom> listByPage(@Param("companyIdList") List<Long> companyIdList, @Param("companyName") String companyName, @Param("roomName") String roomName,
								  @Param("roomLevel") String roomLevel, @Param("roomUse") String roomUse, @Param("roomStatus") String roomStatus);

	/**
	 *查询单位内是否重名
	 * @param roomName
	 * @param unitId
	 * @return
	 */
    List<RsServerRoomDto> listByNameAndUnitId(@Param("roomName") String roomName, @Param("unitId") Long unitId);

	/**
	 * 数量统计
	 * @param v
	 * @return
	 */
    Integer countStat(@Param("v") HomeProjectStatVo v);

    HeadOwnerResourceStatDto countHeadOwenrStat(@Param("orgId") Long orgId);
}
