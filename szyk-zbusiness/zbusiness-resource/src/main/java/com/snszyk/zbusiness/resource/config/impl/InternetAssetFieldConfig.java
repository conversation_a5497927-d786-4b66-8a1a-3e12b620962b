package com.snszyk.zbusiness.resource.config.impl;

import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 互联网资产字段配置实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component("internetAssetFieldConfig")
public class InternetAssetFieldConfig implements BusinessFieldConfig {

    private static final Map<String, String> FIELD_LABEL_MAP = new HashMap<>();
    private static final Map<String, String> FIELD_TYPE_MAP = new HashMap<>();
    private static final List<String> QUERYABLE_FIELDS = new ArrayList<>();
    private static final List<String> IGNORE_FIELDS = Arrays.asList(
        "createTime", "updateTime", "createUser", "updateUser", "createDept", "updateDept"
    );

    static {
        // 字段标签映射
        FIELD_LABEL_MAP.put("systemName", "系统名称");
        FIELD_LABEL_MAP.put("internetAddress", "互联网地址");
        FIELD_LABEL_MAP.put("networkAddress", "内网地址");
        FIELD_LABEL_MAP.put("contactPerson", "负责人");
        FIELD_LABEL_MAP.put("contactPhone", "联系方式");
        FIELD_LABEL_MAP.put("securityLevel", "等保级别");
        FIELD_LABEL_MAP.put("resourceStatus", "资源状态");
        FIELD_LABEL_MAP.put("orgName", "主管单位");
        FIELD_LABEL_MAP.put("internetType", "资源类型");
        FIELD_LABEL_MAP.put("applicationType", "应用类型");
        FIELD_LABEL_MAP.put("domainName", "域名或URL");
        FIELD_LABEL_MAP.put("isPublicCloud", "是否公有云部署");
        FIELD_LABEL_MAP.put("publicCloudSupplier", "公有云供应商");
        FIELD_LABEL_MAP.put("remark", "备注");
        FIELD_LABEL_MAP.put("orgId", "主管单位ID");
        FIELD_LABEL_MAP.put("fullOrgId", "组织全路径ID");
        FIELD_LABEL_MAP.put("fullOrgName", "组织全路径名称");

        // 字段类型映射
        FIELD_TYPE_MAP.put("systemName", "STRING");
        FIELD_TYPE_MAP.put("internetAddress", "STRING");
        FIELD_TYPE_MAP.put("networkAddress", "STRING");
        FIELD_TYPE_MAP.put("contactPerson", "STRING");
        FIELD_TYPE_MAP.put("contactPhone", "STRING");
        FIELD_TYPE_MAP.put("securityLevel", "DICT");
        FIELD_TYPE_MAP.put("resourceStatus", "DICT");
        FIELD_TYPE_MAP.put("orgName", "STRING");
        FIELD_TYPE_MAP.put("internetType", "DICT");
        FIELD_TYPE_MAP.put("applicationType", "DICT");
        FIELD_TYPE_MAP.put("domainName", "STRING");
        FIELD_TYPE_MAP.put("isPublicCloud", "BOOLEAN");
        FIELD_TYPE_MAP.put("publicCloudSupplier", "STRING");
        FIELD_TYPE_MAP.put("remark", "TEXT");
        FIELD_TYPE_MAP.put("orgId", "LONG");

        // 可查询字段
        QUERYABLE_FIELDS.addAll(Arrays.asList(
            "systemName", "internetAddress", "orgName", "resourceStatus",
            "securityLevel", "internetType", "applicationType", "contactPerson"
        ));
    }

    @Override
    public Map<String, String> getFieldLabelMap() {
        return Collections.unmodifiableMap(FIELD_LABEL_MAP);
    }

    @Override
    public Map<String, String> getFieldTypeMap() {
        return Collections.unmodifiableMap(FIELD_TYPE_MAP);
    }

    @Override
    public List<String> getQueryableFields() {
        return Collections.unmodifiableList(QUERYABLE_FIELDS);
    }

    @Override
    public String getDefaultNameField() {
        return "systemName";
    }

    @Override
    public String getDefaultOrgField() {
        return "orgId";
    }

    @Override
    public List<String> getIgnoreFields() {
        return Collections.unmodifiableList(IGNORE_FIELDS);
    }

    @Override
    public Map<String, String> getFieldGroupMap() {
        Map<String, String> groupMap = new HashMap<>();
        groupMap.put("systemName", "基本信息");
        groupMap.put("internetAddress", "基本信息");
        groupMap.put("networkAddress", "基本信息");
        groupMap.put("domainName", "基本信息");
        groupMap.put("internetType", "分类信息");
        groupMap.put("applicationType", "分类信息");
        groupMap.put("contactPerson", "联系信息");
        groupMap.put("contactPhone", "联系信息");
        groupMap.put("orgName", "组织信息");
        groupMap.put("securityLevel", "安全信息");
        groupMap.put("resourceStatus", "状态信息");
        groupMap.put("isPublicCloud", "部署信息");
        groupMap.put("publicCloudSupplier", "部署信息");
        groupMap.put("remark", "其他信息");
        return Collections.unmodifiableMap(groupMap);
    }

    @Override
    public List<String> getSensitiveFields() {
        return Arrays.asList("contactPhone", "networkAddress");
    }

    @Override
    public Map<String, String> getDictFieldMap() {
        Map<String, String> dictMap = new HashMap<>();
        dictMap.put("internetType", "internet_type");
        dictMap.put("applicationType", "application_type");
        dictMap.put("securityLevel", "security_level");
        dictMap.put("resourceStatus", "resource_status");
        dictMap.put("orgName", "org_type");
        return Collections.unmodifiableMap(dictMap);
    }
}
