/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsSystemInternetExportDto;
import com.snszyk.zbusiness.resource.entity.RsSystemInternetExport;
import com.snszyk.zbusiness.resource.mapper.RsSystemInternetExportMapper;
import com.snszyk.zbusiness.resource.service.IRsSystemInternetExportService;
import com.snszyk.zbusiness.resource.vo.RsSystemInternetExportVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * RsSystemInternetExportServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSystemInternetExportServiceImpl extends BaseCrudServiceImpl<RsSystemInternetExportMapper, RsSystemInternetExport, RsSystemInternetExportDto, RsSystemInternetExportVo> implements IRsSystemInternetExportService {


	@Override
	public List<RsSystemInternetExportDto> listByServerRoomId(Long serverroomId) {
		LambdaQueryWrapper<RsSystemInternetExport> queryWrapper = Wrappers.<RsSystemInternetExport>query().lambda()
			.eq(ObjectUtils.isNotEmpty(serverroomId), RsSystemInternetExport::getServerroomId, serverroomId);

		List<RsSystemInternetExport> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsSystemInternetExportDto.class));
	}

	@Override
	public List<RsSystemInternetExportDto> listBySystemId(List<Long> systemIdList) {
		LambdaQueryWrapper<RsSystemInternetExport> queryWrapper = Wrappers.<RsSystemInternetExport>query().lambda()
			.in(CollectionUtils.isNotEmpty(systemIdList), RsSystemInternetExport::getSystemId, systemIdList);

		List<RsSystemInternetExport> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsSystemInternetExportDto.class));
	}

	@Override
	public RsSystemInternetExportDto getBySystemId(Long systemId) {
		LambdaQueryWrapper<RsSystemInternetExport> queryWrapper = Wrappers.<RsSystemInternetExport>query().lambda()
			.eq(ObjectUtils.isNotEmpty(systemId), RsSystemInternetExport::getSystemId, systemId);

		RsSystemInternetExport internetExport = baseMapper.selectOne(queryWrapper);
		if (internetExport == null) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(internetExport, RsSystemInternetExportDto.class));
	}

	@Override
	public int deleteBySystemId(Long systemId) {
		LambdaQueryWrapper<RsSystemInternetExport> queryWrapper = Wrappers.<RsSystemInternetExport>query().lambda()
			.eq(ObjectUtils.isNotEmpty(systemId), RsSystemInternetExport::getSystemId, systemId);

		return baseMapper.delete(queryWrapper);
	}
}
