package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录索引实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_operation_record_index")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录索引实体", description = "操作记录索引实体")
public class RsOperationRecordIndex extends BaseCrudEntity {

    /**
     * 操作记录ID
     */
    @ApiModelProperty(value = "操作记录ID")
    private Long recordId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 索引键
     */
    @ApiModelProperty(value = "索引键")
    private String indexKey;

    /**
     * 索引值
     */
    @ApiModelProperty(value = "索引值")
    private String indexValue;

    /**
     * 索引类型
     */
    @ApiModelProperty(value = "索引类型")
    private String indexType;
}
