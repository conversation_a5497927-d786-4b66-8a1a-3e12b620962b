/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordDto;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordService;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 操作记录控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sys/operation-record")
@Api(value = "操作记录管理", tags = "操作记录管理接口")
public class SysOperationRecordController {

    private final ISysOperationRecordService operationRecordService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询操作记录", notes = "传入查询条件")
    @ApiOperationSupport(order = 1)
    public R<IPage<SysOperationRecordDto>> page(@RequestBody SysOperationRecordPageVo vo) {
        IPage<SysOperationRecordDto> pages = operationRecordService.pageList(vo);
        return R.data(pages);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "查询操作记录详情", notes = "传入操作记录ID")
    @ApiOperationSupport(order = 2)
    public R<SysOperationRecordDto> detail(@ApiParam(value = "操作记录ID", required = true) @PathVariable Long id) {
        SysOperationRecordDto detail = operationRecordService.getDetailWithChanges(id);
        return R.data(detail);
    }

    @GetMapping("/business-data")
    @ApiOperation(value = "获取业务数据", notes = "根据业务类型和业务ID获取业务数据")
    @ApiOperationSupport(order = 3)
    public R<Object> getBusinessData(@ApiParam(value = "业务类型", required = true) @RequestParam String businessType,
                                   @ApiParam(value = "业务ID", required = true) @RequestParam Long businessId) {
        Object businessData = operationRecordService.getBusinessData(businessType, businessId);
        return R.data(businessData);
    }
}
