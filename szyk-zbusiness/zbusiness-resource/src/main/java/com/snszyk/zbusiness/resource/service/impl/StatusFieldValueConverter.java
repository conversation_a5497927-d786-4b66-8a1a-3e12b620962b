package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 状态字段值转换器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
public class StatusFieldValueConverter implements FieldValueConverter {

    private static final List<String> STATUS_FIELDS = Arrays.asList(
        "resourceStatus", "status", "state", "operationStatus"
    );

    private static final Map<String, Map<String, String>> STATUS_MAPPINGS = new HashMap<>();

    static {
        // 资源状态映射
        Map<String, String> resourceStatusMap = new HashMap<>();
        resourceStatusMap.put("0", "在用");
        resourceStatusMap.put("1", "停用");
        resourceStatusMap.put("2", "维护中");
        resourceStatusMap.put("3", "已下线");
        STATUS_MAPPINGS.put("resourceStatus", resourceStatusMap);

        // 通用状态映射
        Map<String, String> commonStatusMap = new HashMap<>();
        commonStatusMap.put("0", "正常");
        commonStatusMap.put("1", "异常");
        commonStatusMap.put("2", "待处理");
        commonStatusMap.put("3", "已处理");
        STATUS_MAPPINGS.put("status", commonStatusMap);
        STATUS_MAPPINGS.put("state", commonStatusMap);

        // 操作状态映射
        Map<String, String> operationStatusMap = new HashMap<>();
        operationStatusMap.put("0", "成功");
        operationStatusMap.put("1", "失败");
        operationStatusMap.put("2", "处理中");
        STATUS_MAPPINGS.put("operationStatus", operationStatusMap);
    }

    @Override
    public String convert(String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        Map<String, String> statusMap = STATUS_MAPPINGS.get(fieldName);
        if (statusMap != null) {
            return statusMap.getOrDefault(fieldValue, fieldValue);
        }

        // 如果没有特定映射，尝试通用状态映射
        Map<String, String> commonMap = STATUS_MAPPINGS.get("status");
        return commonMap.getOrDefault(fieldValue, fieldValue);
    }

    @Override
    public boolean supports(String fieldName) {
        return STATUS_FIELDS.contains(fieldName) || 
               fieldName.contains("status") || 
               fieldName.contains("state");
    }

    @Override
    public int getPriority() {
        return 90;
    }
}
