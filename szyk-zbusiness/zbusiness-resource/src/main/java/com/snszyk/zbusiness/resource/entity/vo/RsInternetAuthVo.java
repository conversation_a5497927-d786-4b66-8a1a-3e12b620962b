/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 互联网资产授权实体类
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsInternetAuthVo对象", description = "互联网资产授权")
public class RsInternetAuthVo extends BaseCrudVo {

	/**
	* 互联网资产id
	*/
		@ApiModelProperty(value = "互联网资产id")
		private Long internetId;
	/**
	* 授权组织id
	*/
		@ApiModelProperty(value = "授权组织id")
		private Long authOrgId;


}
