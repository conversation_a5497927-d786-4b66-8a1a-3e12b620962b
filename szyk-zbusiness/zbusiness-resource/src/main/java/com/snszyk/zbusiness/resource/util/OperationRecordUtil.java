/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.util;

import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordService;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录工具类
 * 
 * 提供手动记录操作日志的便捷方法
 * 适用于无法使用注解的场景
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
@Slf4j
public class OperationRecordUtil {

    @Autowired
    private ISysOperationRecordService operationRecordService;

    /**
     * 记录操作日志
     *
     * @param businessType 业务类型
     * @param operationType 操作类型
     * @param businessId 业务ID
     * @param businessName 业务名称
     * @param description 操作描述
     */
    public void recordOperation(BusinessTypeEnum businessType, OperationTypeEnum operationType,
                              Long businessId, String businessName, String description) {
        recordOperation(businessType, operationType, businessId, businessName, description, null, null);
    }

    /**
     * 记录操作日志（带字段变更）
     *
     * @param businessType 业务类型
     * @param operationType 操作类型
     * @param businessId 业务ID
     * @param businessName 业务名称
     * @param description 操作描述
     * @param oldData 旧数据
     * @param newData 新数据
     */
    public void recordOperation(BusinessTypeEnum businessType, OperationTypeEnum operationType,
                              Long businessId, String businessName, String description,
                              Object oldData, Object newData) {
        try {
            SzykUser currentUser = AuthUtil.getUser();
            if (currentUser == null) {
                log.warn("当前用户为空，跳过操作记录");
                return;
            }

            SysOperationRecordVo recordVo = buildOperationRecord(businessType, operationType,
                businessId, businessName, description, currentUser);

            if (recordVo == null) {
                return;
            }

            // 如果有数据变更，记录字段变更详情
            if (oldData != null || newData != null) {
                List<FieldChangeDto> fieldChanges = operationRecordService.compareFields(
                    businessType.getCode(), oldData, newData);
                
                if (fieldChanges != null && !fieldChanges.isEmpty()) {
                    // 异步保存操作记录和字段变更
                    operationRecordService.saveAsync(recordVo);
                    return;
                }
            }

            // 异步保存操作记录
            operationRecordService.saveAsync(recordVo);

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 记录新增操作
     */
    public void recordCreate(BusinessTypeEnum businessType, Long businessId, String businessName, Object newData) {
        recordOperation(businessType, OperationTypeEnum.CREATE, businessId, businessName,
            "新增" + businessType.getName(), null, newData);
    }

    /**
     * 记录更新操作
     */
    public void recordUpdate(BusinessTypeEnum businessType, Long businessId, String businessName,
                           Object oldData, Object newData) {
        recordOperation(businessType, OperationTypeEnum.UPDATE, businessId, businessName,
            "更新" + businessType.getName(), oldData, newData);
    }

    /**
     * 记录删除操作
     */
    public void recordDelete(BusinessTypeEnum businessType, Long businessId, String businessName, Object oldData) {
        recordOperation(businessType, OperationTypeEnum.DELETE, businessId, businessName,
            "删除" + businessType.getName(), oldData, null);
    }

    /**
     * 记录批量删除操作
     */
    public void recordBatchDelete(BusinessTypeEnum businessType, List<Long> businessIds, String description) {
        recordOperation(businessType, OperationTypeEnum.DELETE, null,
            "批量删除" + businessIds.size() + "条记录", description);
    }

    /**
     * 记录导入操作
     */
    public void recordImport(BusinessTypeEnum businessType, int successCount, int failCount) {
        String description = String.format("导入%s，成功%d条，失败%d条",
            businessType.getName(), successCount, failCount);
        recordOperation(businessType, OperationTypeEnum.IMPORT, null, description, description);
    }

    /**
     * 记录导出操作
     */
    public void recordExport(BusinessTypeEnum businessType, int exportCount) {
        String description = String.format("导出%s，共%d条记录", businessType.getName(), exportCount);
        recordOperation(businessType, OperationTypeEnum.EXPORT, null, description, description);
    }

    /**
     * 构建操作记录
     */
    private SysOperationRecordVo buildOperationRecord(BusinessTypeEnum businessType, OperationTypeEnum operationType,
                                                     Long businessId, String businessName, String description,
                                                     SzykUser currentUser) {
        try {
            SysOperationRecordVo recordVo = new SysOperationRecordVo();

            // 基本信息
            recordVo.setBusinessModule("RESOURCE");
            recordVo.setBusinessType(businessType.getCode());
            recordVo.setBusinessId(businessId);
            recordVo.setBusinessName(businessName);
            recordVo.setOperationType(operationType.getCode());
            recordVo.setOperationTime(LocalDateTime.now());

            // 操作描述
            if (StringUtil.isBlank(description)) {
                description = operationType.getName() + businessType.getName();
            }
            recordVo.setOperationDesc(description);

            // 操作人信息
            recordVo.setOperatorId(currentUser.getUserId());
            recordVo.setOperatorName(currentUser.getUserName());
            recordVo.setOperatorDeptId(currentUser.getDeptId());
            recordVo.setOperatorDeptName(currentUser.getDeptName());

            // 主管单位信息
            if (currentUser.getOrgId() != null) {
                recordVo.setOrgId(currentUser.getOrgId());
                recordVo.setOrgName(currentUser.getOrgName());
            }

            return recordVo;

        } catch (Exception e) {
            log.error("构建操作记录失败", e);
            return null;
        }
    }
}
