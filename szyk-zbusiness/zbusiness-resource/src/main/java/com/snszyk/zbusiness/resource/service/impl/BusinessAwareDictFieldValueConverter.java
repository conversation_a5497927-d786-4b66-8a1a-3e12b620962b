package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.system.service.IDictService;
import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 业务感知的字典字段值转换器
 * 根据业务类型和字段配置进行字典转换
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
@AllArgsConstructor
public class BusinessAwareDictFieldValueConverter implements FieldValueConverter {

    private final IDictService dictService;
    private final ApplicationContext applicationContext;

    // 当前处理的业务类型，通过ThreadLocal传递
    private static final ThreadLocal<BusinessTypeEnum> CURRENT_BUSINESS_TYPE = new ThreadLocal<>();

    @Override
    public String convert(String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        BusinessTypeEnum businessType = CURRENT_BUSINESS_TYPE.get();
        if (businessType == null) {
            return fieldValue;
        }

        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Map<String, String> dictFieldMap = fieldConfig.getDictFieldMap();
            
            String dictCode = dictFieldMap.get(fieldName);
            if (dictCode != null) {
                Map<String, String> dictMap = dictService.getDict(dictCode);
                return dictMap.getOrDefault(fieldValue, fieldValue);
            }
        } catch (Exception e) {
            // 如果字典转换失败，返回原值
        }

        return fieldValue;
    }

    @Override
    public boolean supports(String fieldName) {
        BusinessTypeEnum businessType = CURRENT_BUSINESS_TYPE.get();
        if (businessType == null) {
            return false;
        }

        try {
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Map<String, String> dictFieldMap = fieldConfig.getDictFieldMap();
            return dictFieldMap.containsKey(fieldName);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int getPriority() {
        return 60; // 高于通用字典转换器
    }

    /**
     * 设置当前业务类型
     */
    public static void setCurrentBusinessType(BusinessTypeEnum businessType) {
        CURRENT_BUSINESS_TYPE.set(businessType);
    }

    /**
     * 清除当前业务类型
     */
    public static void clearCurrentBusinessType() {
        CURRENT_BUSINESS_TYPE.remove();
    }

    /**
     * 获取字段配置
     */
    private BusinessFieldConfig getFieldConfig(BusinessTypeEnum businessType) {
        try {
            String configClassName = businessType.getFieldConfigClassName();
            return applicationContext.getBean(configClassName, BusinessFieldConfig.class);
        } catch (Exception e) {
            return applicationContext.getBean("defaultFieldConfig", BusinessFieldConfig.class);
        }
    }
}
