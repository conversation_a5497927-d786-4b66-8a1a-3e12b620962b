/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsSystemFileDto;
import com.snszyk.zbusiness.resource.entity.RsSystemFile;
import com.snszyk.zbusiness.resource.mapper.RsSystemFileMapper;
import com.snszyk.zbusiness.resource.service.IRsSystemFileService;
import com.snszyk.zbusiness.resource.vo.RsSystemFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * RsSystemFileServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSystemFileServiceImpl extends BaseCrudServiceImpl<RsSystemFileMapper, RsSystemFile, RsSystemFileDto, RsSystemFileVo> implements IRsSystemFileService {


	@Override
	public List<RsSystemFileDto> listBySystemId(Long systemId) {
		LambdaQueryWrapper<RsSystemFile> queryWrapper = Wrappers.<RsSystemFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(systemId), RsSystemFile::getSystemId, systemId);

		List<RsSystemFile> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsSystemFileDto.class));
	}

	@Override
	public int deleteBySystemId(Long systemId) {
		LambdaQueryWrapper<RsSystemFile> queryWrapper = Wrappers.<RsSystemFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(systemId), RsSystemFile::getSystemId, systemId);

		return baseMapper.delete(queryWrapper);
	}
}
