/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.project.dto.PlBaseImportErrorDto;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDeleteDto;
import com.snszyk.zbusiness.resource.enums.TargetResourceEnum;
import com.snszyk.zbusiness.resource.service.IRsEquipmentService;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import com.snszyk.zbusiness.resource.service.logic.RsInternetLogicService;
import com.snszyk.zbusiness.resource.service.logic.RsOperationValidLogicService;
import com.snszyk.zbusiness.resource.vo.RsEquipmentDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsInternetPageVo;
import com.snszyk.zbusiness.resource.vo.RsInternetStatusVo;
import com.snszyk.zbusiness.resource.vo.RsInternetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;


/**
 * 互联网资源台账 控制器
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/internet")
@Api(value = "互联网资源台账", tags = "互联网资源台账接口")
public class RsInternetController extends BaseCrudController {

	// private final IRsInternetService rsInternetService;

	private final RsInternetLogicService rsInternetLogicService;

	private final RsOperationValidLogicService rsOperationValidLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsInternetLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "RsInternetVo")
	@OperationRecord(
		businessType = BusinessTypeEnum.INTERNET_ASSET,
		operationType = OperationTypeEnum.CREATE,
		description = "新增互联网资产",
		businessIdExpression = "#result.data.id",
		businessNameExpression = "#result.data.systemName",
		recordFieldChanges = true,
		async = true
	)
	public R<RsInternetDto> saveOrUpdate(@RequestBody RsInternetVo v) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_2, v.getOrgId(), null);
		RsInternetDto save = rsInternetLogicService.saveOrUpdate(v);
		return R.data(save);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "RsInternetVo")
	public R<IPage<RsInternetDto>> page(RsInternetPageVo v) {
		IPage<RsInternetDto> pageQueryResult = rsInternetLogicService.pageList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "列表", notes = "RsInternetVo")
	public R<List<RsInternetDto>> list(RsInternetVo v) {
		List<RsInternetDto> listQueryResult = rsInternetLogicService.listData(v);
		return R.data(listQueryResult);
	}

	/**
	 * 获取单条
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取单条数据", notes = "RsInternetVo")
	public R<RsInternetDto> detail(Long id) {
		RsInternetDto detail = rsInternetLogicService.detail(id);
		return R.data(detail);
	}


	/**
	 * 删除
	 */
	@PostMapping("/delete")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "删除", notes = "id")
	public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsEquipmentDeleteVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_2, null, vo.getIdList());
		List<RsSoftwareDeleteDto> result = rsInternetLogicService.delete(vo);
		return R.data(result);
	}

	/**
	 * 更改资源状态
	 */
	@PostMapping("/updateResourceStatus")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "更改资源状态", notes = "id")
	public R<Boolean> updateResourceStatus(@Valid @RequestBody RsInternetStatusVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_2, null, Collections.singletonList(vo.getId()));
		Boolean result = rsInternetLogicService.updateResourceStatus(vo);
		return R.data(result);
	}

	@ApiOperation("导出")
	@PostMapping("/export")
	public R<Void> export(@RequestBody RsInternetPageVo vo, HttpServletRequest request, HttpServletResponse response) {
		rsInternetLogicService.export(vo, request, response);
		return R.data(null);
	}

	@PostMapping("/importInternet")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导入", notes = "传入excel")
	public R<List<PlBaseImportErrorDto>> importInternet(MultipartFile file) {
		List<PlBaseImportErrorDto> result = rsInternetLogicService.importInternet(file);
		return R.data(result);
	}

}
