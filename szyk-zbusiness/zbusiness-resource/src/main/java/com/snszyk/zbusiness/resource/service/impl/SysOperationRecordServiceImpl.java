/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordDto;
import com.snszyk.zbusiness.resource.entity.SysOperationRecord;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.mapper.SysOperationRecordMapper;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordExtensionService;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordService;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作记录服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
@Slf4j
public class SysOperationRecordServiceImpl extends BaseCrudServiceImpl<SysOperationRecordMapper, SysOperationRecord, SysOperationRecordDto, SysOperationRecordVo> implements ISysOperationRecordService {

    private final SysOperationRecordMapper operationRecordMapper;
    private final ISysOperationRecordExtensionService extensionService;
    private final IRsInternetService rsInternetService;

    @Override
    public IPage<SysOperationRecordDto> pageList(SysOperationRecordPageVo vo) {
        Page<SysOperationRecordDto> page = new Page<>(vo.getCurrent(), vo.getSize());
        IPage<SysOperationRecordDto> result = operationRecordMapper.pageList(page, vo);
        
        // 转换枚举值为名称
        if (result != null && result.getRecords() != null) {
            result.getRecords().forEach(this::convertEnumNames);
        }
        
        return result;
    }

    @Override
    @Async
    public void saveAsync(SysOperationRecordVo vo) {
        try {
            SysOperationRecordDto saved = this.save(vo);
            log.info("异步保存操作记录成功，记录ID: {}", saved.getId());
        } catch (Exception e) {
            log.error("异步保存操作记录失败", e);
        }
    }

    @Override
    public Object getBusinessData(String businessType, Long businessId) {
        if (businessId == null) {
            return null;
        }

        BusinessTypeEnum typeEnum = BusinessTypeEnum.getByCode(businessType);
        if (typeEnum == null) {
            log.warn("未知的业务类型: {}", businessType);
            return null;
        }

        try {
            switch (typeEnum) {
                case INTERNET_ASSET:
                    return rsInternetService.getById(businessId);
                // 可以在这里添加其他业务类型的处理
                default:
                    log.warn("暂不支持的业务类型: {}", businessType);
                    return null;
            }
        } catch (Exception e) {
            log.error("获取业务数据失败，业务类型: {}, 业务ID: {}", businessType, businessId, e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public SysOperationRecordDto getDetailWithChanges(Long recordId) {
        SysOperationRecordDto record = this.getById(recordId);
        if (record == null) {
            return null;
        }

        // 获取字段变更详情
        List<FieldChangeDto> fieldChanges = this.getFieldChangesByRecordId(recordId);
        record.setFieldChanges(fieldChanges);

        // 转换枚举名称
        this.convertEnumNames(record);

        return record;
    }

    @Override
    public List<FieldChangeDto> compareFields(String businessType, Object oldData, Object newData) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        if (oldData == null && newData == null) {
            return changes;
        }

        try {
            // 如果是新增操作，只有新数据
            if (oldData == null) {
                return this.extractNewDataFields(businessType, newData);
            }

            // 如果是删除操作，只有旧数据
            if (newData == null) {
                return this.extractOldDataFields(businessType, oldData);
            }

            // 更新操作，比较字段差异
            return this.compareObjectFields(businessType, oldData, newData);

        } catch (Exception e) {
            log.error("比较字段变更失败", e);
            return changes;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFieldChanges(Long recordId, String businessType, List<FieldChangeDto> fieldChanges) {
        if (recordId == null || fieldChanges == null || fieldChanges.isEmpty()) {
            return;
        }

        try {
            // 过滤出有变更的字段
            List<FieldChangeDto> changedFields = fieldChanges.stream()
                .filter(change -> change.getHasChanged() != null && change.getHasChanged())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

            if (!changedFields.isEmpty()) {
                extensionService.saveBatch(recordId, businessType, changedFields);
                log.debug("保存字段变更详情成功，记录ID: {}, 变更字段数: {}", recordId, changedFields.size());
            }
        } catch (Exception e) {
            log.error("保存字段变更详情失败，记录ID: {}", recordId, e);
            throw e;
        }
    }

    @Override
    public List<FieldChangeDto> getFieldChangesByRecordId(Long recordId) {
        try {
            return extensionService.convertToFieldChanges(extensionService.getByRecordId(recordId));
        } catch (Exception e) {
            log.error("获取字段变更详情失败，记录ID: {}", recordId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换枚举值为名称
     */
    private void convertEnumNames(SysOperationRecordDto record) {
        if (record == null) {
            return;
        }

        // 转换业务类型名称
        BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());
        if (businessType != null) {
            record.setBusinessTypeName(businessType.getName());
        }

        // 转换操作类型名称
        OperationTypeEnum operationType = OperationTypeEnum.getByCode(record.getOperationType());
        if (operationType != null) {
            record.setOperationTypeName(operationType.getName());
        }
    }

    /**
     * 提取新数据字段（用于新增操作）
     */
    private List<FieldChangeDto> extractNewDataFields(String businessType, Object newData) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        if (newData == null) {
            return changes;
        }

        try {
            Map<String, String> fieldLabels = getFieldLabels(businessType);
            Field[] fields = newData.getClass().getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(newData);
                
                if (value != null) {
                    String fieldName = field.getName();
                    String fieldLabel = fieldLabels.getOrDefault(fieldName, fieldName);
                    String valueStr = convertValueToString(value);
                    
                    FieldChangeDto change = new FieldChangeDto(fieldName, fieldLabel, null, valueStr);
                    change.setFieldType(field.getType().getSimpleName());
                    changes.add(change);
                }
            }
        } catch (Exception e) {
            log.error("提取新数据字段失败", e);
        }
        
        return changes;
    }

    /**
     * 提取旧数据字段（用于删除操作）
     */
    private List<FieldChangeDto> extractOldDataFields(String businessType, Object oldData) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        if (oldData == null) {
            return changes;
        }

        try {
            Map<String, String> fieldLabels = getFieldLabels(businessType);
            Field[] fields = oldData.getClass().getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(oldData);
                
                if (value != null) {
                    String fieldName = field.getName();
                    String fieldLabel = fieldLabels.getOrDefault(fieldName, fieldName);
                    String valueStr = convertValueToString(value);
                    
                    FieldChangeDto change = new FieldChangeDto(fieldName, fieldLabel, valueStr, null);
                    change.setFieldType(field.getType().getSimpleName());
                    changes.add(change);
                }
            }
        } catch (Exception e) {
            log.error("提取旧数据字段失败", e);
        }
        
        return changes;
    }

    /**
     * 比较对象字段差异（用于更新操作）
     */
    private List<FieldChangeDto> compareObjectFields(String businessType, Object oldData, Object newData) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        try {
            Map<String, String> fieldLabels = getFieldLabels(businessType);
            Field[] fields = newData.getClass().getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object oldValue = field.get(oldData);
                Object newValue = field.get(newData);
                
                // 检查值是否有变更
                if (!ObjectUtil.equals(oldValue, newValue)) {
                    String fieldName = field.getName();
                    String fieldLabel = fieldLabels.getOrDefault(fieldName, fieldName);
                    String oldValueStr = convertValueToString(oldValue);
                    String newValueStr = convertValueToString(newValue);
                    
                    FieldChangeDto change = new FieldChangeDto(fieldName, fieldLabel, oldValueStr, newValueStr);
                    change.setFieldType(field.getType().getSimpleName());
                    changes.add(change);
                }
            }
        } catch (Exception e) {
            log.error("比较对象字段差异失败", e);
        }
        
        return changes;
    }

    /**
     * 获取字段标签映射
     */
    private Map<String, String> getFieldLabels(String businessType) {
        Map<String, String> labels = new HashMap<>();
        
        BusinessTypeEnum typeEnum = BusinessTypeEnum.getByCode(businessType);
        if (typeEnum == null) {
            return labels;
        }

        // 根据业务类型返回字段标签映射
        switch (typeEnum) {
            case INTERNET_ASSET:
                return getInternetAssetFieldLabels();
            // 可以在这里添加其他业务类型的字段标签
            default:
                return labels;
        }
    }

    /**
     * 获取互联网资产字段标签
     */
    private Map<String, String> getInternetAssetFieldLabels() {
        Map<String, String> labels = new HashMap<>();
        labels.put("systemName", "系统名称");
        labels.put("internetType", "资源类型");
        labels.put("applicationType", "应用类型");
        labels.put("appName", "小程序名称");
        labels.put("internetAddress", "互联网地址");
        labels.put("networkAddress", "内网地址");
        labels.put("associationType", "关联类型");
        labels.put("associationName", "关联名称");
        labels.put("contactPerson", "联系人");
        labels.put("contactPhone", "电话号码");
        labels.put("resourceStatus", "资源状态");
        labels.put("isPublicCloud", "是否公有云部署");
        labels.put("publicCloudSupplier", "公有云供应商");
        labels.put("securityLevel", "等保级别");
        labels.put("domainName", "域名或URL");
        labels.put("authOrg", "授权组织");
        labels.put("remark", "备注");
        return labels;
    }

    /**
     * 将值转换为字符串
     */
    private String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof String) {
            return (String) value;
        }
        
        return value.toString();
    }
}
