package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.*;
import com.snszyk.zbusiness.resource.enums.TargetResourceEnum;
import com.snszyk.zbusiness.resource.service.IRsSoftwareUseService;
import com.snszyk.zbusiness.resource.service.IRsSystemService;
import com.snszyk.zbusiness.resource.service.logic.RsOperationValidLogicService;
import com.snszyk.zbusiness.resource.service.logic.RsSystemService;
import com.snszyk.zbusiness.resource.vo.RsSystemDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsSystemPageVo;
import com.snszyk.zbusiness.resource.vo.RsSystemStatusVo;
import com.snszyk.zbusiness.resource.vo.RsSystemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 信息系统台账 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/system")
@Api(value = "信息系统台账", tags = "信息系统台账")
@Slf4j
public class RsSystemController extends BaseCrudController {

	private final RsSystemService rsSystemService;

	private final RsOperationValidLogicService rsOperationValidLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsSystemService;
	}

	@PostMapping("/page")
	@ApiOperation(value = "分页")
	public R<IPage<RsSystemPageDto>> page(@RequestBody RsSystemPageVo vo) {
		IPage<RsSystemPageDto> pageQueryResult = rsSystemService.page(vo);
		return R.data(pageQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<RsSystemDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		RsSystemDto baseCrudDto = rsSystemService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存")
	public R<RsSystemDto> save(@RequestBody RsSystemVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, vo.getOrgId(), null);
		RsSystemDto baseCrudDto = rsSystemService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/submit")
	@ApiOperation(value = "提交")
	public R<RsSystemDto> submit(@RequestBody RsSystemVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, vo.getOrgId(), null);
		RsSystemDto baseCrudDto = rsSystemService.submit(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/batchSubmit")
	@ApiOperation(value = "批量提交")
	public R<List<RsSystemCheckDto>> batchSubmit(@RequestBody List<Long> ids) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, null, ids);
		List<RsSystemCheckDto> r = rsSystemService.batchSubmit(ids);
		return R.data(r);
	}

	@PostMapping("/status")
	@ApiOperation(value = "停用/启用")
	public R<Boolean> status(@RequestBody RsSystemStatusVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, null, Arrays.asList(vo.getId()));
		Boolean result = rsSystemService.status(vo);
		return R.data(result);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsSystemDeleteVo vo) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, null, vo.getIdList());
		List<RsSoftwareDeleteDto> result = rsSystemService.delete(vo);
		return R.data(result);
	}

	@ApiOperation("导出")
	@PostMapping("/export")
	public R<Void> export(@RequestBody RsSystemPageVo vo, HttpServletRequest request, HttpServletResponse response) {
		rsSystemService.export(vo, request, response);
		return R.data(null);
	}

	@PostMapping("/import")
	@ApiOperation(value = "导入", notes = "传入excel")
	public R<List<RsSystemImportErrorDto>> importData(Long orgId, MultipartFile file) {
		rsOperationValidLogicService.operationValid(TargetResourceEnum.TARGET_RESOURCE_1, orgId, null);
		List<RsSystemImportErrorDto> result = rsSystemService.importData(orgId, file);
		return R.data(result);
	}


	@ApiOperation("下载模板")
	@GetMapping("/exportTemplate")
	public void exportTemplate(HttpServletResponse response) {
		rsSystemService.exportTemplate(response);
	}
}
