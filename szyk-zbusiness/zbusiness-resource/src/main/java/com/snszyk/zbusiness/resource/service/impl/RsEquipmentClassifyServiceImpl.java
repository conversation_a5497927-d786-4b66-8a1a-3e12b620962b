/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.dto.BaseCrudSlimDto;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.EquipmentClassifyTreeNode;
import com.snszyk.zbusiness.resource.dto.RsEquipmentClassifyDto;
import com.snszyk.zbusiness.resource.entity.RsEquipmentClassify;
import com.snszyk.zbusiness.resource.mapper.RsEquipmentClassifyMapper;
import com.snszyk.zbusiness.resource.service.IRsEquipmentClassifyService;
import com.snszyk.zbusiness.resource.vo.RsEquipmentClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * RsEquipmentClassifyServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsEquipmentClassifyServiceImpl extends BaseCrudServiceImpl<RsEquipmentClassifyMapper, RsEquipmentClassify, RsEquipmentClassifyDto, RsEquipmentClassifyVo> implements IRsEquipmentClassifyService {


	@Override
	public List<RsEquipmentClassifyDto> listByParentId(Long parentId) {
		LambdaQueryWrapper<RsEquipmentClassify> queryWrapper = Wrappers.<RsEquipmentClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(parentId), RsEquipmentClassify::getParentId, parentId);

		List<RsEquipmentClassify> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyDto.class));
	}

	@Override
	public List<RsEquipmentClassifyDto> listByClassifyName(String classifyName) {
		LambdaQueryWrapper<RsEquipmentClassify> queryWrapper = Wrappers.<RsEquipmentClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(classifyName), RsEquipmentClassify::getClassifyName, classifyName);

		List<RsEquipmentClassify> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyDto.class));
	}

	@Override
	public List<RsEquipmentClassifyDto> listByClassifyNo(String classifyNo) {
		LambdaQueryWrapper<RsEquipmentClassify> queryWrapper = Wrappers.<RsEquipmentClassify>query().lambda()
			.eq(ObjectUtils.isNotEmpty(classifyNo), RsEquipmentClassify::getClassifyNo, classifyNo);

		List<RsEquipmentClassify> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyDto.class));
	}

	@Override
	public List<RsEquipmentClassifyDto> listAll() {
		List<RsEquipmentClassify> list = super.list();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyDto.class));
	}

	@Override
	public List<EquipmentClassifyTreeNode> lazyUnitTree(Long parentId, String title) {
		List<EquipmentClassifyTreeNode> items = baseMapper.lazyUnitTree( parentId, title);
		if(CollectionUtil.isEmpty(items)){
			return new ArrayList<>();
		}
		List<Long> parentIds = items.stream().map(e -> e.getParentId()).distinct().collect(Collectors.toList());
		List<RsEquipmentClassify> parentList = this.lambdaQuery().in(RsEquipmentClassify::getId, parentIds).list();
		Map<Long, RsEquipmentClassify> parentIdMap = parentList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (e1, e2) -> e2));
		for (EquipmentClassifyTreeNode item : items) {
			Long pid = item.getParentId();
			RsEquipmentClassify parent = parentIdMap.get(pid);
			if(parent!=null){
				item.setParentName(parent.getClassifyName());
			}

		}

		return items;
	}
	/**
	 * 分类下所有子分类id
	 *
	 * @param id
	 * @return
	 */

	@Override
	public List<Long> listChildrenIdList(Long id) {
		return 	this.baseMapper.listChildrenIdList(id);
	}

	@Override
	public RsEquipmentClassifyDto update(RsEquipmentClassifyVo vo) {
		RsEquipmentClassify entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, RsEquipmentClassifyDto.class);
		} else {
			return null;
		}
	}
	/**
	 * 分类名称map
	 *
	 * @return
	 */
	@Override
	public Map<Long, String> allNameMap() {

		List<RsEquipmentClassifyDto> rsEquipmentClassifyDtos = this.listAll();
		if (CollectionUtils.isEmpty(rsEquipmentClassifyDtos)) {
			return new HashMap<>();
		}
		return rsEquipmentClassifyDtos.stream().collect(Collectors.toMap(BaseCrudSlimDto::getId, RsEquipmentClassifyDto::getClassifyName));
	}

	@Override
	public RsEquipmentClassifyDto getByClassifyNo(String classifyNo) {
		if (StringUtils.isBlank(classifyNo)) {
			return null;
		}
		LambdaQueryWrapper<RsEquipmentClassify> queryWrapper = Wrappers.<RsEquipmentClassify>query().lambda()
			.eq(RsEquipmentClassify::getClassifyNo, classifyNo);

		RsEquipmentClassify entity = baseMapper.selectOne(queryWrapper);
		if (entity == null) {
			return null;
		}

		return BeanUtil.copyProperties(entity, RsEquipmentClassifyDto.class);
	}
}
