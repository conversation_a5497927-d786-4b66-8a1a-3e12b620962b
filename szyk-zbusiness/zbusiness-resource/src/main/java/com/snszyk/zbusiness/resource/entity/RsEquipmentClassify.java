/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rs_equipment_classify")
@EqualsAndHashCode(callSuper = false)
public class RsEquipmentClassify extends BaseCrudEntity {

	/**
	 * 父分类id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long parentId;
	/**
	 * 分类编码
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String classifyName;

}
