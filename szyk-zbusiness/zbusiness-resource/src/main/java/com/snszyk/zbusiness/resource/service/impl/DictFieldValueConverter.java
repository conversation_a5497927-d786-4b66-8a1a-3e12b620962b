package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.system.service.IDictService;
import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 字典字段值转换器
 * 用于将字典代码转换为字典显示值
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
@AllArgsConstructor
public class DictFieldValueConverter implements FieldValueConverter {

    private final IDictService dictService;
    private final ApplicationContext applicationContext;

    private static final List<String> DICT_FIELDS = Arrays.asList(
        "internetType", "applicationType", "orgName", "deptName"
    );

    @Override
    public String convert(String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        try {
            // 根据字段名称获取对应的字典代码
            String dictCode = getDictCodeByFieldName(fieldName);
            if (dictCode != null) {
                Map<String, String> dictMap = dictService.getDict(dictCode);
                return dictMap.getOrDefault(fieldValue, fieldValue);
            }
        } catch (Exception e) {
            // 如果字典转换失败，返回原值
        }

        return fieldValue;
    }

    @Override
    public boolean supports(String fieldName) {
        return DICT_FIELDS.contains(fieldName) ||
               fieldName.contains("type") ||
               fieldName.contains("category") ||
               fieldName.endsWith("Name");
    }

    @Override
    public int getPriority() {
        return 70;
    }

    /**
     * 根据字段名称获取字典代码
     */
    private String getDictCodeByFieldName(String fieldName) {
        switch (fieldName) {
            case "internetType":
                return "internet_type";
            case "applicationType":
                return "application_type";
            case "orgName":
                return "org_type";
            case "deptName":
                return "dept_type";
            default:
                return null;
        }
    }
}
