package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

/**
 * 业务名称生成器接口
 * 用于生成业务数据的显示名称和关键标识
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface BusinessNameGenerator {

    /**
     * 生成业务显示名称
     * 
     * @param businessData 业务数据对象
     * @param businessType 业务类型
     * @return 业务显示名称
     */
    String generateBusinessName(Object businessData, BusinessTypeEnum businessType);

    /**
     * 生成业务关键标识
     * 
     * @param businessData 业务数据对象
     * @param businessType 业务类型
     * @return 业务关键标识
     */
    String generateBusinessKey(Object businessData, BusinessTypeEnum businessType);

    /**
     * 从对象中获取字段值
     * 
     * @param object 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    Object getFieldValue(Object object, String fieldName);

    /**
     * 从对象中获取指定类型的字段值
     * 
     * @param object 对象
     * @param fieldName 字段名
     * @param targetType 目标类型
     * @param <T> 类型参数
     * @return 字段值
     */
    <T> T getFieldValue(Object object, String fieldName, Class<T> targetType);
}
