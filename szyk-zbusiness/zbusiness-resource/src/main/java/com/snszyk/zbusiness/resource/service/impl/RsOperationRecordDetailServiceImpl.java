package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordDetail;
import com.snszyk.zbusiness.resource.mapper.RsOperationRecordDetailMapper;
import com.snszyk.zbusiness.resource.service.IRsOperationRecordDetailService;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordDetailVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 操作记录详情服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
public class RsOperationRecordDetailServiceImpl extends BaseCrudServiceImpl<RsOperationRecordDetailMapper, RsOperationRecordDetail, RsOperationRecordDetailDto, RsOperationRecordDetailVo> implements IRsOperationRecordDetailService {

    @Override
    public List<RsOperationRecordDetailDto> listByRecordId(Long recordId) {
        List<RsOperationRecordDetail> details = this.lambdaQuery()
                .eq(RsOperationRecordDetail::getRecordId, recordId)
                .orderByAsc(RsOperationRecordDetail::getFieldName)
                .list();

        return BeanUtil.copy(details, RsOperationRecordDetailDto.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<RsOperationRecordDetailVo> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return true;
        }

        List<RsOperationRecordDetail> entities = BeanUtil.copy(detailList, RsOperationRecordDetail.class);
        return super.saveBatch(entities);
    }
}
