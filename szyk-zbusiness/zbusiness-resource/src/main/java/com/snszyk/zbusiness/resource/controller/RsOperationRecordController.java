package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.service.logic.RsOperationRecordLogicService;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作记录控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/operation-record")
@Api(value = "操作记录管理", tags = "操作记录管理接口")
public class RsOperationRecordController {

    private final RsOperationRecordLogicService operationRecordLogicService;

    /**
     * 分页查询操作记录
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询操作记录", notes = "传入查询条件")
    public R<IPage<RsOperationRecordDto>> page(RsOperationRecordPageVo vo) {
        IPage<RsOperationRecordDto> pageResult = operationRecordLogicService.pageList(vo);
        return R.data(pageResult);
    }

    /**
     * 获取操作记录详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "获取操作记录详情", notes = "传入记录ID")
    public R<RsOperationRecordDto> detail(@ApiParam(value = "记录ID", required = true) @PathVariable Long id) {
        RsOperationRecordDto detail = operationRecordLogicService.getDetailWithChanges(id);
        return R.data(detail);
    }

    /**
     * 根据业务ID查询操作记录
     */
    @GetMapping("/list-by-business")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "根据业务ID查询操作记录", notes = "传入业务类型和业务ID")
    public R<List<RsOperationRecordDto>> listByBusinessId(
            @ApiParam(value = "业务类型", required = true) @RequestParam String businessType,
            @ApiParam(value = "业务ID", required = true) @RequestParam Long businessId) {
        List<RsOperationRecordDto> records = operationRecordLogicService.listByBusinessId(businessType, businessId);
        return R.data(records);
    }
}
