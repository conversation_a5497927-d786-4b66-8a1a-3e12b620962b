package com.snszyk.zbusiness.resource.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.entity.RsBusinessFieldDefinition;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordExtension;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordIndex;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.mapper.RsOperationRecordExtensionMapper;
import com.snszyk.zbusiness.resource.mapper.RsOperationRecordIndexMapper;
import com.snszyk.zbusiness.resource.service.DynamicFieldConfigService;
import com.snszyk.zbusiness.resource.service.ExtensionFieldStorageService;
import com.snszyk.zbusiness.resource.service.FieldSemanticProcessor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扩展字段存储服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class ExtensionFieldStorageServiceImpl implements ExtensionFieldStorageService {

    private final RsOperationRecordExtensionMapper extensionMapper;
    private final RsOperationRecordIndexMapper indexMapper;
    private final DynamicFieldConfigService dynamicFieldConfigService;
    private final FieldSemanticProcessor fieldSemanticProcessor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExtensionData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> extensionData, Map<String, Object> metadata) {
        RsOperationRecordExtension extension = new RsOperationRecordExtension();
        extension.setRecordId(recordId);
        extension.setBusinessType(businessType.getCode());
        extension.setExtensionData(JSON.toJSONString(extensionData));
        extension.setMetadata(JSON.toJSONString(metadata));
        
        extensionMapper.insert(extension);
        
        // 创建索引数据
        createIndexDataInternal(recordId, businessType, extensionData);
    }

    @Override
    public Map<String, Object> getExtensionData(Long recordId, BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsOperationRecordExtension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordExtension::getRecordId, recordId)
                   .eq(RsOperationRecordExtension::getBusinessType, businessType.getCode());
        
        RsOperationRecordExtension extension = extensionMapper.selectOne(queryWrapper);
        if (extension != null && StringUtil.isNotBlank(extension.getExtensionData())) {
            return JSON.parseObject(extension.getExtensionData(), new TypeReference<Map<String, Object>>() {});
        }
        
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getMetadata(Long recordId, BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsOperationRecordExtension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordExtension::getRecordId, recordId)
                   .eq(RsOperationRecordExtension::getBusinessType, businessType.getCode());
        
        RsOperationRecordExtension extension = extensionMapper.selectOne(queryWrapper);
        if (extension != null && StringUtil.isNotBlank(extension.getMetadata())) {
            return JSON.parseObject(extension.getMetadata(), new TypeReference<Map<String, Object>>() {});
        }
        
        return new HashMap<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExtensionData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> extensionData, Map<String, Object> metadata) {
        LambdaQueryWrapper<RsOperationRecordExtension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordExtension::getRecordId, recordId)
                   .eq(RsOperationRecordExtension::getBusinessType, businessType.getCode());
        
        RsOperationRecordExtension extension = extensionMapper.selectOne(queryWrapper);
        if (extension != null) {
            extension.setExtensionData(JSON.toJSONString(extensionData));
            extension.setMetadata(JSON.toJSONString(metadata));
            extensionMapper.updateById(extension);
            
            // 重新创建索引数据
            deleteIndexData(recordId);
            createIndexDataInternal(recordId, businessType, extensionData);
        } else {
            saveExtensionData(recordId, businessType, extensionData, metadata);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExtensionData(Long recordId, BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsOperationRecordExtension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordExtension::getRecordId, recordId)
                   .eq(RsOperationRecordExtension::getBusinessType, businessType.getCode());
        
        extensionMapper.delete(queryWrapper);
        deleteIndexData(recordId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createIndexData(Long recordId, BusinessTypeEnum businessType, Map<String, Object> indexData) {
        createIndexDataInternal(recordId, businessType, indexData);
    }

    private void createIndexDataInternal(Long recordId, BusinessTypeEnum businessType, Map<String, Object> data) {
        if (CollectionUtil.isEmpty(data)) {
            return;
        }
        
        // 获取需要索引的字段
        List<RsBusinessFieldDefinition> indexableFields = dynamicFieldConfigService.getIndexableFields(businessType);
        
        for (RsBusinessFieldDefinition fieldDef : indexableFields) {
            String fieldName = fieldDef.getFieldName();
            Object fieldValue = data.get(fieldName);
            
            if (fieldValue != null) {
                RsOperationRecordIndex index = new RsOperationRecordIndex();
                index.setRecordId(recordId);
                index.setBusinessType(businessType.getCode());
                index.setIndexKey(fieldName);
                index.setIndexValue(convertToIndexValue(fieldValue, fieldDef.getFieldType()));
                index.setIndexType(getIndexType(fieldDef.getFieldType()));
                
                indexMapper.insert(index);
            }
        }
    }

    @Override
    public List<Long> queryByIndex(BusinessTypeEnum businessType, String indexKey, String indexValue) {
        LambdaQueryWrapper<RsOperationRecordIndex> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordIndex::getBusinessType, businessType.getCode())
                   .eq(RsOperationRecordIndex::getIndexKey, indexKey)
                   .eq(RsOperationRecordIndex::getIndexValue, indexValue);
        
        List<RsOperationRecordIndex> indexes = indexMapper.selectList(queryWrapper);
        return indexes.stream()
                .map(RsOperationRecordIndex::getRecordId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIndexData(Long recordId) {
        LambdaQueryWrapper<RsOperationRecordIndex> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordIndex::getRecordId, recordId);
        indexMapper.delete(queryWrapper);
    }

    @Override
    public Map<String, String> validateExtensionData(BusinessTypeEnum businessType, Map<String, Object> extensionData) {
        Map<String, String> validationErrors = new HashMap<>();
        
        if (CollectionUtil.isEmpty(extensionData)) {
            return validationErrors;
        }
        
        Map<String, RsBusinessFieldDefinition> fieldDefinitionMap = dynamicFieldConfigService.getFieldDefinitionMap(businessType);
        
        for (Map.Entry<String, Object> entry : extensionData.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            
            RsBusinessFieldDefinition fieldDef = fieldDefinitionMap.get(fieldName);
            if (fieldDef == null) {
                continue; // 跳过未定义的字段
            }
            
            // 必填验证
            if (Boolean.TRUE.equals(fieldDef.getIsRequired()) && (fieldValue == null || StringUtil.isBlank(fieldValue.toString()))) {
                validationErrors.put(fieldName, fieldDef.getFieldLabel() + "不能为空");
                continue;
            }
            
            // 类型验证
            String validationError = validateFieldType(fieldDef, fieldValue);
            if (StringUtil.isNotBlank(validationError)) {
                validationErrors.put(fieldName, validationError);
            }
        }
        
        return validationErrors;
    }

    @Override
    public Map<String, String> formatDisplayValues(BusinessTypeEnum businessType, Map<String, Object> extensionData) {
        Map<String, String> displayValues = new HashMap<>();
        
        if (CollectionUtil.isEmpty(extensionData)) {
            return displayValues;
        }
        
        for (Map.Entry<String, Object> entry : extensionData.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            
            if (fieldValue != null) {
                String displayValue = fieldSemanticProcessor.convertFieldValueToDisplay(businessType, fieldName, fieldValue.toString());
                displayValues.put(fieldName, displayValue);
            }
        }
        
        return displayValues;
    }

    @Override
    public Map<String, Object> getFieldSchema(BusinessTypeEnum businessType) {
        List<RsBusinessFieldDefinition> fieldDefinitions = dynamicFieldConfigService.getFieldDefinitions(businessType);
        
        Map<String, Object> schema = new HashMap<>();
        schema.put("businessType", businessType.getCode());
        schema.put("businessTypeName", businessType.getName());
        
        List<Map<String, Object>> fields = new ArrayList<>();
        for (RsBusinessFieldDefinition fieldDef : fieldDefinitions) {
            Map<String, Object> fieldSchema = new HashMap<>();
            fieldSchema.put("fieldName", fieldDef.getFieldName());
            fieldSchema.put("fieldLabel", fieldDef.getFieldLabel());
            fieldSchema.put("fieldType", fieldDef.getFieldType());
            fieldSchema.put("fieldGroup", fieldDef.getFieldGroup());
            fieldSchema.put("isRequired", fieldDef.getIsRequired());
            fieldSchema.put("isSensitive", fieldDef.getIsSensitive());
            fieldSchema.put("isSearchable", fieldDef.getIsSearchable());
            fieldSchema.put("isIndexable", fieldDef.getIsIndexable());
            fieldSchema.put("dictCode", fieldDef.getDictCode());
            fieldSchema.put("displayOrder", fieldDef.getDisplayOrder());
            fieldSchema.put("description", fieldDef.getDescription());
            
            fields.add(fieldSchema);
        }
        
        schema.put("fields", fields);
        return schema;
    }

    /**
     * 验证字段类型
     */
    private String validateFieldType(RsBusinessFieldDefinition fieldDef, Object fieldValue) {
        if (fieldValue == null) {
            return null;
        }
        
        String fieldType = fieldDef.getFieldType();
        String fieldLabel = fieldDef.getFieldLabel();
        String valueStr = fieldValue.toString();
        
        try {
            switch (fieldType) {
                case "INTEGER":
                    Integer.parseInt(valueStr);
                    break;
                case "DECIMAL":
                    Double.parseDouble(valueStr);
                    break;
                case "BOOLEAN":
                    if (!"true".equalsIgnoreCase(valueStr) && !"false".equalsIgnoreCase(valueStr) && 
                        !"1".equals(valueStr) && !"0".equals(valueStr)) {
                        return fieldLabel + "必须是布尔值";
                    }
                    break;
                case "DATE":
                    LocalDateTime.parse(valueStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    break;
                default:
                    // STRING 和其他类型不需要特殊验证
                    break;
            }
        } catch (Exception e) {
            return fieldLabel + "格式不正确";
        }
        
        return null;
    }

    /**
     * 转换为索引值
     */
    private String convertToIndexValue(Object value, String fieldType) {
        if (value == null) {
            return "";
        }
        
        switch (fieldType) {
            case "BOOLEAN":
                if (value instanceof Boolean) {
                    return value.toString();
                } else {
                    String str = value.toString().toLowerCase();
                    return "true".equals(str) || "1".equals(str) ? "true" : "false";
                }
            default:
                return value.toString();
        }
    }

    /**
     * 获取索引类型
     */
    private String getIndexType(String fieldType) {
        switch (fieldType) {
            case "INTEGER":
            case "DECIMAL":
                return "NUMBER";
            case "DATE":
                return "DATE";
            default:
                return "STRING";
        }
    }
}
