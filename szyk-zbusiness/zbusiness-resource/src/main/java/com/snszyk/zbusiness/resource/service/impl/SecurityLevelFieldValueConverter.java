package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.zbusiness.resource.service.FieldValueConverter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 等保级别字段值转换器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
public class SecurityLevelFieldValueConverter implements FieldValueConverter {

    private static final List<String> SECURITY_LEVEL_FIELDS = Arrays.asList(
        "securityLevel", "protectionLevel", "gradeLevel"
    );

    private static final Map<String, String> SECURITY_LEVEL_MAP = new HashMap<>();

    static {
        SECURITY_LEVEL_MAP.put("1", "一级");
        SECURITY_LEVEL_MAP.put("2", "二级");
        SECURITY_LEVEL_MAP.put("3", "三级");
        SECURITY_LEVEL_MAP.put("4", "四级");
        SECURITY_LEVEL_MAP.put("5", "五级");
    }

    @Override
    public String convert(String fieldName, String fieldValue) {
        if (fieldValue == null) {
            return "--";
        }

        return SECURITY_LEVEL_MAP.getOrDefault(fieldValue, fieldValue);
    }

    @Override
    public boolean supports(String fieldName) {
        return SECURITY_LEVEL_FIELDS.contains(fieldName) ||
               fieldName.contains("security") ||
               fieldName.contains("protection") ||
               fieldName.contains("grade");
    }

    @Override
    public int getPriority() {
        return 80;
    }
}
