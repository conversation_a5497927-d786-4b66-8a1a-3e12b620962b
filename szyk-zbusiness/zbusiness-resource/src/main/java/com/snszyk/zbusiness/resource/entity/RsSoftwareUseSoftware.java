/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 软件使用情况表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rs_software_use_software")
@EqualsAndHashCode(callSuper = false)
public class RsSoftwareUseSoftware extends BaseCrudEntity {

	/**
	 * 计算机使用明细id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long useComputerId;
	/**
	 * 软件id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long softwareId;
	/**
	 * 填报日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date installDate;

}
