package com.snszyk.zbusiness.resource.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.entity.RsOperationRecord;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.mapper.RsOperationRecordMapper;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import com.snszyk.zbusiness.resource.service.IRsOperationRecordDetailService;
import com.snszyk.zbusiness.resource.service.IRsOperationRecordService;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordDetailVo;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 操作记录服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
@Slf4j
public class RsOperationRecordServiceImpl extends BaseCrudServiceImpl<RsOperationRecordMapper, RsOperationRecord, RsOperationRecordDto, RsOperationRecordVo> implements IRsOperationRecordService {

    private final RsOperationRecordMapper operationRecordMapper;
    private final IRsOperationRecordDetailService operationRecordDetailService;
    private final IRsInternetService rsInternetService;

    @Override
    public IPage<RsOperationRecordDto> pageList(RsOperationRecordPageVo vo) {
        Page<RsOperationRecordDto> page = new Page<>(vo.getCurrent(), vo.getSize());
        IPage<RsOperationRecordDto> pageResult = operationRecordMapper.pageList(page, vo);

        // 字典转换
        handleDictConvert(pageResult.getRecords());

        return pageResult;
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void saveAsync(RsOperationRecordVo vo) {
        try {
            // 转换为实体并保存
            RsOperationRecord entity = BeanUtil.copy(vo, RsOperationRecord.class);
            boolean saved = super.save(entity);

            // 保存变更详情
            if (saved && entity.getId() != null && StringUtil.isNotBlank(vo.getChangeFields())) {
                saveChangeDetails(entity.getId(), vo);
            }
        } catch (Exception e) {
            log.error("异步保存操作记录失败", e);
        }
    }

    @Override
    public Object getBusinessData(String businessType, Long businessId) {
        if (businessId == null) {
            return null;
        }

        try {
            // 使用枚举常量而非魔法值
            if (BusinessTypeEnum.INTERNET_ASSET.getCode().equals(businessType)) {
                return rsInternetService.fetchById(businessId);
            }
            // 可以根据需要添加其他业务类型的查询
            else {
                log.warn("未支持的业务类型: {}", businessType);
                return null;
            }
        } catch (Exception e) {
            log.error("获取业务数据失败: businessType={}, businessId={}", businessType, businessId, e);
            return null;
        }
    }

    @Override
    public RsOperationRecordDto getDetailWithChanges(Long recordId) {
        RsOperationRecord record = this.getById(recordId);
        if (record == null) {
            return null;
        }

        RsOperationRecordDto dto = BeanUtil.copy(record, RsOperationRecordDto.class);

        // 获取变更详情
        List<RsOperationRecordDetailDto> detailList = operationRecordDetailService.listByRecordId(recordId);
        dto.setDetailList(detailList);

        // 字典转换
        handleDictConvert(Arrays.asList(dto));

        return dto;
    }

    @Override
    public List<RsOperationRecordDto> listByBusinessId(String businessType, Long businessId) {
        List<RsOperationRecord> records = this.lambdaQuery()
                .eq(RsOperationRecord::getBusinessType, businessType)
                .eq(RsOperationRecord::getBusinessId, businessId)
                .orderByDesc(RsOperationRecord::getOperationTime)
                .list();

        List<RsOperationRecordDto> dtoList = BeanUtil.copy(records, RsOperationRecordDto.class);

        // 字典转换
        handleDictConvert(dtoList);

        return dtoList;
    }

    /**
     * 保存变更详情
     */
    private void saveChangeDetails(Long recordId, RsOperationRecordVo vo) {
        try {
            if (StringUtil.isBlank(vo.getOldData()) || StringUtil.isBlank(vo.getNewData())) {
                return;
            }

            // 解析变更字段
            List<String> changeFields = JSON.parseObject(vo.getChangeFields(), new TypeReference<List<String>>() {});
            if (CollectionUtil.isEmpty(changeFields)) {
                return;
            }

            // 解析新旧数据
            Map<String, Object> oldDataMap = JSON.parseObject(vo.getOldData(), new TypeReference<Map<String, Object>>() {});
            Map<String, Object> newDataMap = JSON.parseObject(vo.getNewData(), new TypeReference<Map<String, Object>>() {});

            // 构建详情记录
            List<RsOperationRecordDetailVo> detailVoList = new ArrayList<>();
            for (String fieldName : changeFields) {
                RsOperationRecordDetailVo detail = new RsOperationRecordDetailVo();
                detail.setRecordId(recordId);
                detail.setFieldName(fieldName);
                detail.setFieldLabel(getFieldLabel(fieldName, vo.getBusinessType()));
                detail.setOldValue(getStringValue(oldDataMap.get(fieldName)));
                detail.setNewValue(getStringValue(newDataMap.get(fieldName)));
                detail.setFieldType(getFieldType(fieldName, vo.getBusinessType()));

                detailVoList.add(detail);
            }

            // 批量保存
            operationRecordDetailService.saveBatch(detailVoList);

        } catch (Exception e) {
            log.error("保存变更详情失败", e);
        }
    }

    /**
     * 字典转换
     */
    private void handleDictConvert(List<RsOperationRecordDto> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }

        for (RsOperationRecordDto record : records) {
            // 业务类型名称转换
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());
            if (businessType != null) {
                record.setBusinessTypeName(businessType.getName());
            }

            // 操作类型名称转换
            OperationTypeEnum operationType = OperationTypeEnum.getByCode(record.getOperationType());
            if (operationType != null) {
                record.setOperationTypeName(operationType.getName());
            }
        }
    }

    /**
     * 获取字段中文名称
     */
    private String getFieldLabel(String fieldName, String businessType) {
        // 这里可以根据业务类型和字段名称返回对应的中文名称
        // 可以从配置文件、数据库或者硬编码的映射中获取
        Map<String, String> fieldLabelMap = getFieldLabelMap(businessType);
        return fieldLabelMap.getOrDefault(fieldName, fieldName);
    }

    /**
     * 获取字段类型
     */
    private String getFieldType(String fieldName, String businessType) {
        // 这里可以根据业务类型和字段名称返回对应的字段类型
        // 用于前端展示时的格式化
        return "STRING"; // 默认为字符串类型
    }

    /**
     * 获取字段标签映射
     */
    private Map<String, String> getFieldLabelMap(String businessType) {
        Map<String, String> labelMap = new HashMap<>();

        // 使用枚举而非魔法值
        if (BusinessTypeEnum.INTERNET_ASSET.getCode().equals(businessType)) {
            labelMap.put("systemName", "系统名称");
            labelMap.put("internetAddress", "互联网地址");
            labelMap.put("networkAddress", "内网地址");
            labelMap.put("contactPerson", "负责人");
            labelMap.put("contactPhone", "联系方式");
            labelMap.put("securityLevel", "等保级别");
            labelMap.put("resourceStatus", "资源状态");
            labelMap.put("orgName", "主管单位");
            labelMap.put("internetType", "资源类型");
            labelMap.put("applicationType", "应用类型");
            labelMap.put("domainName", "域名或URL");
            labelMap.put("isPublicCloud", "是否公有云部署");
            labelMap.put("publicCloudSupplier", "公有云供应商");
            labelMap.put("remark", "备注");
        }

        return labelMap;
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Object value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
}
