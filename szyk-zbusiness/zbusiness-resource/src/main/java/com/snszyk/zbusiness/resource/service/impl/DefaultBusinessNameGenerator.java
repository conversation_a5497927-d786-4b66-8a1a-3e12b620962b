package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.service.BusinessNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 默认业务名称生成器实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component
@Slf4j
public class DefaultBusinessNameGenerator implements BusinessNameGenerator {

    @Override
    public String generateBusinessName(Object businessData, BusinessTypeEnum businessType) {
        if (businessData == null) {
            return "未知";
        }

        // 根据业务类型进行特殊处理
        switch (businessType) {
            case INTERNET_ASSET:
                return generateInternetAssetName(businessData);
            case SYSTEM_RECORD:
                return generateSystemRecordName(businessData);
            case EQUIPMENT_ASSET:
                return generateEquipmentAssetName(businessData);
            case SOFTWARE_ASSET:
                return generateSoftwareAssetName(businessData);
            case PROJECT_MANAGE:
                return generateProjectName(businessData);
            case PERSON_MANAGE:
                return generatePersonName(businessData);
            default:
                return generateDefaultName(businessData, businessType);
        }
    }

    @Override
    public String generateBusinessKey(Object businessData, BusinessTypeEnum businessType) {
        if (businessData == null) {
            return null;
        }

        // 根据业务类型生成关键标识
        switch (businessType) {
            case INTERNET_ASSET:
                return getFieldValue(businessData, "internetAddress", String.class);
            case SYSTEM_RECORD:
                return getFieldValue(businessData, "systemCode", String.class);
            case EQUIPMENT_ASSET:
                return getFieldValue(businessData, "equipmentCode", String.class);
            case SOFTWARE_ASSET:
                return getFieldValue(businessData, "softwareCode", String.class);
            case PROJECT_MANAGE:
                return getFieldValue(businessData, "projectCode", String.class);
            case PERSON_MANAGE:
                return getFieldValue(businessData, "personCode", String.class);
            default:
                return getFieldValue(businessData, "code", String.class);
        }
    }

    /**
     * 生成互联网资产名称
     */
    private String generateInternetAssetName(Object businessData) {
        String systemName = getFieldValue(businessData, "systemName", String.class);
        String internetAddress = getFieldValue(businessData, "internetAddress", String.class);
        String domainName = getFieldValue(businessData, "domainName", String.class);

        if (StringUtil.isNotBlank(systemName)) {
            return systemName;
        } else if (StringUtil.isNotBlank(domainName)) {
            return domainName;
        } else if (StringUtil.isNotBlank(internetAddress)) {
            return internetAddress;
        }
        return "未命名系统";
    }

    /**
     * 生成信息系统名称
     */
    private String generateSystemRecordName(Object businessData) {
        String systemName = getFieldValue(businessData, "systemName", String.class);
        if (StringUtil.isNotBlank(systemName)) {
            return systemName;
        }
        return "未命名系统";
    }

    /**
     * 生成设备资产名称
     */
    private String generateEquipmentAssetName(Object businessData) {
        String equipmentName = getFieldValue(businessData, "equipmentName", String.class);
        String equipmentCode = getFieldValue(businessData, "equipmentCode", String.class);
        
        if (StringUtil.isNotBlank(equipmentName)) {
            return equipmentName;
        } else if (StringUtil.isNotBlank(equipmentCode)) {
            return equipmentCode;
        }
        return "未命名设备";
    }

    /**
     * 生成软件资产名称
     */
    private String generateSoftwareAssetName(Object businessData) {
        String softwareName = getFieldValue(businessData, "softwareName", String.class);
        if (StringUtil.isNotBlank(softwareName)) {
            return softwareName;
        }
        return "未命名软件";
    }

    /**
     * 生成项目名称
     */
    private String generateProjectName(Object businessData) {
        String projectName = getFieldValue(businessData, "projectName", String.class);
        if (StringUtil.isNotBlank(projectName)) {
            return projectName;
        }
        return "未命名项目";
    }

    /**
     * 生成人员名称
     */
    private String generatePersonName(Object businessData) {
        String personName = getFieldValue(businessData, "personName", String.class);
        String realName = getFieldValue(businessData, "realName", String.class);
        
        if (StringUtil.isNotBlank(personName)) {
            return personName;
        } else if (StringUtil.isNotBlank(realName)) {
            return realName;
        }
        return "未命名人员";
    }

    /**
     * 生成默认名称
     */
    private String generateDefaultName(Object businessData, BusinessTypeEnum businessType) {
        String nameField = businessType.getDefaultNameField();
        String name = getFieldValue(businessData, nameField, String.class);
        
        if (StringUtil.isNotBlank(name)) {
            return name;
        }
        
        // 尝试其他常见的名称字段
        String[] nameFields = {"name", "title", "description", "remark"};
        for (String field : nameFields) {
            String value = getFieldValue(businessData, field, String.class);
            if (StringUtil.isNotBlank(value)) {
                return value;
            }
        }
        
        return "未命名数据";
    }

    @Override
    public Object getFieldValue(Object object, String fieldName) {
        if (object == null || StringUtil.isBlank(fieldName)) {
            return null;
        }

        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            log.debug("获取字段值失败: {}.{}", object.getClass().getSimpleName(), fieldName);
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getFieldValue(Object object, String fieldName, Class<T> targetType) {
        Object value = getFieldValue(object, fieldName);
        if (value == null) {
            return null;
        }

        if (targetType.isInstance(value)) {
            return (T) value;
        }

        // 类型转换
        if (targetType == String.class) {
            return (T) value.toString();
        }

        return null;
    }
}
