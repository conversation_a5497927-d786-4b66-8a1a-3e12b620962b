/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.resource.dto.LockLogDto;
import com.snszyk.zbusiness.resource.entity.LockLog;
import com.snszyk.zbusiness.resource.vo.LockLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源锁定操作日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface LockLogMapper extends BaseMapper<LockLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param lockLog
	 * @return
	 */
	List<LockLogVo> selectLockLogPage(IPage page, LockLogVo lockLog);

	List<LockLogDto> listBy(@Param("orgIdList") List<Long> orgIdList, @Param("targetResource") String targetResource, @Param("operateCode") String operateCode);
}
