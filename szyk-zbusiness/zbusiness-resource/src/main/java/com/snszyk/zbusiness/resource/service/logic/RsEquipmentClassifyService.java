package com.snszyk.zbusiness.resource.service.logic;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import com.snszyk.zbusiness.resource.dto.*;
import com.snszyk.zbusiness.resource.enums.SubmitStatusEnum;
import com.snszyk.zbusiness.resource.service.IRsEquipmentAttrService;
import com.snszyk.zbusiness.resource.service.IRsEquipmentClassifyAttrService;
import com.snszyk.zbusiness.resource.service.IRsEquipmentClassifyService;
import com.snszyk.zbusiness.resource.service.IRsEquipmentService;
import com.snszyk.zbusiness.resource.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.snszyk.core.tool.node.ForestNodeMerger.merge;

/**
 * RsEquipmentClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsEquipmentClassifyService extends BaseCrudLogicService<RsEquipmentClassifyDto, RsEquipmentClassifyVo> {

	private final IRsEquipmentClassifyService rsEquipmentClassifyService;

	private final IRsEquipmentClassifyAttrService rsEquipmentClassifyAttrService;

	private final IRsEquipmentAttrService rsEquipmentAttrService;

	private final IRsEquipmentService rsEquipmentService;

	private final IDictBizService dictBizService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rsEquipmentClassifyService;
	}

	public List<RsEquipmentClassifyTreeDto> treeList() {
		List<RsEquipmentClassifyDto> list = rsEquipmentClassifyService.listAll();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		Map<Long, String> map = list.stream().collect(Collectors.toMap(RsEquipmentClassifyDto::getId, RsEquipmentClassifyDto::getClassifyName));
		for (RsEquipmentClassifyDto dto : list) {
			if (!ObjectUtils.isEmpty(map) && !ObjectUtils.isEmpty(dto.getParentId()) && map.containsKey(dto.getParentId())) {
				dto.setParentName(map.get(dto.getParentId()));
			}
		}

		List<RsEquipmentClassifyTreeDto> result = Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyTreeDto.class));

		return merge(result);
	}

	public List<RsEquipmentClassifyTreeDto> treeList(String classifyNo) {
		List<RsEquipmentClassifyDto> list = rsEquipmentClassifyService.listAll();
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		Map<Long, String> map = list.stream().collect(Collectors.toMap(RsEquipmentClassifyDto::getId, RsEquipmentClassifyDto::getClassifyName));
		for (RsEquipmentClassifyDto dto : list) {
			if (!ObjectUtils.isEmpty(map) && !ObjectUtils.isEmpty(dto.getParentId()) && map.containsKey(dto.getParentId())) {
				dto.setParentName(map.get(dto.getParentId()));
			}
		}

		List<RsEquipmentClassifyTreeDto> result = Objects.requireNonNull(BeanUtil.copy(list, RsEquipmentClassifyTreeDto.class));
		List<RsEquipmentClassifyTreeDto> treeList = merge(result);
		// 过滤数据
		if (!StringUtils.isEmpty(classifyNo)) {
			filterData(treeList, classifyNo);
		}
		return treeList;
	}

	public void filterData(List<RsEquipmentClassifyTreeDto> list, String classifyNo) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (RsEquipmentClassifyTreeDto dto : list) {
			if (dto.getClassifyNo().equals(classifyNo)) {
				list.remove(dto);
				return;
			}
			if (!dto.getClassifyNo().equals(classifyNo)) {
				filterData(dto.getChildren(), classifyNo);
			}
		}
	}

	public RsEquipmentClassifyDto detail(Long id) {
		RsEquipmentClassifyDto result = rsEquipmentClassifyService.fetchById(id);

		if (!StringUtils.isEmpty(result.getParentId())) {
			RsEquipmentClassifyDto equipmentClassifyDto = rsEquipmentClassifyService.fetchById(result.getParentId());
			result.setParentName(equipmentClassifyDto == null ? null : equipmentClassifyDto.getClassifyName());
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public RsEquipmentClassifyDto saveOrUpdate(RsEquipmentClassifyVo vo) {

		RsEquipmentClassifyDto dto = new RsEquipmentClassifyDto();

		if (!StringUtils.isEmpty(vo.getClassifyName())) {
			vo.setClassifyName(vo.getClassifyName().trim());
		}

		if (!StringUtils.isEmpty(vo.getClassifyNo())) {
			vo.setClassifyNo(vo.getClassifyNo().trim());
		}

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<RsEquipmentClassifyDto> nameList = rsEquipmentClassifyService.listByClassifyName(vo.getClassifyName());
			if (!CollectionUtils.isEmpty(nameList)) {
				throw new ServiceException(ExceptionEnum.CLASSIFY_NAME_EXIST.getMessage());
			}

			List<RsEquipmentClassifyDto> noList = rsEquipmentClassifyService.listByClassifyNo(vo.getClassifyNo());
			if (!CollectionUtils.isEmpty(noList)) {
				throw new ServiceException(ExceptionEnum.CLASSIFY_CODE_EXIST.getMessage());
			}

			dto = rsEquipmentClassifyService.save(vo);
		} else {
			// 更新
			List<RsEquipmentClassifyDto> nameList = rsEquipmentClassifyService.listByClassifyName(vo.getClassifyName());
			if (!CollectionUtils.isEmpty(nameList)) {
				Map<String, Long> map = nameList.stream().collect(Collectors.toMap(RsEquipmentClassifyDto::getClassifyName, RsEquipmentClassifyDto::getId));
				if (map.containsKey(vo.getClassifyName()) && !map.get(vo.getClassifyName()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.CLASSIFY_NAME_EXIST.getMessage());
				}
			}

			List<RsEquipmentClassifyDto> noList = rsEquipmentClassifyService.listByClassifyNo(vo.getClassifyNo());
			if (!CollectionUtils.isEmpty(noList)) {
				Map<String, Long> map = noList.stream().collect(Collectors.toMap(RsEquipmentClassifyDto::getClassifyName, RsEquipmentClassifyDto::getId));
				if (map.containsKey(vo.getClassifyName()) && !map.get(vo.getClassifyName()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.CLASSIFY_CODE_EXIST.getMessage());
				}
			}

			RsEquipmentClassifyDto classifyDto = rsEquipmentClassifyService.fetchById(vo.getId());
			if (!classifyDto.getClassifyNo().equals(vo.getClassifyNo())) {
				// 校验是否已存在设备台账
				List<RsEquipmentDto> list = rsEquipmentService.listByClassifyId(vo.getId(), SubmitStatusEnum.SUBMITTED.getCode());
				if (!CollectionUtils.isEmpty(list)) {
					throw new ServiceException(ExceptionEnum.CLASSIFY_NO_EQUIPMENT_EXIST.getMessage());
				}
			}

			dto = rsEquipmentClassifyService.update(vo);
		}

		return dto;

	}

	@Transactional(rollbackFor = Exception.class)
	public List<RsSoftwareDeleteDto> delete(RsEquipmentClassifyDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			RsSoftwareDeleteDto dto = new RsSoftwareDeleteDto();

			RsEquipmentClassifyDto rsEquipmentClassifyDto = rsEquipmentClassifyService.fetchById(id);
			dto.setName(rsEquipmentClassifyDto.getClassifyName());

			// 校验下级节点
			List<RsEquipmentClassifyDto> equipmentClassifyList = rsEquipmentClassifyService.listByParentId(id);
			if (!CollectionUtils.isEmpty(equipmentClassifyList)) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.EQUIPMENT_CLASSIFY_CHILDREN.getMessage());
				result.add(dto);
				continue;
			}

			// 校验设备台账
			List<RsEquipmentDto> equipmentList = rsEquipmentService.listByClassifyId(id);
			if (!CollectionUtils.isEmpty(equipmentList)) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.EQUIPMENT_DATA_EXIST.getMessage());
				result.add(dto);
				continue;
			}

			Boolean flag = rsEquipmentClassifyService.deleteById(id);
			dto.setResult(flag);

			// 删除自定义数据
			rsEquipmentClassifyAttrService.deleteByClassifyId(rsEquipmentClassifyDto.getId());

			result.add(dto);
		}

		return result;
	}

	public RsEquipmentClassifyExpandDto attrDetail(Long id) {
		RsEquipmentClassifyExpandDto result = new RsEquipmentClassifyExpandDto();

		RsEquipmentClassifyDto classifyDto = rsEquipmentClassifyService.fetchById(id);

		if (!StringUtils.isEmpty(classifyDto.getParentId())) {
			RsEquipmentClassifyDto equipmentClassifyDto = rsEquipmentClassifyService.fetchById(classifyDto.getParentId());
			classifyDto.setParentName(equipmentClassifyDto == null ? null : equipmentClassifyDto.getClassifyName());
		}

		BeanUtil.copyProperties(classifyDto, result);

		// 拓展属性
		List<RsEquipmentClassifyAttrDto> list = rsEquipmentClassifyAttrService.listByClassifyId(id);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		// 字段类型
		List<DictBiz> attrTypeList = dictBizService.getList(DictBizEnum.ATTR_TYPE.getCode());
		Map<String, DictBiz> attrTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(attrTypeList)) {
			attrTypeMap.putAll(attrTypeList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}

		// 设备自定义属性
		List<RsEquipmentAttrDto> attrList = rsEquipmentAttrService.listAll();
		Map<Long, List<RsEquipmentAttrDto>> attrMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(attrList)) {
			attrMap.putAll(attrList.stream().collect(Collectors.groupingBy(RsEquipmentAttrDto::getAttrId)));
		}

		for (RsEquipmentClassifyAttrDto attrDto : list) {
			// 字段类型
			if (!ObjectUtils.isEmpty(attrTypeMap) && !ObjectUtils.isEmpty(attrDto.getAttrType()) && attrTypeMap.containsKey(attrDto.getAttrType())) {
				attrDto.setAttrTypeName(attrTypeMap.get(attrDto.getAttrType()).getDictValue());
			}

			// 设备自定义属性
			if (!ObjectUtils.isEmpty(attrMap) && !ObjectUtils.isEmpty(attrDto.getId()) && attrMap.containsKey(attrDto.getId())) {
				if (CollectionUtils.isEmpty(attrMap.get(attrDto.getId()))) {
					continue;
				}
				List<String> valueList = attrMap.get(attrDto.getId()).stream().map(RsEquipmentAttrDto::getAttrValue).collect(Collectors.toList());
				// 去重
				attrDto.setSelectList(valueList.stream().distinct().collect(Collectors.toList()));
			}
		}

		result.setList(list);

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public RsEquipmentClassifyDto attrSave(RsEquipmentClassifyExpandVo vo) {
		RsEquipmentClassifyDto result = rsEquipmentClassifyService.fetchById(vo.getId());

		// 属性明细
		List<RsEquipmentClassifyAttrDto> list = rsEquipmentClassifyAttrService.listByClassifyId(vo.getId());

		boolean flag = false;

		// 新增数据
		if (CollectionUtils.isEmpty(list) && !CollectionUtils.isEmpty(vo.getList())) {
			flag = true;
			for (RsEquipmentClassifyAttrVo attrVo : vo.getList()) {

				attrVo.setClassifyId(vo.getId());
				rsEquipmentClassifyAttrService.save(attrVo);
			}
		}

		// 编辑数据
		if (!CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(vo.getList())) {

			rsEquipmentClassifyAttrService.deleteByClassifyId(vo.getId());
		}

		if (!CollectionUtils.isEmpty(list) && !CollectionUtils.isEmpty(vo.getList())) {
			// 新增
			List<RsEquipmentClassifyAttrVo> addList = vo.getList().stream().filter(item -> StringUtils.isEmpty(item.getId())).collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(addList)) {
				flag = true;
				for (RsEquipmentClassifyAttrVo attrVo : addList) {

					attrVo.setClassifyId(vo.getId());
					rsEquipmentClassifyAttrService.save(attrVo);
				}
			}

			// 编辑
			List<RsEquipmentClassifyAttrVo> editList = vo.getList().stream().filter(item -> !StringUtils.isEmpty(item.getId())).collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(editList)) {
				for (RsEquipmentClassifyAttrVo attrVo : editList) {

					rsEquipmentClassifyAttrService.update(attrVo);
				}
			}

			List<Long> idList = editList.stream().map(RsEquipmentClassifyAttrVo::getId).collect(Collectors.toList());

			// 删除
			for (RsEquipmentClassifyAttrDto attrDto : list) {
				if (!idList.contains(attrDto.getId())) {
					flag = true;
					rsEquipmentClassifyAttrService.deleteById(attrDto.getId());
				}
			}
		}

		// 修改设备台账数据状态
		if (flag) {
			List<RsEquipmentDto> equipmentList = rsEquipmentService.listByClassifyId(vo.getId());
			if (!CollectionUtils.isEmpty(equipmentList)) {
				List<Long> idList = equipmentList.stream().map(RsEquipmentDto::getId).collect(Collectors.toList());
				rsEquipmentService.updateByStatus(idList, SubmitStatusEnum.TO_BE_SUBMITTED.getCode());
			}
		}

		return result;
	}

	public Boolean attrCheck(Long id, String value) {

		List<RsEquipmentAttrDto> list = rsEquipmentAttrService.listByAttrId(id, value);
		if (CollectionUtils.isEmpty(list)) {
			return true;
		}

		return false;
	}

	@Transactional
    public Boolean importData(MultipartFile file) {
		//读取文件
		InputStream inputStream = null;
		try {
			inputStream = file.getInputStream();
		} catch (IOException e) {
			throw new RuntimeException(e);
		} finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException e) {
				throw new RuntimeException(e);
			}

		}
		EasyExcel.read(inputStream, RsEquipmentClassifyImportVo.class, new PageReadListener<RsEquipmentClassifyImportVo>(dataList -> {
			for (int i = 0; i < dataList.size(); i++) {
				//先获取类
				RsEquipmentClassifyImportVo classifyImportVo = dataList.get(i);
				//先查询父类编码
				List<RsEquipmentClassifyDto> rsEquipmentClassifyDtos = rsEquipmentClassifyService.listByClassifyNo(classifyImportVo.getParentClassifyNo());
				if(CollectionUtil.isEmpty(rsEquipmentClassifyDtos)){
					throw new ServiceException("查询不到父类");
				}
				RsEquipmentClassifyVo vo = cn.hutool.core.bean.BeanUtil.copyProperties(classifyImportVo,RsEquipmentClassifyVo.class);
				vo.setParentId(rsEquipmentClassifyDtos.get(0).getId());
				rsEquipmentClassifyService.save(vo);
			}
		})).sheet().doRead();
		return true;
    }
	public List<EquipmentClassifyTreeNode> lazyUnitTree(Long parentId, String title) {
		List<EquipmentClassifyTreeNode> items = rsEquipmentClassifyService.lazyUnitTree(parentId, title);
		if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(title)) {
			items.forEach(item -> {
				item.setHasChildren(false);
				item.setIsLeaf(true);
			});
			return items;
		}
		return merge(items);
	}
}
