package com.snszyk.zbusiness.resource.plugin.impl;

import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.plugin.BusinessModulePlugin;
import com.snszyk.zbusiness.resource.plugin.BusinessModulePluginManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 业务模块插件管理器实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
public class BusinessModulePluginManagerImpl implements BusinessModulePluginManager {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 插件注册表：插件名称 -> 插件实例
     */
    private final Map<String, BusinessModulePlugin> pluginRegistry = new ConcurrentHashMap<>();

    /**
     * 业务类型插件缓存：业务类型 -> 插件列表（按优先级排序）
     */
    private final Map<BusinessTypeEnum, List<BusinessModulePlugin>> businessTypePluginCache = new ConcurrentHashMap<>();

    /**
     * 插件启用状态：插件名称 -> 是否启用
     */
    private final Map<String, Boolean> pluginEnabledStatus = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 自动发现并注册所有BusinessModulePlugin实现
        Map<String, BusinessModulePlugin> plugins = applicationContext.getBeansOfType(BusinessModulePlugin.class);
        for (BusinessModulePlugin plugin : plugins.values()) {
            registerPlugin(plugin);
        }
        log.info("业务模块插件管理器初始化完成，共注册 {} 个插件", pluginRegistry.size());
    }

    @Override
    public void registerPlugin(BusinessModulePlugin plugin) {
        if (plugin == null) {
            log.warn("尝试注册空插件");
            return;
        }

        String pluginName = plugin.getPluginName();
        if (pluginRegistry.containsKey(pluginName)) {
            log.warn("插件 {} 已存在，将被覆盖", pluginName);
        }

        pluginRegistry.put(pluginName, plugin);
        pluginEnabledStatus.put(pluginName, plugin.isEnabled());
        
        // 清除相关缓存
        clearBusinessTypeCache(plugin.getSupportedBusinessTypes());
        
        log.info("注册插件：{} v{} - {}", pluginName, plugin.getPluginVersion(), plugin.getPluginDescription());
    }

    @Override
    public void unregisterPlugin(String pluginName) {
        BusinessModulePlugin removedPlugin = pluginRegistry.remove(pluginName);
        if (removedPlugin != null) {
            pluginEnabledStatus.remove(pluginName);
            clearBusinessTypeCache(removedPlugin.getSupportedBusinessTypes());
            log.info("注销插件：{}", pluginName);
        }
    }

    @Override
    public List<BusinessModulePlugin> getPlugins(BusinessTypeEnum businessType) {
        return businessTypePluginCache.computeIfAbsent(businessType, this::computePluginsForBusinessType);
    }

    @Override
    public BusinessModulePlugin getFirstPlugin(BusinessTypeEnum businessType) {
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        return CollectionUtil.isEmpty(plugins) ? null : plugins.get(0);
    }

    @Override
    public Map<String, BusinessModulePlugin> getAllPlugins() {
        return new HashMap<>(pluginRegistry);
    }

    @Override
    public boolean hasPlugin(BusinessTypeEnum businessType) {
        return !getPlugins(businessType).isEmpty();
    }

    @Override
    public void enablePlugin(String pluginName) {
        if (pluginRegistry.containsKey(pluginName)) {
            pluginEnabledStatus.put(pluginName, true);
            clearAllBusinessTypeCache();
            log.info("启用插件：{}", pluginName);
        }
    }

    @Override
    public void disablePlugin(String pluginName) {
        if (pluginRegistry.containsKey(pluginName)) {
            pluginEnabledStatus.put(pluginName, false);
            clearAllBusinessTypeCache();
            log.info("禁用插件：{}", pluginName);
        }
    }

    @Override
    public void refreshPlugins() {
        clearAllBusinessTypeCache();
        log.info("刷新插件缓存");
    }

    @Override
    public Map<String, Object> executePreprocess(BusinessTypeEnum businessType, Long businessId, String operationType,
                                                Map<String, Object> oldData, Map<String, Object> newData) {
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        Map<String, Object> result = new HashMap<>();
        result.put("oldData", oldData);
        result.put("newData", newData);

        for (BusinessModulePlugin plugin : plugins) {
            try {
                Map<String, Object> pluginResult = plugin.preprocessOperationData(businessType, businessId, operationType, 
                    (Map<String, Object>) result.get("oldData"), (Map<String, Object>) result.get("newData"));
                if (pluginResult != null) {
                    result.putAll(pluginResult);
                }
            } catch (Exception e) {
                log.error("插件 {} 预处理失败", plugin.getPluginName(), e);
            }
        }

        return result;
    }

    @Override
    public void executePostprocess(BusinessTypeEnum businessType, Long businessId, Long recordId, String operationType) {
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        for (BusinessModulePlugin plugin : plugins) {
            try {
                plugin.postprocessOperationRecord(businessType, businessId, recordId, operationType);
            } catch (Exception e) {
                log.error("插件 {} 后处理失败", plugin.getPluginName(), e);
            }
        }
    }

    @Override
    public String executeCustomFieldValueConversion(BusinessTypeEnum businessType, String fieldName, String fieldValue) {
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        for (BusinessModulePlugin plugin : plugins) {
            try {
                String result = plugin.customFieldValueConversion(businessType, fieldName, fieldValue);
                if (result != null) {
                    return result;
                }
            } catch (Exception e) {
                log.error("插件 {} 字段值转换失败", plugin.getPluginName(), e);
            }
        }
        return null;
    }

    @Override
    public String executeCustomOperationDescription(BusinessTypeEnum businessType, String operationType, String businessName, List<String> changeFields) {
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        for (BusinessModulePlugin plugin : plugins) {
            try {
                String result = plugin.customOperationDescription(businessType, operationType, businessName, changeFields);
                if (result != null) {
                    return result;
                }
            } catch (Exception e) {
                log.error("插件 {} 操作描述生成失败", plugin.getPluginName(), e);
            }
        }
        return null;
    }

    @Override
    public Map<String, String> executeBusinessDataValidation(BusinessTypeEnum businessType, Long businessId, String operationType, Map<String, Object> data) {
        Map<String, String> allErrors = new HashMap<>();
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        
        for (BusinessModulePlugin plugin : plugins) {
            try {
                Map<String, String> errors = plugin.validateBusinessData(businessType, businessId, operationType, data);
                if (errors != null && !errors.isEmpty()) {
                    allErrors.putAll(errors);
                }
            } catch (Exception e) {
                log.error("插件 {} 业务数据验证失败", plugin.getPluginName(), e);
            }
        }
        
        return allErrors;
    }

    @Override
    public List<String> getPluginSensitiveFields(BusinessTypeEnum businessType) {
        Set<String> sensitiveFields = new HashSet<>();
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        
        for (BusinessModulePlugin plugin : plugins) {
            try {
                List<String> fields = plugin.getSensitiveFields(businessType);
                if (fields != null) {
                    sensitiveFields.addAll(fields);
                }
            } catch (Exception e) {
                log.error("插件 {} 获取敏感字段失败", plugin.getPluginName(), e);
            }
        }
        
        return new ArrayList<>(sensitiveFields);
    }

    @Override
    public List<String> getPluginIndexFields(BusinessTypeEnum businessType) {
        Set<String> indexFields = new HashSet<>();
        List<BusinessModulePlugin> plugins = getPlugins(businessType);
        
        for (BusinessModulePlugin plugin : plugins) {
            try {
                List<String> fields = plugin.getIndexFields(businessType);
                if (fields != null) {
                    indexFields.addAll(fields);
                }
            } catch (Exception e) {
                log.error("插件 {} 获取索引字段失败", plugin.getPluginName(), e);
            }
        }
        
        return new ArrayList<>(indexFields);
    }

    /**
     * 计算指定业务类型的插件列表
     */
    private List<BusinessModulePlugin> computePluginsForBusinessType(BusinessTypeEnum businessType) {
        return pluginRegistry.values().stream()
                .filter(plugin -> isPluginEnabled(plugin.getPluginName()))
                .filter(plugin -> plugin.getSupportedBusinessTypes().contains(businessType))
                .sorted(Comparator.comparingInt(BusinessModulePlugin::getPriority))
                .collect(Collectors.toList());
    }

    /**
     * 检查插件是否启用
     */
    private boolean isPluginEnabled(String pluginName) {
        return pluginEnabledStatus.getOrDefault(pluginName, true);
    }

    /**
     * 清除指定业务类型的缓存
     */
    private void clearBusinessTypeCache(List<BusinessTypeEnum> businessTypes) {
        if (businessTypes != null) {
            businessTypes.forEach(businessTypePluginCache::remove);
        }
    }

    /**
     * 清除所有业务类型缓存
     */
    private void clearAllBusinessTypeCache() {
        businessTypePluginCache.clear();
    }
}
