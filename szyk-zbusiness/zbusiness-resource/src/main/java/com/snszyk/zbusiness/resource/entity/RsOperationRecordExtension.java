package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录扩展字段实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_operation_record_extension")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录扩展字段实体", description = "操作记录扩展字段实体")
public class RsOperationRecordExtension extends BaseCrudEntity {

    /**
     * 操作记录ID
     */
    @ApiModelProperty(value = "操作记录ID")
    private Long recordId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 扩展数据
     */
    @ApiModelProperty(value = "扩展数据")
    private String extensionData;

    /**
     * 元数据信息
     */
    @ApiModelProperty(value = "元数据信息")
    private String metadata;
}
