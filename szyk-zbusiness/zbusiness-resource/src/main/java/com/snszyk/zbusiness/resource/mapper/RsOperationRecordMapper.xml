<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.resource.mapper.RsOperationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="operationRecordResultMap" type="com.snszyk.zbusiness.resource.entity.RsOperationRecord">
        <id column="id" property="id"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="business_name" property="businessName"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_desc" property="operationDesc"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="full_org_id" property="fullOrgId"/>
        <result column="full_org_name" property="fullOrgName"/>
        <result column="old_data" property="oldData"/>
        <result column="new_data" property="newData"/>
        <result column="change_fields" property="changeFields"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operation_time" property="operationTime"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="user_agent" property="userAgent"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 分页查询操作记录 -->
    <select id="pageList" resultType="com.snszyk.zbusiness.resource.dto.RsOperationRecordDto">
        SELECT 
            ror.id,
            ror.business_type,
            ror.business_id,
            ror.business_name,
            ror.operation_type,
            ror.operation_desc,
            ror.org_id,
            ror.org_name,
            ror.full_org_id,
            ror.full_org_name,
            ror.old_data,
            ror.new_data,
            ror.change_fields,
            ror.operator_id,
            ror.operator_name,
            ror.operation_time,
            ror.ip_address,
            ror.user_agent,
            ror.create_user,
            ror.create_dept,
            ror.create_time,
            ror.update_user,
            ror.update_time,
            ror.status,
            ror.is_deleted
        FROM rs_operation_record ror
        WHERE ror.is_deleted = 0
        <if test="vo.businessType != null and vo.businessType != ''">
            AND ror.business_type = #{vo.businessType}
        </if>
        <if test="vo.businessName != null and vo.businessName != ''">
            AND ror.business_name LIKE CONCAT('%', #{vo.businessName}, '%')
        </if>
        <if test="vo.operationType != null and vo.operationType != ''">
            AND ror.operation_type = #{vo.operationType}
        </if>
        <if test="vo.orgId != null">
            AND ror.org_id = #{vo.orgId}
        </if>
        <if test="vo.orgName != null and vo.orgName != ''">
            AND ror.org_name LIKE CONCAT('%', #{vo.orgName}, '%')
        </if>
        <if test="vo.operatorName != null and vo.operatorName != ''">
            AND ror.operator_name LIKE CONCAT('%', #{vo.operatorName}, '%')
        </if>
        <if test="vo.startUpdateTime != null">
            AND ror.operation_time >= #{vo.startUpdateTime}
        </if>
        <if test="vo.endUpdateTime != null">
            AND ror.operation_time &lt;= #{vo.endUpdateTime}
        </if>
        ORDER BY ror.operation_time DESC, ror.id DESC
    </select>

</mapper>
