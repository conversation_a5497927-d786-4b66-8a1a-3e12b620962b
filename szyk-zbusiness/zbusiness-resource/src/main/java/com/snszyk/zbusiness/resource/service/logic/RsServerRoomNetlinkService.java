package com.snszyk.zbusiness.resource.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.resource.dto.RsServerRoomNetlinkDto;
import com.snszyk.zbusiness.resource.service.IRsServerRoomNetlinkService;
import com.snszyk.zbusiness.resource.vo.RsServerRoomNetlinkVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * RsServerRoomNetlinkService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsServerRoomNetlinkService extends BaseCrudLogicService<RsServerRoomNetlinkDto, RsServerRoomNetlinkVo> {

	private final IRsServerRoomNetlinkService rsServerRoomNetlinkService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rsServerRoomNetlinkService;
	}


}
