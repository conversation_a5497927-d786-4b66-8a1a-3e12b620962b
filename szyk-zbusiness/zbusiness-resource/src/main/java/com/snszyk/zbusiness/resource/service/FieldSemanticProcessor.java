package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;

/**
 * 字段语义处理器接口
 * 用于处理字段的语义转换和显示值转换
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface FieldSemanticProcessor {

    /**
     * 获取字段的语义名称（中文显示名称）
     * 
     * @param businessType 业务类型
     * @param fieldName 字段名
     * @return 语义名称
     */
    String getFieldSemanticName(BusinessTypeEnum businessType, String fieldName);

    /**
     * 转换字段值为显示值
     * 
     * @param businessType 业务类型
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 显示值
     */
    String convertFieldValueToDisplay(BusinessTypeEnum businessType, String fieldName, String fieldValue);

    /**
     * 批量处理变更详情的显示转换
     * 
     * @param businessType 业务类型
     * @param detailList 变更详情列表
     */
    void processChangeDetailsDisplay(BusinessTypeEnum businessType, List<RsOperationRecordDetailDto> detailList);

    /**
     * 判断字段是否为敏感字段（需要特殊处理）
     * 
     * @param businessType 业务类型
     * @param fieldName 字段名
     * @return 是否为敏感字段
     */
    boolean isSensitiveField(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取字段的数据类型
     * 
     * @param businessType 业务类型
     * @param fieldName 字段名
     * @return 数据类型
     */
    String getFieldDataType(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取字段的分组信息
     * 
     * @param businessType 业务类型
     * @param fieldName 字段名
     * @return 字段分组
     */
    String getFieldGroup(BusinessTypeEnum businessType, String fieldName);
}
