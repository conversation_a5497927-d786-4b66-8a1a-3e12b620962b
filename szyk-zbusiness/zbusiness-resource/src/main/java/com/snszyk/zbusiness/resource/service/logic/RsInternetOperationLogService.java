package com.snszyk.zbusiness.resource.service.logic;

import com.alibaba.fastjson.JSON;
import com.snszyk.common.operationlog.enums.BusinessModuleEnum;
import com.snszyk.common.operationlog.enums.OperationTypeEnum;
import com.snszyk.common.operationlog.service.IOperationLogService;
import com.snszyk.common.operationlog.vo.OperationLogVo;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.entity.RsInternet;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 互联网资产操作日志服务
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class RsInternetOperationLogService {

    private final IOperationLogService operationLogService;
    private final IRsInternetService rsInternetService;

    /**
     * 记录新增操作
     *
     * @param internetDto 互联网资产DTO
     */
    @Async
    public void recordCreate(RsInternetDto internetDto) {
        try {
            OperationLogVo logVo = buildBaseOperationLog(
                OperationTypeEnum.CREATE,
                internetDto.getId(),
                internetDto.getSystemName(),
                "新增互联网资产台账"
            );

            // 设置新增数据
            Map<String, Object> newData = convertToLogData(internetDto);
            logVo.setNewData(JSON.toJSONString(newData));

            operationLogService.saveAsync(logVo);
            log.info("记录互联网资产新增操作日志成功，资产ID: {}", internetDto.getId());
        } catch (Exception e) {
            log.error("记录互联网资产新增操作日志失败，资产ID: {}", internetDto.getId(), e);
        }
    }

    /**
     * 记录更新操作
     *
     * @param oldInternetDto 更新前数据
     * @param newInternetDto 更新后数据
     */
    @Async
    public void recordUpdate(RsInternetDto oldInternetDto, RsInternetDto newInternetDto) {
        try {
            OperationLogVo logVo = buildBaseOperationLog(
                OperationTypeEnum.UPDATE,
                newInternetDto.getId(),
                newInternetDto.getSystemName(),
                "更新互联网资产台账"
            );

            // 设置变更数据
            Map<String, Object> oldData = convertToLogData(oldInternetDto);
            Map<String, Object> newData = convertToLogData(newInternetDto);
            
            logVo.setOldData(JSON.toJSONString(oldData));
            logVo.setNewData(JSON.toJSONString(newData));

            // 比较变更字段
            String[] compareFields = getCompareFields();
            java.util.List<String> changedFields = operationLogService.compareObjectChanges(
                oldInternetDto, newInternetDto, compareFields
            );
            
            if (!changedFields.isEmpty()) {
                logVo.setChangeFields(String.join(",", changedFields));
                operationLogService.saveAsync(logVo);
                log.info("记录互联网资产更新操作日志成功，资产ID: {}，变更字段: {}", 
                    newInternetDto.getId(), changedFields);
            }
        } catch (Exception e) {
            log.error("记录互联网资产更新操作日志失败，资产ID: {}", newInternetDto.getId(), e);
        }
    }

    /**
     * 记录删除操作
     *
     * @param internetDto 被删除的互联网资产DTO
     */
    @Async
    public void recordDelete(RsInternetDto internetDto) {
        try {
            OperationLogVo logVo = buildBaseOperationLog(
                OperationTypeEnum.DELETE,
                internetDto.getId(),
                internetDto.getSystemName(),
                "删除互联网资产台账"
            );

            // 设置删除前数据
            Map<String, Object> oldData = convertToLogData(internetDto);
            logVo.setOldData(JSON.toJSONString(oldData));

            operationLogService.saveAsync(logVo);
            log.info("记录互联网资产删除操作日志成功，资产ID: {}", internetDto.getId());
        } catch (Exception e) {
            log.error("记录互联网资产删除操作日志失败，资产ID: {}", internetDto.getId(), e);
        }
    }

    /**
     * 记录状态变更操作
     *
     * @param internetId 互联网资产ID
     * @param oldStatus  旧状态
     * @param newStatus  新状态
     */
    @Async
    public void recordStatusChange(Long internetId, String oldStatus, String newStatus) {
        try {
            RsInternetDto internetDto = rsInternetService.getById(internetId);
            if (internetDto == null) {
                log.warn("互联网资产不存在，无法记录状态变更日志，资产ID: {}", internetId);
                return;
            }

            OperationLogVo logVo = buildBaseOperationLog(
                OperationTypeEnum.UPDATE,
                internetId,
                internetDto.getSystemName(),
                "变更互联网资产状态"
            );

            // 设置状态变更数据
            Map<String, Object> oldData = Map.of("resourceStatus", oldStatus);
            Map<String, Object> newData = Map.of("resourceStatus", newStatus);
            
            logVo.setOldData(JSON.toJSONString(oldData));
            logVo.setNewData(JSON.toJSONString(newData));
            logVo.setChangeFields("resourceStatus");

            operationLogService.saveAsync(logVo);
            log.info("记录互联网资产状态变更操作日志成功，资产ID: {}，状态: {} -> {}", 
                internetId, oldStatus, newStatus);
        } catch (Exception e) {
            log.error("记录互联网资产状态变更操作日志失败，资产ID: {}", internetId, e);
        }
    }

    /**
     * 记录批量导入操作
     *
     * @param successCount 成功导入数量
     * @param failCount    失败数量
     */
    @Async
    public void recordBatchImport(int successCount, int failCount) {
        try {
            OperationLogVo logVo = buildBaseOperationLog(
                OperationTypeEnum.IMPORT,
                null,
                "批量导入",
                String.format("批量导入互联网资产台账，成功: %d条，失败: %d条", successCount, failCount)
            );

            Map<String, Object> importData = new HashMap<>();
            importData.put("successCount", successCount);
            importData.put("failCount", failCount);
            importData.put("totalCount", successCount + failCount);
            
            logVo.setNewData(JSON.toJSONString(importData));

            operationLogService.saveAsync(logVo);
            log.info("记录互联网资产批量导入操作日志成功，成功: {}条，失败: {}条", successCount, failCount);
        } catch (Exception e) {
            log.error("记录互联网资产批量导入操作日志失败", e);
        }
    }

    /**
     * 构建基础操作日志
     */
    private OperationLogVo buildBaseOperationLog(OperationTypeEnum operationType, Long businessId, 
                                                String businessName, String description) {
        OperationLogVo logVo = new OperationLogVo();
        
        // 业务信息
        logVo.setBusinessModule(BusinessModuleEnum.INTERNET_ASSET.getCode());
        logVo.setBusinessId(businessId);
        logVo.setBusinessName(businessName);
        logVo.setOperationType(operationType.getCode());
        logVo.setOperationDescription(description);
        logVo.setOperationTime(LocalDateTime.now());

        // 操作人信息
        try {
            if (AuthUtil.getUser() != null) {
                logVo.setOperatorId(AuthUtil.getUserId());
                logVo.setOperatorName(AuthUtil.getUserName());
                logVo.setOperatorDeptId(Long.valueOf(AuthUtil.getDeptId()));
                logVo.setOperatorDeptName(AuthUtil.getDeptName());
            }
        } catch (Exception e) {
            log.warn("获取操作人信息失败", e);
        }

        // 请求信息
        try {
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                logVo.setClientIp(WebUtil.getIP(request));
                logVo.setUserAgent(request.getHeader("User-Agent"));
                logVo.setRequestUri(request.getRequestURI());
                logVo.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }

        logVo.setOperationResult("SUCCESS");
        
        return logVo;
    }

    /**
     * 转换为日志数据
     */
    private Map<String, Object> convertToLogData(RsInternetDto dto) {
        Map<String, Object> data = new HashMap<>();
        
        // 基础信息
        data.put("systemName", dto.getSystemName());
        data.put("internetAddress", dto.getInternetAddress());
        data.put("networkAddress", dto.getNetworkAddress());
        data.put("domainName", dto.getDomainName());
        
        // 分类信息
        data.put("internetType", dto.getInternetType());
        data.put("applicationType", dto.getApplicationType());
        data.put("appName", dto.getAppName());
        
        // 关联信息
        data.put("associationType", dto.getAssociationType());
        data.put("associationId", dto.getAssociationId());
        data.put("associationNo", dto.getAssociationNo());
        data.put("associationName", dto.getAssociationName());
        
        // 联系信息
        data.put("contactPerson", dto.getContactPerson());
        data.put("contactPhone", dto.getContactPhone());
        
        // 状态信息
        data.put("resourceStatus", dto.getResourceStatus());
        data.put("securityLevel", dto.getSecurityLevel());
        
        // 云部署信息
        data.put("isPublicCloud", dto.getIsPublicCloud());
        data.put("publicCloudSupplier", dto.getPublicCloudSupplier());
        data.put("publicCloudResource", dto.getPublicCloudResource());
        
        // 组织信息
        data.put("orgId", dto.getOrgId());
        data.put("orgName", dto.getOrgName());
        data.put("fullOrgId", dto.getFullOrgId());
        data.put("fullOrgName", dto.getFullOrgName());
        data.put("orgLevel", dto.getOrgLevel());
        
        // 授权信息
        data.put("authOrg", dto.getAuthOrg());
        
        // 备注
        data.put("remark", dto.getRemark());
        
        return data;
    }

    /**
     * 获取需要比较的字段列表
     */
    private String[] getCompareFields() {
        return new String[]{
            "systemName", "internetAddress", "networkAddress", "domainName",
            "internetType", "applicationType", "appName",
            "associationType", "associationId", "associationNo", "associationName",
            "contactPerson", "contactPhone", "resourceStatus", "securityLevel",
            "isPublicCloud", "publicCloudSupplier", "publicCloudResource",
            "orgId", "orgName", "authOrg", "remark"
        };
    }
}
