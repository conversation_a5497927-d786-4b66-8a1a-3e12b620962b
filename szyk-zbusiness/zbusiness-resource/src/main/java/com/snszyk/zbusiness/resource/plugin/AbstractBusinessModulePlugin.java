package com.snszyk.zbusiness.resource.plugin;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务模块插件抽象基类
 * 提供默认实现，子类只需要重写需要自定义的方法
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public abstract class AbstractBusinessModulePlugin implements BusinessModulePlugin {

    @Override
    public String getPluginVersion() {
        return "1.0.0";
    }

    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }

    @Override
    public boolean isEnabled() {
        return true; // 默认启用
    }

    @Override
    public Map<String, Object> preprocessOperationData(BusinessTypeEnum businessType, Long businessId, String operationType,
                                                      Map<String, Object> oldData, Map<String, Object> newData) {
        // 默认不做预处理
        Map<String, Object> result = new HashMap<>();
        result.put("oldData", oldData);
        result.put("newData", newData);
        return result;
    }

    @Override
    public void postprocessOperationRecord(BusinessTypeEnum businessType, Long businessId, Long recordId, String operationType) {
        // 默认不做后处理
    }

    @Override
    public String customFieldValueConversion(BusinessTypeEnum businessType, String fieldName, String fieldValue) {
        // 默认返回null，使用系统默认转换逻辑
        return null;
    }

    @Override
    public String customOperationDescription(BusinessTypeEnum businessType, String operationType, String businessName, List<String> changeFields) {
        // 默认返回null，使用系统默认描述生成逻辑
        return null;
    }

    @Override
    public Map<String, Object> getExtensionConfig(BusinessTypeEnum businessType) {
        // 默认返回空配置
        return new HashMap<>();
    }

    @Override
    public Map<String, String> validateBusinessData(BusinessTypeEnum businessType, Long businessId, String operationType, Map<String, Object> data) {
        // 默认验证通过
        return new HashMap<>();
    }

    @Override
    public List<String> getSensitiveFields(BusinessTypeEnum businessType) {
        // 默认返回空列表
        return Collections.emptyList();
    }

    @Override
    public List<String> getIndexFields(BusinessTypeEnum businessType) {
        // 默认返回空列表
        return Collections.emptyList();
    }

    /**
     * 检查是否支持指定的业务类型
     *
     * @param businessType 业务类型
     * @return 是否支持
     */
    protected boolean supports(BusinessTypeEnum businessType) {
        return getSupportedBusinessTypes().contains(businessType);
    }

    /**
     * 获取字段的显示名称
     * 子类可以重写此方法提供自定义的字段显示名称
     *
     * @param fieldName 字段名称
     * @return 显示名称
     */
    protected String getFieldDisplayName(String fieldName) {
        // 默认返回字段名称本身
        return fieldName;
    }

    /**
     * 格式化操作描述
     * 提供通用的操作描述格式化逻辑
     *
     * @param operationType 操作类型
     * @param businessName 业务名称
     * @param changeFields 变更字段列表
     * @return 格式化后的操作描述
     */
    protected String formatOperationDescription(String operationType, String businessName, List<String> changeFields) {
        StringBuilder desc = new StringBuilder();
        
        switch (operationType) {
            case "CREATE":
                desc.append("创建了").append(businessName);
                break;
            case "UPDATE":
                desc.append("修改了").append(businessName);
                if (changeFields != null && !changeFields.isEmpty()) {
                    desc.append("，变更字段：").append(String.join("、", changeFields));
                }
                break;
            case "DELETE":
                desc.append("删除了").append(businessName);
                break;
            default:
                desc.append("对").append(businessName).append("执行了").append(operationType).append("操作");
                break;
        }
        
        return desc.toString();
    }

    /**
     * 验证必填字段
     *
     * @param data 数据
     * @param requiredFields 必填字段列表
     * @return 验证错误信息
     */
    protected Map<String, String> validateRequiredFields(Map<String, Object> data, List<String> requiredFields) {
        Map<String, String> errors = new HashMap<>();
        
        for (String field : requiredFields) {
            Object value = data.get(field);
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                errors.put(field, getFieldDisplayName(field) + "不能为空");
            }
        }
        
        return errors;
    }

    /**
     * 验证字段长度
     *
     * @param data 数据
     * @param fieldLengthMap 字段长度限制映射
     * @return 验证错误信息
     */
    protected Map<String, String> validateFieldLength(Map<String, Object> data, Map<String, Integer> fieldLengthMap) {
        Map<String, String> errors = new HashMap<>();
        
        for (Map.Entry<String, Integer> entry : fieldLengthMap.entrySet()) {
            String field = entry.getKey();
            Integer maxLength = entry.getValue();
            Object value = data.get(field);
            
            if (value instanceof String) {
                String strValue = (String) value;
                if (strValue.length() > maxLength) {
                    errors.put(field, getFieldDisplayName(field) + "长度不能超过" + maxLength + "个字符");
                }
            }
        }
        
        return errors;
    }
}
