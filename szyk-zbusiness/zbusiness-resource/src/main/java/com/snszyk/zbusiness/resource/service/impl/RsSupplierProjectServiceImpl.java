/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.resource.dto.RsSupplierProjectDto;
import com.snszyk.zbusiness.resource.entity.RsSupplierProject;
import com.snszyk.zbusiness.resource.mapper.RsSupplierProjectMapper;
import com.snszyk.zbusiness.resource.service.IRsSupplierProjectService;
import com.snszyk.zbusiness.resource.vo.RsSupplierProjectVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * RsSupplierProjectServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsSupplierProjectServiceImpl extends BaseCrudServiceImpl<RsSupplierProjectMapper, RsSupplierProject, RsSupplierProjectDto, RsSupplierProjectVo> implements IRsSupplierProjectService {


	@Override
	public List<RsSupplierProjectDto> listBySupplierId(Long supplierId) {
		LambdaQueryWrapper<RsSupplierProject> queryWrapper = Wrappers.<RsSupplierProject>query().lambda()
			.eq(ObjectUtils.isNotEmpty(supplierId), RsSupplierProject::getSupplierId, supplierId);

		List<RsSupplierProject> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsSupplierProjectDto.class));
	}

	@Override
	public int deleteBySupplierId(Long supplierId) {
		LambdaQueryWrapper<RsSupplierProject> queryWrapper = Wrappers.<RsSupplierProject>query().lambda()
			.eq(ObjectUtils.isNotEmpty(supplierId), RsSupplierProject::getSupplierId, supplierId);

		return baseMapper.delete(queryWrapper);
	}
}
