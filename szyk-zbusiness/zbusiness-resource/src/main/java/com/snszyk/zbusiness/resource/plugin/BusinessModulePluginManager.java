package com.snszyk.zbusiness.resource.plugin;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 业务模块插件管理器接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface BusinessModulePluginManager {

    /**
     * 注册业务模块插件
     *
     * @param plugin 插件实例
     */
    void registerPlugin(BusinessModulePlugin plugin);

    /**
     * 注销业务模块插件
     *
     * @param pluginName 插件名称
     */
    void unregisterPlugin(String pluginName);

    /**
     * 获取指定业务类型的插件列表
     *
     * @param businessType 业务类型
     * @return 插件列表（按优先级排序）
     */
    List<BusinessModulePlugin> getPlugins(BusinessTypeEnum businessType);

    /**
     * 获取指定业务类型的第一个插件
     *
     * @param businessType 业务类型
     * @return 插件实例，如果没有则返回null
     */
    BusinessModulePlugin getFirstPlugin(BusinessTypeEnum businessType);

    /**
     * 获取所有已注册的插件
     *
     * @return 插件映射（插件名称 -> 插件实例）
     */
    Map<String, BusinessModulePlugin> getAllPlugins();

    /**
     * 检查是否有插件支持指定的业务类型
     *
     * @param businessType 业务类型
     * @return 是否有插件支持
     */
    boolean hasPlugin(BusinessTypeEnum businessType);

    /**
     * 启用插件
     *
     * @param pluginName 插件名称
     */
    void enablePlugin(String pluginName);

    /**
     * 禁用插件
     *
     * @param pluginName 插件名称
     */
    void disablePlugin(String pluginName);

    /**
     * 刷新插件缓存
     */
    void refreshPlugins();

    /**
     * 执行插件的预处理逻辑
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param operationType 操作类型
     * @param oldData 变更前数据
     * @param newData 变更后数据
     * @return 预处理后的数据
     */
    Map<String, Object> executePreprocess(BusinessTypeEnum businessType, Long businessId, String operationType,
                                         Map<String, Object> oldData, Map<String, Object> newData);

    /**
     * 执行插件的后处理逻辑
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param recordId 操作记录ID
     * @param operationType 操作类型
     */
    void executePostprocess(BusinessTypeEnum businessType, Long businessId, Long recordId, String operationType);

    /**
     * 执行插件的自定义字段值转换
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @param fieldValue 字段值
     * @return 转换后的显示值
     */
    String executeCustomFieldValueConversion(BusinessTypeEnum businessType, String fieldName, String fieldValue);

    /**
     * 执行插件的自定义操作描述生成
     *
     * @param businessType 业务类型
     * @param operationType 操作类型
     * @param businessName 业务名称
     * @param changeFields 变更字段列表
     * @return 自定义操作描述
     */
    String executeCustomOperationDescription(BusinessTypeEnum businessType, String operationType, String businessName, List<String> changeFields);

    /**
     * 执行插件的业务数据验证
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param operationType 操作类型
     * @param data 业务数据
     * @return 验证结果
     */
    Map<String, String> executeBusinessDataValidation(BusinessTypeEnum businessType, Long businessId, String operationType, Map<String, Object> data);

    /**
     * 获取插件定义的敏感字段
     *
     * @param businessType 业务类型
     * @return 敏感字段列表
     */
    List<String> getPluginSensitiveFields(BusinessTypeEnum businessType);

    /**
     * 获取插件定义的索引字段
     *
     * @param businessType 业务类型
     * @return 索引字段列表
     */
    List<String> getPluginIndexFields(BusinessTypeEnum businessType);
}
