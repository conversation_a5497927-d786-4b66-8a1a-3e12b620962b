package com.snszyk.zbusiness.resource.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.*;
import com.snszyk.zbusiness.resource.service.logic.RsEquipmentClassifyService;
import com.snszyk.zbusiness.resource.vo.RsEquipmentClassifyDeleteVo;
import com.snszyk.zbusiness.resource.vo.RsEquipmentClassifyExpandVo;
import com.snszyk.zbusiness.resource.vo.RsEquipmentClassifyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 设备台账分类 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipment-classify")
@Api(value = "设备台账分类", tags = "设备台账分类接口")
@Slf4j
public class RsEquipmentClassifyController extends BaseCrudController {

	private final RsEquipmentClassifyService rsEquipmentClassifyService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsEquipmentClassifyService;
	}

	@GetMapping("/tree/list")
	@ApiOperation(value = "树形列表")
	public R<List<RsEquipmentClassifyTreeDto>> treeList() {
		List<RsEquipmentClassifyTreeDto> listQueryResult = rsEquipmentClassifyService.treeList();
		return R.data(listQueryResult);
	}

	@GetMapping("/tree/list/code")
	@ApiOperation(value = "树形列表（过滤节点数据）")
	public R<List<RsEquipmentClassifyTreeDto>> treeList(@ApiParam(value = "classifyNo", required = true) @RequestParam String classifyNo) {
		List<RsEquipmentClassifyTreeDto> listQueryResult = rsEquipmentClassifyService.treeList(classifyNo);
		return R.data(listQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<RsEquipmentClassifyDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		RsEquipmentClassifyDto baseCrudDto = rsEquipmentClassifyService.detail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/save")
	@ApiOperation(value = "保存/编辑")
	public R<RsEquipmentClassifyDto> save(@RequestBody RsEquipmentClassifyVo vo) {
		RsEquipmentClassifyDto baseCrudDto = rsEquipmentClassifyService.saveOrUpdate(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/delete")
	@ApiOperation(value = "删除")
	public R<List<RsSoftwareDeleteDto>> delete(@RequestBody RsEquipmentClassifyDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = rsEquipmentClassifyService.delete(vo);
		return R.data(result);
	}

	@GetMapping("/attr/detail")
	@ApiOperation(value = "根据ID获取数据(拓展属性)")
	public R<RsEquipmentClassifyExpandDto> attrDetail(@ApiParam(value = "ID", required = true) @RequestParam Long id) {
		RsEquipmentClassifyExpandDto baseCrudDto = rsEquipmentClassifyService.attrDetail(id);
		return R.data(baseCrudDto);
	}

	@PostMapping("/attr/save")
	@ApiOperation(value = "保存/编辑(拓展属性)")
	public R<RsEquipmentClassifyDto> attrSave(@RequestBody RsEquipmentClassifyExpandVo vo) {
		RsEquipmentClassifyDto baseCrudDto = rsEquipmentClassifyService.attrSave(vo);
		return R.data(baseCrudDto);
	}

	@GetMapping("/attr/check")
	@ApiOperation(value = "根据属性ID和待选值判断是否被选中(拓展属性)")
	public R<Boolean> attrCheck(@ApiParam(value = "ID", required = true) @RequestParam Long id, @ApiParam(value = "待选值", required = true) @RequestParam String value) {
		Boolean baseCrudDto = rsEquipmentClassifyService.attrCheck(id, value);
		return R.data(baseCrudDto);
	}


	/**
	 * 懒加载设备分类树形结构
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "懒加载设备分类树形结构", notes = "懒加载设备分类树形结构")
	public R<List<EquipmentClassifyTreeNode>> lazyUnitTree(Long parentId, String title) {
		List<EquipmentClassifyTreeNode> tree = rsEquipmentClassifyService.lazyUnitTree(parentId, title);
		return R.data(tree);
	}
	/**
	 * 导入
	 */
	@PostMapping("/import")
	@ApiOperation(value = "导入", notes = "传入excel")
	public R<Boolean> importData(MultipartFile file)  {
		Boolean result = rsEquipmentClassifyService.importData(file);
		return R.data(result);
	}
}
