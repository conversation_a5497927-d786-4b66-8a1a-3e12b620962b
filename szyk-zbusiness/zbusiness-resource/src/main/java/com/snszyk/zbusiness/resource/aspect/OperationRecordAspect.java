package com.snszyk.zbusiness.resource.aspect;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.zbusiness.resource.annotations.OperationRecord;
import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.service.BusinessNameGenerator;
import com.snszyk.zbusiness.resource.service.IRsOperationRecordService;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 操作记录切面
 * 用于自动记录业务操作日志
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Aspect
@Component
@Slf4j
public class OperationRecordAspect {

    @Autowired
    private IRsOperationRecordService operationRecordService;

    @Autowired
    private BusinessNameGenerator businessNameGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Pointcut("@annotation(com.snszyk.zbusiness.resource.annotations.OperationRecord)")
    public void operationRecordPointcut() {
    }

    /**
     * 环绕通知，记录操作日志
     */
    @Around("operationRecordPointcut()")
    public Object recordOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationRecord annotation = method.getAnnotation(OperationRecord.class);

        Object result = null;
        Object oldData = null;
        Exception exception = null;

        try {
            // 获取操作前的数据（用于UPDATE和DELETE操作）
            if (annotation.operationType() == OperationTypeEnum.UPDATE ||
                annotation.operationType() == OperationTypeEnum.DELETE) {
                oldData = getOldData(joinPoint, annotation);
            }

            // 执行目标方法
            result = joinPoint.proceed();

            // 记录操作日志
            recordOperationLog(joinPoint, annotation, oldData, result, null);

        } catch (Exception e) {
            exception = e;
            // 记录异常操作日志
            recordOperationLog(joinPoint, annotation, oldData, null, e);
            throw e;
        }

        return result;
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, OperationRecord annotation,
                                   Object oldData, Object result, Exception exception) {
        try {
            RsOperationRecordVo recordVo = new RsOperationRecordVo();

            // 设置基本信息 - 使用枚举而非魔法值
            BusinessTypeEnum businessType = annotation.businessType();
            recordVo.setBusinessType(businessType.getCode());
            recordVo.setOperationType(annotation.operationType().getCode());
            recordVo.setOperationDesc(buildOperationDesc(annotation, exception));

            // 获取业务数据信息 - 使用配置化方式
            setBussinessInfo(joinPoint, annotation, recordVo, businessType);

            // 获取操作人信息
            setOperatorInfo(recordVo);

            // 获取组织信息
            setOrgInfo(recordVo);

            // 获取请求信息
            setRequestInfo(recordVo);

            // 设置操作时间
            recordVo.setOperationTime(LocalDateTime.now());

            // 处理数据变更记录
            if (annotation.recordDetails() && oldData != null) {
                Object newData = getNewData(joinPoint, annotation, result);
                setChangeData(recordVo, oldData, newData, annotation, businessType);
            }

            // 异步保存操作记录
            operationRecordService.saveAsync(recordVo);

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 获取操作前数据
     */
    private Object getOldData(ProceedingJoinPoint joinPoint, OperationRecord annotation) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object firstArg = args[0];
                Long businessId = getFieldValue(firstArg, annotation.businessIdField(), Long.class);
                if (businessId != null) {
                    // 这里需要根据业务类型调用相应的查询服务
                    return operationRecordService.getBusinessData(annotation.businessType().getCode(), businessId);
                }
            }
        } catch (Exception e) {
            log.warn("获取操作前数据失败", e);
        }
        return null;
    }

    /**
     * 获取操作后数据
     */
    private Object getNewData(ProceedingJoinPoint joinPoint, OperationRecord annotation, Object result) {
        if (annotation.operationType() == OperationTypeEnum.DELETE) {
            return null;
        }

        // 对于UPDATE操作，如果返回结果包含完整数据，直接使用
        if (result != null) {
            return result;
        }

        // 否则重新查询
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object firstArg = args[0];
                Long businessId = getFieldValue(firstArg, annotation.businessIdField(), Long.class);
                if (businessId != null) {
                    return operationRecordService.getBusinessData(annotation.businessType().getCode(), businessId);
                }
            }
        } catch (Exception e) {
            log.warn("获取操作后数据失败", e);
        }
        return null;
    }

    /**
     * 设置业务信息 - 使用配置化方式
     */
    private void setBussinessInfo(ProceedingJoinPoint joinPoint, OperationRecord annotation,
                                 RsOperationRecordVo recordVo, BusinessTypeEnum businessType) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                Object firstArg = args[0];

                // 获取业务ID
                Long businessId = getFieldValue(firstArg, annotation.businessIdField(), Long.class);
                recordVo.setBusinessId(businessId);

                // 使用业务名称生成器生成名称
                String businessName = businessNameGenerator.generateBusinessName(firstArg, businessType);
                recordVo.setBusinessName(businessName);

                // 获取组织ID - 使用配置的默认字段或注解指定的字段
                String orgFieldName = StringUtil.isNotBlank(annotation.orgIdField())
                    ? annotation.orgIdField()
                    : businessType.getDefaultOrgField();
                Long orgId = getFieldValue(firstArg, orgFieldName, Long.class);
                recordVo.setOrgId(orgId);
            }
        } catch (Exception e) {
            log.warn("设置业务信息失败", e);
        }
    }

    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(RsOperationRecordVo recordVo) {
        try {
            recordVo.setOperatorId(AuthUtil.getUserId());
            recordVo.setOperatorName(AuthUtil.getNickName());
        } catch (Exception e) {
            log.warn("设置操作人信息失败", e);
        }
    }

    /**
     * 设置组织信息
     */
    private void setOrgInfo(RsOperationRecordVo recordVo) {
        try {
            Long orgId = recordVo.getOrgId();
            if (orgId == null) {
                orgId = Long.valueOf(AuthUtil.getDeptId());
                recordVo.setOrgId(orgId);
            }

            Dept dept = SysCache.getDept(orgId);
            if (dept != null) {
                recordVo.setOrgName(dept.getDeptName());
                recordVo.setFullOrgId(dept.getAncestors() + "," + dept.getId());
                recordVo.setFullOrgName(dept.getAncestorName() + "-" + dept.getDeptName());
            }
        } catch (Exception e) {
            log.warn("设置组织信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(RsOperationRecordVo recordVo) {
        try {
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                recordVo.setIpAddress(WebUtil.getIP(request));
                recordVo.setUserAgent(request.getHeader("User-Agent"));
            }
        } catch (Exception e) {
            log.warn("设置请求信息失败", e);
        }
    }

    /**
     * 设置变更数据 - 使用字段配置
     */
    private void setChangeData(RsOperationRecordVo recordVo, Object oldData, Object newData,
                              OperationRecord annotation, BusinessTypeEnum businessType) {
        try {
            if (oldData != null) {
                recordVo.setOldData(JSON.toJSONString(oldData));
            }
            if (newData != null) {
                recordVo.setNewData(JSON.toJSONString(newData));
            }

            // 计算变更字段 - 使用字段配置
            List<String> changedFields = calculateChangedFields(oldData, newData, annotation, businessType);
            recordVo.setChangeFields(JSON.toJSONString(changedFields));

        } catch (Exception e) {
            log.warn("设置变更数据失败", e);
        }
    }

    /**
     * 计算变更字段 - 使用字段配置
     */
    private List<String> calculateChangedFields(Object oldData, Object newData,
                                               OperationRecord annotation, BusinessTypeEnum businessType) {
        List<String> changedFields = new ArrayList<>();

        if (oldData == null || newData == null) {
            return changedFields;
        }

        try {
            // 获取字段配置
            BusinessFieldConfig fieldConfig = getFieldConfig(businessType);
            Set<String> ignoreFields = new HashSet<>(fieldConfig.getIgnoreFields());

            Field[] fields = oldData.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (ignoreFields.contains(field.getName())) {
                    continue;
                }

                field.setAccessible(true);
                Object oldValue = field.get(oldData);
                Object newValue = field.get(newData);

                if (!Objects.equals(oldValue, newValue)) {
                    changedFields.add(field.getName());
                }
            }
        } catch (Exception e) {
            log.warn("计算变更字段失败", e);
        }

        return changedFields;
    }

    /**
     * 获取字段配置
     */
    private BusinessFieldConfig getFieldConfig(BusinessTypeEnum businessType) {
        try {
            String configBeanName = businessType.getFieldConfigClassName();
            return applicationContext.getBean(configBeanName, BusinessFieldConfig.class);
        } catch (Exception e) {
            log.warn("获取字段配置失败，使用默认配置: {}", businessType.getCode());
            return applicationContext.getBean("defaultFieldConfig", BusinessFieldConfig.class);
        }
    }

    /**
     * 构建操作描述
     */
    private String buildOperationDesc(OperationRecord annotation, Exception exception) {
        StringBuilder desc = new StringBuilder();

        // 直接使用枚举值，无需转换
        BusinessTypeEnum businessType = annotation.businessType();
        desc.append(businessType.getName());

        desc.append(annotation.operationType().getName());

        if (StringUtil.isNotBlank(annotation.description())) {
            desc.append(" - ").append(annotation.description());
        }

        if (exception != null) {
            desc.append(" (失败: ").append(exception.getMessage()).append(")");
        }

        return desc.toString();
    }

    /**
     * 获取字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> targetType) {
        if (obj == null || StringUtil.isBlank(fieldName)) {
            return null;
        }

        try {
            Field field = getField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null && targetType.isAssignableFrom(value.getClass())) {
                    return (T) value;
                }
            }
        } catch (Exception e) {
            log.warn("获取字段值失败: {}", fieldName, e);
        }

        return null;
    }

    /**
     * 获取字段（包括父类字段）
     */
    private Field getField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
