/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.aspect;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.resource.annotation.OperationRecord;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordService;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录切面
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Aspect
@Component
@Order(1)
@AllArgsConstructor
@Slf4j
public class OperationRecordAspect {

    private final ISysOperationRecordService operationRecordService;
    private final ExpressionParser parser = new SpelExpressionParser();

    @Pointcut("@annotation(com.snszyk.zbusiness.resource.annotation.OperationRecord)")
    public void operationRecordPointcut() {
    }

    @Around("operationRecordPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationRecord annotation = method.getAnnotation(OperationRecord.class);

        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 获取当前用户信息
        SzykUser currentUser = AuthUtil.getUser();
        if (currentUser == null) {
            log.warn("当前用户为空，跳过操作记录");
            return joinPoint.proceed();
        }

        // 获取请求信息
        HttpServletRequest request = WebUtil.getRequest();

        // 执行前获取旧数据
        Object oldData = null;
        Long businessId = null;
        String businessName = null;

        try {
            // 解析业务ID
            businessId = parseBusinessId(annotation, joinPoint, null);

            // 如果是更新或删除操作，获取旧数据
            if (businessId != null && (annotation.operationType().getCode().equals("UPDATE") ||
                annotation.operationType().getCode().equals("DELETE"))) {
                oldData = operationRecordService.getBusinessData(annotation.businessType().getCode(), businessId);
            }
        } catch (Exception e) {
            log.warn("获取旧数据失败", e);
        }

        // 执行目标方法
        Object result = null;
        Exception exception = null;
        long startTime = System.currentTimeMillis();

        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            try {
                // 记录操作日志
                recordOperation(annotation, joinPoint, result, exception, oldData, currentUser, request, startTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }

        return result;
    }

    /**
     * 记录操作
     */
    private void recordOperation(OperationRecord annotation, ProceedingJoinPoint joinPoint, Object result,
                               Exception exception, Object oldData, SzykUser currentUser,
                               HttpServletRequest request, long startTime) {
        try {
            // 构建操作记录
            SysOperationRecordVo recordVo = buildOperationRecord(annotation, joinPoint, result, exception,
                                                                currentUser, request, startTime);

            if (recordVo == null) {
                return;
            }

            // 如果需要记录字段变更详情
            if (annotation.recordFieldChanges()) {
                recordFieldChanges(annotation, recordVo, oldData, result);
            }

            // 保存操作记录
            if (annotation.async()) {
                operationRecordService.saveAsync(recordVo);
            } else {
                operationRecordService.save(recordVo);
            }

        } catch (Exception e) {
            log.error("记录操作失败", e);
        }
    }

    /**
     * 构建操作记录
     */
    private SysOperationRecordVo buildOperationRecord(OperationRecord annotation, ProceedingJoinPoint joinPoint,
                                                     Object result, Exception exception, SzykUser currentUser,
                                                     HttpServletRequest request, long startTime) {
        try {
            SysOperationRecordVo recordVo = new SysOperationRecordVo();

            // 基本信息
            recordVo.setBusinessModule("RESOURCE");
            recordVo.setBusinessType(annotation.businessType().getCode());

            // 动态判断操作类型
            String operationType = determineOperationType(annotation, joinPoint, result);
            recordVo.setOperationType(operationType);
            recordVo.setOperationTime(LocalDateTime.now());

            // 根据操作类型设置描述
            String description = determineDescription(annotation, operationType);
            if (StringUtil.isBlank(description)) {
                OperationTypeEnum opTypeEnum = OperationTypeEnum.fromCode(operationType);
                description = (opTypeEnum != null ? opTypeEnum.getName() : operationType) + annotation.businessType().getName();
            }
            recordVo.setOperationDesc(description);

            // 解析业务ID和业务名称
            Long businessId = parseBusinessId(annotation, joinPoint, result);
            String businessName = parseBusinessName(annotation, joinPoint, result);
            recordVo.setBusinessId(businessId);
            recordVo.setBusinessName(businessName);

            // 操作人信息
            recordVo.setOperatorId(currentUser.getUserId());
            recordVo.setOperatorName(currentUser.getUserName());
            recordVo.setOperatorDeptId(Long.valueOf(AuthUtil.getDeptId()));

            // 通过DeptScopeUtil获取部门信息
            Dept loginDept = DeptScopeUtil.getLoginDept();
            if (loginDept != null) {
                recordVo.setOperatorDeptName(loginDept.getDeptName());

                // 主管单位信息
                if (loginDept.getUnitId() != null) {
                    recordVo.setOrgId(loginDept.getUnitId());
                    Dept unitDept = SysCache.getDept(loginDept.getUnitId());
                    if (unitDept != null) {
                        recordVo.setOrgName(unitDept.getDeptName());
                    }
                }
            }

            // 请求信息
            if (request != null) {
                recordVo.setRequestIp(getClientIp(request));
                recordVo.setRequestUri(request.getRequestURI());
                recordVo.setRequestMethod(request.getMethod());
            }

            // 记录请求参数
            if (annotation.logParams()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    recordVo.setOldData(JSON.toJSONString(args));
                }
            }

            // 记录返回结果
            if (annotation.logResult() && result != null) {
                recordVo.setNewData(JSON.toJSONString(result));
            }

            // 记录异常信息
            if (annotation.logException() && exception != null) {
                recordVo.setNewData(exception.getMessage());
            }

            return recordVo;

        } catch (Exception e) {
            log.error("构建操作记录失败", e);
            return null;
        }
    }

    /**
     * 记录字段变更详情
     */
    private void recordFieldChanges(OperationRecord annotation, SysOperationRecordVo recordVo,
                                  Object oldData, Object result) {
        try {
            Object newData = null;

            // 从返回结果中提取新数据
            if (result instanceof R) {
                R<?> r = (R<?>) result;
                if (r.isSuccess()) {
                    newData = r.getData();
                }
            } else {
                newData = result;
            }

            // 比较字段变更
            List<FieldChangeDto> fieldChanges = operationRecordService.compareFields(
                annotation.businessType().getCode(), oldData, newData);

            if (fieldChanges != null && !fieldChanges.isEmpty()) {
                // 这里可以将字段变更信息临时存储，在保存操作记录后再保存字段变更
                recordVo.setOldData(JSON.toJSONString(oldData));
                recordVo.setNewData(JSON.toJSONString(newData));
            }

        } catch (Exception e) {
            log.error("记录字段变更详情失败", e);
        }
    }

    /**
     * 解析业务ID
     */
    private Long parseBusinessId(OperationRecord annotation, ProceedingJoinPoint joinPoint, Object result) {
        String expression = annotation.businessIdExpression();
        if (StringUtil.isBlank(expression)) {
            return null;
        }

        try {
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);

            if (value instanceof Long) {
                return (Long) value;
            } else if (value instanceof Number) {
                return ((Number) value).longValue();
            } else if (value instanceof String) {
                return Long.parseLong((String) value);
            }
        } catch (Exception e) {
            log.warn("解析业务ID失败，表达式: {}", expression, e);
        }

        return null;
    }

    /**
     * 解析业务名称
     */
    private String parseBusinessName(OperationRecord annotation, ProceedingJoinPoint joinPoint, Object result) {
        String expression = annotation.businessNameExpression();
        if (StringUtil.isBlank(expression)) {
            return null;
        }

        try {
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);

            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.warn("解析业务名称失败，表达式: {}", expression, e);
        }

        return null;
    }

    /**
     * 创建SpEL表达式上下文
     */
    private EvaluationContext createEvaluationContext(ProceedingJoinPoint joinPoint, Object result) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 添加方法参数
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        if (paramNames != null && args != null) {
            for (int i = 0; i < paramNames.length && i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }

        // 添加返回结果
        if (result != null) {
            context.setVariable("result", result);
        }

        return context;
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 动态判断操作类型
     *
     * @param annotation 注解信息
     * @param joinPoint 切点信息
     * @param result 方法返回结果
     * @return 操作类型代码
     */
    private String determineOperationType(OperationRecord annotation, ProceedingJoinPoint joinPoint, Object result) {
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();

            // 对于saveOrUpdate类型的方法，通过参数中的ID判断是新增还是修改
            if (args != null && args.length > 0) {
                Object firstArg = args[0];
                if (firstArg != null) {
                    // 通过反射获取ID字段
                    try {
                        java.lang.reflect.Field idField = firstArg.getClass().getDeclaredField("id");
                        idField.setAccessible(true);
                        Object idValue = idField.get(firstArg);

                        if (idValue != null) {
                            return OperationTypeEnum.UPDATE.getCode();
                        } else {
                            return OperationTypeEnum.CREATE.getCode();
                        }
                    } catch (Exception e) {
                        log.debug("无法获取参数ID字段，使用默认操作类型: {}", e.getMessage());
                    }
                }
            }

            // 如果无法判断，返回注解中指定的操作类型
            return annotation.operationType().getCode();
        } catch (Exception e) {
            log.warn("判断操作类型失败，使用默认操作类型: {}", e.getMessage());
            return annotation.operationType().getCode();
        }
    }

    /**
     * 根据操作类型确定描述
     *
     * @param annotation 注解信息
     * @param operationType 操作类型
     * @return 操作描述
     */
    private String determineDescription(OperationRecord annotation, String operationType) {
        String description = annotation.description();

        // 如果注解中没有指定描述，根据操作类型生成默认描述
        if (StringUtil.isBlank(description) || "保存互联网资产".equals(description)) {
            if (OperationTypeEnum.CREATE.getCode().equals(operationType)) {
                return "新增" + annotation.businessType().getName();
            } else if (OperationTypeEnum.UPDATE.getCode().equals(operationType)) {
                return "修改" + annotation.businessType().getName();
            }
        }

        return description;
    }
}
