/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.resource.dto.LockApplyDto;
import com.snszyk.zbusiness.resource.entity.LockApply;
import com.snszyk.zbusiness.resource.mapper.LockApplyMapper;
import com.snszyk.zbusiness.resource.service.ILockApplyService;
import com.snszyk.zbusiness.resource.vo.LockApplyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static java.util.Collections.emptyList;

/**
 * 资源解锁申请表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@AllArgsConstructor
@Service
public class LockApplyServiceImpl extends BaseCrudServiceImpl<LockApplyMapper, LockApply, LockApplyDto, LockApplyVo> implements ILockApplyService {


	@Override
	public List<LockApplyDto> listNotApproval(List<Long> lockIdList) {
		List<LockApply> list = this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(lockIdList), LockApply::getLockId, lockIdList)
			.isNull(LockApply::getApplyResult)
			.list();
		return ObjectUtil.isEmpty(list) ? emptyList() : BeanUtil.copy(list, LockApplyDto.class);
	}

	@Override
	public Boolean updateBatchByIds(List<Long> idList, String applyResult) {
		return this.lambdaUpdate()
			.in(LockApply::getId, idList)
			.set(LockApply::getApplyResult, applyResult)
			.update();
	}

	@Override
	public List<LockApplyDto> listByIds(List<Long> applyIdList) {
		List<LockApply> lockApplies = super.listByIds(applyIdList);
		return ObjectUtil.isEmpty(lockApplies) ? Collections.emptyList() : BeanUtil.copy(lockApplies, LockApplyDto.class);
	}

	@Override
	public Boolean updateBatchApplyResultByLockIds(List<Long> lockIdList, String applyResult) {
		return this.lambdaUpdate()
			.in(LockApply::getId, lockIdList)
			.isNull(LockApply::getApplyResult)
			.set(LockApply::getApplyResult, applyResult)
			.update();
	}
}
