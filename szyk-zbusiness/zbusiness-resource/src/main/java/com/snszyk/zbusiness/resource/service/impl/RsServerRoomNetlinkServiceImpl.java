/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.RsServerRoomNetlinkDto;
import com.snszyk.zbusiness.resource.entity.RsServerRoomNetlink;
import com.snszyk.zbusiness.resource.mapper.RsServerRoomNetlinkMapper;
import com.snszyk.zbusiness.resource.service.IRsServerRoomNetlinkService;
import com.snszyk.zbusiness.resource.vo.RsServerRoomNetlinkVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RsServerRoomNetlinkServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RsServerRoomNetlinkServiceImpl extends BaseCrudServiceImpl<RsServerRoomNetlinkMapper, RsServerRoomNetlink, RsServerRoomNetlinkDto, RsServerRoomNetlinkVo> implements IRsServerRoomNetlinkService {


	@Override
	public List<RsServerRoomNetlinkDto> listByRoomId(Long roomId) {
		LambdaQueryWrapper<RsServerRoomNetlink> queryWrapper = Wrappers.<RsServerRoomNetlink>query().lambda()
			.eq(ObjectUtils.isNotEmpty(roomId), RsServerRoomNetlink::getRoomId, roomId);

		List<RsServerRoomNetlink> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RsServerRoomNetlinkDto.class));
	}

	@Override
	public List<RsServerRoomNetlinkDto> listDataByRoomIdList(List<Long> roomIdList) {
		if(CollectionUtil.isEmpty(roomIdList)){
			return new ArrayList<>();
		}
		List<RsServerRoomNetlink> list = this.lambdaQuery().in(RsServerRoomNetlink::getRoomId, roomIdList).list();

		return BeanUtil.copy(list,RsServerRoomNetlinkDto.class);
	}

	@Override
	public int deleteByRoomId(Long roomId) {
		LambdaQueryWrapper<RsServerRoomNetlink> queryWrapper = Wrappers.<RsServerRoomNetlink>query().lambda()
			.eq(ObjectUtils.isNotEmpty(roomId), RsServerRoomNetlink::getRoomId, roomId);

		return baseMapper.delete(queryWrapper);
	}
}
