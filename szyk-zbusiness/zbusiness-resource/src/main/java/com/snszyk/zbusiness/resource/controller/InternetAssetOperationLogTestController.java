/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.annotations.InternetAssetOperationLog;
import com.snszyk.zbusiness.resource.dto.RsInternetDto;
import com.snszyk.zbusiness.resource.service.IInternetAssetOperationLogService;
import com.snszyk.zbusiness.resource.service.IRsInternetService;
import com.snszyk.zbusiness.resource.vo.RsInternetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 互联网资产操作日志测试控制器
 * 用于测试操作日志功能
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/test/internet-asset-log")
@Api(value = "互联网资产操作日志测试", tags = "互联网资产操作日志测试接口")
public class InternetAssetOperationLogTestController {

    private final IRsInternetService rsInternetService;
    private final IInternetAssetOperationLogService operationLogService;

    /**
     * 测试创建操作日志
     */
    @PostMapping("/test-create")
    @ApiOperation(value = "测试创建操作日志", notes = "测试创建操作日志功能")
    @InternetAssetOperationLog(
        operationType = "CREATE",
        description = "测试创建互联网资产",
        businessIdField = "id",
        businessNameField = "systemName",
        orgIdField = "orgId",
        orgNameField = "orgName"
    )
    public R<String> testCreate(@RequestBody RsInternetVo vo) {
        // 模拟创建操作
        boolean result = operationLogService.createOperationLog(
            vo.getId(),
            vo.getSystemName(),
            "CREATE",
            "测试创建互联网资产：" + vo.getSystemName(),
            null,
            vo,
            vo.getOrgId(),
            vo.getOrgName()
        );
        
        return result ? R.success("测试成功") : R.fail("测试失败");
    }

    /**
     * 测试更新操作日志
     */
    @PostMapping("/test-update")
    @ApiOperation(value = "测试更新操作日志", notes = "测试更新操作日志功能")
    @InternetAssetOperationLog(
        operationType = "UPDATE",
        description = "测试更新互联网资产",
        businessIdField = "id",
        businessNameField = "systemName",
        orgIdField = "orgId",
        orgNameField = "orgName"
    )
    public R<String> testUpdate(@RequestBody RsInternetVo vo) {
        // 获取旧数据
        RsInternetDto oldData = null;
        if (vo.getId() != null) {
            oldData = rsInternetService.fetchById(vo.getId());
        }
        
        // 模拟更新操作
        boolean result = operationLogService.createOperationLog(
            vo.getId(),
            vo.getSystemName(),
            "UPDATE",
            "测试更新互联网资产：" + vo.getSystemName(),
            oldData,
            vo,
            vo.getOrgId(),
            vo.getOrgName()
        );
        
        return result ? R.success("测试成功") : R.fail("测试失败");
    }

    /**
     * 测试删除操作日志
     */
    @DeleteMapping("/test-delete/{id}")
    @ApiOperation(value = "测试删除操作日志", notes = "测试删除操作日志功能")
    @InternetAssetOperationLog(
        operationType = "DELETE",
        description = "测试删除互联网资产",
        businessIdField = "id",
        recordDetails = false
    )
    public R<String> testDelete(@PathVariable Long id) {
        // 获取要删除的数据
        RsInternetDto data = rsInternetService.fetchById(id);
        if (data == null) {
            return R.fail("数据不存在");
        }
        
        // 模拟删除操作
        boolean result = operationLogService.createOperationLog(
            id,
            data.getSystemName(),
            "DELETE",
            "测试删除互联网资产：" + data.getSystemName(),
            data,
            null,
            data.getOrgId(),
            data.getOrgName()
        );
        
        return result ? R.success("测试成功") : R.fail("测试失败");
    }
}
