package com.snszyk.zbusiness.resource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    /**
     * 互联网资产
     */
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产", "systemName", "orgId"),

    /**
     * 信息系统
     */
    SYSTEM_RECORD("SYSTEM_RECORD", "信息系统", "systemName", "orgId"),

    /**
     * 设备资产
     */
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产", "equipmentName", "orgId"),

    /**
     * 软件资产
     */
    SOFTWARE_ASSET("SOFTWARE_ASSET", "软件资产", "softwareName", "orgId"),

    /**
     * 项目管理
     */
    PROJECT_MANAGE("PROJECT_MANAGE", "项目管理", "projectName", "deptId"),

    /**
     * 人员管理
     */
    PERSON_MANAGE("PERSON_MANAGE", "人员管理", "personName", "deptId");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型名称
     */
    private final String name;

    /**
     * 默认名称字段
     */
    private final String defaultNameField;

    /**
     * 默认组织字段
     */
    private final String defaultOrgField;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static BusinessTypeEnum getByCode(String code) {
        for (BusinessTypeEnum businessType : values()) {
            if (businessType.getCode().equals(code)) {
                return businessType;
            }
        }
        return null;
    }

    /**
     * 获取业务字段配置类名
     *
     * @return 配置类名
     */
    public String getFieldConfigClassName() {
        switch (this) {
            case INTERNET_ASSET:
                return "internetAssetFieldConfig";
            case SYSTEM_RECORD:
                return "systemRecordFieldConfig";
            case EQUIPMENT_ASSET:
                return "equipmentAssetFieldConfig";
            case SOFTWARE_ASSET:
                return "softwareAssetFieldConfig";
            case PROJECT_MANAGE:
                return "projectManageFieldConfig";
            case PERSON_MANAGE:
                return "personManageFieldConfig";
            default:
                return "defaultFieldConfig";
        }
    }
}
