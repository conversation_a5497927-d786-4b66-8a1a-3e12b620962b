/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordExtensionDto;
import com.snszyk.zbusiness.resource.entity.SysOperationRecordExtension;
import com.snszyk.zbusiness.resource.mapper.SysOperationRecordExtensionMapper;
import com.snszyk.zbusiness.resource.service.ISysOperationRecordExtensionService;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordExtensionVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作记录扩展字段服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
@AllArgsConstructor
@Slf4j
public class SysOperationRecordExtensionServiceImpl extends BaseCrudServiceImpl<SysOperationRecordExtensionMapper, SysOperationRecordExtension, SysOperationRecordExtensionDto, SysOperationRecordExtensionVo> implements ISysOperationRecordExtensionService {

    private final SysOperationRecordExtensionMapper extensionMapper;

    @Override
    public List<SysOperationRecordExtensionDto> getByRecordId(Long recordId) {
        if (recordId == null) {
            return new ArrayList<>();
        }

        try {
            return extensionMapper.selectByRecordId(recordId);
        } catch (Exception e) {
            log.error("根据记录ID获取扩展字段失败，记录ID: {}", recordId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(Long recordId, String businessType, List<FieldChangeDto> fieldChanges) {
        if (recordId == null || fieldChanges == null || fieldChanges.isEmpty()) {
            return;
        }

        try {
            // 先删除已存在的扩展字段
            this.deleteByRecordId(recordId);

            // 转换为实体对象
            List<SysOperationRecordExtension> extensions = fieldChanges.stream()
                .map(change -> convertToExtension(recordId, businessType, change))
                .collect(Collectors.toList());

            // 批量保存
            if (!extensions.isEmpty()) {
                this.saveBatch(extensions);
                log.debug("批量保存扩展字段成功，记录ID: {}, 数量: {}", recordId, extensions.size());
            }
        } catch (Exception e) {
            log.error("批量保存扩展字段失败，记录ID: {}", recordId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRecordId(Long recordId) {
        if (recordId == null) {
            return;
        }

        try {
            int deletedCount = extensionMapper.deleteByRecordId(recordId);
            log.debug("删除扩展字段成功，记录ID: {}, 删除数量: {}", recordId, deletedCount);
        } catch (Exception e) {
            log.error("删除扩展字段失败，记录ID: {}", recordId, e);
            throw e;
        }
    }

    @Override
    public List<FieldChangeDto> convertToFieldChanges(List<SysOperationRecordExtensionDto> extensions) {
        if (extensions == null || extensions.isEmpty()) {
            return new ArrayList<>();
        }

        return extensions.stream()
            .map(this::convertToFieldChange)
            .collect(Collectors.toList());
    }

    /**
     * 将字段变更DTO转换为扩展字段实体
     */
    private SysOperationRecordExtension convertToExtension(Long recordId, String businessType, FieldChangeDto change) {
        SysOperationRecordExtension extension = new SysOperationRecordExtension();
        extension.setRecordId(recordId);
        extension.setBusinessType(businessType);
        extension.setFieldName(change.getFieldName());
        extension.setFieldLabel(change.getFieldLabel());
        extension.setOldValue(change.getOldValue());
        extension.setNewValue(change.getNewValue());
        extension.setOldDisplayValue(change.getOldDisplayValue());
        extension.setNewDisplayValue(change.getNewDisplayValue());
        extension.setFieldType(change.getFieldType());
        return extension;
    }

    /**
     * 将扩展字段DTO转换为字段变更DTO
     */
    private FieldChangeDto convertToFieldChange(SysOperationRecordExtensionDto extension) {
        FieldChangeDto change = new FieldChangeDto();
        change.setFieldName(extension.getFieldName());
        change.setFieldLabel(extension.getFieldLabel());
        change.setOldValue(extension.getOldValue());
        change.setNewValue(extension.getNewValue());
        change.setOldDisplayValue(extension.getOldDisplayValue());
        change.setNewDisplayValue(extension.getNewDisplayValue());
        change.setFieldType(extension.getFieldType());
        change.setHasChanged(!java.util.Objects.equals(extension.getOldValue(), extension.getNewValue()));
        return change;
    }
}
