/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.resource.dto.RsUnifiedCatalogDetailDto;
import com.snszyk.zbusiness.resource.entity.RsUnifiedCatalogDetail;
import com.snszyk.zbusiness.resource.vo.RsUnifiedCatalogDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统谈分签目录明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
public interface RsUnifiedCatalogDetailMapper extends BaseMapper<RsUnifiedCatalogDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rsUnifiedCatalogDetail
	 * @return
	 */
	List<RsUnifiedCatalogDetailVo> selectRsUnifiedCatalogDetailPage(IPage page, RsUnifiedCatalogDetailVo rsUnifiedCatalogDetail);

    List<RsUnifiedCatalogDetailDto> queryByName(@Param("name") String name);
}
