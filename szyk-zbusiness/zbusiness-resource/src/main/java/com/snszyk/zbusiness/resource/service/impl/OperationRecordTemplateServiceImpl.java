package com.snszyk.zbusiness.resource.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordTemplate;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.mapper.RsOperationRecordTemplateMapper;
import com.snszyk.zbusiness.resource.service.DynamicFieldConfigService;
import com.snszyk.zbusiness.resource.service.OperationRecordTemplateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 操作记录模板服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationRecordTemplateServiceImpl implements OperationRecordTemplateService {

    private final RsOperationRecordTemplateMapper templateMapper;
    private final DynamicFieldConfigService dynamicFieldConfigService;

    @Override
    @Cacheable(value = "defaultTemplate", key = "#businessType.code")
    public RsOperationRecordTemplate getDefaultTemplate(BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsOperationRecordTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordTemplate::getBusinessType, businessType.getCode())
                   .eq(RsOperationRecordTemplate::getIsDefault, true)
                   .orderByDesc(RsOperationRecordTemplate::getCreateTime)
                   .last("LIMIT 1");
        
        return templateMapper.selectOne(queryWrapper);
    }

    @Override
    @Cacheable(value = "businessTypeTemplates", key = "#businessType.code")
    public List<RsOperationRecordTemplate> getTemplates(BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsOperationRecordTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordTemplate::getBusinessType, businessType.getCode())
                   .orderByDesc(RsOperationRecordTemplate::getIsDefault)
                   .orderByDesc(RsOperationRecordTemplate::getCreateTime);
        
        return templateMapper.selectList(queryWrapper);
    }

    @Override
    public RsOperationRecordTemplate getTemplateByName(BusinessTypeEnum businessType, String templateName) {
        LambdaQueryWrapper<RsOperationRecordTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsOperationRecordTemplate::getBusinessType, businessType.getCode())
                   .eq(RsOperationRecordTemplate::getTemplateName, templateName);
        
        return templateMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"defaultTemplate", "businessTypeTemplates"}, key = "#template.businessType")
    public boolean createTemplate(RsOperationRecordTemplate template) {
        // 验证模板配置
        BusinessTypeEnum businessType = BusinessTypeEnum.fromCode(template.getBusinessType());
        Map<String, String> validationErrors = validateTemplateConfig(businessType, template.getTemplateConfig());
        if (!validationErrors.isEmpty()) {
            log.error("模板配置验证失败: {}", validationErrors);
            return false;
        }

        // 如果设置为默认模板，需要将其他模板的默认标志设为false
        if (Boolean.TRUE.equals(template.getIsDefault())) {
            clearDefaultTemplate(template.getBusinessType());
        }

        return templateMapper.insert(template) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"defaultTemplate", "businessTypeTemplates"}, key = "#template.businessType")
    public boolean updateTemplate(RsOperationRecordTemplate template) {
        // 验证模板配置
        BusinessTypeEnum businessType = BusinessTypeEnum.fromCode(template.getBusinessType());
        Map<String, String> validationErrors = validateTemplateConfig(businessType, template.getTemplateConfig());
        if (!validationErrors.isEmpty()) {
            log.error("模板配置验证失败: {}", validationErrors);
            return false;
        }

        // 如果设置为默认模板，需要将其他模板的默认标志设为false
        if (Boolean.TRUE.equals(template.getIsDefault())) {
            clearDefaultTemplate(template.getBusinessType());
        }

        return templateMapper.updateById(template) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long templateId) {
        RsOperationRecordTemplate template = templateMapper.selectById(templateId);
        if (template == null) {
            return false;
        }

        boolean result = templateMapper.deleteById(templateId) > 0;
        if (result) {
            // 清除缓存
            clearTemplateCache(template.getBusinessType());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultTemplate(BusinessTypeEnum businessType, Long templateId) {
        // 清除当前默认模板
        clearDefaultTemplate(businessType.getCode());

        // 设置新的默认模板
        LambdaUpdateWrapper<RsOperationRecordTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RsOperationRecordTemplate::getId, templateId)
                    .set(RsOperationRecordTemplate::getIsDefault, true);

        boolean result = templateMapper.update(null, updateWrapper) > 0;
        if (result) {
            clearTemplateCache(businessType.getCode());
        }
        return result;
    }

    @Override
    public Map<String, Object> parseTemplateConfig(RsOperationRecordTemplate template) {
        if (template == null || StringUtil.isBlank(template.getTemplateConfig())) {
            return new HashMap<>();
        }

        try {
            return JSON.parseObject(template.getTemplateConfig(), new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.error("解析模板配置失败: {}", template.getTemplateConfig(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, String> validateTemplateConfig(BusinessTypeEnum businessType, String templateConfig) {
        Map<String, String> errors = new HashMap<>();

        if (StringUtil.isBlank(templateConfig)) {
            errors.put("templateConfig", "模板配置不能为空");
            return errors;
        }

        try {
            Map<String, Object> config = JSON.parseObject(templateConfig, new TypeReference<Map<String, Object>>() {});
            
            // 验证必要的配置项
            if (!config.containsKey("fields")) {
                errors.put("fields", "模板配置必须包含字段配置");
            }

            // 验证字段配置
            Object fieldsObj = config.get("fields");
            if (fieldsObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> fields = (List<Map<String, Object>>) fieldsObj;
                validateFieldConfigs(businessType, fields, errors);
            }

        } catch (Exception e) {
            errors.put("templateConfig", "模板配置格式不正确: " + e.getMessage());
        }

        return errors;
    }

    @Override
    public Map<String, Object> applyTemplate(RsOperationRecordTemplate template, Map<String, Object> operationData) {
        Map<String, Object> result = new HashMap<>(operationData);
        
        if (template == null) {
            return result;
        }

        Map<String, Object> templateConfig = parseTemplateConfig(template);
        
        // 应用字段配置
        Object fieldsObj = templateConfig.get("fields");
        if (fieldsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> fields = (List<Map<String, Object>>) fieldsObj;
            applyFieldConfigs(fields, result);
        }

        // 应用其他配置
        applyOtherConfigs(templateConfig, result);

        return result;
    }

    @Override
    public List<Map<String, Object>> getTemplateFieldConfigs(RsOperationRecordTemplate template) {
        Map<String, Object> config = parseTemplateConfig(template);
        Object fieldsObj = config.get("fields");
        
        if (fieldsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> fields = (List<Map<String, Object>>) fieldsObj;
            return fields;
        }
        
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RsOperationRecordTemplate copyTemplate(Long sourceTemplateId, String newTemplateName, String newDescription) {
        RsOperationRecordTemplate sourceTemplate = templateMapper.selectById(sourceTemplateId);
        if (sourceTemplate == null) {
            return null;
        }

        RsOperationRecordTemplate newTemplate = BeanUtil.copy(sourceTemplate, RsOperationRecordTemplate.class);
        newTemplate.setId(null);
        newTemplate.setTemplateName(newTemplateName);
        newTemplate.setDescription(newDescription);
        newTemplate.setIsDefault(false); // 复制的模板默认不是默认模板
        newTemplate.setCreateTime(null);
        newTemplate.setUpdateTime(null);

        if (templateMapper.insert(newTemplate) > 0) {
            clearTemplateCache(newTemplate.getBusinessType());
            return newTemplate;
        }

        return null;
    }

    @Override
    public String exportTemplateConfig(Long templateId) {
        RsOperationRecordTemplate template = templateMapper.selectById(templateId);
        if (template == null) {
            return null;
        }

        Map<String, Object> exportData = new HashMap<>();
        exportData.put("templateName", template.getTemplateName());
        exportData.put("businessType", template.getBusinessType());
        exportData.put("description", template.getDescription());
        exportData.put("templateConfig", parseTemplateConfig(template));

        return JSON.toJSONString(exportData, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RsOperationRecordTemplate importTemplateConfig(BusinessTypeEnum businessType, String templateName, String configJson) {
        try {
            Map<String, Object> importData = JSON.parseObject(configJson, new TypeReference<Map<String, Object>>() {});
            
            RsOperationRecordTemplate template = new RsOperationRecordTemplate();
            template.setBusinessType(businessType.getCode());
            template.setTemplateName(templateName);
            template.setDescription((String) importData.get("description"));
            template.setIsDefault(false);
            
            Object templateConfigObj = importData.get("templateConfig");
            if (templateConfigObj != null) {
                template.setTemplateConfig(JSON.toJSONString(templateConfigObj));
            }

            if (createTemplate(template)) {
                return template;
            }

        } catch (Exception e) {
            log.error("导入模板配置失败", e);
        }

        return null;
    }

    /**
     * 清除默认模板标志
     */
    private void clearDefaultTemplate(String businessType) {
        LambdaUpdateWrapper<RsOperationRecordTemplate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RsOperationRecordTemplate::getBusinessType, businessType)
                    .set(RsOperationRecordTemplate::getIsDefault, false);
        templateMapper.update(null, updateWrapper);
    }

    /**
     * 清除模板缓存
     */
    @CacheEvict(value = {"defaultTemplate", "businessTypeTemplates"}, key = "#businessType")
    private void clearTemplateCache(String businessType) {
        log.debug("清除业务类型 {} 的模板缓存", businessType);
    }

    /**
     * 验证字段配置
     */
    private void validateFieldConfigs(BusinessTypeEnum businessType, List<Map<String, Object>> fields, Map<String, String> errors) {
        // 这里可以添加更详细的字段配置验证逻辑
        for (int i = 0; i < fields.size(); i++) {
            Map<String, Object> field = fields.get(i);
            String fieldName = (String) field.get("fieldName");
            if (StringUtil.isBlank(fieldName)) {
                errors.put("fields[" + i + "].fieldName", "字段名称不能为空");
            }
        }
    }

    /**
     * 应用字段配置
     */
    private void applyFieldConfigs(List<Map<String, Object>> fields, Map<String, Object> operationData) {
        // 这里可以根据字段配置对操作数据进行处理
        for (Map<String, Object> field : fields) {
            String fieldName = (String) field.get("fieldName");
            Object defaultValue = field.get("defaultValue");
            
            if (StringUtil.isNotBlank(fieldName) && defaultValue != null && !operationData.containsKey(fieldName)) {
                operationData.put(fieldName, defaultValue);
            }
        }
    }

    /**
     * 应用其他配置
     */
    private void applyOtherConfigs(Map<String, Object> templateConfig, Map<String, Object> operationData) {
        // 这里可以应用其他模板配置，如格式化规则、验证规则等
    }
}
