/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.resource.dto.RsInternetFileDto;
import com.snszyk.zbusiness.resource.entity.RsInternetFile;
import com.snszyk.zbusiness.resource.mapper.RsInternetFileMapper;
import com.snszyk.zbusiness.resource.service.IRsInternetFileService;
import com.snszyk.zbusiness.resource.vo.RsInternetFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 互联网资源文档表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@AllArgsConstructor
@Service
public class RsInternetFileServiceImpl extends BaseCrudServiceImpl<RsInternetFileMapper, RsInternetFile, RsInternetFileDto, RsInternetFileVo> implements IRsInternetFileService {


	@Override
	public boolean batchSave(ArrayList<RsInternetFileVo> list) {

		if (CollectionUtil.isEmpty(list)) {
			return false;
		}
		return SpringUtil.getBean(RsInternetFileServiceImpl.class).saveBatch(BeanUtil.copy(list, RsInternetFile.class));
	}

	@Override
	public List<RsInternetFileDto> listByBusinessIds(List<Long> businessIds) {

		if (CollectionUtil.isEmpty(businessIds)) {
			return new ArrayList<>();
		}
		List<RsInternetFileDto> list = this.baseMapper.listByBusinessIds(businessIds);
		return list;
	}

	@Override
	public List<RsInternetFileDto> listByBusiness(Long businessId, String businessType) {
		if (businessId == null | StringUtil.isEmpty(businessType)) {
			return new ArrayList<>();
		}
		List<RsInternetFile> list = this.lambdaQuery().eq(RsInternetFile::getBusinessId, businessId)
			.eq(RsInternetFile::getBusinessType, businessType)
			.list();
		return BeanUtil.copy(list,RsInternetFileDto.class);
	}

	/**
	 * 根据资产id删除
	 *
	 * @param internetId
	 * @return
	 */
	@Override
	public boolean deleteByInternetId(Long internetId) {
		boolean remove = this.lambdaUpdate().eq(RsInternetFile::getBusinessId, internetId).remove();
		return remove;
	}
}
