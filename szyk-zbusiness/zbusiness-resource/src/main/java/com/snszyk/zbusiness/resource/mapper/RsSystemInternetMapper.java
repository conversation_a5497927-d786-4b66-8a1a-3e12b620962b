/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.resource.entity.RsSystemInternet;
import com.snszyk.zbusiness.resource.vo.RsSystemInternetVo;

import java.util.List;

/**
 * 信息系统互联网明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
public interface RsSystemInternetMapper extends BaseMapper<RsSystemInternet> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param rsSystemInternet
	 * @return
	 */
	List<RsSystemInternetVo> selectRsSystemInternetPage(IPage page, RsSystemInternetVo rsSystemInternet);

}
