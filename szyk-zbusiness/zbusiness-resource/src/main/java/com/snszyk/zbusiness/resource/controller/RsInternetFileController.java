/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.service.logic.RsInternetFileLogicService;
import com.snszyk.zbusiness.resource.vo.RsInternetFileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 互联网资源文档表 控制器
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Deprecated
@RestController
@AllArgsConstructor
//@RequestMapping("/rsinternetfile")
@Api(value = "互联网资源文档表", tags = "互联网资源文档表接口")
public class RsInternetFileController extends BaseCrudController {

	// private final IRsInternetFileService rsInternetFileService;

	private final RsInternetFileLogicService rsInternetFileLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsInternetFileLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "RsInternetFileVo")
	public R<BaseCrudDto> save(@RequestBody RsInternetFileVo v) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "RsInternetFileVo")
	public R<IPage<BaseCrudDto>> page(RsInternetFileVo v) {
		IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "列表", notes = "RsInternetFileVo")
	public R<List<BaseCrudDto>> list(RsInternetFileVo v) {
		List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
		return R.data(listQueryResult);
	}

	/**
	 * 获取单条
	 */
	@GetMapping("/fetchOne")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取单条数据", notes = "RsInternetFileVo")
	public R<BaseCrudDto> fetchOne(RsInternetFileVo v) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 根据ID获取数据
	 */
	@Override
	@GetMapping("/fetchById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<BaseCrudDto> fetchById(Long id) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
		return R.data(baseCrudDto);
	}

	/**
	 * 删除
	 */
	@Override
	@PostMapping("/deleteById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "删除", notes = "id")
	public R<Boolean> deleteById(Long id) {
		Boolean result = this.fetchBaseLogicService().deleteById(id);
		return R.data(result);
	}

}
