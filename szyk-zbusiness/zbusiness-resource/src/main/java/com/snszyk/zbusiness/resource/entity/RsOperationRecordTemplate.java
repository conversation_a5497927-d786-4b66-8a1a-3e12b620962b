package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录模板实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_operation_record_template")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录模板实体", description = "操作记录模板实体")
public class RsOperationRecordTemplate extends BaseCrudEntity {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板配置
     */
    @ApiModelProperty(value = "模板配置")
    private String templateConfig;

    /**
     * 是否默认模板
     */
    @ApiModelProperty(value = "是否默认模板")
    private Boolean isDefault;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String description;
}
