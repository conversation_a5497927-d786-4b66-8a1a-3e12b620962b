package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.entity.RsOperationRecord;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordPageVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 操作记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Repository
public interface RsOperationRecordMapper extends BaseMapper<RsOperationRecord> {

    /**
     * 分页查询操作记录
     *
     * @param page 分页参数
     * @param vo   查询条件
     * @return 分页结果
     */
    IPage<RsOperationRecordDto> pageList(Page<RsOperationRecordDto> page, @Param("vo") RsOperationRecordPageVo vo);
}
