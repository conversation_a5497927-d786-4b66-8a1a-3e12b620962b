package com.snszyk.zbusiness.resource.config.impl;

import com.snszyk.zbusiness.resource.config.BusinessFieldConfig;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 默认字段配置实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Component("defaultFieldConfig")
public class DefaultFieldConfig implements BusinessFieldConfig {

    private static final Map<String, String> FIELD_LABEL_MAP = new HashMap<>();
    private static final Map<String, String> FIELD_TYPE_MAP = new HashMap<>();
    private static final List<String> QUERYABLE_FIELDS = new ArrayList<>();
    private static final List<String> IGNORE_FIELDS = Arrays.asList(
        "createTime", "updateTime", "createUser", "updateUser", "createDept", "updateDept"
    );

    static {
        // 基础字段标签映射
        FIELD_LABEL_MAP.put("id", "主键");
        FIELD_LABEL_MAP.put("name", "名称");
        FIELD_LABEL_MAP.put("code", "编码");
        FIELD_LABEL_MAP.put("status", "状态");
        FIELD_LABEL_MAP.put("remark", "备注");
        FIELD_LABEL_MAP.put("orgId", "组织ID");
        FIELD_LABEL_MAP.put("orgName", "组织名称");
        FIELD_LABEL_MAP.put("deptId", "部门ID");
        FIELD_LABEL_MAP.put("deptName", "部门名称");

        // 基础字段类型映射
        FIELD_TYPE_MAP.put("id", "LONG");
        FIELD_TYPE_MAP.put("name", "STRING");
        FIELD_TYPE_MAP.put("code", "STRING");
        FIELD_TYPE_MAP.put("status", "INTEGER");
        FIELD_TYPE_MAP.put("remark", "TEXT");
        FIELD_TYPE_MAP.put("orgId", "LONG");
        FIELD_TYPE_MAP.put("orgName", "STRING");
        FIELD_TYPE_MAP.put("deptId", "LONG");
        FIELD_TYPE_MAP.put("deptName", "STRING");

        // 基础可查询字段
        QUERYABLE_FIELDS.addAll(Arrays.asList("name", "code", "status"));
    }

    @Override
    public Map<String, String> getFieldLabelMap() {
        return Collections.unmodifiableMap(FIELD_LABEL_MAP);
    }

    @Override
    public Map<String, String> getFieldTypeMap() {
        return Collections.unmodifiableMap(FIELD_TYPE_MAP);
    }

    @Override
    public List<String> getQueryableFields() {
        return Collections.unmodifiableList(QUERYABLE_FIELDS);
    }

    @Override
    public String getDefaultNameField() {
        return "name";
    }

    @Override
    public String getDefaultOrgField() {
        return "orgId";
    }

    @Override
    public List<String> getIgnoreFields() {
        return Collections.unmodifiableList(IGNORE_FIELDS);
    }

    @Override
    public Map<String, String> getFieldGroupMap() {
        Map<String, String> groupMap = new HashMap<>();
        groupMap.put("name", "基本信息");
        groupMap.put("code", "基本信息");
        groupMap.put("status", "状态信息");
        groupMap.put("remark", "其他信息");
        return Collections.unmodifiableMap(groupMap);
    }

    @Override
    public List<String> getSensitiveFields() {
        return Arrays.asList("password", "phone", "email");
    }

    @Override
    public Map<String, String> getDictFieldMap() {
        Map<String, String> dictMap = new HashMap<>();
        dictMap.put("status", "common_status");
        dictMap.put("type", "common_type");
        return Collections.unmodifiableMap(dictMap);
    }
}
