package com.snszyk.zbusiness.resource.service;

import com.snszyk.zbusiness.resource.entity.RsBusinessFieldDefinition;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 动态字段配置服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface DynamicFieldConfigService {

    /**
     * 获取业务类型的字段定义列表
     *
     * @param businessType 业务类型
     * @return 字段定义列表
     */
    List<RsBusinessFieldDefinition> getFieldDefinitions(BusinessTypeEnum businessType);

    /**
     * 获取业务类型的字段定义映射
     *
     * @param businessType 业务类型
     * @return 字段定义映射 (字段名 -> 字段定义)
     */
    Map<String, RsBusinessFieldDefinition> getFieldDefinitionMap(BusinessTypeEnum businessType);

    /**
     * 获取字段的中文标签
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 中文标签
     */
    String getFieldLabel(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取字段类型
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 字段类型
     */
    String getFieldType(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取字段分组
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 字段分组
     */
    String getFieldGroup(BusinessTypeEnum businessType, String fieldName);

    /**
     * 判断是否为敏感字段
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 是否敏感
     */
    boolean isSensitiveField(BusinessTypeEnum businessType, String fieldName);

    /**
     * 判断是否为可搜索字段
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 是否可搜索
     */
    boolean isSearchableField(BusinessTypeEnum businessType, String fieldName);

    /**
     * 判断是否需要索引
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 是否需要索引
     */
    boolean isIndexableField(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取字段的字典代码
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @return 字典代码
     */
    String getFieldDictCode(BusinessTypeEnum businessType, String fieldName);

    /**
     * 获取业务类型的所有分组
     *
     * @param businessType 业务类型
     * @return 分组列表
     */
    List<String> getFieldGroups(BusinessTypeEnum businessType);

    /**
     * 获取分组下的字段列表
     *
     * @param businessType 业务类型
     * @param fieldGroup 字段分组
     * @return 字段列表
     */
    List<RsBusinessFieldDefinition> getFieldsByGroup(BusinessTypeEnum businessType, String fieldGroup);

    /**
     * 获取可搜索字段列表
     *
     * @param businessType 业务类型
     * @return 可搜索字段列表
     */
    List<RsBusinessFieldDefinition> getSearchableFields(BusinessTypeEnum businessType);

    /**
     * 获取需要索引的字段列表
     *
     * @param businessType 业务类型
     * @return 需要索引的字段列表
     */
    List<RsBusinessFieldDefinition> getIndexableFields(BusinessTypeEnum businessType);

    /**
     * 刷新字段定义缓存
     *
     * @param businessType 业务类型
     */
    void refreshFieldDefinitions(BusinessTypeEnum businessType);

    /**
     * 获取可索引的字段列表
     *
     * @param businessType 业务类型
     * @return 可索引的字段列表
     */
    List<RsBusinessFieldDefinition> getIndexableFields(BusinessTypeEnum businessType);

    /**
     * 刷新所有字段定义缓存
     */
    void refreshAllFieldDefinitions();
}
