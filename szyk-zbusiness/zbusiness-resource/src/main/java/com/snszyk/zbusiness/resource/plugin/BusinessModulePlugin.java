package com.snszyk.zbusiness.resource.plugin;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 业务模块插件接口
 * 各业务模块实现此接口以提供操作记录的自定义功能
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface BusinessModulePlugin {

    /**
     * 获取支持的业务类型
     *
     * @return 业务类型列表
     */
    List<BusinessTypeEnum> getSupportedBusinessTypes();

    /**
     * 获取插件名称
     *
     * @return 插件名称
     */
    String getPluginName();

    /**
     * 获取插件版本
     *
     * @return 插件版本
     */
    String getPluginVersion();

    /**
     * 获取插件描述
     *
     * @return 插件描述
     */
    String getPluginDescription();

    /**
     * 获取插件优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    int getPriority();

    /**
     * 插件是否启用
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 预处理操作记录数据
     * 在保存操作记录之前调用，可以对数据进行预处理
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param operationType 操作类型
     * @param oldData 变更前数据
     * @param newData 变更后数据
     * @return 预处理后的数据
     */
    Map<String, Object> preprocessOperationData(BusinessTypeEnum businessType, Long businessId, String operationType, 
                                               Map<String, Object> oldData, Map<String, Object> newData);

    /**
     * 后处理操作记录数据
     * 在保存操作记录之后调用，可以执行额外的业务逻辑
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param recordId 操作记录ID
     * @param operationType 操作类型
     */
    void postprocessOperationRecord(BusinessTypeEnum businessType, Long businessId, Long recordId, String operationType);

    /**
     * 自定义字段值转换
     * 提供业务特定的字段值转换逻辑
     *
     * @param businessType 业务类型
     * @param fieldName 字段名称
     * @param fieldValue 字段值
     * @return 转换后的显示值，返回null表示使用默认转换逻辑
     */
    String customFieldValueConversion(BusinessTypeEnum businessType, String fieldName, String fieldValue);

    /**
     * 自定义操作描述生成
     * 提供业务特定的操作描述生成逻辑
     *
     * @param businessType 业务类型
     * @param operationType 操作类型
     * @param businessName 业务名称
     * @param changeFields 变更字段列表
     * @return 自定义操作描述，返回null表示使用默认逻辑
     */
    String customOperationDescription(BusinessTypeEnum businessType, String operationType, String businessName, List<String> changeFields);

    /**
     * 获取业务特定的扩展配置
     *
     * @param businessType 业务类型
     * @return 扩展配置
     */
    Map<String, Object> getExtensionConfig(BusinessTypeEnum businessType);

    /**
     * 验证业务数据
     * 在记录操作之前验证业务数据的有效性
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param operationType 操作类型
     * @param data 业务数据
     * @return 验证结果，空Map表示验证通过
     */
    Map<String, String> validateBusinessData(BusinessTypeEnum businessType, Long businessId, String operationType, Map<String, Object> data);

    /**
     * 获取业务数据的敏感字段列表
     *
     * @param businessType 业务类型
     * @return 敏感字段列表
     */
    List<String> getSensitiveFields(BusinessTypeEnum businessType);

    /**
     * 获取业务数据的索引字段列表
     *
     * @param businessType 业务类型
     * @return 索引字段列表
     */
    List<String> getIndexFields(BusinessTypeEnum businessType);
}
