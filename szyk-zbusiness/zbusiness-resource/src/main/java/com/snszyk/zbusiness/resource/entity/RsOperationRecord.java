package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作记录实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_operation_record")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录实体", description = "操作记录实体")
public class RsOperationRecord extends BaseCrudEntity {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 组织全路径ID
     */
    @ApiModelProperty(value = "组织全路径ID")
    private String fullOrgId;

    /**
     * 组织全路径名称
     */
    @ApiModelProperty(value = "组织全路径名称")
    private String fullOrgName;

    /**
     * 变更前数据
     */
    @ApiModelProperty(value = "变更前数据")
    private String oldData;

    /**
     * 变更后数据
     */
    @ApiModelProperty(value = "变更后数据")
    private String newData;

    /**
     * 变更字段列表
     */
    @ApiModelProperty(value = "变更字段列表")
    private String changeFields;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作IP地址
     */
    @ApiModelProperty(value = "操作IP地址")
    private String ipAddress;

    /**
     * 用户代理
     */
    @ApiModelProperty(value = "用户代理")
    private String userAgent;
}
