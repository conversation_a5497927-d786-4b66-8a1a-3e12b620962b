package com.snszyk.zbusiness.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupDto;
import com.snszyk.zbusiness.resource.entity.RsSoftwareGroup;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupPageVo;
import org.apache.ibatis.annotations.Param;

public interface RsSoftwareGroupMapper extends BaseMapper<RsSoftwareGroup> {

	/**
	 * 自定义分页
	 *
	 * @param page

	 * @return
	 */
	IPage<RsSoftwareGroupDto> pageList(Page<RsSoftwareGroup> page, @Param("vo") RsSoftwareGroupPageVo vo);


}
