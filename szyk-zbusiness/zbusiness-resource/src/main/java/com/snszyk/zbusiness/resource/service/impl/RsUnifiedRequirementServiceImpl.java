/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.RsUnifiedRequirementDto;
import com.snszyk.zbusiness.resource.entity.RsUnifiedRequirement;
import com.snszyk.zbusiness.resource.mapper.RsUnifiedRequirementMapper;
import com.snszyk.zbusiness.resource.service.IRsUnifiedRequirementService;
import com.snszyk.zbusiness.resource.vo.RsUnifiedRequirementPageVo;
import com.snszyk.zbusiness.resource.vo.RsUnifiedRequirementVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 统谈分签采购需求 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@AllArgsConstructor
@Service
public class RsUnifiedRequirementServiceImpl extends BaseCrudServiceImpl<RsUnifiedRequirementMapper, RsUnifiedRequirement, RsUnifiedRequirementDto, RsUnifiedRequirementVo> implements IRsUnifiedRequirementService {


	/**
	 * 历史分页
	 *
	 * @param v
	 * @return
	 */
	@Override
	public IPage<RsUnifiedRequirementDto> historyPage(RsUnifiedRequirementVo v) {
		Page<RsUnifiedRequirement> page = new Page(v.getCurrent(), v.getSize());
		LambdaQueryWrapper<RsUnifiedRequirement> queryWrapper = Wrappers.<RsUnifiedRequirement>query().lambda()
			.eq(RsUnifiedRequirement::getContactPersonId, v.getContactPersonId())
			.eq(RsUnifiedRequirement::getOrgId, v.getOrgId())
			.eq(RsUnifiedRequirement::getCatalogDetailId, v.getCatalogDetailId())
			.orderByDesc(RsUnifiedRequirement::getCreateTime);
		IPage<RsUnifiedRequirement> majorPage = baseMapper.selectPage(page, queryWrapper);
		return majorPage.convert(e -> BeanUtil.copy(e, RsUnifiedRequirementDto.class));
	}

	/**
	 * 修改状态
	 *
	 * @param id
	 * @param requireStatus
	 * @return
	 */
	@Override
	public boolean updateStatus(Long id, String requireStatus) {
		return this.lambdaUpdate().eq(BaseCrudEntity::getId, id)
			.set(RsUnifiedRequirement::getRequireStatus, requireStatus).update();
	}

	/**
	 * 分页
	 *
	 * @param v
	 * @return
	 */
	@Override
	public IPage<RsUnifiedRequirementDto> pageList(RsUnifiedRequirementPageVo v) {
		return this.baseMapper.pageList(new Page<>(v.getCurrent(), v.getSize()), v);
	}

	@Override
	public Integer countByUserScope(Long id, Long userId, Long deptId) {
		List<RsUnifiedRequirement> list = this.lambdaQuery().eq(RsUnifiedRequirement::getCatalogDetailId, id)
			.eq(RsUnifiedRequirement::getCreateUser, userId)
			.eq(RsUnifiedRequirement::getCreateDept, deptId).list();
		if(CollectionUtil.isNotEmpty(list)){
			return list.size();
		}
		return CommonConstant.ZERO ;
	}
}
