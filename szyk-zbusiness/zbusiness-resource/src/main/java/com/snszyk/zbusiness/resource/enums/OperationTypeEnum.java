/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {

    /**
     * 创建
     */
    CREATE("CREATE", "创建"),

    /**
     * 更新
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除"),

    /**
     * 查询
     */
    QUERY("QUERY", "查询"),

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 状态变更
     */
    STATUS_CHANGE("STATUS_CHANGE", "状态变更");

    /**
     * 操作类型代码
     */
    private final String code;

    /**
     * 操作类型名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OperationTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationTypeEnum operationType : values()) {
            if (operationType.getCode().equals(code)) {
                return operationType;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举，如果不存在则抛出异常
     *
     * @param code 编码
     * @return 枚举值
     */
    public static OperationTypeEnum fromCode(String code) {
        OperationTypeEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("未知的操作类型编码: " + code);
        }
        return result;
    }
}
