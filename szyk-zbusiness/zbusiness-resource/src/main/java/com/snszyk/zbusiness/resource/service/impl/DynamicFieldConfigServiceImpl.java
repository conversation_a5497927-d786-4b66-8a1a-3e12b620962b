package com.snszyk.zbusiness.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.entity.RsBusinessFieldDefinition;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.mapper.RsBusinessFieldDefinitionMapper;
import com.snszyk.zbusiness.resource.service.DynamicFieldConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态字段配置服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class DynamicFieldConfigServiceImpl implements DynamicFieldConfigService {

    private final RsBusinessFieldDefinitionMapper fieldDefinitionMapper;

    @Override
    @Cacheable(value = "fieldDefinitions", key = "#businessType.code")
    public List<RsBusinessFieldDefinition> getFieldDefinitions(BusinessTypeEnum businessType) {
        LambdaQueryWrapper<RsBusinessFieldDefinition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RsBusinessFieldDefinition::getBusinessType, businessType.getCode())
                   .eq(RsBusinessFieldDefinition::getStatus, 1)
                   .eq(RsBusinessFieldDefinition::getIsDeleted, 0)
                   .orderByAsc(RsBusinessFieldDefinition::getDisplayOrder);
        
        return fieldDefinitionMapper.selectList(queryWrapper);
    }

    @Override
    @Cacheable(value = "fieldDefinitionMaps", key = "#businessType.code")
    public Map<String, RsBusinessFieldDefinition> getFieldDefinitionMap(BusinessTypeEnum businessType) {
        List<RsBusinessFieldDefinition> definitions = getFieldDefinitions(businessType);
        return definitions.stream()
                .collect(Collectors.toMap(
                    RsBusinessFieldDefinition::getFieldName,
                    definition -> definition,
                    (existing, replacement) -> existing
                ));
    }

    @Override
    public String getFieldLabel(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null ? definition.getFieldLabel() : fieldName;
    }

    @Override
    public String getFieldType(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null ? definition.getFieldType() : "STRING";
    }

    @Override
    public String getFieldGroup(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null ? definition.getFieldGroup() : "基本信息";
    }

    @Override
    public boolean isSensitiveField(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null && Boolean.TRUE.equals(definition.getIsSensitive());
    }

    @Override
    public boolean isSearchableField(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null && Boolean.TRUE.equals(definition.getIsSearchable());
    }

    @Override
    public boolean isIndexableField(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null && Boolean.TRUE.equals(definition.getIsIndexable());
    }

    @Override
    public String getFieldDictCode(BusinessTypeEnum businessType, String fieldName) {
        Map<String, RsBusinessFieldDefinition> definitionMap = getFieldDefinitionMap(businessType);
        RsBusinessFieldDefinition definition = definitionMap.get(fieldName);
        return definition != null ? definition.getDictCode() : null;
    }

    @Override
    @Cacheable(value = "fieldGroups", key = "#businessType.code")
    public List<String> getFieldGroups(BusinessTypeEnum businessType) {
        List<RsBusinessFieldDefinition> definitions = getFieldDefinitions(businessType);
        return definitions.stream()
                .map(RsBusinessFieldDefinition::getFieldGroup)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<RsBusinessFieldDefinition> getFieldsByGroup(BusinessTypeEnum businessType, String fieldGroup) {
        List<RsBusinessFieldDefinition> definitions = getFieldDefinitions(businessType);
        return definitions.stream()
                .filter(def -> fieldGroup.equals(def.getFieldGroup()))
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "searchableFields", key = "#businessType.code")
    public List<RsBusinessFieldDefinition> getSearchableFields(BusinessTypeEnum businessType) {
        List<RsBusinessFieldDefinition> definitions = getFieldDefinitions(businessType);
        return definitions.stream()
                .filter(def -> Boolean.TRUE.equals(def.getIsSearchable()))
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "indexableFields", key = "#businessType.code")
    public List<RsBusinessFieldDefinition> getIndexableFields(BusinessTypeEnum businessType) {
        List<RsBusinessFieldDefinition> definitions = getFieldDefinitions(businessType);
        return definitions.stream()
                .filter(def -> Boolean.TRUE.equals(def.getIsIndexable()))
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = {"fieldDefinitions", "fieldDefinitionMaps", "fieldGroups", "searchableFields", "indexableFields"}, key = "#businessType.code")
    public void refreshFieldDefinitions(BusinessTypeEnum businessType) {
        log.info("刷新业务类型 {} 的字段定义缓存", businessType.getName());
    }

    @Override
    @CacheEvict(value = {"fieldDefinitions", "fieldDefinitionMaps", "fieldGroups", "searchableFields", "indexableFields"}, allEntries = true)
    public void refreshAllFieldDefinitions() {
        log.info("刷新所有字段定义缓存");
    }
}
