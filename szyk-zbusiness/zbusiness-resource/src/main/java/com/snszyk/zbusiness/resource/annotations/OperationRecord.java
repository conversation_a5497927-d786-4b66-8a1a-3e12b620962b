package com.snszyk.zbusiness.resource.annotations;

import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperationTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作记录注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationRecord {

    /**
     * 业务类型
     */
    BusinessTypeEnum businessType();

    /**
     * 操作类型
     */
    OperationTypeEnum operationType() default OperationTypeEnum.UPDATE;

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 业务ID字段名
     * 用于从方法参数中获取业务数据ID
     */
    String businessIdField() default "id";

    /**
     * 业务名称字段名
     * 用于从方法参数中获取业务数据名称
     * 空字符串表示使用业务类型的默认名称字段
     */
    String businessNameField() default "";

    /**
     * 组织ID字段名
     * 用于从方法参数中获取组织ID
     * 空字符串表示使用业务类型的默认组织字段
     */
    String orgIdField() default "";

    /**
     * 是否记录变更详情
     */
    boolean recordDetails() default true;

    /**
     * 需要忽略的字段列表
     * 这些字段的变更不会被记录
     */
    String[] ignoreFields() default {"createTime", "updateTime", "createUser", "updateUser", "version"};

    /**
     * 字段中文名称映射
     * 格式：fieldName:中文名称
     */
    String[] fieldLabels() default {};
}
