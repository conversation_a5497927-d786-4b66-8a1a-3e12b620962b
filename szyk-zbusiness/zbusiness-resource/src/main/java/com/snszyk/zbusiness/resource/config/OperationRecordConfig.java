/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 操作记录配置类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Configuration
@EnableAspectJAutoProxy
@EnableAsync
public class OperationRecordConfig {

    // 可以在这里添加其他配置，比如线程池配置等
}
