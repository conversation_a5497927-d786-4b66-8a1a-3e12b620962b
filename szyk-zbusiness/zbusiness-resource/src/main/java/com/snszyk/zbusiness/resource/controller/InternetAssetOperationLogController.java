/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.dto.FieldChangeDetailDto;
import com.snszyk.zbusiness.resource.dto.InternetAssetOperationLogDto;
import com.snszyk.zbusiness.resource.service.IInternetAssetOperationLogService;
import com.snszyk.zbusiness.resource.vo.InternetAssetOperationLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 互联网资产操作日志控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/internet-asset/operation-log")
@Api(value = "互联网资产操作日志", tags = "互联网资产操作日志接口")
public class InternetAssetOperationLogController {

    private final IInternetAssetOperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @PostMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页查询操作日志", notes = "传入查询条件")
    public R<IPage<InternetAssetOperationLogDto>> page(@RequestBody InternetAssetOperationLogVo vo) {
        IPage<InternetAssetOperationLogDto> page = operationLogService.page(vo);
        return R.data(page);
    }

    /**
     * 根据业务ID查询操作日志列表
     */
    @GetMapping("/list-by-business")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据业务ID查询操作日志", notes = "传入业务数据ID")
    public R<List<InternetAssetOperationLogDto>> listByBusinessId(
            @ApiParam(value = "业务数据ID", required = true) @RequestParam Long businessId) {
        List<InternetAssetOperationLogDto> list = operationLogService.listByBusinessId(businessId);
        return R.data(list);
    }

    /**
     * 获取操作日志详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "获取操作日志详情", notes = "传入操作日志ID")
    public R<InternetAssetOperationLogDto> detail(
            @ApiParam(value = "操作日志ID", required = true) @RequestParam Long id) {
        InternetAssetOperationLogDto detail = operationLogService.getDetail(id);
        return R.data(detail);
    }

    /**
     * 获取字段变更详情
     */
    @GetMapping("/change-details")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "获取字段变更详情", notes = "传入操作日志ID")
    public R<List<FieldChangeDetailDto>> getChangeDetails(
            @ApiParam(value = "操作日志ID", required = true) @RequestParam Long id) {
        List<FieldChangeDetailDto> changeDetails = operationLogService.getChangeDetails(id);
        return R.data(changeDetails);
    }
}
