/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.resource.service.logic.RsServerroomEqLogicService;
import com.snszyk.zbusiness.resource.vo.RsServerroomEqVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 数据中心机房关联设备明细 控制器
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Deprecated
@RestController
@AllArgsConstructor
//@RequestMapping("/rsserverroomeq")
@Api(value = "数据中心机房关联设备明细", tags = "数据中心机房关联设备明细接口")
public class RsServerroomEqController extends BaseCrudController {

	// private final IRsServerroomEqService rsServerroomEqService;

	private final RsServerroomEqLogicService rsServerroomEqLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rsServerroomEqLogicService;
	}

	/**
	 * 保存
	 */
//	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "RsServerroomEqVo")
	public R<BaseCrudDto> save(@RequestBody RsServerroomEqVo v) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
//	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "RsServerroomEqVo")
	public R<IPage<BaseCrudDto>> page(RsServerroomEqVo v) {
		IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 列表
	 */
//	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "列表", notes = "RsServerroomEqVo")
	public R<List<BaseCrudDto>> list(RsServerroomEqVo v) {
		List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
		return R.data(listQueryResult);
	}

	/**
	 * 获取单条
	 */
//	@GetMapping("/fetchOne")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "获取单条数据", notes = "RsServerroomEqVo")
	public R<BaseCrudDto> fetchOne(RsServerroomEqVo v) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 根据ID获取数据
	 */
	@Override
//	@GetMapping("/fetchById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<BaseCrudDto> fetchById(Long id) {
		BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
		return R.data(baseCrudDto);
	}

	/**
	 * 删除
	 */
	@Override
//	@PostMapping("/deleteById")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "删除", notes = "id")
	public R<Boolean> deleteById(Long id) {
		Boolean result = this.fetchBaseLogicService().deleteById(id);
		return R.data(result);
	}

}
