package com.snszyk.zbusiness.resource.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.entity.RsOperationRecordTemplate;
import com.snszyk.zbusiness.resource.enums.BusinessTypeEnum;
import com.snszyk.zbusiness.resource.plugin.BusinessModulePluginManager;
import com.snszyk.zbusiness.resource.service.*;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作记录逻辑服务类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class RsOperationRecordLogicService {

    private final IRsOperationRecordService operationRecordService;
    private final FieldSemanticProcessor fieldSemanticProcessor;
    private final ExtensionFieldStorageService extensionFieldStorageService;
    private final DynamicFieldConfigService dynamicFieldConfigService;
    private final OperationRecordTemplateService templateService;
    private final BusinessModulePluginManager pluginManager;

    /**
     * 分页查询操作记录
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    @Transactional(readOnly = true)
    public IPage<RsOperationRecordDto> pageList(RsOperationRecordPageVo vo) {
        // 设置当前用户的组织权限
        if (vo.getOrgId() == null) {
            vo.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
        }

        IPage<RsOperationRecordDto> pageResult = operationRecordService.pageList(vo);

        // 使用语义处理器处理变更详情的格式化显示
        enhanceRecordsWithSemanticInfo(pageResult.getRecords());

        return pageResult;
    }

    /**
     * 获取操作记录详情
     *
     * @param id 记录ID
     * @return 操作记录详情
     */
    @Transactional(readOnly = true)
    public RsOperationRecordDto getDetailWithChanges(Long id) {
        RsOperationRecordDto record = operationRecordService.getDetailWithChanges(id);

        if (record != null && CollectionUtil.isNotEmpty(record.getDetailList())) {
            // 使用语义处理器处理变更详情的显示格式
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());
            if (businessType != null) {
                fieldSemanticProcessor.processChangeDetailsDisplay(businessType, record.getDetailList());
            }
        }

        return record;
    }

    /**
     * 根据业务ID查询操作记录
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 操作记录列表
     */
    @Transactional(readOnly = true)
    public List<RsOperationRecordDto> listByBusinessId(String businessType, Long businessId) {
        List<RsOperationRecordDto> records = operationRecordService.listByBusinessId(businessType, businessId);

        // 使用语义处理器处理变更详情的格式化显示
        enhanceRecordsWithSemanticInfo(records);

        return records;
    }

    /**
     * 使用语义处理器增强记录的语义信息
     */
    private void enhanceRecordsWithSemanticInfo(List<RsOperationRecordDto> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }

        for (RsOperationRecordDto record : records) {
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());
            if (businessType != null && CollectionUtil.isNotEmpty(record.getDetailList())) {
                fieldSemanticProcessor.processChangeDetailsDisplay(businessType, record.getDetailList());
            }
        }
    }

    /**
     * 创建操作记录（增强版）
     *
     * @param vo 操作记录VO
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOperationRecord(RsOperationRecordVo vo) {
        try {
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(vo.getBusinessType());

            // 1. 插件预处理
            Map<String, Object> preprocessedData = pluginManager.executePreprocess(
                businessType, vo.getBusinessId(), vo.getOperationType(),
                parseJsonToMap(vo.getOldData()), parseJsonToMap(vo.getNewData())
            );

            // 2. 应用模板配置
            RsOperationRecordTemplate template = templateService.getDefaultTemplate(businessType);
            if (template != null) {
                preprocessedData = templateService.applyTemplate(template, preprocessedData);
            }

            // 3. 验证业务数据
            Map<String, String> validationErrors = pluginManager.executeBusinessDataValidation(
                businessType, vo.getBusinessId(), vo.getOperationType(), preprocessedData
            );
            if (!validationErrors.isEmpty()) {
                log.warn("操作记录业务数据验证失败: {}", validationErrors);
                // 可以选择继续保存或抛出异常
            }

            // 4. 保存基础操作记录
            operationRecordService.saveAsync(vo);

            // 5. 保存扩展字段数据
            if (vo.getId() != null) {
                Map<String, Object> extensionData = extractExtensionData(preprocessedData);
                Map<String, Object> metadata = buildMetadata(vo, businessType);

                extensionFieldStorageService.saveExtensionData(
                    vo.getId(), businessType, extensionData, metadata
                );

                // 6. 插件后处理
                pluginManager.executePostprocess(businessType, vo.getBusinessId(), vo.getId(), vo.getOperationType());
            }

            return true;

        } catch (Exception e) {
            log.error("创建操作记录失败", e);
            return false;
        }
    }

    /**
     * 获取操作记录详情（增强版）
     *
     * @param recordId 记录ID
     * @return 操作记录详情
     */
    @Transactional(readOnly = true)
    public RsOperationRecordDto getEnhancedDetail(Long recordId) {
        RsOperationRecordDto record = operationRecordService.getDetailWithChanges(recordId);
        if (record == null) {
            return null;
        }

        BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());

        // 获取扩展字段数据
        Map<String, Object> extensionData = extensionFieldStorageService.getExtensionData(recordId, businessType);
        if (!extensionData.isEmpty()) {
            // 格式化扩展字段显示值
            Map<String, String> displayValues = extensionFieldStorageService.formatDisplayValues(businessType, extensionData);
            record.setExtensionData(extensionData);
            record.setExtensionDisplayValues(displayValues);
        }

        // 增强语义信息
        enhanceRecordsWithSemanticInfoV2(List.of(record));

        return record;
    }

    /**
     * 增强记录的语义信息（V2版本，支持插件）
     *
     * @param records 操作记录列表
     */
    private void enhanceRecordsWithSemanticInfoV2(List<RsOperationRecordDto> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }

        for (RsOperationRecordDto record : records) {
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());

            // 处理业务名称的语义化显示
            if (record.getBusinessName() != null) {
                String semanticBusinessName = fieldSemanticProcessor.processBusinessName(
                    businessType, record.getBusinessName()
                );
                record.setBusinessName(semanticBusinessName);
            }

            // 处理变更详情的语义化显示
            enhanceChangeDetailsV2(record, businessType);
        }
    }

    /**
     * 增强变更详情的语义化显示（V2版本，支持插件）
     *
     * @param record 操作记录
     * @param businessType 业务类型
     */
    private void enhanceChangeDetailsV2(RsOperationRecordDto record, BusinessTypeEnum businessType) {
        List<RsOperationRecordDetailDto> detailList = record.getDetailList();
        if (CollectionUtil.isEmpty(detailList)) {
            return;
        }

        for (RsOperationRecordDetailDto detail : detailList) {
            // 优先使用插件的自定义转换
            String customOldValue = pluginManager.executeCustomFieldValueConversion(
                businessType, detail.getFieldName(), detail.getOldValue()
            );
            String customNewValue = pluginManager.executeCustomFieldValueConversion(
                businessType, detail.getFieldName(), detail.getNewValue()
            );

            // 转换旧值显示
            if (detail.getOldValue() != null) {
                String semanticOldValue = customOldValue != null ? customOldValue :
                    fieldSemanticProcessor.convertFieldValueToDisplay(
                        businessType, detail.getFieldName(), detail.getOldValue()
                    );
                detail.setOldValueDisplay(semanticOldValue);
            }

            // 转换新值显示
            if (detail.getNewValue() != null) {
                String semanticNewValue = customNewValue != null ? customNewValue :
                    fieldSemanticProcessor.convertFieldValueToDisplay(
                        businessType, detail.getFieldName(), detail.getNewValue()
                    );
                detail.setNewValueDisplay(semanticNewValue);
            }
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, Object> parseJsonToMap(String jsonStr) {
        if (jsonStr == null) {
            return new HashMap<>();
        }
        try {
            return com.alibaba.fastjson.JSON.parseObject(jsonStr, Map.class);
        } catch (Exception e) {
            log.warn("解析JSON失败: {}", jsonStr, e);
            return new HashMap<>();
        }
    }

    /**
     * 提取扩展字段数据
     */
    private Map<String, Object> extractExtensionData(Map<String, Object> data) {
        Map<String, Object> extensionData = new HashMap<>();

        // 提取以 "_ext_" 开头的字段作为扩展字段
        data.entrySet().stream()
            .filter(entry -> entry.getKey().startsWith("_ext_"))
            .forEach(entry -> {
                String fieldName = entry.getKey().substring(5); // 去掉 "_ext_" 前缀
                extensionData.put(fieldName, entry.getValue());
            });

        return extensionData;
    }

    /**
     * 构建元数据
     */
    private Map<String, Object> buildMetadata(RsOperationRecordVo vo, BusinessTypeEnum businessType) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("operationType", vo.getOperationType());
        metadata.put("businessType", businessType.getCode());
        metadata.put("businessTypeName", businessType.getName());
        metadata.put("createTime", System.currentTimeMillis());
        metadata.put("operator", AuthUtil.getUserName());
        return metadata;
    }
}
