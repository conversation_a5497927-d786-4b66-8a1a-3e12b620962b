package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务字段定义实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("rs_business_field_definition")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "业务字段定义实体", description = "业务字段定义实体")
public class RsBusinessFieldDefinition extends BaseCrudEntity {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段中文名称
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldLabel;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 字段分组
     */
    @ApiModelProperty(value = "字段分组")
    private String fieldGroup;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isRequired;

    /**
     * 是否敏感字段
     */
    @ApiModelProperty(value = "是否敏感字段")
    private Boolean isSensitive;

    /**
     * 是否可搜索
     */
    @ApiModelProperty(value = "是否可搜索")
    private Boolean isSearchable;

    /**
     * 是否需要索引
     */
    @ApiModelProperty(value = "是否需要索引")
    private Boolean isIndexable;

    /**
     * 字典代码
     */
    @ApiModelProperty(value = "字典代码")
    private String dictCode;

    /**
     * 验证规则
     */
    @ApiModelProperty(value = "验证规则")
    private String validationRules;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer displayOrder;

    /**
     * 字段描述
     */
    @ApiModelProperty(value = "字段描述")
    private String description;
}
