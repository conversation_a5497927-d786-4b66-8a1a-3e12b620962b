package com.snszyk.zbusiness.assess.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.assess.dto.*;
import com.snszyk.zbusiness.assess.entity.RwManage;
import com.snszyk.zbusiness.assess.vo.RwManageProcessPageVo;
import com.snszyk.zbusiness.assess.vo.RwManageScorePageVo;
import com.snszyk.zbusiness.assess.vo.RwManageSelfPageVo;
import com.snszyk.zbusiness.assess.vo.RwResultListVo;
import com.snszyk.zbusiness.stat.dto.BoardRwRankDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RwManageMapper extends BaseMapper<RwManage> {

	/**
	 * 自定义分页
	 *
	 * @param page

	 * @return
	 */
	IPage<RwManage> scorePageList(Page<RwManage> page, @Param("vo") RwManageScorePageVo vo);
	/**
	 * 查询扣分项,用于发起督办
	 *
	 * @param rwId
	 * @param evaluateTarget
	 * @param scoreMethod
	 * @return
	 */
	List<RwManageCommonDto> listDeductMethod(@Param("rwId") String rwId, @Param("evaluateTarget") String evaluateTarget, @Param("scoreMethod") String scoreMethod);

	/**
	 * 驾驶舱考核排名默认参数
	 * @return
	 */
    RwResultQueryParamDto rankDefaultParam(@Param("initOrgId") Long initOrgId, @Param("assessOrgIds")
	List<Long> assessOrgIds);

	/**
	 * 驾驶舱考核排行
	 * @param sort
	 * @param rwPeriodDetailId
	 * @param allTwoDeptIdList
	 * @return
	 */
	List<BoardRwRankDto>  rwRank(@Param("sort") Integer sort,
								 @Param("rwPeriodDetailId") Long rwPeriodDetailId,
								 @Param("list") List<Long> allTwoDeptIdList);

	/**
	 * 自评分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */

	IPage<RwManageSelfPageDto> selfPageList(Page<RwManageSelfPageDto> page, @Param("vo") RwManageSelfPageVo vo);

	/**
	 * 过程考核分页
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<RwManageProcessPageDto> processPageList(Page<RwManage> page, @Param("vo") RwManageProcessPageVo vo);

	/**
	 * 被考核记录的分页查询
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<RwManageScorePageDto> assessedPage(Page<RwManage> page, @Param("vo") RwManageScorePageVo vo);

    List<RwManageDto> listByResult(@Param("schemeId") Long schemeId, @Param("initiateOrgId") Long initiateOrgId,
								   @Param("rwPeriodDetailId") Long rwPeriodDetailId, @Param("assessedOrgId") Long assessedOrgId);

	/**
	 * 考核结果分页
	 * @param vo
	 * @return
	 */
	IPage<RwResultDto> resultPage( @Param("vo") RwResultListVo vo);
}
