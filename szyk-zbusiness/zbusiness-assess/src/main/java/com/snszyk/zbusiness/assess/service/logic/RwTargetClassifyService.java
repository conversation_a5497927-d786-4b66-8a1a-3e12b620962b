package com.snszyk.zbusiness.assess.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.zbusiness.assess.dto.RwSchemeTargetClassifyDto;
import com.snszyk.zbusiness.assess.dto.RwTargetClassifyDto;
import com.snszyk.zbusiness.assess.service.IRwSchemeTargetClassifyService;
import com.snszyk.zbusiness.assess.service.IRwTargetClassifyService;
import com.snszyk.zbusiness.assess.vo.*;
import com.snszyk.zbusiness.dict.dto.CommonDeleteDto;
import com.snszyk.zbusiness.dict.enums.DictCodeEnum;
import com.snszyk.zbusiness.dict.enums.ExceptionEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RwTargetClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwTargetClassifyService extends BaseCrudLogicService<RwTargetClassifyDto, RwTargetClassifyVo> {

	private final IRwTargetClassifyService rwTargetClassifyService;

	private final IRwSchemeTargetClassifyService rwSchemeTargetClassifyService;

	private final IDictBizService dictBizService;

	private final IDeptService deptService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rwTargetClassifyService;
	}

	public IPage<RwTargetClassifyDto> page(RwTargetClassifyPageVo vo) {
		//v1.4 单位范围
		if (!DeptScopeUtil.isUnitLogin()) {
			return null;
		}
		Dept depart = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));

		IPage<RwTargetClassifyDto> page = rwTargetClassifyService.pageList(vo,depart.getId());
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return null;
		}

		for (RwTargetClassifyDto dto : page.getRecords()) {

			String classifyStatusName = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dto.getClassifyStatus());
			dto.setClassifyStatusName(classifyStatusName);

			String addScoreName = dictBizService.getValue(DictCodeEnum.YES_NO.getCode(), dto.getAddScore());
			dto.setAddScoreName(addScoreName);

			//如果所属组织id不为空，则赋值最新的组织名称
			if (Func.isNotEmpty(dto.getCompanyId())) {
				dto.setCompanyName(SysCache.getDeptName(dto.getCompanyId()));
			}
		}

		return page;
	}

	public List<RwTargetClassifyDto> list(String classifyStatus, Long companyId) {

		List<RwTargetClassifyDto> result = rwTargetClassifyService.listByStatusAndCompany(classifyStatus, companyId == null ? null : Arrays.asList(companyId));
		if (CollectionUtils.isEmpty(result)) {
			return null;
		}

		for (RwTargetClassifyDto dto : result) {
			String classifyStatusName = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), dto.getClassifyStatus());
			dto.setClassifyStatusName(classifyStatusName);

			String addScoreName = dictBizService.getValue(DictCodeEnum.YES_NO.getCode(), dto.getAddScore());
			dto.setAddScoreName(addScoreName);
		}
		return result;
	}

	public RwTargetClassifyDto detail(Long id) {
		RwTargetClassifyDto result = rwTargetClassifyService.fetchById(id);

		String classifyStatusName = dictBizService.getValue(DictCodeEnum.ENABLE_TYPE.getCode(), result.getClassifyStatus());
		result.setClassifyStatusName(classifyStatusName);

		String addScoreName = dictBizService.getValue(DictCodeEnum.YES_NO.getCode(), result.getAddScore());
		result.setAddScoreName(addScoreName);

		//如果所属组织id不为空，则赋值最新的组织名称
		if (Func.isNotEmpty(result.getCompanyId())) {
			result.setCompanyName(SysCache.getDeptName(result.getCompanyId()));
		}

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public RwTargetClassifyDto saveOrUpdate(RwTargetClassifyVo vo) {
		RwTargetClassifyDto dto = new RwTargetClassifyDto();
		vo.setClassifyName(vo.getClassifyName().trim());

		if (ObjectUtils.isEmpty(vo.getId())) {
			// 新增
			List<RwTargetClassifyDto> list = rwTargetClassifyService.listByNameAndCompany(vo.getClassifyName(), vo.getCompanyId());
			if (!CollectionUtils.isEmpty(list)) {
				throw new ServiceException(ExceptionEnum.CLASSIFY_NAME_COMPANY_ID.getMessage());
			}
			dto = rwTargetClassifyService.save(vo);
		} else {
			// 更新
			List<RwTargetClassifyDto> list = rwTargetClassifyService.listByNameAndCompany(vo.getClassifyName(), vo.getCompanyId());
			if (!CollectionUtils.isEmpty(list)) {
				Map<String, Long> map = list.stream().collect(Collectors.toMap(item -> item.getClassifyName() + item.getCompanyId(), RwTargetClassifyDto::getId));
				if (map.containsKey(vo.getClassifyName() + vo.getCompanyId()) && !map.get(vo.getClassifyName() + vo.getCompanyId()).equals(vo.getId())) {
					throw new ServiceException(ExceptionEnum.CLASSIFY_NAME_COMPANY_ID.getMessage());
				}
			}
			dto = rwTargetClassifyService.update(vo);

			// 更新体系数据
			rwSchemeTargetClassifyService.updateByStatus(vo.getId(), vo.getClassifyName(), vo.getAddScore());
		}

		return dto;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean status(RwTargetClassifyStatusVo vo) {

		return rwTargetClassifyService.updateByStatus(vo.getId(), vo.getClassifyStatus());
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean score(RwTargetClassifyScoreVo vo) {

		boolean result = rwTargetClassifyService.updateByAddScore(vo.getId(), vo.getAddScore());

		// 更新体系数据
		rwSchemeTargetClassifyService.updateByStatus(vo.getId(), null, vo.getAddScore());

		return result;
	}

	@Transactional(rollbackFor = Exception.class)
	public List<CommonDeleteDto> delete(RwTargetClassifyDeleteVo vo) {
		List<CommonDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}

		for (Long id : vo.getIdList()) {
			CommonDeleteDto dto = new CommonDeleteDto();

			RwTargetClassifyDto rwTargetClassifyDto = rwTargetClassifyService.fetchById(id);
			dto.setName(rwTargetClassifyDto.getClassifyName());

			// 校验是否引用
			List<RwSchemeTargetClassifyDto> list = rwSchemeTargetClassifyService.listByClassifyId(id);
			if (!CollectionUtils.isEmpty(list)) {
				dto.setResult(false);
				dto.setMessage(ExceptionEnum.SCHEME_TARGET_DATA.getMessage());
				result.add(dto);
				continue;
			}

			Boolean flag = rwTargetClassifyService.deleteById(id);
			dto.setResult(flag);

			result.add(dto);
		}

		return result;
	}

}
