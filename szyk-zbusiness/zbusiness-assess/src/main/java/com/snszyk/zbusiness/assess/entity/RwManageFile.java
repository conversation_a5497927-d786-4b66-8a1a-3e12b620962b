/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库项目文档表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rw_manage_file")
@EqualsAndHashCode(callSuper = false)
public class RwManageFile extends BaseCrudEntity {

	/**
	 * 考核管理id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long rwId;
	/**
	 * 附件id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long attachId;
	/**
	 * 业务id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long businessId;
	/**
	 * 业务类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String businessType;
}
