/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.mapper;

import com.snszyk.zbusiness.assess.entity.ManageTargetScore;
import com.snszyk.zbusiness.assess.vo.ManageTargetScoreVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 被考核组织周期内剩余分数 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface ManageTargetScoreMapper extends BaseMapper<ManageTargetScore> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param manageTargetScore
	 * @return
	 */
	List<ManageTargetScoreVo> selectManageTargetScorePage(IPage page, ManageTargetScoreVo manageTargetScore);

}
