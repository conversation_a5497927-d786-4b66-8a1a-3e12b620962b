/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 被考核组织周期内剩余分数实体类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@TableName("rw_manage_target_score")
@EqualsAndHashCode(callSuper = true)
public class ManageTargetScore extends BaseCrudEntity {

	/**
	* 体系id
	*/
	private Long schemeId;
	/**
	* 被考核组织id
	*/
	private Long assessedOrgId;
	/**
	* 考核周期明细id
	*/
	private Long rwPeriodDetailId;
	/**
	* 考核指标id
	*/
	private Long targetId;
	/**
	* 剩余指标分值
	*/
	private BigDecimal leftScore;


}
