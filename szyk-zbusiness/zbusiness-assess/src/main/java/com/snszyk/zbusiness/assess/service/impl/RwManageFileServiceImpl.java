/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.assess.dto.RwManageFileDto;
import com.snszyk.zbusiness.assess.entity.RwManageFile;
import com.snszyk.zbusiness.assess.mapper.RwManageFileMapper;
import com.snszyk.zbusiness.assess.service.IRwManageFileService;
import com.snszyk.zbusiness.assess.vo.RwManageFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RwManageFileServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwManageFileServiceImpl extends BaseCrudServiceImpl<RwManageFileMapper, RwManageFile, RwManageFileDto, RwManageFileVo> implements IRwManageFileService {

	@Transactional
	@Override
	public boolean batchSave(List<RwManageFileVo> needAddFileList) {
		if (CollectionUtil.isEmpty(needAddFileList)) {
			return true;
		}
		return SpringUtil.getBean(RwManageFileServiceImpl.class).saveBatch(BeanUtil.copy(needAddFileList, RwManageFile.class));
	}

	@Override
	public List<RwManageFileDto> listByBusinessIdAndType(Long businessId, String businessType) {
		List<RwManageFile> list = this.lambdaQuery().eq(businessId != null, RwManageFile::getBusinessId, businessId)
			.eq(StringUtil.isNotBlank(businessType), RwManageFile::getBusinessType, businessType).list();
		if(CollectionUtils.isEmpty(list)){
			return new ArrayList<>();
		}
		return BeanUtil.copy(list,RwManageFileDto.class);
	}

	@Override
	public List<RwManageFileDto> listByBusinessId(Long businessId) {
		LambdaQueryWrapper<RwManageFile> queryWrapper = Wrappers.<RwManageFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(businessId), RwManageFile::getBusinessId, businessId);

		List<RwManageFile> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageFileDto.class));
	}

	/**
	 * 根据业务类型和id删除
	 *
	 * @param businessId
	 * @param businessType
	 * @return
	 */
	@Override
	public boolean deleteByBusiness(Long businessId, String businessType) {
		if(businessId==null|| StringUtil.isBlank(businessType)){
			return false;
		}
		return this.lambdaUpdate().eq(RwManageFile::getBusinessId, businessId)
			.eq(RwManageFile::getBusinessType, businessType).remove();
	}

	@Override
	public int deleteByBusinessId(Long businessId) {
		LambdaQueryWrapper<RwManageFile> queryWrapper = Wrappers.<RwManageFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(businessId), RwManageFile::getBusinessId, businessId);
		return baseMapper.delete(queryWrapper);
	}

	@Override
	public int deleteByRwId(Long rwId) {
		LambdaQueryWrapper<RwManageFile> queryWrapper = Wrappers.<RwManageFile>query().lambda()
			.eq(ObjectUtils.isNotEmpty(rwId), RwManageFile::getRwId, rwId);

		return baseMapper.delete(queryWrapper);
	}
}
