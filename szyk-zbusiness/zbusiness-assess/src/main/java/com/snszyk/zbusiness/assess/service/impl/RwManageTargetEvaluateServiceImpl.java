/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.assess.dto.RwManageTargetEvaluateDto;
import com.snszyk.zbusiness.assess.entity.RwManageTargetEvaluate;
import com.snszyk.zbusiness.assess.mapper.RwManageTargetEvaluateMapper;
import com.snszyk.zbusiness.assess.service.IRwManageTargetEvaluateService;
import com.snszyk.zbusiness.assess.vo.RwManageTargetEvaluateVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * RwManageTargetEvaluateServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwManageTargetEvaluateServiceImpl extends BaseCrudServiceImpl<RwManageTargetEvaluateMapper, RwManageTargetEvaluate, RwManageTargetEvaluateDto, RwManageTargetEvaluateVo> implements IRwManageTargetEvaluateService {


	@Override
	public List<RwManageTargetEvaluateDto> listByRwId(Long rwId) {
		LambdaQueryWrapper<RwManageTargetEvaluate> queryWrapper = Wrappers.<RwManageTargetEvaluate>query().lambda()
			.eq(ObjectUtils.isNotEmpty(rwId), RwManageTargetEvaluate::getRwId, rwId)
			.orderByDesc(RwManageTargetEvaluate::getCreateTime);

		List<RwManageTargetEvaluate> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageTargetEvaluateDto.class));
	}

	@Override
	public List<RwManageTargetEvaluateDto> listByRwIds(List<Long> rwIds) {
		LambdaQueryWrapper<RwManageTargetEvaluate> queryWrapper = Wrappers.<RwManageTargetEvaluate>query().lambda()
			.in(CollectionUtils.isNotEmpty(rwIds), RwManageTargetEvaluate::getRwId, rwIds)
			.orderByDesc(RwManageTargetEvaluate::getCreateTime);

		List<RwManageTargetEvaluate> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageTargetEvaluateDto.class));
	}


	@Override
	public int deleteByRwId(Long rwId) {
		LambdaQueryWrapper<RwManageTargetEvaluate> queryWrapper = Wrappers.<RwManageTargetEvaluate>query().lambda()
			.eq(ObjectUtils.isNotEmpty(rwId), RwManageTargetEvaluate::getRwId, rwId);

		return baseMapper.delete(queryWrapper);
	}

	/**
	 * 根据体系考核指标ID统计
	 * @param evaluateTargetId
	 * @return
	 */
    @Override
    public Integer countByEvaluateTargetId(Long evaluateTargetId) {
		if(evaluateTargetId==null){
			throw new ServiceException("id不能为空");
		}
		return this.lambdaQuery().eq(RwManageTargetEvaluate::getEvaluateTargetId,evaluateTargetId).count();
    }
}
