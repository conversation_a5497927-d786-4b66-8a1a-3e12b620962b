<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.assess.mapper.ManageTargetScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="manageTargetScoreResultMap" type="com.snszyk.zbusiness.assess.entity.ManageTargetScore">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="scheme_id" property="schemeId"/>
        <result column="assessed_org_id" property="assessedOrgId"/>
        <result column="rw_period_detail_id" property="rwPeriodDetailId"/>
        <result column="target_id" property="targetId"/>
        <result column="left_score" property="leftScore"/>
    </resultMap>


    <select id="selectManageTargetScorePage" resultMap="manageTargetScoreResultMap">
        select * from rw_manage_target_score where is_deleted = 0
    </select>

</mapper>
