/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.MenuEnum;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IUserService;
import com.snszyk.task.constant.MsgContentConstant;
import com.snszyk.task.constant.MsgUrlConstant;
import com.snszyk.task.dto.SzykMsgGenDto;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.service.ISzykMsgService;
import com.snszyk.zbusiness.assess.dto.*;
import com.snszyk.zbusiness.assess.entity.RwManage;
import com.snszyk.zbusiness.assess.enums.RwStatusEnum;
import com.snszyk.zbusiness.assess.enums.RwTypeEnum;
import com.snszyk.zbusiness.assess.mapper.RwManageMapper;
import com.snszyk.zbusiness.assess.service.IRwManageService;
import com.snszyk.zbusiness.assess.service.IRwSchemeTargetService;
import com.snszyk.zbusiness.assess.vo.*;
import com.snszyk.zbusiness.stat.dto.BoardRwRankDto;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * RwManageServiceImpl
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwManageServiceImpl extends BaseCrudServiceImpl<RwManageMapper, RwManage, RwManageDto, RwManageVo> implements IRwManageService {

	private final RwManageMapper rwManageMapper;

	private final IUserService userService;

	private final ISzykMsgService msgService;

	private final IDeptService deptService;

	private final IRwSchemeTargetService schemeTargetService;

	@Override
	public IPage<RwManageScorePageDto> scorePageList(RwManageScorePageVo vo) {
		//发组织为当期的机构
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		List<Long> initiateOrgIdList = new ArrayList<>();
		initiateOrgIdList.add(dept.getId());
		vo.setInitiateOrgIdList(initiateOrgIdList);
		//类型为专项检查
		vo.setRwType(RwTypeEnum.ASSESS.getCode());
		IPage<RwManage> majorPage = rwManageMapper.scorePageList(new Page<>(vo.getCurrent(), vo.getSize()), vo);
		return majorPage.convert(RwManage -> BeanUtil.copyProperties(RwManage, RwManageScorePageDto.class));
	}

	/**
	 * 自评分页
	 *
	 * @param vo
	 * @param assessedOrgIdList
	 * @param rwStatus
	 * @param rwType
	 * @return
	 */
	@Override
	public IPage<RwManageSelfPageDto> selfPageList(RwManageSelfPageVo vo, List<Long> assessedOrgIdList, String rwStatus, String rwType) {
		vo.setAssessedOrgIdList(assessedOrgIdList);
		vo.setRwType(rwType);
		Page<RwManageSelfPageDto> page = new Page(vo.getCurrent(), vo.getSize());
		return baseMapper.selfPageList(page, vo);
	}

	/**
	 * 过程考核分页
	 * @param vo
	 * @return
	 */
	@Override
	public IPage<RwManageProcessPageDto> processPageList(RwManageProcessPageVo vo) {
		Page<RwManage> page = new Page(vo.getCurrent(), vo.getSize());
		IPage<RwManageProcessPageDto> majorPage = baseMapper.processPageList(page, vo);
		return majorPage;
	}

	@Override
	public List<RwManageDto> listByPeriodDetailId(List<Long> rwPeriodDetailIdList) {
		LambdaQueryWrapper<RwManage> queryWrapper = Wrappers.<RwManage>query().lambda()
			.in(ObjectUtils.isNotEmpty(rwPeriodDetailIdList), RwManage::getRwPeriodDetailId, rwPeriodDetailIdList)
			.orderByDesc(RwManage::getCreateTime);

		List<RwManage> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageDto.class));
	}

	@Override
	public List<RwManageDto> listByCheck(Long schemeId, Long initiateOrgId, Long assessedOrgId, Long rwPeriodDetailId, String rwType) {
		LambdaQueryWrapper<RwManage> queryWrapper = Wrappers.<RwManage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), RwManage::getSchemeId, schemeId)
			.eq(ObjectUtils.isNotEmpty(initiateOrgId), RwManage::getInitiateOrgId, initiateOrgId)
			.eq(ObjectUtils.isNotEmpty(assessedOrgId), RwManage::getAssessedOrgId, assessedOrgId)
			.eq(ObjectUtils.isNotEmpty(rwPeriodDetailId), RwManage::getRwPeriodDetailId, rwPeriodDetailId)
			.eq(ObjectUtils.isNotEmpty(rwType), RwManage::getRwType, rwType)

			.orderByDesc(RwManage::getCreateTime);

		List<RwManage> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageDto.class));
	}

	@Override
	public List<RwManageDto> listByResult(Long schemeId, Long initiateOrgId, Long rwPeriodDetailId, Long assessedOrgId) {
		List<RwManageDto> list = baseMapper.listByResult(schemeId, initiateOrgId, rwPeriodDetailId, assessedOrgId);
		return list;
	}

	@Override
	public List<RwManageDto> listBySchemeId(Long schemeId) {
		LambdaQueryWrapper<RwManage> queryWrapper = Wrappers.<RwManage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(schemeId), RwManage::getSchemeId, schemeId)
			.orderByDesc(RwManage::getCreateTime);

		List<RwManage> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageDto.class));
	}

	@Override
	public List<RwManageDto> listByStatus(String rwType, String rwStatus) {
		LambdaQueryWrapper<RwManage> queryWrapper = Wrappers.<RwManage>query().lambda()
			.eq(ObjectUtils.isNotEmpty(rwType), RwManage::getRwType, rwType)
			.eq(ObjectUtils.isNotEmpty(rwStatus), RwManage::getRwStatus, rwStatus)
			.orderByDesc(RwManage::getCreateTime);

		List<RwManage> list = baseMapper.selectList(queryWrapper);

		if (CollectionUtils.isEmpty(list)) {
			return null;
		}

		return Objects.requireNonNull(BeanUtil.copy(list, RwManageDto.class));
	}

	@Override
	public boolean updateBySelfTotalScore(Long id, BigDecimal selfTotalScore, String rwStatus, Date selfAssessmentComplete, Long selfUserId) {
		LambdaUpdateWrapper<RwManage> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(RwManage::getSelfTotalScore, selfTotalScore);
		updateWrapper.set(ObjectUtils.isNotEmpty(rwStatus), RwManage::getRwStatus, rwStatus);
		updateWrapper.set(RwManage::getSelfAssessmentComplete, selfAssessmentComplete);
		updateWrapper.set(RwManage::getSelfUserId, selfUserId);
		updateWrapper.eq(RwManage::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public boolean updateByRwTotalScore(Long id, BigDecimal totalScore, BigDecimal rwTotalScore,
										String rwStatus, String remark, String sceneMembers,
										String teamMembers, LocalDate rwDate) {
		LambdaUpdateWrapper<RwManage> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(RwManage::getRwTotalScore, rwTotalScore);
		updateWrapper.set(RwManage::getTargerTotalScore, totalScore);
		updateWrapper.set(ObjectUtils.isNotEmpty(rwStatus), RwManage::getRwStatus, rwStatus);
		updateWrapper.set(RwManage::getRemark, remark);
		//v.1.3 考核小组信息
		updateWrapper.set(RwManage::getSceneMembers, sceneMembers);
		updateWrapper.set(RwManage::getTeamMembers, teamMembers);
		updateWrapper.set(rwDate != null, RwManage::getRwDate, rwDate);
		updateWrapper.eq(RwManage::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public boolean updateByStatus(Long id, String rwStatus) {
		LambdaUpdateWrapper<RwManage> updateWrapper = Wrappers.lambdaUpdate();
		updateWrapper.set(ObjectUtils.isNotEmpty(rwStatus), RwManage::getRwStatus, rwStatus);
		updateWrapper.eq(RwManage::getId, id);

		return this.update(updateWrapper);
	}

	@Override
	public RwManageDto update(RwManageVo vo) {
		RwManage entity = this.getById(vo.getId());
		cn.hutool.core.bean.BeanUtil.copyProperties(vo, entity, CopyOptions.create().setIgnoreNullValue(false));
		boolean result = this.updateById(entity);
		if (result) {
			return BeanUtil.copyProperties(entity, RwManageDto.class);
		} else {
			return null;
		}
	}

	@Override
	public RwManageDto getDetailById(Long id) {
		RwManage entity = this.getById(id);
		if (entity == null) {
			return null;
		}
		RwManageDto copy = BeanUtil.copy(entity, RwManageDto.class);
		return copy;
	}

	@Override
	public List<RwManageDto> getByProject(Long id, String code) {
		List<RwManage> list = this.lambdaQuery().eq(RwManage::getAssociateId, id).eq(RwManage::getSourceType, code).list();
		if (CollectionUtils.isNotEmpty(list))
			return list.stream().map(entity -> BeanUtil.copyProperties(entity, RwManageDto.class)).collect(Collectors.toList());
		return null;
	}


	/**
	 * 提醒自评
	 * 发送钉钉通知
	 *
	 * @param rwMangeId
	 */
	@Override
	public void sendMessge(String rwMangeId) {
		RwManageDto rwManageDto = fetchById(Long.valueOf(rwMangeId));
		if (rwManageDto == null) {
			return;
		}
		Date selfAssessmentComplete = rwManageDto.getSelfAssessmentComplete();
		//已经自评了
		if (selfAssessmentComplete != null) {
			return;
		}
		//List<UserDTO> userDTOList = userService.getContactPersonListByDeptRole(rwManageDto.getAssessedOrgId(), Collections.singletonList(RoleEnum.ASSESS_OPERATION.getName()));
		List<UserDTO> userDTOList = userService.getContactPersonListByDeptMenu(rwManageDto.getAssessedOrgId(), MenuEnum.SELF_EVALUATION_START.getCode());
		List<SzykMsgGenDto> szykMsgGenDtoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(userDTOList)) {
			for (UserDTO userDTO : userDTOList) {
				SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
				szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.SPECIAL_INSPECTION.getCode());
				szykMsgDto.setBusinessId(Long.valueOf(rwMangeId));
				szykMsgDto.setUrl(String.format(MsgUrlConstant.START_SELF_EVALUATION, rwMangeId) + "?deptScopeId=" + rwManageDto.getAssessedOrgId());
				//v1.4业务范围
				szykMsgDto.setDeptId(rwManageDto.getAssessedOrgId());
				szykMsgDto.setReceiverId(userDTO.getId());
				szykMsgDto.setContent(String.format(MsgContentConstant.SPECIAL_INSPECTION_DEADLINE, DateUtil.format(rwManageDto.getSelfAssessmentDeadline(), "yyyy-MM-dd")));
				boolean b = msgService.canSend(szykMsgDto);
				if (b) {
					szykMsgGenDtoList.add(szykMsgDto);
				}
			}
		}
		msgService.genMsg(szykMsgGenDtoList);
	}

	@Override
	public List<RwManageDto> listByParam(RwManageVo rwManageVo) {
		List<RwManage> list = this.lambdaQuery().eq(rwManageVo.getAssessedOrgId() != null, RwManage::getAssessedOrgId, rwManageVo.getAssessedOrgId()).list();
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<>();
		}
		return BeanUtil.copy(list, RwManageDto.class);
	}

	/**
	 * 驾驶舱考核排名
	 * @param sort
	 * @param rwPeriodDetailId
	 * @return
	 */
	@Override
	public List<BoardRwRankDto> rwRank(Integer sort, Long rwPeriodDetailId) {

		//二级单位和权属单位
		List<DeptDTO> allTwoDept = deptService.getAllTwoDept();
		List<Long> allTwoDeptIdList = allTwoDept.stream().map(Dept::getId).collect(Collectors.toList());
		List<BoardRwRankDto> rankDtos = baseMapper.rwRank(sort, rwPeriodDetailId, allTwoDeptIdList);
		//计算得分
		for (BoardRwRankDto rankDto : rankDtos) {
			BigDecimal deduct = rankDto.getDeduct();
			BigDecimal score = rankDto.getSchemeTotalScore().add(deduct);
			rankDto.setScore(score);
		}
		if (sort == 2) {
			rankDtos.sort(Comparator.comparing(BoardRwRankDto::getScore));
		} else {
			rankDtos.sort(Comparator.comparing(BoardRwRankDto::getScore).reversed());
		}
		rankDtos = rankDtos.stream().limit(10).collect(Collectors.toList());
		return rankDtos;
	}

	@Override
	public List<RwManageDto> listBy(String sourceType, List<Long> taskIssueIdList) {
		List<RwManage> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(sourceType), RwManage::getSourceType, sourceType)
			.in(ObjectUtil.isNotEmpty(taskIssueIdList), RwManage::getAssociateId, taskIssueIdList)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, RwManageDto.class);
	}

	/**
	 * 考核结果的分页查询
	 * @param vo
	 * @return
	 */
	@Override
	public IPage<RwResultDto> resultPage(RwResultListVo vo) {
		return baseMapper.resultPage(vo);
	}

	/**
	 * 驾驶舱考核排名默认参数
	 * 只查询山东能源的
	 * @return
	 */
	@Override
	public RwResultQueryParamDto rankDefaultParam() {
		List<DeptDTO> allTwoDept = deptService.getAllTwoDept();
		DeptDTO headDept = deptService.getHeadDept();
		List<Long> twoDeptIds = allTwoDept.stream().map(e -> e.getId()).collect(Collectors.toList());
		return this.baseMapper.rankDefaultParam(headDept.getId(), twoDeptIds);
	}

	/**
	 * 被考核记录的分页查询
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public IPage<RwManageScorePageDto> assessedPage(RwManageScorePageVo vo) {

		//被考核机构为当前的登录机构
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		Long unitId = dept.getUnitId();
		vo.setAssessedOrgIdList(Arrays.asList(unitId));
		Page<RwManage> page = new Page(vo.getCurrent(), vo.getSize());
		IPage<RwManageScorePageDto> majorPage = baseMapper.assessedPage(page, vo);
		return majorPage;
	}

	/**
	 * 归档
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public Boolean placeOnFile(RwBaseObjectVo vo) {
		Long rwId = vo.getId();
		RwManageDto rwManageDto = this.fetchById(rwId);
		if (rwManageDto == null) {
			throw new ServiceException("考核记录不存在!");
		}
		String rwStatus = rwManageDto.getRwStatus();
		if (!RwStatusEnum.ASSESSMENT_COMPLETE.getCode().equals(rwStatus)) {
			throw new ServiceException("仅完成考核状态的考核可以进行归档!");
		}
		RwManageVo manageVo = BeanUtil.copy(rwManageDto, RwManageVo.class);
		//变更为归档状态
		manageVo.setRwStatus(RwStatusEnum.PLACE_ON_FILE.getCode());
		RwManageDto save = save(manageVo);
		if (save == null) {
			return false;
		}
		return true;
	}

	/**
	 * 专项检查-取消材料锁定
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public Boolean unLockFile(RwBaseObjectVo vo) {
		Long rwId = vo.getId();
		RwManageDto rwManageDto = this.fetchById(rwId);
		if (rwManageDto == null) {
			throw new ServiceException("考核记录不存在!");
		}
		String rwStatus = rwManageDto.getRwStatus();
		if (!RwStatusEnum.FILE_LOCK.getCode().equals(rwStatus)) {
			throw new ServiceException("仅完成材料锁定状态可以取消锁定!");
		}
		RwManageVo manageVo = BeanUtil.copy(rwManageDto, RwManageVo.class);
		//变更为完成自评状态
		manageVo.setRwStatus(RwStatusEnum.SELF_EVALUATION_COMPLETE.getCode());
		RwManageDto save = save(manageVo);
		if (save == null) {
			return false;
		}
		return true;
	}

	/**
	 * 专项检查-材料锁定
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public Boolean lockFile(RwBaseObjectVo vo) {

		Long rwId = vo.getId();
		RwManageDto rwManageDto = this.fetchById(rwId);
		if (rwManageDto == null) {
			throw new ServiceException("考核记录不存在!");
		}
		String rwStatus = rwManageDto.getRwStatus();
		if (!RwStatusEnum.SELF_EVALUATION_COMPLETE.getCode().equals(rwStatus)) {
			throw new ServiceException("仅完成自评状态可以进行材料锁定!");
		}
		RwManageVo manageVo = BeanUtil.copy(rwManageDto, RwManageVo.class);
		manageVo.setRwStatus(RwStatusEnum.FILE_LOCK.getCode());
		RwManageDto save = save(manageVo);
		if (save == null) {
			return false;
		}
		return true;
	}

	/**
	 * list 根据发起组织
	 *
	 * @param unitId
	 * @return
	 */
	@Override
	public List<RwManageDto> listByInitiateOrgId(Long unitId) {
		List<RwManage> list = this.lambdaQuery().eq(RwManage::getInitiateOrgId, unitId).list();
		if (CollectionUtil.isEmpty(list)) {
			return Lists.newArrayList();
		}
		return BeanUtil.copy(list, RwManageDto.class);
	}

	/**
	 * 查询扣分项,用于发起督办
	 *
	 * @param rwId
	 * @param evaluateTarget
	 * @param scoreMethod
	 * @return
	 */
	@Override
	public List<RwManageCommonDto> listDeductMethod(String rwId, String evaluateTarget, String scoreMethod) {
		List<RwManageCommonDto> result = baseMapper.listDeductMethod(rwId, evaluateTarget, scoreMethod);
		return result;
	}
}
