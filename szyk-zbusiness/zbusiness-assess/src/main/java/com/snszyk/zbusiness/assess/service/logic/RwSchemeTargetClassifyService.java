package com.snszyk.zbusiness.assess.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.assess.dto.RwSchemeTargetClassifyDto;
import com.snszyk.zbusiness.assess.service.IRwSchemeTargetClassifyService;
import com.snszyk.zbusiness.assess.vo.RwSchemeTargetClassifyVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * RwSchemeTargetClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwSchemeTargetClassifyService extends BaseCrudLogicService<RwSchemeTargetClassifyDto, RwSchemeTargetClassifyVo> {

	private final IRwSchemeTargetClassifyService schemeTargetClassifyService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.schemeTargetClassifyService;
	}

}
