/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 考核指标分类
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rw_scheme_target_classify")
@EqualsAndHashCode(callSuper = false)
public class RwSchemeTargetClassify extends BaseCrudEntity {

	/**
	 * 体系id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long schemeId;
	/**
	 * 指标分类id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String addScore;
	/**
	 * 分类分值
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal classifyScore;
}
