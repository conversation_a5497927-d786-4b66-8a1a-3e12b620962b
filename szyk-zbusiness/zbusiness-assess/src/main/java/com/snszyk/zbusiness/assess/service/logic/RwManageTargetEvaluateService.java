package com.snszyk.zbusiness.assess.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.assess.dto.RwManageTargetEvaluateDto;
import com.snszyk.zbusiness.assess.service.IRwManageTargetEvaluateService;
import com.snszyk.zbusiness.assess.vo.RwManageTargetEvaluateVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * RwManageTargetEvaluateService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class RwManageTargetEvaluateService extends BaseCrudLogicService<RwManageTargetEvaluateDto, RwManageTargetEvaluateVo> {

	private final IRwManageTargetEvaluateService rwManageTargetEvaluateService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.rwManageTargetEvaluateService;
	}

}
