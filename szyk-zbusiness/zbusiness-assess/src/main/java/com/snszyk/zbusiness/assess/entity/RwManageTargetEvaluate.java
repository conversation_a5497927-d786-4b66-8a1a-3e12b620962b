/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 考核管理指标评价
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("rw_manage_target_evaluate")
@EqualsAndHashCode(callSuper = false)
public class RwManageTargetEvaluate extends BaseCrudEntity {

	/**
	 * 项目后评价id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long rwId;
	/**
	 * 关联指标分类id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long projectEvaluateClassifyId;
	/**
	 * 评价指标id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long evaluateTargetId;
	/**
	 * 评价指标
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String evaluateTarget;
	/**
	 * 指标解释
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String targetExplain;
	/**
	 * 指标分值
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal score;
	/**
	 * 指标得分
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal getScore;
}
