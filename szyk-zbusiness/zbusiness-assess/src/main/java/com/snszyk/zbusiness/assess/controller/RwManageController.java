package com.snszyk.zbusiness.assess.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.annotation.DeptScopeValid;
import com.snszyk.annotation.enums.DeptScopeEnum;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.assess.dto.*;
import com.snszyk.zbusiness.assess.service.logic.RwManageScoreMethodService;
import com.snszyk.zbusiness.assess.service.logic.RwManageService;
import com.snszyk.zbusiness.assess.vo.*;
import com.snszyk.zbusiness.dict.dto.CommonDeleteDto;
import com.snszyk.zbusiness.project.service.IProjectBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * 考核管理 控制器
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
@RestController
@AllArgsConstructor
@RequestMapping("/rw-manage")
@Api(value = "考核管理", tags = "考核管理接口")
@Slf4j
public class RwManageController extends BaseCrudController {

	private final RwManageService rwManageService;

	private final RwManageScoreMethodService manageScoreMethodService;


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return rwManageService;
	}

	@PostMapping("/score/page")
	@ApiOperation(value = "专项检查-分页")
	public R<IPage<RwManageScorePageDto>> scorePage(@RequestBody RwManageScorePageVo vo, SzykUser szykUser) {
		IPage<RwManageScorePageDto> pageQueryResult = rwManageService.scorePage(vo);
		return R.data(pageQueryResult);
	}
	@PostMapping("/assessedPage")
	@ApiOperation(value = "被考核记录的分页查询")
	public R<IPage<RwManageScorePageDto>> assessedPage(@RequestBody RwManageScorePageVo vo) {
		IPage<RwManageScorePageDto> pageQueryResult = rwManageService.assessedPage(vo);
		return R.data(pageQueryResult);
	}
	@PostMapping("/self/page")
	@ApiOperation(value = "专项检查自评-分页")
	public R<IPage<RwManageSelfPageDto>> selfPage(@RequestBody RwManageSelfPageVo vo, SzykUser szykUser) {
		IPage<RwManageSelfPageDto> pageQueryResult = rwManageService.selfPage(vo, szykUser);
		return R.data(pageQueryResult);
	}

	@PostMapping("/process/page")
	@ApiOperation(value = "过程考核-分页")
	public R<IPage<RwManageProcessPageDto>> processPage(@RequestBody RwManageProcessPageVo vo, SzykUser szykUser) {
		IPage<RwManageProcessPageDto> pageQueryResult = rwManageService.processPage(vo, szykUser);
		return R.data(pageQueryResult);
	}

	@GetMapping("/getByProject")
	@ApiOperation(value = "查询项目考核记录")
	//@DeptScopeValid(type = DeptScopeEnum.SELF,interfaceClass = IProjectBaseService.class )
	public R<List<RwManageDto>> getByProject(@ApiParam(value = "项目id", required = true) @RequestParam Long id) {
		List<RwManageDto> pageQueryResult = rwManageService.getByProject(id);
		return R.data(pageQueryResult);
	}

	@GetMapping("/detail")
	@ApiOperation(value = "根据ID获取数据")
	public R<RwManageDto> detail(@ApiParam(value = "ID", required = true) @RequestParam Long id, @RequestParam(required = false) Integer flag) {
		RwManageDto baseCrudDto = rwManageService.detail(id,flag,true);
		return R.data(baseCrudDto);
	}

	@PostMapping("/exportMultiSheet")
	@ApiOperation("专项检查分页签导出")
	public R<Void> exportMultiSheet(@RequestBody RwManageScorePageVo vo, HttpServletRequest request, HttpServletResponse response) throws IOException {
		rwManageService.exportMultiSheet(vo, request, response);
		return R.data(null);
	}

	@PostMapping("/self/save")
	@ApiOperation(value = "专项检查自评-保存")
	public R<Boolean> selfSave(@RequestBody RwManageSelfSaveVo vo) {
		Boolean baseCrudDto = rwManageService.selfSave(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/self/submit")
	@ApiOperation(value = "专项检查自评-提交")
	public R<Boolean> selfSubmit(@RequestBody RwManageSelfSaveVo vo) {
		Boolean baseCrudDto = rwManageService.selfSubmit(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/score/save")
	@ApiOperation(value = "专项检查-保存")
	public R<Boolean> scoreSave(@RequestBody RwManageScoreSaveVo vo) {
		Boolean baseCrudDto = rwManageService.scoreSave(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/score/submit")
	@ApiOperation(value = "专项检查-打分")
	public R<Boolean> scoreSubmit(@RequestBody RwManageScoreSaveVo vo) {
		Boolean baseCrudDto = rwManageService.scoreSubmit(vo);
		return R.data(baseCrudDto);
	}

	@PostMapping("/process/submit")
	@ApiOperation(value = "过程考核-提交")
	public R<RwManageDto> processSubmit(@RequestBody RwManageProcessSaveVo vo) {
		RwManageDto rwManageDto = rwManageService.processSubmit(vo);
		return R.data(rwManageDto);
	}



	@PostMapping("/score/delete")
	@ApiOperation(value = "专项检查-删除")
	public R<List<CommonDeleteDto>> scoreDelete(@RequestBody RwManageDeleteVo vo) {
		List<CommonDeleteDto> result = rwManageService.scoreDelete(vo);
		return R.data(result);
	}

	@PostMapping("/process/delete")
	@ApiOperation(value = "过程考核-删除")
	public R<List<CommonDeleteDto>> processDelete(@RequestBody RwManageDeleteVo vo) {
		List<CommonDeleteDto> result = rwManageService.processDelete(vo);
		return R.data(result);
	}
	@GetMapping("/score/listDeductMethod")
	@ApiOperation(value = "查询扣分项,用于发起督办")
	public R<List<RwManageCommonDto>> listDeductMethod(@RequestParam String rwId,@RequestParam(required = false)String evaluateTarget, @RequestParam(required = false) String scoreMethod) {
		List<RwManageCommonDto> result = rwManageService.listDeductMethod(rwId,evaluateTarget,scoreMethod);
		return R.data(result);
	}

	@GetMapping("/score/listScoreMethodByRwId")
	@ApiOperation(value = "根据考核id查询考核标准")
	public R<List<Long>> listByRwId(@NotNull @RequestParam Long rwId) {
		return R.data(manageScoreMethodService.listByRwId(rwId));
	}
	@PostMapping("/lockFile")
	@ApiOperation(value = "专项检查-材料锁定")
	public R<Boolean> lockFile(@RequestBody RwBaseObjectVo vo) {
		Boolean r= rwManageService.lockFile(vo);
		return R.data(r);
	}
	@PostMapping("/unLockFile")
	@ApiOperation(value = "专项检查-取消材料锁定")
	public R<Boolean> unLockFile(@RequestBody RwBaseObjectVo vo) {
		Boolean r= rwManageService.unLockFile(vo);
		return R.data(r);
	}
	@PostMapping("/placeOnFile")
	@ApiOperation(value = "归档")
	public R<Boolean> placeOnFile(@RequestBody RwBaseObjectVo vo) {
		Boolean r= rwManageService.placeOnFile(vo);
		return R.data(r);
	}

	@ApiOperation("导出被考核记录")
	@PostMapping("/exportAssessed")
	public R<Void> export(@RequestBody RwManageScorePageVo vo, HttpServletRequest request, HttpServletResponse response) {
		rwManageService.exportAssessed(vo, request, response);
		return R.data(null);
	}
	@ApiOperation("过程考核导出")
	@PostMapping("/exportProcess")
	public R<Void> exportProcess(@RequestBody RwManageProcessPageVo vo, SzykUser szykUser, HttpServletRequest request, HttpServletResponse response) {
		rwManageService.exportProcess(vo, szykUser,request, response);
		return R.data(null);
	}

	@GetMapping("/filterInitiateOrgList")
	@ApiOperation(value = "根据被考核记录过滤发起组织")
	public R<List<RwParamCommonDto>> filterInitiateOrgList() {
		List<RwParamCommonDto> result = rwManageService.filterInitiateOrgList();
		return R.data(result);
	}

	@GetMapping("/filterPeriodList")
	@ApiOperation(value = "根据被考核记录过滤考核周期")
	public R<List<RwParamCommonDto>> filterPeriodList() {
		List<RwParamCommonDto> result = rwManageService.filterPeriodList();
		return R.data(result);
	}

}
