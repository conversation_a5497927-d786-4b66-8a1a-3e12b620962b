/**
 * 文件名：TaskCreditApplyJob.java
 * 版权： Copyright 2002-2007 QLYS. All Rights Reserved.
 * 描述：
 * 修改人：杜洋洋
 * 修改时间：2021年03月31日
 * 修改内容：新增
 **/
package com.snszyk.zbusiness.assess.job;


import com.snszyk.core.redis.lock.LockType;
import com.snszyk.core.redis.lock.RedisLockClient;
import com.snszyk.task.service.ISzykTaskService;
import com.snszyk.zbusiness.assess.dto.RwManageDto;
import com.snszyk.zbusiness.assess.enums.RwStatusEnum;
import com.snszyk.zbusiness.assess.enums.RwTypeEnum;
import com.snszyk.zbusiness.assess.service.IRwManageService;
import com.snszyk.zbusiness.assess.service.logic.RwManageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 考核管理自评截至日期-定时任务
 *
 * <AUTHOR>
 * @since 1.0
 */
@AllArgsConstructor
@Slf4j
@Component
public class ManageJob {

	private IRwManageService rwManageService;
	private RwManageService rwService;

	private ISzykTaskService szykTaskService;
	private RedisLockClient redisLockClient;

	private static final String expiredKey = "RwFinishSelfAccess";

	@Scheduled(cron = "59 59 23 * * ?")
	@Transactional(rollbackFor = Exception.class)
	public void mange() {
		try {
			boolean tryLock = redisLockClient.tryLock("LOCK:" + expiredKey, LockType.REENTRANT, 0, 60 * 10, TimeUnit.SECONDS);
			if (tryLock) {
				// 当前时间
				Long time = System.currentTimeMillis();

				List<RwManageDto> list = rwManageService.listByStatus(RwTypeEnum.ASSESS.getCode(), RwStatusEnum.SELF_EVALUATION_ING.getCode());

				if (CollectionUtils.isEmpty(list)) {
					return;
				}
				for (RwManageDto manageDto : list) {
					if (StringUtils.isEmpty(manageDto.getSelfAssessmentDeadline())) {
						continue;
					}
					if (manageDto.getSelfAssessmentDeadline().getTime() >= time) {
						continue;
					}
					Long rwId = manageDto.getId();
					rwManageService.updateByStatus(manageDto.getId(), RwStatusEnum.SELF_EVALUATION_TIMEOUT.getCode());
					rwService.handleTimeoutSelfAssessment(rwId);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
