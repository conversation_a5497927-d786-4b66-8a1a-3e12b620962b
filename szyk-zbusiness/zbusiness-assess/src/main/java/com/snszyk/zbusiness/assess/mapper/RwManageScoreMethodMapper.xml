<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.assess.mapper.RwManageScoreMethodMapper">


    <select id="listDetailByRwId" resultType="com.snszyk.zbusiness.assess.dto.RwManageScoreMethodDto">
        SELECT
            t2.add_score,
            t.rw_id,
            t.rw_cause,t.rw_score,
            t.project_evaluate_target_id,
            t1.evaluate_target_id
        FROM
            rw_manage_score_method t
                LEFT JOIN rw_manage_target_evaluate t1 ON t.project_evaluate_target_id = t1.id
                LEFT JOIN rw_manage_target_classify t2 ON t1.project_evaluate_classify_id = t2.id
        WHERE
            t.rw_id= #{rwId}
          AND t.is_deleted =0
    </select>

</mapper>
