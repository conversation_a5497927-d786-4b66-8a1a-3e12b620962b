<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.assess.mapper.RwSchemeScoreMethodMapper">

    <select id="listData" resultType="com.snszyk.zbusiness.assess.dto.RwSchemeScoreMethodPageDto">
        select t.id, t.scheme_id, t.scheme_evaluate_id, t.score_method, t.create_user, t.create_dept, t.create_time,
        t.update_user, t.update_time, t.status, t.is_deleted
        from rw_scheme_score_method t
        left join rw_scheme_target_evaluate t1
        on t.scheme_evaluate_id = t1.id and t1.is_deleted=0
        left join rw_scheme_target_classify t2
        on t2.id = t1.scheme_classify_id and t2.is_deleted=0
        where t.is_deleted=0
        <if test="vo.scoreMethod!=null and vo.scoreMethod!=''">
            and t.score_method like concat('%',#{vo.scoreMethod},'%')
        </if>
        <if test="vo.schemeEvaluateIdList!=null and vo.schemeEvaluateIdList.size()>0">
            and t.scheme_evaluate_id in
            <foreach collection="vo.schemeEvaluateIdList" item="item" index="index" separator="," open=" ( " close=" )">
                #{item}
            </foreach>
        </if>
        <if test="vo.schemeId!=null and vo.schemeId!=''">
            and t.scheme_id = #{vo.schemeId}
        </if>
        order by t1.scheme_classify_id asc,t1.id asc,t.id asc
    </select>
</mapper>
