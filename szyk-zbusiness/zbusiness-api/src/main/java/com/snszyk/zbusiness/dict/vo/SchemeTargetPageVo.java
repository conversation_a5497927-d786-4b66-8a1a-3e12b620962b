package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "SchemeTargetPageVo", description = "后评价指标体系")
public class SchemeTargetPageVo extends BaseCrudSlimVo {

	/**
	 * 体系编号
	 */
	@ApiModelProperty("体系编号")
	private String schemeNo;
	/**
	 * 体系名称
	 */
	@ApiModelProperty("体系名称")
	private String schemeName;
	/**
	 * 所属组织名称
	 */
	@ApiModelProperty("所属组织名称")
	private String companyName;
	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long companyId;

}
