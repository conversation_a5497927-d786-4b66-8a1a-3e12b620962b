/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    /**
     * 互联网资产台账
     */
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产台账"),

    /**
     * 设备台账
     */
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备台账"),

    /**
     * 信息系统台账
     */
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统台账"),

    /**
     * 软件台账
     */
    SOFTWARE_ASSET("SOFTWARE_ASSET", "软件台账"),

    /**
     * 机房台账
     */
    SERVER_ROOM("SERVER_ROOM", "机房台账");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BusinessTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举，如果不存在则抛出异常
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessTypeEnum fromCode(String code) {
        BusinessTypeEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("未知的业务类型编码: " + code);
        }
        return result;
    }
}
