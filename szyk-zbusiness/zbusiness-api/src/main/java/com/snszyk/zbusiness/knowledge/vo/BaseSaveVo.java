/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库实体类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BaseSaveVo对象", description = "知识库")
public class BaseSaveVo extends BaseCrudSlimVo {

	/**
	 * 收集方式
	 */
	@ApiModelProperty(value = "收集方式（字典，1-自动，2-手动）", required = true)
	private String collectType;
	/**
	 * 文档名称
	 */
	@ApiModelProperty(value = "文档名称（取附件上传成功后回传的originalName字段，多附件逗号分割）", required = true)
	private String fileName;
	/**
	 * 分类id
	 */
	@ApiModelProperty(value = "分类id", required = true)
	private Long classifyId;
	/**
	 * 上传人id
	 */
	@ApiModelProperty(value = "上传人id", hidden = true)
	private Long uploaderId;
	/**
	 * 上传人姓名
	 */
	@ApiModelProperty(value = "上传人姓名", hidden = true)
	private String uploaderName;
	/**
	 * 上传日期
	 */
	@ApiModelProperty(value = "上传日期（格式：yyyy-MM-dd hh:mm:ss）", hidden = true)
	private LocalDateTime uploadDateTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 权限类型
	 */
	@ApiModelProperty(value = "权限类型(1-全员可见,2-指定组织可见)", required = true)
	private String permissionType;
	/**
	 * 部门
	 */
	@ApiModelProperty("部门id集合")
	private List<Long> deptIdList;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id，多附件逗号分割", required = true)
	private String attachId;
	/**
	 * 附件路径
	 */
	@ApiModelProperty(value = "附件路径，多附件逗号分割", required = true)
	private String attachUrl;


	interface save{

	}

	interface update{

	}
}
