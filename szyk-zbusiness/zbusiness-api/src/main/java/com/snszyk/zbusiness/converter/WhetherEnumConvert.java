package com.snszyk.zbusiness.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.snszyk.zbusiness.person.enums.WhetherEnum;

/**
 * <AUTHOR>
 */

public class WhetherEnumConvert implements Converter<WhetherEnum> {
	@Override
	public WriteCellData<WhetherEnum> convertToExcelData(WriteConverterContext<WhetherEnum> context) {
		return new WriteCellData<>(context.getValue().getDictValue());
	}
}
