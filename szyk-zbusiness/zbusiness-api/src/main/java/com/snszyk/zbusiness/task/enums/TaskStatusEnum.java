package com.snszyk.zbusiness.task.enums;

/**
 * 任务状态
 * 任务状态，不可修改，根据任务内容自动更新
 * <p>
 * 空：新增时默认为空。
 * <p>
 * 进行中：发起人下发后，单据状态置为进行中，表示任务已经下发。
 * <p>
 * 部分提报：进度跟踪页签中，状态部分已提报，部分为进行中或已退回时，任务状态为部分提报
 * <p>
 * 全部提报：进度跟踪页签中，状态全部为已提报，任务状态为全部提报
 * <p>
 * 已归档： 发起人可进行归档操作，确认任务结束，整单归档，同时将进度跟踪中的状态置为“已归档”，相关负责人不能再进行提报。如果是否引用任务模板为否，
 * 则下发后，将任务状态置为已归档，代表任务执行人不需要进行提报，此任务为通知类任务。
 *
 * <AUTHOR>
 * @create 2023/4/24
 */
public enum TaskStatusEnum {


	IN_PROCESS("1", "进行中"),
	PART_SUBMIT("2", "部分提报"),
	ALL_SUBMIT("3", "全部提报"),
	PLACE_ON_FILE("4", "已归档"),
	REVOKED("6", "已撤销"),
	;

	private String code;
	private String message;

	TaskStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
