package com.snszyk.zbusiness.assess.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考核结果的默认参数
 */
@Data
@ApiModel
public class RwResultQueryParamDto {

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "体系id")
	private Long schemeId;

	@ApiModelProperty(value = "体系名")
	private String schemeName;
	/**
	 * 发起组织id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;

	@ApiModelProperty(value = "发起组织名")
	private String initiateOrgName;
	/**
	 * 考核周期明细id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;

	@ApiModelProperty(value = "考核周期明细名")
	private String rwPeriodDetailName;
}
