package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsSupplierDto;
import com.snszyk.zbusiness.resource.dto.RsSupplierPageDto;
import com.snszyk.zbusiness.resource.vo.RsSupplierPageVo;
import com.snszyk.zbusiness.resource.vo.RsSupplierVo;

import java.util.List;

/**
 * IRsSupplierService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRsSupplierService extends IBaseCrudService<RsSupplierDto, RsSupplierVo> {

	IPage<RsSupplierPageDto> pageList(RsSupplierPageVo vo);

	List<RsSupplierDto> listAll();

	List<RsSupplierDto> listByName(String supplierName);

	List<RsSupplierDto> listByNo(String supplierNo);

	RsSupplierDto update(RsSupplierVo vo);

    Boolean saveBatch(List<RsSupplierVo> list);

	/*

	/**
	 * 根据供应商名查询list
	 * @param supplyNameList
	 * @return
	 */
	List<RsSupplierDto> listByNames(List<String> supplyNameList);
}
