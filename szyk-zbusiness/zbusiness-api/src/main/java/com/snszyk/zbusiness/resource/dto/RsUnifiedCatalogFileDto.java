/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 统谈分签文档表实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsUnifiedCatalogFileDto对象", description = "统谈分签文档表")
public class RsUnifiedCatalogFileDto extends BaseCrudDto {

	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;


}
