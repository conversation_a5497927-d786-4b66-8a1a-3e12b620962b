package com.snszyk.zbusiness.resource.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class RsServerRoomExportDto {

	/**
	 * 数据中心机房名称
	 */
	@ExcelProperty(value = "数据中心机房名称")
	private String roomName;
	/**
	 * 机房编号
	 */
	@ExcelProperty(value = "数据中心机房编号")
	private String roomNo;
	/**
	 * 机房类型,数据字典
	 */
	@ExcelProperty(value = "机房类型")
	private String roomTypeName;
	/**
	 * 机房面积
	 */
	@ExcelProperty(value = "机房面积（平米）")
	private BigDecimal roomArea;
	/**
	 * 机房等级，数据字典
	 */
	@ExcelProperty(value = "机房等级")
	private String roomLevelName;
	/**
	 * 机房定位，数据字典
	 */
	@ExcelProperty(value = "机房定位（用途）")
	private String roomUseName;
	/**
	 * 机房承重
	 */
	@ExcelProperty(value = "机房承重（kg/m²）")
	private BigDecimal roomBear;
	/**
	 * 所在楼层
	 */
	@ExcelProperty(value = "所在楼层")
	private String floorNo;
	/**
	 * 楼层高度
	 */
	@ExcelProperty(value = "楼层高度（米）")
	private BigDecimal floorHeight;
	/**
	 * PUE水平
	 */
	@ExcelProperty(value = "PUE水平")
	private BigDecimal pueLevel;
	/**
	 * 负责人姓名
	 */
	@ExcelProperty(value = "负责人")
	private String headPersonName;
	/**
	 * 负责人联系方式
	 */
	@ExcelProperty(value = "联系方式")
	private String headPersonTel;
	/**
	 * 机房状态
	 */
	@ExcelProperty(value = "资源状态")
	private String roomStatusName;
	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注")
	private String remark;
	/**
	 * 送风方式，数据字典
	 */
	@ExcelProperty(value = "送风方式")
	private String airModeName;
	/**
	 * 制冷类型，数据字典
	 */
	@ExcelProperty(value = "制冷类型")
	private String airRefrigerationName;
	/**
	 * 持续制冷时间
	 */
	@ExcelProperty(value = "持续制冷时间（H）")
	private BigDecimal airTime;
	/**
	 * 总数量
	 */
	@ExcelProperty(value = "总数量（个）")
	private Integer rackTotal;
	/**
	 * 单机柜空间
	 */
	@ExcelProperty(value = "单机柜空间（U）")
	private Integer rackSingleSpace;
	/**
	 * 功率密度
	 */
	@ExcelProperty(value = "功率密度（KW/柜）")
	private BigDecimal rackPowerDensity;
	/**
	 * 已部署服务器数量
	 */
	@ExcelProperty(value = "已部署服务器数量（台）")
	private Integer rackDeployServer;
	/**
	 * 市电多路及每路容量
	 */
	@ExcelProperty(value = "市电多路及每路容量")
	private String powerSupply;
	/**
	 * 是否配备油机
	 */
	@ExcelProperty(value = "是否配备油机")
	private String powerEngineName;
	/**
	 * UPS冗余模式
	 */
	@ExcelProperty(value = "UPS冗余模式")
	private String powerUps;

	@ExcelProperty(value = "UPS持续时长（H）")
	private BigDecimal upsTime;

	/**
	 * 创建人
	 */
	@ExcelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "创建时间")
	private String createTimeName;
	/**
	 * 更新人
	 */
	@ExcelProperty(value = "修改人")
	private String updateUserName;
	/**
	 * 更新时间
	 */
	@ExcelProperty(value = "修改时间")
	private String updateTimeName;
	/**
	 * 是否配备动环监控系统,0否1是
	 */
	@ExcelProperty(value = "是否配备动环监控系统")
	private Integer deSystemName;
	/**
	 * 动环系统品牌
	 */
	@ExcelProperty(value = "动环系统品牌")
	private String deBrand;

	/**
	 * 动环系统型号
	 */
	@ExcelProperty(value = "动环系统型号")
	private String deModel;


	/**
	 * 提交状态，数据字典
	 */
	@ExcelProperty(value = "提交状态")
	private String submitStatusName;
	/**
	 * 资源状态，数据字典
	 */
	@ExcelProperty(value = "资源状态")
	private String resourceStatusName;

}
