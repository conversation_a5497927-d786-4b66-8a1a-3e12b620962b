package com.snszyk.zbusiness.assess.dto;

import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class RwResultDetailDto {

	/**
	 * 考核管理id
	 */
	@ApiModelProperty(value = "考核管理id")
	private Long rwId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty(value = "指标分类id")
	private Long classifyId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty(value = "体系指标分类id")
	private Long schemeClassifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty(value = "指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty(value = "是否加分项：0否1是")
	private String addScore;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty(value = "是否加分项名称")
	private String addScoreName;


	/**
	 * 合并行
	 */
	@ApiModelProperty("合并行")
	private Integer classifyNumber;


	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long evaluateId;
	/**
	 * 关联指标分类id
	 */
	@ApiModelProperty(value = "关联指标分类id")
	private Long projectEvaluateClassifyId;
	/**
	 * 评价指标id
	 */
	@ApiModelProperty(value = "评价指标id")
	private Long evaluateTargetId;
	/**
	 * 评价指标
	 */
	@ApiModelProperty(value = "评价指标")
	private String evaluateTarget;
	/**
	 * 指标解释
	 */
	@ApiModelProperty(value = "指标解释")
	private String targetExplain;
	/**
	 * 指标分值
	 */
	@ApiModelProperty(value = "指标分值")
	private BigDecimal score;
	/**
	 * 指标得分
	 */
	@ApiModelProperty(value = "指标得分")
	private BigDecimal getScore;


	/**
	 * 合并行
	 */
	@ApiModelProperty("合并行")
	private Integer evaluateNumber;


	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long methodId;
	/**
	 * 关联指标评价id
	 */
	@ApiModelProperty(value = "关联指标评价id")
	private Long projectEvaluateTargetId;
	/**
	 * 评分方法id
	 */
	@ApiModelProperty(value = "评分方法id")
	private Long scoreMethodId;
	/**
	 * 评分方法
	 */
	@ApiModelProperty(value = "评分方法")
	private String scoreMethod;
	/**
	 * 自评扣分/加分
	 */
	@ApiModelProperty(value = "自评扣分/加分")
	private BigDecimal selfScore;
	/**
	 * 考核扣分/加分
	 */
	@ApiModelProperty(value = "考核扣分/加分")
	private BigDecimal rwScore;
	/**
	 * 考核原因
	 */
	@ApiModelProperty(value = "考核原因")
	private String rwCause;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private List<SzykAttachDto> fileList = new ArrayList<>();
	/**
	 * 剩余得分
	 */
	@ApiModelProperty(value = "剩余得分")
	private BigDecimal leftScore;

	/**
	 * 考核方式，数据字典
	 */
	@ApiModelProperty("考核方式，数据字典")
	private String rwType;
	/**
	 * 考核方式，数据字典
	 */
	@ApiModelProperty("考核方式名称")
	private String rwTypeName;




}
