package com.snszyk.zbusiness.assess.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class RwManageDto extends BaseCrudDto {

	/**
	 * 考核单号
	 */
	@ApiModelProperty(value = "考核单号")
	private String rwNo;
	/**
	 * 考核方式，数据字典  1 专项考核 2 过程考核
	 */
	@ApiModelProperty(value = "考核方式，数据字典")
	private String rwType;
	/**
	 * 考核方式，数据字典
	 */
	@ApiModelProperty(value = "考核方式")
	private String rwTypeName;
	/**
	 * 体系id
	 */
	@ApiModelProperty(value = "体系id")
	private Long schemeId;
	/**
	 * 体系名称
	 */
	@ApiModelProperty(value = "体系名称")
	private String schemeName;
	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;
	/**
	 * 发起组织名称
	 */
	@ApiModelProperty(value = "发起组织名称")
	private String initiateOrgName;
	/**
	 * 被考核组织id
	 */
	@ApiModelProperty(value = "被考核组织id")
	private Long assessedOrgId;
	/**
	 * 被考核组织名称
	 */
	@ApiModelProperty(value = "被考核组织名称")
	private String assessedOrgName;
	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;
	/**
	 * 考核期间名称
	 */
	@ApiModelProperty(value = "考核期间名称")
	private String periodName;
	/**
	 * 自评截止日期
	 */
	@ApiModelProperty(value = "自评截止日期")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date selfAssessmentDeadline;
	/**
	 * 完成自评日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "完成自评日期")
	private Date selfAssessmentComplete;
	/**
	 * 指标得分合计
	 */
	@ApiModelProperty(value = "指标得分合计")
	private BigDecimal targerTotalScore;
	/**
	 * 考核扣分/加分合计
	 */
	@ApiModelProperty(value = "考核扣分/加分合计")
	private BigDecimal rwTotalScore;
	/**
	 * 自评扣分/加分合计
	 */
	@ApiModelProperty(value = "自评扣分/加分合计")
	private BigDecimal selfTotalScore;
	/**
	 * 来源类型
	 */
	@ApiModelProperty(value = "来源类型")
	private String sourceType;
	/**
	 * 来源类型
	 */
	@ApiModelProperty(value = "来源名称")
	private String sourceTypeName;
	/**
	 * 关联id
	 */
	@ApiModelProperty(value = "关联id")
	private Long associateId;
	/**
	 * 关联单号
	 */
	@ApiModelProperty(value = "关联单号")
	private String associateNo;
	/**
	 * 关联单号
	 */
	@ApiModelProperty(value = "关联名称")
	private String associateName;
	/**
	 * 考核状态，数据字典
	 */
	@ApiModelProperty(value = "考核状态，数据字典")
	private String rwStatus;
	/**
	 * 考核状态，数据字典
	 */
	@ApiModelProperty(value = "考核状态名称")
	private String rwStatusName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty(value = "创建人名称")
	private String createUserName;
	/**
	 * 考核指标
	 */
	@ApiModelProperty(value = "考核指标")
	private List<RwManageCommonDto> list = new ArrayList<>();

	/**
	 * 督办状态
	 */
	@ApiModelProperty(value = "督办状态(1 已督办)")
	private String superviseStatus;
	/**
	 * 督办任务id
	 */
	@ApiModelProperty(value = "督办任务id")
	private String superviseTaskId;
	/**
	 * 考核小组成员
	 */
	@ApiModelProperty(value = "考核小组成员")
	private String teamMembers;
	/**
	 * 现场考核成员
	 */
	@ApiModelProperty(value = "现场考核成员")
	private String sceneMembers;
	/**
	 * 工作计划
	 */
	@ApiModelProperty(value = "工作计划")
	private List<SzykAttachDto> workPlanFileList;
	/**
	 * 过程考核附件
	 */
	@ApiModelProperty(value = "过程考核附件")
	private List<SzykAttachDto> processFileList;
	@JsonFormat(
		pattern = "yyyy-MM-dd"
	)
	@ApiModelProperty("创建时间")
	private Date createTime;

	@ApiModelProperty("自评人id")
	private Long selfUserId;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("考核日期")
	private LocalDate rwDate;

	private BigDecimal totalScore;

	@ApiModelProperty("来源名称")
	private String sourceName;
}
