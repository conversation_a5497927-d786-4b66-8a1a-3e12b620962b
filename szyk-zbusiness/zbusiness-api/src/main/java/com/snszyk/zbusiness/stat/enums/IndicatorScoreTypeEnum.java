package com.snszyk.zbusiness.stat.enums;

/**
 * 计分规则类型
 *
 *
 * <AUTHOR>
 * @create 2023/4/24
 */
public enum IndicatorScoreTypeEnum {


	STRATEGY_ONE("1", "计分规则一"),
	STRATEGY_TWO("2", "计分规则二"),
	STRATEGY_THREE("3", "计分规则三"),
	STRATEGY_FOUR("4", "计分规则四"),
	STRATEGY_FIVE("5", "计分规则五"),
	;

	private String code;
	private String message;

	IndicatorScoreTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
