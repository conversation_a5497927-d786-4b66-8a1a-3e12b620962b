/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录扩展字段实体类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("sys_operation_record_extension")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录扩展字段", description = "操作记录扩展字段实体")
public class SysOperationRecordExtension extends BaseCrudEntity {

    /**
     * 操作记录ID
     */
    @ApiModelProperty(value = "操作记录ID")
    private Long recordId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段标签
     */
    @ApiModelProperty(value = "字段标签")
    private String fieldLabel;

    /**
     * 变更前值
     */
    @ApiModelProperty(value = "变更前值")
    private String oldValue;

    /**
     * 变更后值
     */
    @ApiModelProperty(value = "变更后值")
    private String newValue;

    /**
     * 变更前显示值
     */
    @ApiModelProperty(value = "变更前显示值")
    private String oldDisplayValue;

    /**
     * 变更后显示值
     */
    @ApiModelProperty(value = "变更后显示值")
    private String newDisplayValue;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;
}
