package com.snszyk.zbusiness.rpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @atuthor
 * @date 2023/2/23
 * @apiNote
 */
@Data
@ToString
@Accessors(chain = true)
@ApiModel(value = "LoginLogVo", description = "登录日志查询VO")
public class LoginLogVo {


	@ApiModelProperty("组织编码")
	public List<String> orgCode;


	@ApiModelProperty("组织名称")
	public String orgName;

	@ApiModelProperty("工号")
	public String userCode;

	@ApiModelProperty("姓名")
	public String userName;

	@ApiModelProperty("角色类型")
	public String userType;

	@ApiModelProperty("登录时间开始")
	public String loginStartTime;

	@ApiModelProperty("登录时间结束")
	public String loginEndTime;

	@ApiModelProperty("页面")
	public Integer pageNumber;

	@ApiModelProperty("每页条目数")
	public Integer pageSize;


}
