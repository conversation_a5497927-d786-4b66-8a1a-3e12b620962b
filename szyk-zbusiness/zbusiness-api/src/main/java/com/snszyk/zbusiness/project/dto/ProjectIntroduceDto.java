package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ProjectIntroduceDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 项目现状及存在问题
	 */
	@ApiModelProperty(value = "项目现状及存在问题")
	private String projectIntroduction;
	/**
	 * 项目必要性
	 */
	@ApiModelProperty(value = "项目必要性")
	private String projectNecessity;
	/**
	 * 建设内容
	 */
	@ApiModelProperty(value = "建设内容")
	private String constructionContent;
	/**
	 * 设备投入明细
	 */
	@ApiModelProperty(value = "设备投入明细")
	private String equipmentInputDetails;
	/**
	 * 信息化智能化投入明细
	 */
	@ApiModelProperty(value = "信息化智能化投入明细")
	private String imInputDetails;

}
