package com.snszyk.zbusiness.flow.enums;

/**
 *
 *
 * <AUTHOR> @create 2022/8/1
 */
public enum FirstProcessEnum {


	STEP_NAME("发起人"),
	STEP_TYPE("0"),
	STEP_MODE("ONESIGN"),
	SUBMIT_CHOOSE("1"),
	SCREEN_DEPT("1"),
	FLOW_SORT("0"),
	BEFORE_EVENT("0"),
	APPROVE_STATUS("1")
	;

	private String value;

	FirstProcessEnum(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}
}
