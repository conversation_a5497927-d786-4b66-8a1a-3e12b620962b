/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.person.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.person.dto.PersonalBaseDto;
import com.snszyk.zbusiness.person.dto.PersonalDetailDto;
import com.snszyk.zbusiness.person.vo.PersonalBaseVo;
import com.snszyk.zbusiness.person.vo.PersonalDetailVo;

import java.util.List;

/**
 * 人员明细表 服务类
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
public interface IPersonalDetailService extends IBaseCrudService<PersonalDetailDto, PersonalDetailVo> {
	/**
	 * 根据人员id删除人员详情
	 *
	 * @param personalIdList
	 * @return
	 */
	boolean deleteByPersonIds(List<Long> personalIdList);

	/**
	 * 根据人员id查询人员详情信息
	 * @param personIdList
	 * @return
	 */
	List<PersonalDetailDto> list(List<Long> personIdList);

	PersonalDetailDto update(PersonalDetailVo vo);
}
