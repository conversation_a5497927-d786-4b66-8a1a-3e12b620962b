/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 系统得分表实体类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndicatorReportSystemVo对象", description = "系统得分表")
public class IndicatorReportSystemVo extends BaseCrudVo {

	/**
	* 系统编号
	*/
		@ApiModelProperty(value = "系统编号")
		private String systemNo;
	/**
	* 年份
	*/
		@ApiModelProperty(value = "年份")
		private Integer year;
	/**
	* 月份
	*/
		@ApiModelProperty(value = "月份")
		private Integer month;
	/**
	* 本月得分
	*/
		@ApiModelProperty(value = "本月得分")
		private BigDecimal score;
	/**
	* 上月得分
	*/
		@ApiModelProperty(value = "上月得分")
		private BigDecimal lastMonthScore;
	/**
	* 环比
	*/
		@ApiModelProperty(value = "环比")
		private BigDecimal chainBase;
	/**
	* 指标个数
	*/
		@ApiModelProperty(value = "指标个数")
		private Integer indicatorNum;


}
