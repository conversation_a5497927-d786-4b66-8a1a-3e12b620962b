/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 监控:异常单位个数
 *
 * <AUTHOR>
 * @since 2023-10-8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MonitorAbnormalCompany", description = "异常单位个数")
public class MonitorAbnormalCompanyDto {


	@ApiModelProperty(value = "描述")
	private String indexDesc;

	@ApiModelProperty(value = "数量")
	private Integer count;

	@ApiModelProperty(value = "阈值描述")
	private String thresholdDesc;

	@ApiModelProperty(value = "公司名称列表")
	List<String> companyNameList;

}
