/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import com.snszyk.zbusiness.resource.enums.LockStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * 资源锁定表实体类
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LockVo对象", description = "资源锁定表")
@Accessors(chain = true)
public class LockVo extends BaseCrudVo {
	/**
	 * 组织id
	 */
	@ApiModelProperty(value = "组织id")
	private Long orgId;
	/**
	 * 组织idList列表
	 */
	private Collection<Long> orgIdList;
	/**
	 * 组织名称
	 */
	@ApiModelProperty(value = "组织名称")
	private String orgName;
	/**
	 * 组织层级
	 */
	@ApiModelProperty("组织层级")
	private Integer orgLevel;
	/**
	 * 目标资源，业务字典
	 */
	@ApiModelProperty(value = "目标资源，业务字典")
	private String targetResource;
	/**
	 * 锁定状态，业务字典
	 */
	@ApiModelProperty(value = "锁定状态，业务字典")
	private String lockStatus;
	/**
	 * 上一个锁定状态
	 */
	@ApiModelProperty("上一个锁定状态")
	private String lastLockStatus;
	/**
	 * 二级锁定状态，业务字典
	 */
	@ApiModelProperty(value = "二级锁定状态，业务字典")
	private String secondLockStatus;
	/**
	 * 锁定层级，业务字典，多个用英文逗号隔开
	 */
	@ApiModelProperty(value = "锁定层级，业务字典，多个用英文逗号隔开")
	private String lockType;
	/**
	 * 自动锁定时间
	 */
	@ApiModelProperty(value = "自动锁定时间")
	private LocalDateTime autoLockTime;
	/**
	 * 仅集团使用的锁定时间
	 */
	@ApiModelProperty("仅集团使用的锁定时间")
	private LocalDateTime showLockTime;

	/**
	 * 初始化
	 *
	 * @param orgId
	 * @param orgName
	 * @param targetResource
	 * @return
	 */
	public LockVo init(Long parentOrgId, Long orgId, String orgName, Integer orgLevel, String targetResource) {
		this.setLockStatus(LockStatusEnum.LOCK_STATUS_1.getCode());
		this.setSecondLockStatus(LockStatusEnum.LOCK_STATUS_1.getCode());
		this.setTargetResource(targetResource);
		this.setOrgId(orgId);
		this.setOrgName(orgName);
		this.setOrgLevel(orgLevel);
		return this;
	}
}
