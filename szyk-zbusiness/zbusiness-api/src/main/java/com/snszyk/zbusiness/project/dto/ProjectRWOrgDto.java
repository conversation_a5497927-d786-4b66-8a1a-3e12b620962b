package com.snszyk.zbusiness.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ProjectRWOrgDto {


	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;
	/**
	 * 发起组织名称
	 */
	@ApiModelProperty(value = "发起组织名称")
	private String initiateOrgName;
	/**
	 * 被考核组织id
	 */
	@ApiModelProperty(value = "被考核组织id")
	private Long assessedOrgId;
	/**
	 * 被考核组织名称
	 */
	@ApiModelProperty(value = "被考核组织名称")
	private String assessedOrgName;
}
