package com.snszyk.zbusiness.dict.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DictLabelStatusVo", description = "项目标签字典表")
public class DictLabelStatusVo {

	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long id;

	/**
	 * 状态，业务字典enable_type
	 */
	@ApiModelProperty("状态")
	private String dictStatus;
}
