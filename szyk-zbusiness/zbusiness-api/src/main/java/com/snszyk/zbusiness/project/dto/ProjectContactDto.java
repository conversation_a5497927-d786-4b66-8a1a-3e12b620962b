package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ProjectContactDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 项目负责人
	 */
	@ApiModelProperty(value = "项目负责人")
	private String headPerson;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "负责人联系方式")
	private String headPersonTel;
	/**
	 * 项目联系人
	 */
	@ApiModelProperty(value = "项目联系人")
	private String constructionPerson;
	/**
	 * 项目联系人联系方式
	 */
	@ApiModelProperty(value = "项目联系人联系方式")
	private String constructionPersonTel;

}
