package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "RwManageTargetEvaluateVo", description = "考核管理")
public class RwManageTargetEvaluateVo extends BaseCrudSlimVo {

	/**
	 * 项目后评价id
	 */
	@ApiModelProperty(value = "项目后评价id")
	private Long rwId;
	/**
	 * 关联指标分类id
	 */
	@ApiModelProperty(value = "关联指标分类id")
	private Long projectEvaluateClassifyId;
	/**
	 * 评价指标id
	 */
	@ApiModelProperty(value = "评价指标id")
	private Long evaluateTargetId;
	/**
	 * 评价指标
	 */
	@ApiModelProperty(value = "评价指标")
	private String evaluateTarget;
	/**
	 * 指标解释
	 */
	@ApiModelProperty(value = "指标解释")
	private String targetExplain;
	/**
	 * 指标分值
	 */
	@ApiModelProperty(value = "指标分值")
	private BigDecimal score;
	/**
	 * 指标得分
	 */
	@ApiModelProperty(value = "指标得分")
	private BigDecimal getScore;
}
