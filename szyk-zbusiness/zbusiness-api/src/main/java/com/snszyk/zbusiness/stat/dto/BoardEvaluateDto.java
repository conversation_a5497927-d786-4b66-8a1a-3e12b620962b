/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 驾驶舱项目后评价
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BoardEvaluateDto", description = "驾驶舱项目后评价")
public class BoardEvaluateDto {


	@ApiModelProperty(value = ">95")
	private Integer scoreGt95;

	@ApiModelProperty(value = "90-95")
	private Integer score90To95;

	@ApiModelProperty(value = "<90")
	private Integer scoreLt90;

	@ApiModelProperty(value = "总数")
	private Integer total;

}
