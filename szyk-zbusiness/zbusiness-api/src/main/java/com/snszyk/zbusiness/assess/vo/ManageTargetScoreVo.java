/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 被考核组织周期内剩余分数实体类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ManageTargetScoreVo对象", description = "被考核组织周期内剩余分数")
public class ManageTargetScoreVo extends BaseCrudVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty(value = "体系id")
	private Long schemeId;
	/**
	 * 被考核组织id
	 */
	@ApiModelProperty(value = "被考核组织id")
	private Long assessedOrgId;
	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;
	/**
	 * 考核指标id
	 */
	@ApiModelProperty(value = "考核指标id")
	private Long targetId;
	/**
	 * 剩余指标分值
	 */
	@ApiModelProperty(value = "剩余指标分值")
	private BigDecimal leftScore;


}
