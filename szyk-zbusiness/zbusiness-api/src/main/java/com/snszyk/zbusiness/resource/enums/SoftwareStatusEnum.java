package com.snszyk.zbusiness.resource.enums;

/**
 * 调度力度
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum SoftwareStatusEnum {

	/**
	 * 在用
	 */
	IN_USE("0", "在用"),

	/**
	 * 停用
	 */
	STOP("1", "停用"),
	;

	private String code;
	private String message;

	SoftwareStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
