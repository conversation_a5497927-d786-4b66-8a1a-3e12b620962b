/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service;

import com.snszyk.zbusiness.project.dto.PlFileDto;
import com.snszyk.zbusiness.project.dto.ProjectFileDto;
import com.snszyk.zbusiness.project.vo.PlFileVo;
import com.snszyk.core.crud.service.IBaseCrudService;

import java.util.List;

/**
 * 项目库项目文档表 服务类
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
public interface IPlFileService extends IBaseCrudService<PlFileDto, PlFileVo> {
	/**
	 * 批量新增
	 * @param fileVoList
	 * @return
	 */
	Boolean saveBatch(List<PlFileVo> fileVoList);

	List<PlFileDto> listByProject(Long projectId, Long businessId, String businessType);

	int deleteByProjectId(Long projectId, List<Long> businessIdList, String businessType);
}
