package com.snszyk.zbusiness.project.enums;

/**
 * 错误信息
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum ExceptionEnum {

	NOT_DATA("暂无承载数据"),
	NOT_PARAMS("参数为空"),

	DATA_NOT_DELETE("当前数据不可删除"),

	DEPT_NOT_EXIST_PARENT("当前单位不存在上级单位"),

	PROGRESS_DATA_EXIST_PROJECT("必须先处理已存在同项目的单据，才能新增新的单据"),

	REPORT_CHECK_ERROR_1("建设单位为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_2("公司名称为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_3("项目名称为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_4("计划资金为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_5("合同金额为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_6("投资主体为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_7("项目阶段数据不能为空"),
	REPORT_CHECK_ERROR_8("计划开始时间为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_9("计划完成时间为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_10("项目验收数据不能为空"),
	REPORT_CHECK_ERROR_11("验收时间为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_12("集团信息化资源使用情况为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_13("项目完成情况为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_14("项目运行情况为必填数据，请核查是否未填写"),

	REPORT_CHECK_ERROR_15("专业分类为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_16("项目分类为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_17("计划招标日期为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_18("计划开始日期为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_19("计划完成日期为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_20("列支渠道为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_21("申报年份为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_22("投资主体为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_23("项目预计总投资为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_24("项目本年度投资为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_25("项目现状及存在问题为必填数据，请核查是否未填写"),

	REPORT_CHECK_ERROR_26("项目必要性为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_27("项目现状为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_28("建设内容为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_29("项目目标为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_30("设备投入明细为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_31("信息化智能化系统投入明细为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_32("项目负责人为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_33("项目负责人联系方式为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_34("项目联系人为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_35("项目联系人联系方式为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_36("至少需要一条项目成员数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_37("至少需要上传一份附件，请核查是否未填写"),
	REPORT_CHECK_ERROR_38("合作单位为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_39("合作单位联系人为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_40("合作单位联系方式为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_41("监理单位为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_42("监理单位联系人为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_43("监理单位联系方式为必填数据，请核查是否未填写"),
	REPORT_CHECK_ERROR_44("合作单位联系方式手机号格式不正确"),
	REPORT_CHECK_ERROR_45("监理单位联系方式手机号格式不正确"),
	REPORT_CHECK_ERROR_46("项目负责人手机号格式不正确"),
	REPORT_CHECK_ERROR_47("项目联系人手机号格式不正确"),

	PROJECT_NAME_EXIST("提报的数据中存在项目名称重复的数据"),

	PROJECT_LABEL_NOT_DATA("当前数据不存在项目标签"),

	PROJECT_DATA_EXIST("项目名称已存在相同的项目"),

	PROJECT_EVALUATE_EXIST("存在项目+建设单位重复的数据"),

	DISPATCH_NOT_DETAIL("项目明细数据不存在"),

	PROJECT_NOT_AUTH("当前项目没有权限提报"),

	PROJECT_NOT_STARTED("只有未开始状态的数据才能删除"),

	PROJECT_TO_BE_REPORTED("只有待提交/已撤回/退回状态的数据才能提报"),
	PROJECT_TO_START_PERSON("只有退回到发起人才能重新提报"),

	PROJECT_TO_BE_REPORTED_DELETE("只有待提交状态的数据才能删除"),
	NO_SELF_CANT_DEL("非本人创建,无法删除"),
	FILE_NOT_FILL("交付物不完整"),
	DING_DATA_NO_EXIST("已不存在或删除"),
	;
	private String message;

	ExceptionEnum(String message) {
		this.message = message;
	}

	public String getMessage() {
		return message;
	}
}
