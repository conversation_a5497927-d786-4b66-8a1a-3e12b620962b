package com.snszyk.zbusiness.dict.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.dict.dto.EvaluateTargetClassifyDto;
import com.snszyk.zbusiness.dict.vo.EvaluateTargetClassifyPageVo;
import com.snszyk.zbusiness.dict.vo.EvaluateTargetClassifyVo;

import java.util.List;

/**
 * IEvaluateTargetClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IEvaluateTargetClassifyService extends IBaseCrudService<EvaluateTargetClassifyDto, EvaluateTargetClassifyVo> {

	IPage<EvaluateTargetClassifyDto> pageList(EvaluateTargetClassifyPageVo vo,Long deptId);

	List<EvaluateTargetClassifyDto> listByClassifyStatus(String classifyStatus);

	List<EvaluateTargetClassifyDto> listByStatusAndCompany(String classifyStatus, List<Long> companyIdList);

	List<EvaluateTargetClassifyDto> listByNameAndCompany(String classifyName, Long companyId);

	List<EvaluateTargetClassifyDto> listAll();

	boolean updateByStatus(Long id, String classifyStatus);

	boolean updateByAddScore(Long id, String addScore);

	EvaluateTargetClassifyDto update(EvaluateTargetClassifyVo vo);
}
