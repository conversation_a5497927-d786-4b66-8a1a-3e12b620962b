/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库项目文档表实体类
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlFileDto对象", description = "项目库项目文档表")
public class PlFileDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;
	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;


}
