package com.snszyk.zbusiness.project.enums;

/**
 * 项目阶段
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum DispatchStatusEnum {

	/**
	 * 待下发
	 */
	ISSUE("1", "待下发"),

	/**
	 * 待提报
	 */
	REPORT("2", "待提报"),

	/**
	 * 部分提报
	 */
	PART_REPORT("3", "部分提报"),

	/**
	 * 全部提报
	 */
	ALL_REPORT("4", "全部提报"),

	/**
	 * 已归档
	 */
	FILE("5", "已归档"),
	;

	private String code;
	private String message;

	DispatchStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
