/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信息系统-服务器信息实体类
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsSystemServerVo对象", description = "信息系统-服务器信息")
public class RsSystemServerVo extends BaseCrudVo {

	/**
	 * 系统id
	 */
	@ApiModelProperty(value = "系统id")
	private Long systemId;

	@ApiModelProperty(value = "系统名称")
	private String systemName;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	@ApiModelProperty(value = "设备编码")
	private String equipmentNo;
	/**
	 * 服务器角色
	 */
	@ApiModelProperty(value = "服务器角色")
	private String severRole;
	/**
	 * 机器名
	 */
	@ApiModelProperty(value = "机器名")
	private String machineName;
	/**
	 * 规划IP
	 */
	@ApiModelProperty(value = "规划IP")
	private  String planIp;;
	/**
	 * 内存(G)
	 */
	@ApiModelProperty(value = "内存(G)")
	private String memory;

	/**
	 * 系统盘(G)
	 */
	@ApiModelProperty(value = "系统盘(G)")
	private String systemDisk;
	/**
	 * 外挂硬盘(G)
	 */
	@ApiModelProperty(value = "外挂硬盘(G)")
	private String externalHardDrive;
	/**
	 * Web服务器类型
	 */
	@ApiModelProperty(value = "Web服务器类型")
	private String middlewareInformation;
	/**
	 * 数据库及版本号
	 */
	@ApiModelProperty(value = "数据库及版本号")
	private String databaseInformation;
	/**
	 * 操作系统类型
	 */
	@ApiModelProperty(value = "操作系统类型")
	private String osType;
	/**
	 * 操作系统名称及版本号
	 */
	@ApiModelProperty(value = "操作系统名称及版本号")
	private String osInformation;

	/**
	 * cpu(核)
	 */
	@ApiModelProperty(value = "cpu(核)")
	private String cpuCore;

	/**
	 * 服务器类型
	 */
	@ApiModelProperty(value = "服务器类型")
	private String serverType;

	/**
	 * 服务器类型
	 */
	@ApiModelProperty(value = "服务器类型名称")
	private String serverTypeName;
	/**
	 *'部署载体'
	 */
	@ApiModelProperty(value = "部署载体'")
	private String deployType;

	/**'部署平台'
	 *
	 */
	@ApiModelProperty(value = "部署平台")
	private String deployPlatform;

	/**
	 *Web服务器类型，数据字典
	 */
	@ApiModelProperty(value = "*Web服务器类型，数据字典")
	private String webServerType;
	/**
	 * Web服务器版本号
	 */
	@ApiModelProperty(value = "Web服务器版本号")
	private String webServerVersion;

	/**
	 *'其他组件，数据字典'
	 */
	@ApiModelProperty(value = "其他组件，数据字典")
	private String otherMiddleware;

	/**
	 *'数据库类型'
	 */
	@ApiModelProperty(value = "据库类型")
	private String databaseType;

	/**
	 *数据库版本号
	 */
	@ApiModelProperty(value = "数据库版本号")
	private String databaseVersion;

	/**
	 *操作系统名称，数据字典
	 */
	@ApiModelProperty(value = "作系统名称，数据字典")
	private String osName;

	/**
	 *操作系统版本号
	 */
	@ApiModelProperty(value = "操作系统版本号")
	private String osVersion;
}
