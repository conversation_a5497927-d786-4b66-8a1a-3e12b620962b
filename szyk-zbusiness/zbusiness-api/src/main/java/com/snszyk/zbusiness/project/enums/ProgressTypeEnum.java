package com.snszyk.zbusiness.project.enums;

/**
 * 进度类型
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum ProgressTypeEnum {

	/**
	 * 项目进度
	 */
	PROJECT_PROGRESS("1", "项目进度"),

	/**
	 * 项目验收
	 */
	PROJECT_ACCEPTED("2", "项目验收"),
	;

	private String code;
	private String message;

	ProgressTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
