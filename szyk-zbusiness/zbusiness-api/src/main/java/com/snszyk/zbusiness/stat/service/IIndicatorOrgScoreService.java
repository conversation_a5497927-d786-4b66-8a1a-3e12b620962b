/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgScoreDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgScoreVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单位分数 服务类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface IIndicatorOrgScoreService extends IBaseCrudService<IndicatorOrgScoreDto, IndicatorOrgScoreVo> {


	/*
	 * 单位得分平均值
	 */
	BigDecimal orgAvgRank(IndicatorOrgRankVo vo);

	/*单位的历史得分趋势

	 */
	List<IndicatorOrgHistoryRankDto> orgHistoryScore(IndicatorOrgHistoryRankVo v);


	boolean saveDataBatch(List<IndicatorOrgScoreVo> copy);

	/*
     分析单位排名
     */
	List<IndicatorReportOrgDto> analyzeReportOrg(Integer year, Integer month,Integer lastYear,Integer lastMonth);

	/**
	 * 分析系统排名
	 * @param year
	 * @param month
	 * @return
	 */
	List<IndicatorReportSystemDto> analyzeReportSystem(Integer year, Integer month,Integer lastYear,Integer lastMonth);

    boolean removeByDate(int year, int monthValue);


	/**
	 * 获取全部系统的指标个数
	 * @param year
	 * @param month
	 * @param lastYear
	 * @param lastMonth
	 * @return
	 */
	List<IndicatorReportOrgDto> getAllSysIndicatorNum(int year, int month, int lastYear, int lastMonth);

	List<IndicatorOrgScoreDto> analyzeZeroOrgScore(int year, int month);

    boolean deleteByMonth(Integer year, Integer month);
}
