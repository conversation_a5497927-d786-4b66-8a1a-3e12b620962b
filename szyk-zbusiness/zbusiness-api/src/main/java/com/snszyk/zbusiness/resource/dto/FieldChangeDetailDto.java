/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字段变更详情数据传输对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@ApiModel(value = "字段变更详情", description = "字段变更详情")
public class FieldChangeDetailDto {

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段中文名称
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldLabel;

    /**
     * 变更前值
     */
    @ApiModelProperty(value = "变更前值")
    private String oldValue;

    /**
     * 变更后值
     */
    @ApiModelProperty(value = "变更后值")
    private String newValue;

    /**
     * 变更前显示值（字典转换后）
     */
    @ApiModelProperty(value = "变更前显示值")
    private String oldDisplayValue;

    /**
     * 变更后显示值（字典转换后）
     */
    @ApiModelProperty(value = "变更后显示值")
    private String newDisplayValue;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 是否为重要字段
     */
    @ApiModelProperty(value = "是否为重要字段")
    private Boolean isImportant;

    public FieldChangeDetailDto() {
    }

    public FieldChangeDetailDto(String fieldName, String fieldLabel, String oldValue, String newValue) {
        this.fieldName = fieldName;
        this.fieldLabel = fieldLabel;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.oldDisplayValue = oldValue;
        this.newDisplayValue = newValue;
        this.isImportant = false;
    }

    public FieldChangeDetailDto(String fieldName, String fieldLabel, String oldValue, String newValue, 
                               String oldDisplayValue, String newDisplayValue) {
        this.fieldName = fieldName;
        this.fieldLabel = fieldLabel;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.oldDisplayValue = oldDisplayValue;
        this.newDisplayValue = newDisplayValue;
        this.isImportant = false;
    }
}
