/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service;

import com.snszyk.zbusiness.task.dto.TaskFileDto;
import com.snszyk.zbusiness.task.vo.TaskFileVo;
import com.snszyk.core.crud.service.IBaseCrudService;

import java.util.List;

/**
 * 任务文档表 服务类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITaskFileService extends IBaseCrudService<TaskFileDto, TaskFileVo> {

	/**
	 * 根据任务id删除附件
	 * @param taskId
	 * @return
	 */
//	int deleteByTaskId(Long taskId);

	boolean deleteByTaskIdsAndType(List<Long> taskIds, List<String> businessTypes);

	void batchUpdate(List<TaskFileVo> fileList);

	/**
	 * 根据任务id获取附件列表
	 * @param taskId
	 * @return
	 */
//	List<TaskFileDto> listByTaskId(Long taskId);

	/**
	 * 根据id批量删除
	 * @param needDeleteIds
	 * @return
	 */
	boolean deleteByIds(List<Long> needDeleteIds);


	void batchSave(List<TaskFileVo> fileList);

	/**
	 * 据业务ids和文件类型查询list
	 *
	 * @param businessIds
	 * @param fileTypeLlist
	 * @return
	 */
	List<TaskFileDto> listByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeLlist);

	List<TaskFileDto> listBaseByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeList);

	List<TaskFileDto> listDetailByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeList);
	/**
	 * 补充附件详细信息
	 *
	 * @param taskFileDtoList
	 */
	void supplyAttachDetail(List<TaskFileDto> taskFileDtoList);
}
