package com.snszyk.zbusiness.dict.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DispatchPeriodStatusVo", description = "调度周期")
public class DispatchPeriodStatusVo {

	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long id;

	/**
	 * 周期状态：0全部开启1部分关闭3全部关闭
	 */
	@ApiModelProperty("周期状态")
	private String periodStatus;
}
