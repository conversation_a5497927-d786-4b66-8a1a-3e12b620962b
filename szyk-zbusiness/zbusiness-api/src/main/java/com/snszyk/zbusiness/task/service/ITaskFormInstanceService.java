///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.service;
//
//import com.snszyk.core.crud.service.IBaseCrudService;
//import com.snszyk.zbusiness.task.vo.TaskFormInstanceVo;
//
//import java.util.List;
//
///**
// * 任务表单实例 服务类
// *
// * <AUTHOR>
// * @since 2023-04-24
// */
//public interface ITaskFormInstanceService extends IBaseCrudService<TaskFormInstanceDto, TaskFormInstanceVo> {
//
//	void batchSave(List<TaskFormInstanceVo> formList);
//	void batchUpdate(List<TaskFormInstanceVo> formList);
//
//	/**
//	 * 根据模版实例id查询
//	 * @param modelInstantId
//	 * @return
//	 */
//	List<TaskFormInstanceDto> listByModelInstanceId(Long modelInstantId);
//
//	void batchDeleteByIds(List<Long> collect);
//}
