package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RsSystemInternetExportVo", description = "信息系统台账")
public class RsSystemInternetExportVo extends BaseCrudSlimVo {

	/**
	 * 系统id
	 */
	@ApiModelProperty(value = "软件id")
	private Long systemId;
	/**
	 * 单位级别，数据字典
	 */
	@ApiModelProperty(value = "单位级别，数据字典")
	private String unitLevel;
	/**
	 * 等保级别，数据字典
	 */
	@ApiModelProperty(value = "等保级别，数据字典")
	private String securityLevel;
	/**
	 * 互联网地址
	 */
	@ApiModelProperty(value = "互联网地址")
	private String internetAddress;
	/**
	 * 内网地址
	 */
	@ApiModelProperty(value = "内网地址")
	private String intranetAddress;
	/**
	 * 域名或URL
	 */
	@ApiModelProperty(value = "域名或URL")
	private String domainName;
	/**
	 * 应用类型，数据字典
	 */
	@ApiModelProperty(value = "应用类型，数据字典")
	private String applicationType;
	/**
	 * 系统所需资源
	 */
	@ApiModelProperty(value = "系统所需资源")
	private String resourceRequire;
	/**
	 * 负责人
	 */
	@ApiModelProperty(value = "负责人")
	private String headPerson;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "负责人联系方式")
	private String headPersonTel;
	/**
	 * 数据中心机房id
	 */
	@ApiModelProperty(value = "数据中心机房id")
	private Long serverroomId;

}
