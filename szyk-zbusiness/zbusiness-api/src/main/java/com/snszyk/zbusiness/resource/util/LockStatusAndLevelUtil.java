package com.snszyk.zbusiness.resource.util;

import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.resource.enums.LockStatusEnum;
import com.snszyk.zbusiness.resource.enums.LockTypeEnum;
import com.snszyk.zbusiness.resource.enums.OperateActionEnum;
import com.snszyk.zbusiness.resource.enums.UnitLevelEnum;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023-12-28-14:26
 */
public class LockStatusAndLevelUtil {


	public static String getLockStatus(@NotNull OperateActionEnum actionEnum, @NotEmpty Integer unitLevel) {
		switch (actionEnum) {
			// 锁定
			case OPERATE_ACTION_1:
				// 集团锁定
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_2.getCode();
				}
				// 二级公司锁定
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return null;
				}
				throw new BusinessException("锁定异常！未知的单位层级");

				// 解锁
			case OPERATE_ACTION_2:
				// 集团解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_1.getCode();
				}
				// 二级公司解锁
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return null;
				}
				throw new BusinessException("解锁异常！未知的单位层级");

				// 台账申请解锁
			case OPERATE_ACTION_3:
				// 集团公司申请解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_3.getCode();
				}
				return null;

			// 拒绝解锁
			case OPERATE_ACTION_4:
				// 集团拒绝解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_2.getCode();
				}
				// 二级公司拒绝解锁
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return null;
				}
				throw new BusinessException("拒绝解锁异常！未知的单位层级");
				// 资源锁定页面申请解锁
			case OPERATE_ACTION_5:

				if ( UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_3.getCode();
				}
				return null;
			default:
				throw new BusinessException("获取集团锁定状态异常！");
		}
	}

	/**
	 * 获取真实锁定状态
	 *
	 * @param actionEnum  操作类型
	 * @param unitLevel   操作单位层级
	 * @param oldLockType 旧的锁定层级
	 * @return 真实锁定状态
	 */
	public static String getSecondLockStatus(@NotNull OperateActionEnum actionEnum, @NotEmpty Integer unitLevel, @NotEmpty String oldLockType) {
		switch (actionEnum) {
			// 锁定
			case OPERATE_ACTION_1:
				// 集团锁定或者二级公司锁定
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)
					|| UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_2.getCode();
				}
				throw new BusinessException("获取真实锁定状态异常！未知的单位层级操作！");

				// 解锁
			case OPERATE_ACTION_2:
				// 集团解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					// 如果原来锁定层级是集团锁定
					if (LockTypeEnum.LOCK_TYPE_1.getCode().equals(oldLockType)) {
						return LockStatusEnum.LOCK_STATUS_1.getCode();
					}
					// 如果原来锁定层级是集团锁定、二级公司锁定
					if (LockTypeEnum.LOCK_TYPE_3.getCode().equals(oldLockType)) {
						return LockStatusEnum.LOCK_STATUS_2.getCode();
					}
					throw new BusinessException("集团解锁时，获取真实锁定状态失败！");
				}
				// 二级公司解锁
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					if (!LockTypeEnum.LOCK_TYPE_2.getCode().equals(oldLockType)) {
						throw new BusinessException("二级公司没有解锁权限！");
					}
					return LockStatusEnum.LOCK_STATUS_1.getCode();
				}

				throw new BusinessException("获取真实锁定状态异常！未知的单位层级操作！");

				// 申请解锁
			case OPERATE_ACTION_3:
				return LockStatusEnum.LOCK_STATUS_3.getCode();

			// 拒绝解锁
			case OPERATE_ACTION_4:
				// 集团或者二级公司拒绝解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)
					|| UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return LockStatusEnum.LOCK_STATUS_2.getCode();
				}
				throw new BusinessException("获取真实锁定状态异常！未知的单位层级操作！");

			default:
				throw new BusinessException("获取真实锁定状态异常！");
		}
	}

	/**
	 * 获取锁定层级
	 *
	 * @param actionEnum 操作动作
	 * @param unitLevel  操作单位层级
	 * @return 锁定层级
	 */
	public static String getLockType(@NotNull OperateActionEnum actionEnum, @NotEmpty Integer unitLevel, String oldLockType) {
		switch (actionEnum) {
			// 锁定
			case OPERATE_ACTION_1:
				// 集团锁定
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					// 如果原来没有锁定层级
					if (ObjectUtil.isEmpty(oldLockType)) {
						return LockTypeEnum.LOCK_TYPE_1.getCode();
					}
					// 如果原来的锁定层级是二级公司锁定
					if (LockTypeEnum.LOCK_TYPE_2.getCode().equals(oldLockType)) {
						return LockTypeEnum.LOCK_TYPE_3.getCode();
					}
					// 如果原来的锁定层级是集团锁定或者集团锁定、二级公司锁定就无需改变
					return oldLockType;
					// throw new BusinessException("获取锁定层级失败！已被集团锁定无需再次锁定");
				}
				// 二级公司锁定
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					// 如果原来没有锁定层级
					if (ObjectUtil.isEmpty(oldLockType)) {
						return LockTypeEnum.LOCK_TYPE_2.getCode();
					}
					throw new BusinessException("获取锁定层级失败！已被二级公司或集团锁定，无需再次锁定");
				}

				throw new BusinessException("获取锁定层级失败！未知的单位层级");

				// 解锁
			case OPERATE_ACTION_2:
				// 集团解锁
				if (UnitLevelEnum.UNIT_LEVEL_1.getCode().equals(unitLevel)) {
					// 如果原来锁定层级是集团锁定
					if (LockTypeEnum.LOCK_TYPE_1.getCode().equals(oldLockType)) {
						return null;
					}
					// 如果原来锁定层级是集团锁定、二级公司锁定
					if (LockTypeEnum.LOCK_TYPE_3.getCode().equals(oldLockType)) {
						return LockTypeEnum.LOCK_TYPE_2.getCode();
					}
					throw new BusinessException("获取锁定层级失败！");
				}
				// 二级公司解锁
				if (UnitLevelEnum.UNIT_LEVEL_2.getCode().equals(unitLevel)) {
					return null;
				}
				throw new BusinessException("获取锁定层级失败！");

				// 申请解锁
			case OPERATE_ACTION_3:
				return oldLockType;
			// 拒绝解锁
			case OPERATE_ACTION_4:
				return oldLockType;

			default:
				throw new BusinessException("获取锁定层级异常！");
		}
	}
}
