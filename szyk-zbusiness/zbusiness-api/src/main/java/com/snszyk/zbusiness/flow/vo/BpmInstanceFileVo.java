/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 流程文档表实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BpmInstanceFileVo对象", description = "流程文档表")
public class BpmInstanceFileVo extends BaseCrudVo {

	/**
	* 附件id
	*/
		@ApiModelProperty(value = "附件id")
		private Long attachId;
	/**
	* 业务id
	*/
		@ApiModelProperty(value = "业务id")
		private Long businessId;
	/**
	* 业务类型
	*/
		@ApiModelProperty(value = "业务类型")
		private String businessType;


}
