package com.snszyk.zbusiness.assess.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class RwTargetClassifyDto extends BaseCrudDto {

	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@ApiModelProperty("所属组织名称")
	private String companyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项")
	private String addScore;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项")
	private String addScoreName;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态")
	private String classifyStatus;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态")
	private String classifyStatusName;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;
}
