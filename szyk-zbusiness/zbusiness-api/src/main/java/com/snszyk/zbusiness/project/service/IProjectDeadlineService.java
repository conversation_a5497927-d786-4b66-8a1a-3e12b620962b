/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.project.dto.ProjectDeadlineDto;
import com.snszyk.zbusiness.project.entity.ProjectDeadline;
import com.snszyk.zbusiness.project.vo.ProjectDeadlinePageVo;

/**
 * 申报截止日期设置 服务类
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
public interface IProjectDeadlineService extends BaseService<ProjectDeadline> {



    /**
    * 分页查询
    */
    IPage<ProjectDeadlineDto> pageList(ProjectDeadlinePageVo v);

    /**
    * 详情
    */
    ProjectDeadlineDto detail(Long id);

}
