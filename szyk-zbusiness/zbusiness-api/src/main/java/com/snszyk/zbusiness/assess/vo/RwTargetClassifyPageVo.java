package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RwTargetClassifyPageVo", description = "考核指标分类")
public class RwTargetClassifyPageVo extends BaseCrudSlimVo {

	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态")
	private String classifyStatus;
}
