/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 统谈分签目录明细实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsUnifiedCatalogDetailSaveVo", description = "统谈分签目录明细")
public class RsUnifiedCatalogDetailSaveVo extends BaseCrudVo {


	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String specification;
	/**
	 * 入围供应商id
	 */
	@ApiModelProperty(value = "入围供应商id")
	private String supplierId;
	/**
	 * 入围供应商名称
	 */
	@ApiModelProperty(value = "入围供应商名称")
	private String supplierName;
	/**
	 * 价格体系
	 */
	@ApiModelProperty(value = "价格体系")
	private String priceScheme;
	/**
	 * 价格有效期开始日期
	 */
	@ApiModelProperty(value = "价格有效期开始日期")
	private LocalDate priceStartDate;
	/**
	 * 加个有效期结束日期
	 */
	@ApiModelProperty(value = "价格有效期结束日期")
	private LocalDate priceEndDate;
	/**
	 * 采购方式，数据字典
	 */
	@ApiModelProperty(value = "采购方式，数据字典")
	private String procurementMethod;
	/**
	 * 采购方式说明
	 */
	@ApiModelProperty(value = "采购方式说明")
	private String procurementExplain;
	/**
	 * 供应商对接联系人
	 */
	@ApiModelProperty(value = "供应商对接联系人")
	private String supplierContact;

	@ApiModelProperty(value = "规格型号附件id集合")
	private List<Long> specificationFiles;

	@ApiModelProperty(value = "价格体系附件id集合")
	private List<Long> priceSchemeFiles;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "是否永久有效")
	private Boolean priceValidity;


}
