/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 设备台账授权实体类
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsEquipmentAuthDto对象", description = "设备台账授权")
public class RsEquipmentAuthDto extends BaseCrudDto {

	/**
	* 设备id
	*/
		@ApiModelProperty(value = "设备id")
		private Long equipmentId;
	/**
	* 授权组织id
	*/
		@ApiModelProperty(value = "授权组织id")
		private Long authOrgId;


}
