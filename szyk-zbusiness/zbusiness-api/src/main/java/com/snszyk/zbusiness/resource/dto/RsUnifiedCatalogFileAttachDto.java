/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 统谈分签文档attach表
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsUnifiedCatalogFileDto对象", description = "统谈分签文档attach表")
public class RsUnifiedCatalogFileAttachDto extends SzykAttachDto {

	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;


}
