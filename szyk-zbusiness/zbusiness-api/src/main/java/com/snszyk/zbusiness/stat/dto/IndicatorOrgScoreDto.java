/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 单位分数实体类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndicatorOrgScoreDto对象", description = "单位分数")
public class IndicatorOrgScoreDto extends BaseCrudDto {

	/**
	 * 系统编号
	 */
	@ApiModelProperty(value = "系统编号")
	private String systemNo;
	/**
	 * 单位编码
	 */
	@ApiModelProperty(value = "单位编码")
	private String orgCode;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;
	/**
	 * 月份
	 */
	@ApiModelProperty(value = "月份")
	private Integer month;
	/**
	 * 分数
	 */
	@ApiModelProperty(value = "分数")
	private BigDecimal score;


}
