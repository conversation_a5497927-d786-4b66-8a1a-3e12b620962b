/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库项目成员表实体类
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlMemberDto对象", description = "项目库项目成员表")
public class PlMemberDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 负责人
	 */
	@ApiModelProperty(value = "负责人")
	private String memberName;
	/**
	 * 是否联系人，0否1是
	 */
	@ApiModelProperty(value = "是否联系人，0否1是")
	private Integer projectContact;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String tel;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private String unit;
	/**
	 * 职称/职务
	 */
	@ApiModelProperty(value = "职称/职务")
	private String titles;
	/**
	 * 责任分工
	 */
	@ApiModelProperty(value = "责任分工")
	private String workDivision;


}
