package com.snszyk.zbusiness.dict.enums;

import com.snszyk.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
@Getter
@AllArgsConstructor
public enum DictCommonEnum {

	/**
	 * 项目状态
	 */
	PROJECT_STATUS_0("project_status", "0", "正常"),

	PROJECT_STATUS_1("project_status", "1", "终止"),

	/**
	 * 项目审核状态
	 */
	REVIEW_STATUS_0("review_status", "0", "审核中"),

	REVIEW_STATUS_1("review_status", "1", "通过"),

	REVIEW_STATUS_2("review_status", "2", "驳回"),


	;
	private String code;
	private String dictKey;
	private String dictValue;

	public static String getValue(String code, String dictKey) {
		if (ObjectUtil.isEmpty(code) || ObjectUtil.isEmpty(dictKey)) {
			return null;
		}
		for (DictCommonEnum value : values()) {
			if (value.getCode().equals(code) && value.getDictKey().equals(dictKey)) {
				return value.getDictValue();
			}
		}
		return null;
	}


}
