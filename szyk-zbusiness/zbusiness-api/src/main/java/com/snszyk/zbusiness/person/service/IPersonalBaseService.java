/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.person.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.person.dto.PersonalBaseDto;
import com.snszyk.zbusiness.person.dto.PersonalBasePageDto;
import com.snszyk.zbusiness.person.vo.PersonalBaseListVo;
import com.snszyk.zbusiness.person.vo.PersonalBaseVo;
import com.snszyk.zbusiness.person.vo.PersonalPageVo;

import java.util.List;

/**
 * 人员基本信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
public interface IPersonalBaseService extends IBaseCrudService<PersonalBaseDto, PersonalBaseVo> {
	/**
	 * 根据id删除人员信息
	 *
	 * @param idList
	 * @return
	 */
	Boolean deleteByIds(List<Long> idList);

	/**
	 * 统计人员数量
	 *
	 * @param manageOrgId
	 * @return
	 */
	Integer countByOrgId(Long manageOrgId);


	Integer countByOrgIds(List<Long> manageOrgIds);

	IPage<PersonalBaseDto> pageList(PersonalBaseListVo vo);

	PersonalBaseDto update(PersonalBaseVo vo);

	List<PersonalBaseDto> listByNo(String jobNo);

    IPage<PersonalBasePageDto> dataPage(PersonalPageVo vo,List<Long> orgIds);

	List<PersonalBaseDto> listByDeptId(List<Long> orgList);

	List<PersonalBaseDto> dataList(PersonalPageVo vo, List<Long> orgList, List<Long> personIds);

	Integer countByOrgIdsNew(Long id);

	PersonalBaseDto queryByJobNo(String jobNo);

	List<PersonalBaseDto> queryList();

	List<PersonalBaseDto> listByDeptCode(String deptCode);

	Boolean updatePerson(String jobNo,Long deptId,String contactPerson);

}
