package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectMemberVo", description = "项目成员表")
public class ProjectMemberVo extends BaseCrudSlimVo {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 成员姓名
	 */
	@ApiModelProperty(value = "成员姓名")
	private String memberName;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private String unit;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String tel;
	/**
	 * 职称/职务
	 */
	@ApiModelProperty(value = " 职称/职务")
	private String titles;
	/**
	 * 责任分工
	 */
	@ApiModelProperty(value = "责任分工")
	private String workDivision;

}
