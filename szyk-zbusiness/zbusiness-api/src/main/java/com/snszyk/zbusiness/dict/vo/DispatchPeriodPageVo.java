package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DispatchPeriodPageVo", description = "调度周期")
public class DispatchPeriodPageVo extends BaseCrudSlimVo {

	/**
	 * 周期类型，字典
	 */
	@ApiModelProperty("周期类型")
	private String periodType;
	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Integer year;
	/**
	 * 所属组织名称
	 */
	@ApiModelProperty("所属组织名称")
	private String companyName;
}
