package com.snszyk.zbusiness.assess.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "RwManageSelfSaveVo", description = "考核管理")
public class RwManageSelfSaveVo {

	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long id;
	/**
	 * 自评扣分/加分合计
	 */
	@ApiModelProperty(value = "自评扣分/加分合计")
	private BigDecimal selfTotalScore;
	/**
	 * 考核指标
	 */
	@ApiModelProperty("考核指标")
	private List<RwManageCommonVo> list;
}
