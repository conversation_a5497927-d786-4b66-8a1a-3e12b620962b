package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作记录查询对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录查询对象", description = "操作记录查询对象")
public class RsOperationRecordVo extends BaseCrudVo {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 更新开始时间
     */
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startUpdateTime;

    /**
     * 更新结束时间
     */
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endUpdateTime;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    /**
     * 完整组织ID
     */
    @ApiModelProperty(value = "完整组织ID")
    private String fullOrgId;

    /**
     * 完整组织名称
     */
    @ApiModelProperty(value = "完整组织名称")
    private String fullOrgName;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 用户代理
     */
    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 旧数据（JSON格式）
     */
    @ApiModelProperty(value = "旧数据")
    private String oldData;

    /**
     * 新数据（JSON格式）
     */
    @ApiModelProperty(value = "新数据")
    private String newData;

    /**
     * 变更字段（JSON格式）
     */
    @ApiModelProperty(value = "变更字段")
    private String changeFields;
}
