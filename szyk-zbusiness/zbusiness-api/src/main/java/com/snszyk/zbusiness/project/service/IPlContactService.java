/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.PlContactDto;
import com.snszyk.zbusiness.project.vo.PlContactVo;

import java.util.List;

/**
 * 项目库项目联系人表 服务类
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
public interface IPlContactService extends IBaseCrudService<PlContactDto, PlContactVo> {

	PlContactDto getByProjectId(Long projectId);

	List<PlContactDto> listAll();

	int deleteByProjectId(Long projectId);

	/**
	 * 更新联系人
	 * @param projectId

	 * @return
	 */

	boolean updateContact(Long projectId, PlContactVo vo);
}
