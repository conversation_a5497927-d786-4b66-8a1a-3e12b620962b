///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.service;
//
//import com.snszyk.zbusiness.task.dto.TaskFormDto;
//import com.snszyk.zbusiness.task.vo.TaskFormVo;
//import com.snszyk.core.crud.service.IBaseCrudService;
//
//import java.util.List;
//
///**
// * 任务表单 服务类
// *
// * <AUTHOR>
// * @since 2023-04-23
// */
//public interface ITaskFormService extends IBaseCrudService<TaskFormDto, TaskFormVo> {
//
//	/**
//	 * 根据模板删除
//	 * @param id
//	 */
//    void deleteByModelId(Long id);
//
//	List<TaskFormDto> listByModelId(Long modelId);
//
//	void saveBatch(List<TaskFormVo> formVoList);
//
//	void upadteBatch(List<TaskFormVo> formVoList);
//
//	void batchDeleteByIds(List<Long> collect);
//}
