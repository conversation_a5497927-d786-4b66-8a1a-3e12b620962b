package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "RwSchemeTargetCommonVo", description = "考核指标体系")
public class RwSchemeTargetCommonVo extends BaseCrudSlimVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty("指标分类id")
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项")
	private String addScore;
	/**
	 * 分类分值
	 */
	@ApiModelProperty("分类分值")
	private BigDecimal classifyScore;


	/**
	 * 指标分类id
	 */
	@ApiModelProperty("指标分类id")
	private Long schemeClassifyId;
	/**
	 * 评价指标
	 */
	@ApiModelProperty("评价指标")
	private String evaluateTarget;
	/**
	 * 指标解释
	 */
	@ApiModelProperty("指标解释")
	private String targetExplain;
	/**
	 * 考核方式，数据字典
	 */
	@ApiModelProperty("考核方式，数据字典")
	private String rwType;
	/**
	 * 考核方式，数据字典
	 */
	@ApiModelProperty("考核方式名称")
	private String rwTypeName;
	/**
	 * 分值
	 */
	@ApiModelProperty("分值")
	private BigDecimal score;
	/**
	 * 评分方法集合
	 */
	@ApiModelProperty("评分方法集合")
	private List<RwSchemeScoreMethodVo> list;


}
