package com.snszyk.zbusiness.flow.enums;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum StepTypeEnum {

	USER("0", "用户"),
	ROLE("1", "角色"),
	DEPT("2", "部门"),
	POST("3", "岗位"),
	CONTACT("4", "联络人");

	private String code;
	private String message;

	StepTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
