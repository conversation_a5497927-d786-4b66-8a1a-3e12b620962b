/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.person.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 人员基本信息表实体类
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PersonalBaseEditVo", description = "人员基本信息表")
public class PersonalBaseEditVo extends BaseCrudSlimVo {


	/**
	 * 是否部门联系人，0否1是
	 */
	@ApiModelProperty(value = "是否部门联系人，0否1是")
	private String departContact;

	/**
	 * 毕业院校
	 */
	@ApiModelProperty(value = "毕业院校")
	private String university;
	/**
	 * 专业
	 */
	@ApiModelProperty(value = "专业")
	private String subject;
	/**
	 * 职称，数据字典
	 */
	@ApiModelProperty(value = "职称，数据字典")
	private String titles;

	/**
	 * 职级，数据字典
	 */
	@ApiModelProperty(value = "职级，数据字典")
	private String rank;
	/**
	 * 工作类别，数据字典
	 */
	@ApiModelProperty(value = "工作类别，数据字典")
	private String jobCategory;
	/**
	 * 岗位类别，数据字典
	 */
	@ApiModelProperty(value = "岗位类别，数据字典")
	private String postCategory;

	/**
	 * 岗位从业年限
	 */
	@ApiModelProperty(value = "岗位从业年限")
	private Integer postYears;

	/**
	 * 主要技术能力
	 */
	@ApiModelProperty(value = "主要技术能力")
	private String mainTechnicalCapability;

}
