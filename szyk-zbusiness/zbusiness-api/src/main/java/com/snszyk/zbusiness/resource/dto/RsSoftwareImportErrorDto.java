package com.snszyk.zbusiness.resource.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RsSoftwareImportErrorDto {


	/**
	 * 行号
	 */
	@ApiModelProperty(value = "行号")
	private Integer rowIndex;

	/**
	 * 软件名称
	 */
	@ApiModelProperty(value = "软件名称")
	private String name;

	/**
	 * 失败原因
	 */
	@ApiModelProperty(value = "失败原因")
	private String errorMsg;



}
