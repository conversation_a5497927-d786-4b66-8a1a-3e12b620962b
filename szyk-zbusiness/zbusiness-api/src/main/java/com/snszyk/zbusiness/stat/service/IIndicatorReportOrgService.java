/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorReportOrgVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单位得分表 服务类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
public interface IIndicatorReportOrgService extends IBaseCrudService<IndicatorReportOrgDto, IndicatorReportOrgVo> {
	/**
	 * 单位排名分页
	 * @param v
	 * @return
	 */
	IPage<IndicatorReportOrgDto> orgRankPage(IndicatorOrgRankVo v);

	/*
	 * 单位得分平均值
	 */
	BigDecimal orgAvgRank(IndicatorOrgRankVo vo);

	/*单位的历史得分趋势

	 */
	List<IndicatorOrgHistoryRankDto> orgHistoryScore(IndicatorOrgHistoryRankVo v);

	/**单位排名-系统明细
	 *
	 * @param v
	 * @return
	 */
	IPage<IndicatorReportOrgDto> systemDetailList(IndicatorOrgRankVo v);

	boolean saveDataBatch(List<IndicatorReportOrgVo> list);

	boolean removeByDate(int year, int monthValue);

    boolean deleteByMonth(Integer year, Integer month);
}
