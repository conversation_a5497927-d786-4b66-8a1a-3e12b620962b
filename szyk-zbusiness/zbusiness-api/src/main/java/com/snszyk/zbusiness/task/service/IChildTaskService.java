/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.task.dto.ChildTaskDto;
import com.snszyk.zbusiness.task.dto.ChildTaskPageDto;
import com.snszyk.zbusiness.task.vo.ChildTaskVo;
import com.snszyk.zbusiness.task.vo.TaskQueryVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 子任务管理 服务类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface IChildTaskService extends IBaseCrudService<ChildTaskDto, ChildTaskVo> {

	List<ChildTaskDto> saveBatch(List<ChildTaskVo> childTaskList);

	/**
	 * 分页查询
	 * @param taskVo
	 * @return
	 */
    IPage<ChildTaskPageDto> pageList(TaskQueryVo taskVo);

	ChildTaskDto getInfoById(Long taskId);

	/**
	 * 根据任务下发情况id查询子任务
	 * @param issueDtoId
	 * @return
	 */
	ChildTaskDto getChildTaskByIssueId(Long issueDtoId);

	/**
	 * 根据父任务id查询子任务的列表
	 * @param childDispatchTaskId
	 * @return
	 */
	List<ChildTaskDto> listByParentTaskId(Long childDispatchTaskId);

	/**
	 * 根据下发id修改子任务状态
	 *
	 * @param taskIssueIdList
	 * @param code
	 * @return
	 */
	boolean updateStatusByIssueIds(List<Long> taskIssueIdList, String code);

	ChildTaskDto getInfoByIdIgnoreDel(Long taskId);

	/**
	 * 根据issueid查询关连的子任务
	 * @param issueIdList
	 * @return
	 */
	List<ChildTaskDto> listByIssueIds(List<Long> issueIdList);

	/**
	 * 根据付任务id查询子任务list
	 * @param taskId
	 * @return
	 */
	List<ChildTaskDto> getByParentTaskId(Long taskId);

	ChildTaskDto getByBusinessIdAndBusinessType(Long businessId, String businessType);

	/**
	 * 根据issueIds查询子任务列表
	 * @param issueIds
	 * @return
	 */
	List<ChildTaskDto> getChildTaskByIssueIds(List<Long> issueIds);

	boolean updateBatch(List<ChildTaskVo> childTaskVos);

	List<ChildTaskDto> listByBusiness(List<Long> businessIds, String rwSourceType);

	/**
	 * 根据祖籍列表查询
	 * @param taskId
	 * @return
	 */
	List<ChildTaskDto> listByAncestor(Long taskId);

	boolean batchUpdateFileStatus(List<Long> childTaskIds, LocalDateTime fileTime);

	boolean delByIds(List<Long> collect);

	ChildTaskDto getByIssueId(Long issueId);

	void batchDeleteByIds(List<Long> childTaskIds);

    void updateStatusByIds(List<Long> childTaskIds, String code);
}
