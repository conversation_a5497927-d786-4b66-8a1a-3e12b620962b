package com.snszyk.zbusiness.project.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectIntroduceDto;
import com.snszyk.zbusiness.project.dto.ProjectProgressDto;
import com.snszyk.zbusiness.project.vo.ProjectIntroduceVo;

import java.util.List;

/**
 * IProjectIntroduceService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IProjectIntroduceService extends IBaseCrudService<ProjectIntroduceDto, ProjectIntroduceVo> {

	ProjectIntroduceDto getByProjectId(Long projectId);

	int deleteByProjectId(Long projectId);

}
