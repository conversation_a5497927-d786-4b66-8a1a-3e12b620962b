package com.snszyk.zbusiness.resource.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "RsSystemSecurityImportVo", description = "信息系统台账")
public class RsSystemSecurityImportVo {



	/**
	 * 投入运行时间
	 */
	@ExcelProperty(value = "投入运行时间")
	private String operationTime;
	/**
	 * 定级备案时间
	 */
	@ExcelProperty(value = "定级备案时间")
	private String rateFilingTime;
	/**
	 * 等保备案等级
	 */
	@ExcelProperty(value = "等保备案等级")
	private String securityFilingLevel;
	/**
	 * 密码技术使用,0未使用1使用
	 */
	@ExcelProperty(value = "密码技术使用")
	private String encrypUse;
	/**
	 * 密码用途，数据字典
	 */
	@ExcelProperty(value = "密码用途")
	private String encrypPurpose;
	/**
	 * 系统使用的主要密码算法，数据字典
	 */
	@ExcelProperty(value = "系统使用的主要密码算法")
	private String encrypAlgorithm;
	/**
	 * 密码评估情况，数据字典
	 */
	@ExcelProperty(value = "密码评估情况")
	private String encrypEvaluate;
	/**
	 * 密评得分
	 */
	@ExcelProperty(value = "密评得分")
	private BigDecimal encrypEvaluateScore;
	/**
	 * 密评时间
	 */
	@ExcelProperty(value = "密评时间")
	private String encrypEvaluateDate;
	/**
	 * 密评机构名称
	 */
	@ExcelProperty(value = "密评机构名称")
	private String encrypEvaluateOrg;
	/**
	 * 密码应用改造责任处室
	 */
	@ExcelProperty(value = "密码应用改造责任处室")
	private String encrypResponsibleOrg;
	/**
	 * 密码改造完成日期
	 */
	@ExcelProperty(value = "密码改造完成日期")
	private String encrypTransformDate;
	/**
	 * 联系人
	 */
	@ExcelProperty(value = "联系人")
	private String contactName;
	/**
	 * 联系方式
	 */
	@ExcelProperty(value = "联系方式")
	private String contactPhone;

}
