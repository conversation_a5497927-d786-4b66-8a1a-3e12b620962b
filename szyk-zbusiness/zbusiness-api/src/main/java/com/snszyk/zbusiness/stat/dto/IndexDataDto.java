/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 指标数仓实体类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndexDataDto对象", description = "指标数仓")
public class IndexDataDto extends BaseCrudDto {

	/**
	 * 指标id
	 */
	@ApiModelProperty(value = "指标id")
	private Long indexId;
	/**
	 * 对象id
	 */
	@ApiModelProperty(value = "对象id")
	private Long objectId;
	/**
	 * 数据
	 */
	@ApiModelProperty(value = "数据")
	private String indexData;
	/**
	 * 日期
	 */
	@ApiModelProperty(value = "日期")
	private LocalDate indexDate;


}
