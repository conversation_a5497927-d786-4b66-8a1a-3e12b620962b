/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 统谈分签目录明细实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data

@ApiModel(value = "RsUnifiedCatalogDetailCheckVo", description = "统谈分签目录明细")
public class RsUnifiedCatalogDetailCheckVo {

	/**
	 * 统谈分签目录id
	 */
	@ApiModelProperty(value = "明细id")
	private Long id;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;



}
