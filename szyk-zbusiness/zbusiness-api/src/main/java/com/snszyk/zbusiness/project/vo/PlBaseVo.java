/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目库基本信息表实体类
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlBaseVo对象", description = "项目库基本信息表")
@Accessors(chain = true)
public class PlBaseVo extends BaseCrudSlimVo {

	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;

	@ApiModelProperty(value = "批次")
	private String batchNo;
	/**
	 * 批次
	 */
	@ApiModelProperty(value = "批次名")
	private String batchName;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类，字典sc")
	private String specialtyClassification;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类，字典pc")
	private String projectClassification;
	/**
	 * 项目阶段，字段pp
	 */
	@ApiModelProperty(value = "项目阶段，字段pp")
	private String projectPhase;
	/**
	 * 项目阶段
	 */
	@ApiModelProperty(value = "项目阶段")
	private String projectPhaseName;
	/**
	 * 牵头部门id
	 */
	@ApiModelProperty(value = "牵头部门id")
	private Long leadOrgId;
	/**
	 * 牵头部门名称
	 */
	@ApiModelProperty(value = "牵头部门名称")
	private String leadOrgName;
	/**
	 * 合作单位
	 */
	@ApiModelProperty(value = "合作单位")
	private String cooperationUnit;
	/**
	 * 监理单位
	 */
	@ApiModelProperty(value = "监理单位")
	private String supervisionUnit;
	/**
	 * 项目状态，字典project_status
	 */
	@ApiModelProperty(value = "项目状态，字典project_status")
	private String projectStatus;
	/**
	 * 审查状态，字典review_status
	 */
	@ApiModelProperty(value = "审查状态，字典review_status")
	private String reviewStatus;
	/**
	 * 验收状态，字典acceptance_status
	 */
	@ApiModelProperty(value = "验收状态，字典acceptance_status")
	private String acceptanceStatus;
	/**
	 * 项目标签，字典pl
	 */
	@ApiModelProperty(value = "项目标签，字典pl")
	private String projectLabel;
	/**
	 * 项目当前节点
	 */
	@ApiModelProperty(value = "项目当前节点")
	private String projectNode;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "集团审查结论")
	private String corpApproveResult;

	/**
	 * 集团审查意见
	 */
	@ApiModelProperty(value = "集团审查意见")
	private String corpApproveRemark;

	/**
	 * 二级审查意见
	 */
	@ApiModelProperty(value = "二级审查意见")
	private String secApproveRemark;

	@ApiModelProperty(value = "计划资金")
	private BigDecimal planFunds;

	/**
	 * 合同金额
	 */
	@ApiModelProperty(value = "合同金额")
	private BigDecimal contractAmount;

	@ApiModelProperty("创建人")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long createUser;
	@ApiModelProperty("创建部门")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long createDept;


	@ApiModelProperty(value = "部门id")
	private Long deptId;


	@ApiModelProperty(value = "标签")
	private List<String> labelList;

	@ApiModelProperty(value = "用户id")
	private Long userId;


	/**
	 * 建设内容
	 */
	@ApiModelProperty(value = "建设内容")
	private String constructionContent;

	@ApiModelProperty(value = "ids")
	private List<Long> ids;

	// ==================== 查询条件属性 ====================

	/**
	 * 计划招标开始日期
	 */
	@ApiModelProperty(value = "计划招标开始日期")
	private LocalDate startPlanTenderDate;

	/**
	 * 计划招标结束日期
	 */
	@ApiModelProperty(value = "计划招标结束日期")
	private LocalDate endPlanTenderDate;

	/**
	 * 计划开始日期
	 */
	@ApiModelProperty(value = "计划开始日期")
	private LocalDate startPlanDate;

	/**
	 * 计划结束日期
	 */
	@ApiModelProperty(value = "计划结束日期")
	private LocalDate endPlanDate;

	/**
	 * 计划上线开始日期
	 */
	@ApiModelProperty(value = "计划上线开始日期")
	private LocalDate startPlanCompleteDate;

	/**
	 * 计划上线结束日期
	 */
	@ApiModelProperty(value = "计划上线结束日期")
	private LocalDate endPlanCompleteDate;

	/**
	 * 项目现状
	 */
	@ApiModelProperty(value = "项目现状及必要性")
	private String projectIntroduction;

	/**
	 * 立项依据
	 */
	@ApiModelProperty(value = "立项依据")
	private String projectNecessity;

	/**
	 * 设备投入明细
	 */
	@ApiModelProperty(value = "设备投入明细")
	private String equipmentInputDetails;

	/**
	 * 信息化智能化投入明细
	 */
	@ApiModelProperty(value = "信息化智能化投入明细")
	private String imInputDetails;

	/**
	 * 列支渠道
	 */
	@ApiModelProperty(value = "列支渠道")
	private String distributionChannel;

	/**
	 * 预计总投资最小值
	 */
	@ApiModelProperty(value = "预计总投资最小值（万元）")
	private BigDecimal minTotalEstimate;

	/**
	 * 预计总投资最大值
	 */
	@ApiModelProperty(value = "预计总投资最大值（万元）")
	private BigDecimal maxTotalEstimate;

	/**
	 * 本年度投资最小值
	 */
	@ApiModelProperty(value = "本年度投资最小值（万元）")
	private BigDecimal minCurrentInvestment;

	/**
	 * 本年度投资最大值
	 */
	@ApiModelProperty(value = "本年度投资最大值（万元）")
	private BigDecimal maxCurrentInvestment;

	/**
	 * 资金计划最小值
	 */
	@ApiModelProperty(value = "资金计划最小值（万元）")
	private BigDecimal minPlanFunds;

	/**
	 * 资金计划最大值
	 */
	@ApiModelProperty(value = "资金计划最大值（万元）")
	private BigDecimal maxPlanFunds;

}
