/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordExtensionDto;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordExtensionVo;

import java.util.List;

/**
 * 操作记录扩展字段服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface ISysOperationRecordExtensionService extends IBaseCrudService<SysOperationRecordExtensionDto, SysOperationRecordExtensionVo> {

    /**
     * 根据记录ID获取扩展字段列表
     *
     * @param recordId 记录ID
     * @return 扩展字段列表
     */
    List<SysOperationRecordExtensionDto> getByRecordId(Long recordId);

    /**
     * 批量保存扩展字段
     *
     * @param recordId     记录ID
     * @param businessType 业务类型
     * @param fieldChanges 字段变更列表
     */
    void saveBatch(Long recordId, String businessType, List<FieldChangeDto> fieldChanges);

    /**
     * 根据记录ID删除扩展字段
     *
     * @param recordId 记录ID
     */
    void deleteByRecordId(Long recordId);

    /**
     * 转换扩展字段为字段变更DTO
     *
     * @param extensions 扩展字段列表
     * @return 字段变更列表
     */
    List<FieldChangeDto> convertToFieldChanges(List<SysOperationRecordExtensionDto> extensions);
}
