/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 项目库立项信息表实体类
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlInitiationVo对象", description = "项目库立项信息表")
public class PlInitiationVo extends BaseCrudSlimVo {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 计划招标日期
	 */
	@ApiModelProperty(value = "计划招标日期")
	private LocalDate planTenderDate;
	/**
	 * 计划开始日期
	 */
	@ApiModelProperty(value = "计划开始日期")
	private LocalDate planStartDate;
	/**
	 * 计划上线日期
	 */
	@ApiModelProperty(value = "计划上线日期")
	private LocalDate planCompleteDate;
	/**
	 * 项目现状及存在问题
	 */
	@ApiModelProperty(value = "项目现状及存在问题")
	private String projectIntroduction;
	/**
	 * 项目必要性
	 */
	@ApiModelProperty(value = "项目必要性")
	private String projectNecessity;
	/**
	 * 建设内容
	 */
	@ApiModelProperty(value = "建设内容")
	private String constructionContent;
	/**
	 * 设备投入明细
	 */
	@ApiModelProperty(value = "设备投入明细")
	private String equipmentInputDetails;
	/**
	 * 信息化智能化投入明细
	 */
	@ApiModelProperty(value = "信息化智能化投入明细")
	private String imInputDetails;
	/**
	 * 审查意见
	 */
	@ApiModelProperty(value = "审查意见")
	private String reviewComments;


}
