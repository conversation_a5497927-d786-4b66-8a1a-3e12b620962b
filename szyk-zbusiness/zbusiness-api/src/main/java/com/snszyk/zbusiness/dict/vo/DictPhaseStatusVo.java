package com.snszyk.zbusiness.dict.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DictPhaseStatusVo", description = "项目阶段字典表")
public class DictPhaseStatusVo {

	/**
	 * ID
	 */
	@ApiModelProperty(value = "ID")
	private Long id;

	/**
	 * 状态，业务字典enable_type
	 */
	@ApiModelProperty("状态")
	private String dictStatus;
}
