package com.snszyk.zbusiness.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectBaseDto;
import com.snszyk.zbusiness.project.dto.ProjectBaseOaDto;
import com.snszyk.zbusiness.project.dto.ProjectBasePageDto;
import com.snszyk.zbusiness.project.vo.*;
import com.snszyk.zbusiness.stat.dto.BoardCompanyExamineDto;
import com.snszyk.zbusiness.stat.dto.BoardExamineDto;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * IProjectBaseService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IProjectBaseService extends IBaseCrudService<ProjectBaseDto, ProjectBaseVo> {

	boolean handleSupplyTask(List<Long> projectIdList);

	IPage<ProjectBasePageDto> pageList(ProjectBasePageVo vo, Long deptId, Integer level);

	List<ProjectBaseDto> listByPage(ProjectBasePageVo vo,List<Long> deptIdList,Integer unitLevel);

	/**
	 * 项目名的重复校验
	 * @param reviewStatus
	 * @param projectName
	 * @param Id
	 * @param constructionUnitId
	 * @return
	 */
	List<ProjectBasePageDto> listByNoStatus(List<String> reviewStatus, String projectName, Long Id, Long constructionUnitId);

	List<ProjectBasePageDto> listByIds(List<Long> idList);

	List<ProjectBaseDto> listByUnitNoStatus(Long constructionUnitId, String reviewStatus);

	boolean updateByStatus(Long id, String reviewStatus, Date submitTime, String projectNo, String supplementStatus);

	boolean updateByLabel(Long id, String projectLabel);

	ProjectBaseDto update(ProjectBaseVo vo);

    Boolean cancelInstance(Long businessId , Integer Level);

	ProjectBaseDto fetchByIdIgnoreDelete(Long id);



	List<ProjectBaseDto> listByDeptIdList(List<Long> deptIdList);

    Boolean updateApprovalOpinionBatch(ProjectApprovalOpinionNewVo v);

	Boolean intiApprovalOpinionBatch(Long businessId);

	ProjectBaseDto getByDictKey(String specialtyClassification, String projectClassification, String distributionChannel);

	IPage<ProjectBasePageDto> progressPage(ProjectProgressPageNewVo vo);

	Boolean updateFunds(ProjectProgressNewVo vo);

	Boolean updateLevel(Long businessId, Integer level);

    List<ProjectBaseOaDto> queryDataByIdForOa(List<Long> businessId);

    List<ProjectBaseDto> listByProjectName(Long deptId, String projectName
		, Long id, Integer year);

	Boolean handleInstanceLogBatchNo(List<Long> instanceIds, String batchNo, LocalDateTime batchNoTime);

	/**
	 * 更新补充材料的状态
	 * @param projectId
	 * @param supplementStatus
	 * @return
	 */
	boolean updateSupplementStatus(Long projectId, String supplementStatus);

	Boolean initFromInstance(Long businessId, Long instanceId, Map<String, Object> params,Boolean createFlag);

	List<ProjectBaseDto> listByconstructionUnitAndUser(Long constructionUnitId, Long userId, String batchNo);

	/**
	 * 项目的
	 * 只查询提报到集团的总数
	 * @return
	 */
    Integer getReportHeadCount(LocalDateTime startDate, LocalDateTime endDate);

	/**
	 * 预估总投资

	 * @return
	 */
	BigDecimal projectInvestmentCount(LocalDateTime startDate, LocalDateTime endDate);

	/**
	 * 根据列支渠道分类统计项目数量
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<EchartCircleDto> projectInvestment(LocalDateTime startDate, LocalDateTime endDate);

	/**
	 * 驾驶舱项目审查数量
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	BoardExamineDto projectExamine(LocalDateTime startDate, LocalDateTime endDate,Long parentUnitId);

	/**
	 * 驾驶舱项目审查数量分布
	 * @param startDate
	 * @param endDate
	 * @return
	 */
    List<BoardCompanyExamineDto> companyExamine(LocalDateTime startDate, LocalDateTime endDate);

	Boolean updateExpertReview(Long projectId, String jsonString);

	Boolean clearExpertReview(Long businessId);

	boolean updateaCancelFlag(Long instanceId, boolean b);

	List<ProjectBaseDto> listByInstanceIds(List<Long> instanceIds);

	boolean handleProjectNo(List<Long> instanceIds);

	/**
	 * 生成项目编号
	 * @param instanceIds 实例主键ID列表
	 * @param handleExistingProjectNo 是否处理已有编号的项目
	 * @return 处理结果
	 */
	boolean generateProjectNo(List<Long> instanceIds, boolean handleExistingProjectNo);

	/**
	 * 生成待审批项目的编号
	 * @return 处理结果
	 */
	boolean generateWaitingApproveProjectNo();

	ProjectBaseDto detail(Long id);

	List<ProjectBaseDto> listByCompanyAndUser(Long companyId, Long userId, String batchNo);

	Boolean handleCreateBatchNo(List<Long> instanceIds);

	Integer getMaxBatchNo(Integer year);

	List<ProjectBaseDto> listByBatchNo(Integer year, String batchNo);

	void handleBatchNoAdd(Long instanceIds);

	Boolean updateBatchNo(Long id, Integer batchNo, String batchName);

	/**
	 * 获取reviewStatus=1的记录中最新的年份和对应的最新批次号
	 * @return Map<String, Object> 包含latestYear和latestBatchNo
	 */
	Map<String, Object> getLatestYearAndBatchNo();


	void clearProjectNo(Long id);
}
