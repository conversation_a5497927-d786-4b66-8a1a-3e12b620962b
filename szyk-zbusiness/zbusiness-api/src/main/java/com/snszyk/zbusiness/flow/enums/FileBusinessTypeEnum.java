package com.snszyk.zbusiness.flow.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Optional;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum FileBusinessTypeEnum {


	TEMP("TEMP", "临时"),
	LOG("LOG", "日志"),

	;

	private String code;
	private String message;

	FileBusinessTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

}
