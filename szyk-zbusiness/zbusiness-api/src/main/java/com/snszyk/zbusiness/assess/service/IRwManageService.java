package com.snszyk.zbusiness.assess.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.assess.dto.*;
import com.snszyk.zbusiness.assess.vo.*;
import com.snszyk.zbusiness.stat.dto.BoardRwRankDto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * IRwManageService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRwManageService extends IBaseCrudService<RwManageDto, RwManageVo> {

	IPage<RwManageScorePageDto> scorePageList(RwManageScorePageVo vo);

	IPage<RwManageSelfPageDto> selfPageList(RwManageSelfPageVo vo, List<Long> assessedOrgIdList, String rwStatus, String rwType);

	IPage<RwManageProcessPageDto> processPageList(RwManageProcessPageVo vo);

	List<RwManageDto> listByPeriodDetailId(List<Long> rwPeriodDetailIdList);

	List<RwManageDto> listByCheck(Long schemeId, Long initiateOrgId, Long assessedOrgId, Long rwPeriodDetailId, String rwType);

	List<RwManageDto> listByResult(Long schemeId, Long initiateOrgId, Long rwPeriodDetailId, Long assessedOrgId);

	List<RwManageDto> listBySchemeId(Long schemeId);

	List<RwManageDto> listByStatus(String rwType, String rwStatus);

	boolean updateBySelfTotalScore(Long id, BigDecimal selfTotalScore, String rwStatus, Date selfAssessmentComplete,Long selfUserId);

	boolean updateByRwTotalScore(Long id, BigDecimal totalScore, BigDecimal rwTotalScore,
								 String rwStatus, String remark, String sceneMembers,
								 String teamMembers, LocalDate rwDate);

	boolean updateByStatus(Long id, String rwStatus);

	RwManageDto update(RwManageVo vo);

	List<RwManageDto> getByProject(Long id, String code);

	RwManageDto getDetailById(Long id);

	void sendMessge(String rwMangeId);

	/**
	 * 查询扣分项,用于发起督办
	 *
	 * @param rwId
	 * @param evaluateTarget
	 * @param scoreMethod
	 * @return
	 */
	List<RwManageCommonDto> listDeductMethod(String rwId, String evaluateTarget, String scoreMethod);

	/**
	 * list 根据发起组织
	 *
	 * @param unitId
	 * @return
	 */
	List<RwManageDto> listByInitiateOrgId(Long unitId);

	/**
	 * 专项检查-材料锁定
	 *
	 * @param vo
	 * @return
	 */

	Boolean lockFile(RwBaseObjectVo vo);

	/**
	 * 专项检查-取消材料锁定
	 * @param vo
	 * @return
	 */
	Boolean unLockFile(RwBaseObjectVo vo);

	/**
	 * 归档
	 * @param vo
	 * @return
	 */
	Boolean placeOnFile(RwBaseObjectVo vo);
	/**
	 * 被考核记录的分页查询
	 * @param vo
	 * @return
	 */
	IPage<RwManageScorePageDto> assessedPage(RwManageScorePageVo vo);

	List<RwManageDto> listByParam(RwManageVo rwManageVo);

	/**
	 * 驾驶舱考核排名默认参数
	 * @return
	 */
    RwResultQueryParamDto rankDefaultParam();

	/**
	 * 驾驶舱考核排名
	 * @param sort
	 * @param rwPeriodDetailId
	 * @return
	 */
    List<BoardRwRankDto> rwRank(Integer sort, Long rwPeriodDetailId);

	List<RwManageDto> listBy(String sourceType, List<Long> taskIssueIdList);

	/**
	 * 考核结果的分页查询
	 * @param vo
	 * @return
	 */
	IPage<RwResultDto> resultPage(RwResultListVo vo);
}
