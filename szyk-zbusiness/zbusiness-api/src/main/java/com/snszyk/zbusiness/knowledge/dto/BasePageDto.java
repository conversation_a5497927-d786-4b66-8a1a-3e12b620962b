/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识库实体类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BaseDto对象", description = "知识库")
public class BasePageDto extends BaseCrudDto {

	/**
	 * 收集方式
	 */
	@ApiModelProperty(value = "收集方式")
	private String collectType;
	/**
	 * 收集方式
	 */
	@ApiModelProperty(value = "收集方式名称")
	private String collectTypeName;
	/**
	 * 文档名称
	 */
	@ApiModelProperty(value = "文档名称")
	private String fileName;
	/**
	 * 分类id
	 */
	@ApiModelProperty(value = "分类id")
	private Long classifyId;
	/**
	 * 分类id
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;
	/**
	 * 来源id
	 */
	@ApiModelProperty(value = "来源id")
	private Long sourceId;
	/**
	 * 来源单号
	 */
	@ApiModelProperty(value = "来源单号")
	private String sourceNo;
	/**
	 * 上传人id
	 */
	@ApiModelProperty(value = "上传人id")
	private Long uploaderId;
	/**
	 * 上传人姓名
	 */
	@ApiModelProperty(value = "上传人姓名")
	private String uploaderName;
	/**
	 * 上传日期
	 */
	@ApiModelProperty(value = "上传日期")
	private LocalDateTime uploadDateTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 权限类型
	 */
	@ApiModelProperty(value = "权限类型")
	private String permissionType;
	/**
	 * 权限类型
	 */
	@ApiModelProperty(value = "权限类型名称")
	private String permissionTypeName;


	@ApiModelProperty(value = "检索结果")
	private String searchResult;

	@ApiModelProperty(value = "收藏标识：1已收藏0未收藏")
	private Integer collectionFlag = 0;

	@ApiModelProperty(value = "编辑标识：true可编辑false不可编辑")
	private Boolean editFlag = false;

	@ApiModelProperty(value = "查看权限：true可查看false不可查看")
	private Boolean checkFlag = false;
	/**
	 * 点击次数
	 */
	@ApiModelProperty("点击次数")
	private Long clickCount;
	/**
	 * 下载次数
	 */
	@ApiModelProperty("下载次数")
	private Long downloadCount;
	/**
	 * 阅读次数
	 */
	@ApiModelProperty("阅读次数")
	private Long readCount;

}
