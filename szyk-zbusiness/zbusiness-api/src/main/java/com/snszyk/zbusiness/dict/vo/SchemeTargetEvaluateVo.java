package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "SchemeTargetEvaluateVo", description = "体系指标评价")
public class SchemeTargetEvaluateVo extends BaseCrudSlimVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty("指标分类id")
	private Long schemeClassifyId;
	/**
	 * 评价指标
	 */
	@ApiModelProperty("评价指标")
	private String evaluateTarget;
	/**
	 * 指标解释
	 */
	@ApiModelProperty("指标解释")
	private String targetExplain;
	/**
	 * 分值
	 */
	@ApiModelProperty("分值")
	private BigDecimal score;
}
