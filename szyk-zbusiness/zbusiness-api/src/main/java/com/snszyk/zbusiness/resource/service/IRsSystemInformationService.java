package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsSystemInformationDto;
import com.snszyk.zbusiness.resource.vo.RsSystemInformationVo;

import java.util.List;

/**
 * IRsSystemInformationService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRsSystemInformationService extends IBaseCrudService<RsSystemInformationDto, RsSystemInformationVo> {

	List<RsSystemInformationDto> listByOperationSupplyId(Long operationSupplyId);

	List<RsSystemInformationDto> listBySystemId(List<Long> systemIdList);

	RsSystemInformationDto getBySystemId(Long systemId);

	int deleteBySystemId(Long systemId);

}
