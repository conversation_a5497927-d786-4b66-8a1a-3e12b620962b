///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.dto;
//
//import com.snszyk.core.crud.dto.BaseCrudDto;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.List;
//
///**
// * 任务模板实体类
// *
// * <AUTHOR>
// * @since 2023-04-23
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ApiModel(value = "TaskModelDto对象", description = "任务模板")
//public class TaskModelDto extends BaseCrudDto {
//
//	/**
//	 * 模板名称
//	 */
//	@ApiModelProperty(value = "模板名称")
//	private String modelName;
//	/**
//	 * 所属组织id
//	 */
//	@ApiModelProperty(value = "所属组织id")
//	private Long companyId;
//	/**
//	 * 所属组织名称
//	 */
//	@ApiModelProperty(value = "所属组织名称")
//	private String companyName;
//	/**
//	 * 状态
//	 */
//	@ApiModelProperty(value = "状态")
//	private String modelStatus;
//	@ApiModelProperty(value = "创建人姓名")
//	private String createUserName;
//
//	@ApiModelProperty(value = "表单list")
//	private List<TaskFormDto> formVoList;
//
//
//}
