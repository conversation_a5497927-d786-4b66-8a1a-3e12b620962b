/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 互联网资源台账实体类
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsInternetVo对象", description = "互联网资源台账")
public class RsInternetVo extends BaseCrudVo {

	/**
	 * 所属组织id全路径
	 */
	@ApiModelProperty(value = "所属组织id")
	private Long orgId;
	/**
	 * 所属组织名称全路径
	 */
	@ApiModelProperty(value = "所属组织名称")
	private String orgName;

	/**
	 * 所属组织id全路径
	 */
	@ApiModelProperty(value = "所属组织id全路径")
	private String fullOrgId;
	/**
	 * 所属组织名称全路径
	 */
	@ApiModelProperty(value = "所属组织名称全路径")
	private String fullOrgName;
	/**
	 * 单位级别
	 */
	@ApiModelProperty(value = "单位级别")
	private Integer orgLevel;
	/**
	 * 资源类型,数据字典
	 */
	@ApiModelProperty(value = "资源类型,数据字典")
	private String internetType;
	/**
	 * 应用类型，数据字典
	 */
	@ApiModelProperty(value = "应用类型，数据字典")
	private String applicationType;
	/**
	 * 小程序名称
	 */
	@ApiModelProperty(value = "小程序名称")
	private String appName;
	/**
	 * 公有云所需资源
	 */
	@ApiModelProperty(value = "公有云所需资源")
	private String publicCloudResource;
	/**
	 * 互联网地址
	 */
	@ApiModelProperty(value = "互联网地址")
	private String internetAddress;
	/**
	 * 内网地址
	 */
	@ApiModelProperty(value = "内网地址")
	private String networkAddress;
	/**
	 * 关联类型
	 */
	@ApiModelProperty(value = "关联类型")
	private String associationType;
	/**
	 * 关联id
	 */
	@ApiModelProperty(value = "关联id")
	private Long associationId;
	/**
	 * 关联编号
	 */
	@ApiModelProperty(value = "关联编号")
	private String associationNo;
	/**
	 * 关联名称
	 */
	@ApiModelProperty(value = "关联名称")
	private String associationName;
	/**
	 * 联系人
	 */
	@ApiModelProperty(value = "联系人")
	private String contactPerson;
	/**
	 * 电话号码
	 */
	@ApiModelProperty(value = "电话号码")
	private String contactPhone;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 资源状态 0 在用 1停用
	 */
	@ApiModelProperty(value = "资源状态 0 在用 1停用")
	private String resourceStatus;
	/**
	 * 接口地址文件
	 */
	@ApiModelProperty(value = "接口地址文件")
	private List<Long> interfaceAddressFileList;

	/**
	 * 渗透报告文件
	 */
	@ApiModelProperty(value = "渗透报告文件")
	private List<Long> penetrationReportFileList;



	/**
	 * 是否公有云部署
	 */
	@ApiModelProperty(value = "是否公有云部署")
	private Boolean isPublicCloud;

	/**
	 * 系统名称
	 */
	@ApiModelProperty(value = "系统名称")
	private String systemName;



	/**
	 * 公有云供应商
	 */
	@ApiModelProperty(value = "公有云供应商")
	private String publicCloudSupplier;

	/**
	 * 等保级别
	 */
	@ApiModelProperty(value = "等保级别")
	private String securityLevel;

	/**
	 * 域名或url
	 */
	@ApiModelProperty(value = "渗透报告文件")
	private String domainName;
	/*
	 *授权组织
	 */
	@ApiModelProperty(value="授权组织")
	private String authOrg;

}
