package com.snszyk.zbusiness.project.dto;

import com.snszyk.zbusiness.dict.dto.DictLabelDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlPageLabelAssociatedProjectDto", description = "标签分页列表关联项目信息")
public class PlPageLabelAssociatedProjectDto extends DictLabelDto {

	@ApiModelProperty(value = "是否关联项目")
	private Integer isAssociatedProject = 0;
}
