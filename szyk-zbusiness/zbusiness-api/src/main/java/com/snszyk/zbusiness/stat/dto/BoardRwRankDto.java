/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 驾驶舱考核排名
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BoardRwRankDto", description = "驾驶舱考核排名")
public class BoardRwRankDto {


	@ApiModelProperty(value = "二级单位名称")
	private String companyName;

	@ApiModelProperty(value = "得分")
	private BigDecimal score;

	@ApiModelProperty(value = "扣分")
	private BigDecimal deduct;

	@ApiModelProperty(value = "体系的总分")
	private BigDecimal schemeTotalScore;

	@ApiModelProperty(value = "体系id")
	private Long schemeId;
}
