package com.snszyk.zbusiness.flow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @atuthor
 * @date 2023/3/28
 * @apiNote
 */
@Data
@ApiModel(value = "NextProcessVo")
@Accessors(chain = true)
public class NextProcessVo {

	@ApiModelProperty(value = "关联业务类型")
	private String businessType;

	@ApiModelProperty(value = "审批流id")
	private List<Long> instanceIds;


}
