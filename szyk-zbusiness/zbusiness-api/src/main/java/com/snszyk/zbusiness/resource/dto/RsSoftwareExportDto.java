package com.snszyk.zbusiness.resource.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class RsSoftwareExportDto {

	/**
	 * 单位名称
	 */
	@ExcelProperty(value = "单位名称")
	private String fullOrgName;
	/**
	 * 软件编号
	 */
	@ExcelProperty(value = "软件编号")
	private String softwareNo;
	/**
	 * 软件分类，数据字典
	 */
	@ExcelProperty(value = "软件分类")
	private String softwareClassifyName;
	/**
	 * 软件名称
	 */
	@ExcelProperty(value = "软件名称")
	private String softwareName;
	/**
	 * 软件版本
	 */
	@ExcelProperty(value = "软件版本")
	private String softwareVersion;
	/**
	 * 软件类型，数据字典
	 */
	@ExcelProperty(value = "软件类型")
	private String softwareTypeName;
	/**
	 * 软件状态
	 */
	@ExcelProperty(value = "状态")
	private String softwareStatusName;
	/**
	 * 采购时间
	 */
	@ExcelProperty(value = "采购时间")
	private String purchaseTime;
	/**
	 * 采购金额
	 */
	@ExcelProperty(value = "采购金额(元)")
	private BigDecimal purchaseAmount;
	/**
	 * 供应商名称
	 */
	@ExcelProperty(value = "供应商名称")
	private String supplierName;
	/**
	 * 供应商编号
	 */
	@ExcelProperty(value = "供应商编号")
	private String supplierNo;
	/**
	 * 许可类型，数据字典
	 */
	@ExcelProperty(value = "许可类型")
	private String licenseTypeName;
	/**
	 * 许可期限
	 */
	@ExcelProperty(value = "许可期限")
	private String licenseTime;
	/**
	 * 序列号
	 */
	@ExcelProperty(value = "序列号")
	private String serialNumber;
	/**
	 * 许可数量
	 */
	@ExcelProperty(value = "许可数量")
	private Integer licenseQuantity;
	/**
	 * 负责人姓名
	 */
	@ExcelProperty(value = "负责人")
	private String headPersonName;
	/**
	 * 负责人联系方式
	 */
	@ExcelProperty(value = "联系方式")
	private String headPersonTel;
	/**
	 * 创建日期
	 */
	@ExcelProperty(value = "创建日期")
	private String createTime;
	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注")
	private String softwareRemark;

}
