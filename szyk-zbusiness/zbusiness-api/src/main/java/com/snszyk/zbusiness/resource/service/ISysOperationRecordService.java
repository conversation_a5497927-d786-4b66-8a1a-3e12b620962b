/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.FieldChangeDto;
import com.snszyk.zbusiness.resource.dto.SysOperationRecordDto;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.SysOperationRecordVo;

import java.util.List;

/**
 * 操作记录服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface ISysOperationRecordService extends IBaseCrudService<SysOperationRecordDto, SysOperationRecordVo> {

    /**
     * 分页查询操作记录
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<SysOperationRecordDto> pageList(SysOperationRecordPageVo vo);

    /**
     * 异步保存操作记录
     *
     * @param vo 操作记录
     */
    void saveAsync(SysOperationRecordVo vo);

    /**
     * 根据业务类型和业务ID获取业务数据
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 业务数据
     */
    Object getBusinessData(String businessType, Long businessId);

    /**
     * 根据记录ID获取变更详情
     *
     * @param recordId 记录ID
     * @return 变更详情
     */
    SysOperationRecordDto getDetailWithChanges(Long recordId);

    /**
     * 比较两个对象的字段变更
     *
     * @param businessType 业务类型
     * @param oldData      旧数据
     * @param newData      新数据
     * @return 字段变更列表
     */
    List<FieldChangeDto> compareFields(String businessType, Object oldData, Object newData);

    /**
     * 保存字段变更详情
     *
     * @param recordId     记录ID
     * @param businessType 业务类型
     * @param fieldChanges 字段变更列表
     */
    void saveFieldChanges(Long recordId, String businessType, List<FieldChangeDto> fieldChanges);

    /**
     * 根据记录ID获取字段变更详情
     *
     * @param recordId 记录ID
     * @return 字段变更列表
     */
    List<FieldChangeDto> getFieldChangesByRecordId(Long recordId);
}
