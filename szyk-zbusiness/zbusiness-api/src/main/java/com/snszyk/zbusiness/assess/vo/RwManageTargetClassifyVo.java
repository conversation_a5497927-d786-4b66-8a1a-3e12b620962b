package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RwManageTargetClassifyVo", description = "考核管理")
public class RwManageTargetClassifyVo extends BaseCrudSlimVo {

	/**
	 * 考核管理id
	 */
	@ApiModelProperty(value = "考核管理id")
	private Long rwId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty(value = "指标分类id")
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty(value = "指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty(value = "是否加分项：0否1是")
	private String addScore;
}
