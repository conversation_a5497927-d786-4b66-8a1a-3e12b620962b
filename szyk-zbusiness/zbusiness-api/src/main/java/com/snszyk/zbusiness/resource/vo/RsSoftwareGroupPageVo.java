package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RsSoftwareGroupPageVo", description = "软件正版化小组")
public class RsSoftwareGroupPageVo extends BaseCrudSlimVo {


	/**
	 * 单位范围
	 */
	@ApiModelProperty(value = "单位范围 1本单位的 2 本单位及权属单位的")
	private Integer unitRange=1;

	@ApiModelProperty(value = "所属组织的id")
	private Long orgId;


	@ApiModelProperty(value = "状态")
	private String groupStatus;

}
