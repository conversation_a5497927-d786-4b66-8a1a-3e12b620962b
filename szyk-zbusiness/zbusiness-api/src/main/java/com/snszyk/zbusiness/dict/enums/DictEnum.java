package com.snszyk.zbusiness.dict.enums;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum DictEnum {

	/**
	 * 专业分类
	 */
	SC("sc", "专业分类"),

	/**
	 * 列支渠道
	 */
	DC("dc", "列支渠道"),

	/**
	 * 项目阶段
	 */
	PP("pp", "项目阶段"),

	/**
	 * 项目分类
	 */
	PC("pc", "项目分类"),

	/**
	 * 项目标签
	 */
	PL("pl", "项目标签"),

	/**
	 * 调度周期
	 */
	SHC("shc", "调度周期"),

	/**
	 * 推进进度
	 */
	PR("pr", "推进进度"),

	/**
	 * 考核周期
	 */
	AC("ac", "考核周期");

	private String code;
	private String message;

	DictEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
