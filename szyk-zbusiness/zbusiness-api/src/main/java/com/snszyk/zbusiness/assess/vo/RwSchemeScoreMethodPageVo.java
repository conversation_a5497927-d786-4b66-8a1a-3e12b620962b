package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "RwSchemeScoreMethodPageVo", description = "考核指标体系")
public class RwSchemeScoreMethodPageVo extends BaseCrudSlimVo {

	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 评价指标
	 */
	@ApiModelProperty("评价指标")
	private String evaluateTarget;
	/**
	 * 评分方法
	 */
	@ApiModelProperty("评分方法")
	private String scoreMethod;
	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;

	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;

	/**
	 * 被考核机构
	 */
	@ApiModelProperty(value = "被考核机构id")
	private Long assessedOrgId;

	@ApiModelProperty(value = "剩余分值不为0 (0否 1是)")
	private Integer leftScoreNoZero;

	private List<Long> schemeEvaluateIdList;

}
