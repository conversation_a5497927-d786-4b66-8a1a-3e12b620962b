package com.snszyk.zbusiness.flow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @atuthor
 * @date 2023/3/27
 * @apiNote
 */
@Data
@ApiModel(value = "StepUserDto对象")
@Accessors(chain = true)
public class StepChooseDto {

	/**
	 * 工作流名称
	 */
	@ApiModelProperty(value = "审批流id")
	private Long flowId;

	@ApiModelProperty(value = "是否提交人自选：0否1是")
	private Integer submitChoose;

	@ApiModelProperty(value = "下个节点身份：1分管领导")
	private Integer userType = 0;


	@ApiModelProperty(value = "节点审核人列表")
	private List<StepUserDto> users;

}
