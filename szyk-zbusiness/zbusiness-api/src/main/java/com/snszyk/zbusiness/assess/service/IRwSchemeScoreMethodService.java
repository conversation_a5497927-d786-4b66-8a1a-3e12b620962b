package com.snszyk.zbusiness.assess.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.assess.dto.RwSchemeScoreMethodDto;
import com.snszyk.zbusiness.assess.dto.RwSchemeScoreMethodPageDto;
import com.snszyk.zbusiness.assess.vo.RwSchemeScoreMethodPageVo;
import com.snszyk.zbusiness.assess.vo.RwSchemeScoreMethodVo;

import java.util.List;

/**
 * IRwSchemeScoreMethodService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRwSchemeScoreMethodService extends IBaseCrudService<RwSchemeScoreMethodDto, RwSchemeScoreMethodVo> {

	List<RwSchemeScoreMethodPageDto> listData(RwSchemeScoreMethodPageVo vo, List<Long> schemeEvaluateIdList);

	List<RwSchemeScoreMethodDto> listBySchemeEvaluateId(Long rwSchemeEvaluateId);

	List<RwSchemeScoreMethodDto> listBySchemeId(Long rwSchemeId);

	List<RwSchemeScoreMethodDto> listAll();

	int deleteBySchemeId(Long rwSchemeId);

	boolean removeListByIds(List<Long> needRemoveMethodIdList);
}
