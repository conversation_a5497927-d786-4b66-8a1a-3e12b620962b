package com.snszyk.zbusiness.project.enums;

/**
 * 记录类型
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum OperateEnum {

	/**
	 * 下发
	 */
	ISSUE("1", "下发"),

	/**
	 * 提报
	 */
	REPORT("2", "提报"),

	/**
	 * 退回
	 */
	RETURN("3", "退回"),
	;

	private String code;
	private String message;

	OperateEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
