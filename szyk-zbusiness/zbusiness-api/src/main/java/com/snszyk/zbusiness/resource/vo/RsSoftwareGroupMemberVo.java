package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RsSoftwareGroupMemberVo", description = "软件正版化小组")
public class RsSoftwareGroupMemberVo extends BaseCrudSlimVo {

	/**
	 * 小组id
	 */
	@ApiModelProperty(value = "小组id")
	private Long groupId;
	/**
	 * 分工，字典数据
	 */
	@ApiModelProperty(value = "分工")
	private String division;
	/**
	 * 成员姓名
	 */
	@ApiModelProperty(value = "成员姓名")
	private String memberName;
	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String departName;
	/**
	 * 职务
	 */
	@ApiModelProperty(value = "职务")
	private String portfolio;
	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String phoneNo;
	/**
	 * 传真
	 */
	@ApiModelProperty(value = "传真")
	private String faxNo;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
