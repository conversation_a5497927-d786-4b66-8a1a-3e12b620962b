/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.stat.dto.DateRangeDto;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;
import com.snszyk.zbusiness.stat.dto.IndexDataDto;
import com.snszyk.zbusiness.stat.enums.IndexLibraryEnum;
import com.snszyk.zbusiness.stat.vo.IndexDataVo;

import java.util.List;

/**
 * 指标数仓 服务类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public interface IIndexDataService extends IBaseCrudService<IndexDataDto, IndexDataVo> {

    boolean saveBatchData(List<IndexDataVo> addList);

	/**
	 * 统一 率计算
	 *
	 * @param sort
	 * @param limit
	 * @param libraryOne
	 * @param libraryTwo
	 * @return
	 */
	List<EchartCircleDto> commonDataRate(DateRangeDto dateRangeDto, Integer sort,
										 Integer limit, IndexLibraryEnum libraryOne, IndexLibraryEnum libraryTwo);



	/**
	 * 根据时间范围和指标统计数量
	 * @param libraryOne
	 * @param dateRange
	 * @return
	 */
	List<EchartCircleDto> commonCountData(IndexLibraryEnum libraryOne, DateRangeDto dateRange);
}
