package com.snszyk.zbusiness.assess.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.assess.dto.RwSchemeTargetClassifyDto;
import com.snszyk.zbusiness.assess.vo.RwSchemeTargetClassifyVo;

import java.util.List;

/**
 * IRwSchemeTargetClassifyService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRwSchemeTargetClassifyService extends IBaseCrudService<RwSchemeTargetClassifyDto, RwSchemeTargetClassifyVo> {

	List<RwSchemeTargetClassifyDto> listBySchemeId(Long rwSchemeId);

	List<RwSchemeTargetClassifyDto> listBySchemeId(Long rwSchemeId, List<Long> idList);

	List<RwSchemeTargetClassifyDto> listByClassifyId(Long classifyId);

	List<RwSchemeTargetClassifyDto> listByClassifyName(String classifyName);

	List<RwSchemeTargetClassifyDto> listAll();

	boolean updateByStatus(Long classifyId, String classifyName, String addScore);

	int deleteBySchemeId(Long rwSchemeId);

	boolean removeListByIds(List<Long> needRemoveClassifyIdList);
}
