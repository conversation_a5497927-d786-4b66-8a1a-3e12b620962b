package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "RsSoftwareGroupVo", description = "软件正版化小组")
public class RsSoftwareGroupVo extends BaseCrudSlimVo {


	/**
	 * 所属组织id
	 */

	@ApiModelProperty(value="所属组织id")
	private Long orgId;
	/**
	 * 所属组织全路径id
	 */

	@ApiModelProperty(value="所属组织全路径id")
	private String fullOrgId;
	/**
	 * 所属组织名称
	 */

	@ApiModelProperty(value="所属组织名称")
	private String orgName;
	/**
	 * 所属组织全路径名称
	 */

	@ApiModelProperty(value="所属组织全路径名称")
	private String fullOrgName;

	/**
	 * 填报人id
	 */
	@ApiModelProperty(value = "填报人id")
	private Long formPersonId;
	/**
	 * 填报人姓名
	 */
	@ApiModelProperty(value = "填报人姓名")
	private String formPersonName;
	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String phoneNo;
	/**
	 * 填报日期
	 */
	@ApiModelProperty(value = "填报日期")
	private Date formDate;
	/**
	 * 小组状态
	 */
	@ApiModelProperty(value = "小组状态")
	private String groupStatus;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 小组明细
	 */
	@ApiModelProperty(value = "小组明细")
	private List<RsSoftwareGroupMemberVo> list;

}
