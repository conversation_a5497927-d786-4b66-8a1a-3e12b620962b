/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据中心机房关联设备明细实体类
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsServerroomEqDto对象", description = "数据中心机房关联设备明细")
public class RsServerroomEqDto extends BaseCrudDto {

	/**
	 * 机房id
	 */
	@ApiModelProperty(value = "机房id")
	private Long roomId;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String equipmentType;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;


}
