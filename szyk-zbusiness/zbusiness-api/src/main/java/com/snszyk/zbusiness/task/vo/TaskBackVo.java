/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.snszyk.common.utils.LongFormatSerialize;
import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 任务退回
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "任务退回", description = "任务退回")
public class TaskBackVo extends BaseCrudSlimVo {

	@ApiModelProperty("任务执行传转发任务的id,任务下发转任务的id ")
	@JsonSerialize(using = LongFormatSerialize.class)
	@NotNull
	private Long taskId;


	@NotEmpty(message = "列表不能为空")
	@ApiModelProperty(value = "任务下发情况列表")
	private List<TaskIssueBackVo> taskIssueBackList;



}
