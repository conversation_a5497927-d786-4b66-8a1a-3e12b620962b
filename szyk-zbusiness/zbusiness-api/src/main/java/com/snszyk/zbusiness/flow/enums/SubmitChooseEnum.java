package com.snszyk.zbusiness.flow.enums;

import io.swagger.models.auth.In;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum SubmitChooseEnum {

	AUTO(0, "否"),
	HAND(1, "是"),
	;

	private Integer code;
	private String message;

	SubmitChooseEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
