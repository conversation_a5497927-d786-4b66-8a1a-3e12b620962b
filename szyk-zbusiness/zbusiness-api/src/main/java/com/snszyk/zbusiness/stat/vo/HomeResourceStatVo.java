/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 首页资源管理统计
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@ApiModel(value = "HomeResourceStatVo", description = "首页资源管理统计")
public class HomeResourceStatVo {


	@ApiModelProperty(value = "单位范围 1本单位的 2 本单位及权属单位的")
	private Integer unitRange;

	@ApiModelProperty(value = "机构id",hidden = true)
	private  Long orgId;

}
