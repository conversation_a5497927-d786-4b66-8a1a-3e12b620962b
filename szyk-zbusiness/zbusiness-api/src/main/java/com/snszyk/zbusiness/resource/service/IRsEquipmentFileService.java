package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsEquipmentFileDto;
import com.snszyk.zbusiness.resource.dto.RsServerRoomFileDto;
import com.snszyk.zbusiness.resource.vo.RsEquipmentFileVo;

import java.util.List;

/**
 * IRsEquipmentFileService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRsEquipmentFileService extends IBaseCrudService<RsEquipmentFileDto, RsEquipmentFileVo> {

	List<RsEquipmentFileDto> listByEquipmentId(Long equipmentId);

	int deleteByEquipmentId(Long equipmentId);

}
