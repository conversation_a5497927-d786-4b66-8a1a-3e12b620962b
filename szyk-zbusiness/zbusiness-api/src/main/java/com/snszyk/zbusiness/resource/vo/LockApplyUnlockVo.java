package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-12-21-18:06
 */
@Data
@ApiModel(value = "申请解锁对象", description = "申请解锁对象")
public class LockApplyUnlockVo {
	/**
	 * 组织机构id
	 */
	@NotNull
	@ApiModelProperty(value = "组织机构idList")
	private Long orgId;
	/**
	 * 组织机构名称
	 */
	@NotBlank
	@ApiModelProperty("组织机构名称")
	private String orgName;
	/**
	 * 申请原因
	 */
	@ApiModelProperty("申请原因")
	private String applyReason;

	/**
	 * 申请解锁相关信息
	 */
	@ApiModelProperty("申请解锁相关信息（仅当锁定状态为“申请解锁状态”有值）")
	private List<LockApplyVo> lockApplyList;
}
