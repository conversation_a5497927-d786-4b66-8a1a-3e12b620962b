package com.snszyk.zbusiness.person.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.zbusiness.flow.enums.ApproveStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum JobStatusEnum {
	/**
	 * 在职
	 */
	ON_JOB("0", "离职"),
	/**
	 * 离职
	 */
	LEAVE_JOB("1", "离岗"),
	TUIXIU("2", "退休"),
	ZAIGANG("3", "在岗"),
	SIWANG("4", "死亡"),
	;
	@EnumValue
	String dictKey;

	String dictValue;


	public static JobStatusEnum getByCode(String dictKey) {
		Optional<JobStatusEnum> result = Arrays.stream(values()) // values() 可以获取当前枚举类所有枚举常量
			.filter(t -> t.getDictKey().equals(dictKey.trim()) ) // 判断相等的条件
			.findFirst();
		if (result.isPresent()) {
			return result.get();
		} else {
			throw new SecurityException(StrUtil.format("No  matches type value {}", dictKey)); // NOSONAR
		}
	}
	/*
		*/
/**
	 * 用于保存所有的枚举值
	 */	/*


	private static final Map<String, JobStatusEnum> RESOURCE_MAP = Stream

		.of(JobStatusEnum.values())

		.collect(Collectors.toMap(JobStatusEnum::getDictKey, Function.identity()));

		*/
/**
	 * 枚举反序列话调用该方法 (必须修饰static方法)
	 *
	 * @param jsonNode jsonNode
	 * @return GenderEnum
	 */	/*


	@JsonCreator
	public static JobStatusEnum des(final JsonNode jsonNode) {
		return Optional.ofNullable(RESOURCE_MAP.get(jsonNode.get("dictKey").asText()))
			.orElseThrow(() -> new IllegalArgumentException(jsonNode.get("dictKey").asText()));
	}
	*/

}
