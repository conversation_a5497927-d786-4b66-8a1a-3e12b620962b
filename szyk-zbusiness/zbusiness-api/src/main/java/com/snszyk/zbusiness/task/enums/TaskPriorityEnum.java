package com.snszyk.zbusiness.task.enums;

/**
 *
 * 工作管理任务优先级
 * <AUTHOR>
 * @create 2023/4/24
 */
public enum TaskPriorityEnum {


	LEVEL_ONE("1", "最低"),
	LEVEL_TWO("2", "较低"),
	LEVEL_THREE("3", "普通"),
	LEVEL_FOUR("4", "较高"),
	LEVEL_FIVE("5", "最高"),

	;

	private String code;
	private String message;

	TaskPriorityEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
