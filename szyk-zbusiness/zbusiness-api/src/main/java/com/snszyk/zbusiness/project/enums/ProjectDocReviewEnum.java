package com.snszyk.zbusiness.project.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 文审状态枚举
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ProjectDocReviewEnum {
	TODO_REVIEW("0","待文审"),
	PASS("1","文审通过"),
	NO_PASS("2","文审不通过"),

	;
	@EnumValue
	String code;

	String value;

	@Override
	public String toString() {
		return this.value;
	}

}
