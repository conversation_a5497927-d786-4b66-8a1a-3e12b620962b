package com.snszyk.zbusiness.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class ProjectDispatchDetailDto extends BaseCrudDto {

	/**
	 * 调度id
	 */
	@ApiModelProperty(value = "调度id")
	private Long dispatchId;
	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 项目负责人
	 */
	@ApiModelProperty(value = "项目负责人")
	private String headPerson;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "负责人联系方式")
	private String headPersonTel;
	/**
	 * 计划金额
	 */
	@ApiModelProperty(value = "计划金额")
	private BigDecimal planFunds;
	/**
	 * 合同金额
	 */
	@ApiModelProperty(value = "合同金额")
	private BigDecimal contractAmount;
	/**
	 * 完成金额
	 */
	@ApiModelProperty(value = "完成金额")
	private BigDecimal completeAmount;
	/**
	 * 项目总体进展情况
	 */
	@ApiModelProperty(value = "项目总体进展情况")
	private String generalProgress;
	/**
	 * 项目阶段
	 */
	@ApiModelProperty(value = "项目阶段")
	private String projectPhase;
	/**
	 * 项目阶段
	 */
	@ApiModelProperty(value = "项目阶段名称")
	private String projectPhaseName;
	/**
	 * 本季度进展情况
	 */
	@ApiModelProperty(value = "本季度进展情况")
	private String thisQuarterProgress;
	/**
	 * 推进进度，数据字典
	 */
	@ApiModelProperty(value = "推进进度")
	private String pushProgress;
	/**
	 * 推进进度，数据字典
	 */
	@ApiModelProperty(value = "推进进度名称")
	private String pushProgressName;
	/**
	 * 存在主要问题
	 */
	@ApiModelProperty(value = "存在主要问题")
	private String mainQuestion;
	/**
	 * 下季度工作计划
	 */
	@ApiModelProperty(value = "下季度工作计划")
	private String nextQuarterPlan;
	/**
	 * 提报人id
	 */
	@ApiModelProperty(value = "提报人id")
	private Long submitPersonId;
	/**
	 * 提报人姓名
	 */
	@ApiModelProperty(value = "提报人姓名")
	private String submitPersonName;
	/**
	 * 提报时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "提报时间")
	private Date submitTime;
	/**
	 * 操作单位id
	 */
	@ApiModelProperty(value = "操作单位id")
	private Long operateCompanyId;
	/**
	 * 操作单位名称
	 */
	@ApiModelProperty(value = "操作单位名称")
	private String operateCompanyName;
	/**
	 * 项目调度状态，字典
	 */
	@ApiModelProperty(value = "项目调度状态")
	private String projectDispatchStatus;
	/**
	 * 项目调度状态，字典
	 */
	@ApiModelProperty(value = "项目调度状态名称")
	private String projectDispatchStatusName;
	/**
	 * 项目单位层级祖级列表
	 */
	@ApiModelProperty(value = "项目单位层级祖级列表")
	private String ancestors;
	/**
	 * 当前单位id
	 */
	@ApiModelProperty(value = "当前单位id")
	private Long presentCompanyId;
	/**
	 * 交付物
	 */
	@ApiModelProperty(value = "交付物")
	private List<SzykAttachDto> fileList = new ArrayList<>();

	/**
	 * 判断当前是否能编辑
	 */
	@ApiModelProperty(value = "判断当前是否能编辑 true 是 false 否")
	private Boolean flag;

}
