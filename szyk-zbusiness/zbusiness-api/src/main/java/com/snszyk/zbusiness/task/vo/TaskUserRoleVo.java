/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务文档表实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskUserRoleVo")
public class TaskUserRoleVo extends BaseCrudSlimVo {

	@ApiModelProperty(value = "部门id,多个用逗号隔开")
	private String deptIds;

	/**
	 * 系统id
	 */
	@ApiModelProperty(value = "角色名称")
	private String roleName;


}
