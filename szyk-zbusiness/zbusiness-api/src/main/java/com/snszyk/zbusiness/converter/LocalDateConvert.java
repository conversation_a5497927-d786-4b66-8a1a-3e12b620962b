package com.snszyk.zbusiness.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.time.LocalDate;

public class LocalDateConvert implements Converter<LocalDate> {


	@Override
	public WriteCellData<LocalDate> convertToExcelData(WriteConverterContext<LocalDate> context) {
		return new WriteCellData<>(context.getValue().toString());
	}
}
