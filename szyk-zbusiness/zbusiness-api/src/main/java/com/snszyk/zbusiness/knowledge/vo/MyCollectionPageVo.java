/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 我的收藏实体类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MyCollectionPageVo对象", description = "我的收藏")
public class MyCollectionPageVo extends BaseCrudSlimVo {

	/**
	 * 收集方式
	 */
	@ApiModelProperty(value = "收集方式（字典id）")
	private String collectType;

	/**
	 * 知识分类名称
	 */
	@ApiModelProperty(value = "知识分类名称", required = true)
	private String classifyName;


}
