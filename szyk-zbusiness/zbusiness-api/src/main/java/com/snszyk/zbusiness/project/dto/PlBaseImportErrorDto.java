package com.snszyk.zbusiness.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目库导入失败原因
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlBaseImportErrorDto {


	/**
	 * 行号
	 */
	@ApiModelProperty(value = "行号")
	private Integer rowIndex;


	/**
	 * 失败原因
	 */
	@ApiModelProperty(value = "失败原因")
	private String errorMsg;



}
