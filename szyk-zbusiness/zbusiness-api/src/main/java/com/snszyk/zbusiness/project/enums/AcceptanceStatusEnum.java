package com.snszyk.zbusiness.project.enums;

/**
 * 验收状态
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum AcceptanceStatusEnum {

	/**
	 * 未验收
	 */
	NOT_ACCEPTED("0", "未验收"),

	/**
	 * 已验收
	 */
	ACCEPTED("1", "已验收"),
	;

	private String code;
	private String message;

	AcceptanceStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
