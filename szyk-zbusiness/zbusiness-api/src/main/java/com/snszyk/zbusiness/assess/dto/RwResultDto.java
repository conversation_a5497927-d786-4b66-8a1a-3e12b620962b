package com.snszyk.zbusiness.assess.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class RwResultDto {

	/**
	 * 体系id
	 */
	@ApiModelProperty(value = "体系id")
	private Long schemeId;
	/**
	 * 体系名称
	 */
	@ApiModelProperty(value = "体系名称")
	private String schemeName;
	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;
	/**
	 * 发起组织名称
	 */
	@ApiModelProperty(value = "发起组织名称")
	private String initiateOrgName;
	/**
	 * 被考核组织id
	 */
	@ApiModelProperty(value = "被考核组织id")
	private Long assessedOrgId;
	/**
	 * 被考核组织名称
	 */
	@ApiModelProperty(value = "被考核组织名称")
	private String assessedOrgName;
	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;
	/**
	 * 考核期间名称
	 */
	@ApiModelProperty(value = "考核期间名称")
	private String periodName;
	/**
	 * 考核得分
	 */
	@ApiModelProperty(value = "考核得分")
	private BigDecimal score;


	@ApiModelProperty(value = "专项考核加减分总计")
	private BigDecimal assessAddDeductScore;

	@ApiModelProperty(value = "过程考核加减分总计")
	private BigDecimal processAddDeductScore;

	@ApiModelProperty(value = "加减分总计")
	private BigDecimal totalAddDeductScore;

	/**
	 * 根据评分结果，分为优秀、良好、一般、较差、不合格五个等次，考核得分≥90分为优秀等次；
	 * 90分＞考核得分≥80分为良好等次；80分＞考核得分≥70分为一般等次；
	 * 70分＞考核得分≥60分为较差等次；考核得分<60分为不合格等次，
	 * 考核年度内发生重大及以上网络安全事件的，直接评定为不合格
	 */
	@ApiModelProperty(value = "等级")
	private Integer level;
	@ApiModelProperty(value = "等级名称")
	private String levelName;
	/**
	 * 是否明细
	 */
	@ApiModelProperty(value = "是否明细 true 是 false 否")
	private Boolean flag;
}
