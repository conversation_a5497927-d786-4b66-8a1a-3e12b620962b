package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作记录详情数据传输对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录详情数据传输对象", description = "操作记录详情数据传输对象")
public class RsOperationRecordDetailDto extends BaseCrudDto {

    /**
     * 操作记录ID
     */
    @ApiModelProperty(value = "操作记录ID")
    private Long recordId;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段中文名称
     */
    @ApiModelProperty(value = "字段中文名称")
    private String fieldLabel;

    /**
     * 变更前值
     */
    @ApiModelProperty(value = "变更前值")
    private String oldValue;

    /**
     * 变更后值
     */
    @ApiModelProperty(value = "变更后值")
    private String newValue;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 变更前显示值（用于字典转换后的显示）
     */
    @ApiModelProperty(value = "变更前显示值")
    private String oldDisplayValue;

    /**
     * 变更后显示值（用于字典转换后的显示）
     */
    @ApiModelProperty(value = "变更后显示值")
    private String newDisplayValue;
}
