/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 工作流实例审批流程实体类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InstanceProcessVo对象", description = "工作流实例审批流程")
@Accessors(chain = true)
public class InstanceProcessVo extends BaseCrudVo {

	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private Long instanceId;
	/**
	 * 节点名称
	 */
	@ApiModelProperty(value = "节点名称")
	private String stepName;
	/**
	 * 节点类型：0用户1角色2部门3岗位
	 */
	@ApiModelProperty(value = "节点类型：0用户1角色2部门3岗位")
	private Integer stepType;
	/**
	 * 操作对象id
	 */
	@ApiModelProperty(value = "操作对象id")
	private String operatorId;
	/**
	 * 节点审批模式：COUNTERSIGN会签；ONESIGN或签
	 */
	@ApiModelProperty(value = "节点审批模式：COUNTERSIGN会签；ONESIGN或签")
	private String stepMode;
	/**
	 * 是否提交人自选：0否1是
	 */
	@ApiModelProperty(value = "是否提交人自选：0否1是")
	private Integer submitChoose;
	/**
	 * 筛选部门：0不筛选1本部门2关联上级
	 */
	@ApiModelProperty(value = "筛选部门：0不筛选1本部门2关联上级")
	private Integer screenDept;
	/**
	 * 审批顺序，从1开始
	 */
	@ApiModelProperty(value = "审批顺序，从1开始")
	private Integer flowSort;
	/**
	 * 节点审批人id
	 */
	@ApiModelProperty(value = "节点审批人id")
	private String stepUserId;
	/**
	 * 节点审核人姓名，多个用英文逗号隔开
	 */
	@ApiModelProperty(value = "节点审核人姓名，多个用英文逗号隔开")
	private String stepUserName;

	/**
	 * 节点审核部门id
	 */
	@ApiModelProperty(value = "节点审核部门id")
	private Long stepDeptId;

	/**
	 * 节点审核部门
	 */
	@ApiModelProperty(value = "节点审核部门")
	private String stepDeptName;
	/**
	 * 审核人id，多个用英文逗号隔开
	 */
	@ApiModelProperty(value = "审核人id，多个用英文逗号隔开")
	private String approveUserId;
	/**
	 * 审核人姓名，多个用英文逗号隔开
	 */
	@ApiModelProperty(value = "审核人姓名，多个用英文逗号隔开")
	private String approveUserName;
	/**
	 * 审核： 0同意 1拒绝
	 */
	@ApiModelProperty(value = "审核： 0同意 1拒绝")
	private Integer approveStatus;
	/**
	 * 操作备注
	 */
	private String operateRemark;
	/**
	 * 审批时间
	 */
	@ApiModelProperty(value = "审批时间")
	private LocalDateTime approveTime;

	/**
	 * 提报前事件
	 */
	@ApiModelProperty(value = "提报前事件：0无1有")
	private Integer beforeEvent;
	/**
	 * 是否完成事前事件：0否1是
	 */
	@ApiModelProperty(value = "是否完成事前事件：0否1是")
	private Integer completeEvent;

	@ApiModelProperty(value = "操作渠道：0本系统1OA")
	private Integer operatorChannel;

	@ApiModelProperty(value = "单位范围")
	private Integer unitLevel;
	@ApiModelProperty(value = "提交人id")
	private Long submitUserId;
	@ApiModelProperty(value = "提交人姓名")
	private String submitUserName;

	@ApiModelProperty(value = "提交人单位id")
	private Long submitDeptId;
	@ApiModelProperty(value = "提交人单位")
	private String submitDeptName;
	@ApiModelProperty(value = "提交时间")
	private LocalDateTime submitTime;
	/**
	 * 文审状态
	 */
	@ApiModelProperty(value = "文审状态")
	private String docReview;

	@ApiModelProperty(value = "是否可文审")
	private boolean enableDocReview;

	@ApiModelProperty(value = "退回逻辑：0退回后不可提交1退回后可提交")
	private Integer backLogic;

	@ApiModelProperty(value = "集团联络人的办理状态 0 待办理 1 办理中 ")
	private String contactApprovalStatus;

}
