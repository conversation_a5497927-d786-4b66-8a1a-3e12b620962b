package com.snszyk.zbusiness.assess.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.assess.dto.RwSchemeTargetDto;
import com.snszyk.zbusiness.assess.dto.RwSchemeTargetPageDto;
import com.snszyk.zbusiness.assess.vo.RwSchemeTargetPageVo;
import com.snszyk.zbusiness.assess.vo.RwSchemeTargetVo;

import java.util.List;

/**
 * IRwSchemeTargetService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRwSchemeTargetService extends IBaseCrudService<RwSchemeTargetDto, RwSchemeTargetVo> {

	IPage<RwSchemeTargetPageDto> pageList(RwSchemeTargetPageVo vo );

	List<RwSchemeTargetDto> listBySchemeStatus(String rwSchemeStatus);

	List<RwSchemeTargetDto> listAll();

	List<RwSchemeTargetDto> listByStatusAndCompany(String rwSchemeStatus, List<Long> companyIds);

	List<RwSchemeTargetDto> listByStatusAndCompany(String rwSchemeStatus, String assessedOrgId, Long initiateOrgId);

	List<RwSchemeTargetDto> listByNameAndCompany(String rwSchemeName, Long companyId);

	List<RwSchemeTargetDto> listByIdAndOrg(Long id, Long initiateOrgId);

	boolean updateByStatus(Long id, String RwSchemeStatus);

	RwSchemeTargetDto update(RwSchemeTargetVo vo);

}
