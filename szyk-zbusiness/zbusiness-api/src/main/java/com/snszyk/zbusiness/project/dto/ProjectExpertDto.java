/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目分配专家表实体类
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjectExpertDto对象", description = "项目分配专家表")
public class ProjectExpertDto extends BaseCrudDto {

	@ApiModelProperty(value = "转发人的orgId")
	private Long sendOrgId;
	/**
	 * 专家分组id
	 */
	@ApiModelProperty(value = "专家分组id")
	private Long expertGroupId;
	/**
	 * 专家id
	 */
	@ApiModelProperty(value = "专家id")
	private Long expertId;
	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long userId;
	/**
	 * 用户姓名
	 */
	@ApiModelProperty(value = "用户姓名")
	private String userName;
	/**
	 * 转发人id
	 */
	@ApiModelProperty(value = "转发人id")
	private Long sendUserId;
	/**
	 * 转发人姓名
	 */
	@ApiModelProperty(value = "转发人姓名")
	private String sendUserName;
	/**
	 * 转发时间
	 */
	@ApiModelProperty(value = "转发时间")
	private LocalDateTime sendTime;
	/**
	 * 是否线上：0是1否
	 */
	@ApiModelProperty(value = "是否线上：0是1否")
	private Boolean onlineStatus;
	/**
	 * 评审状态，数据字典
	 */
	@ApiModelProperty(value = "评审状态，数据字典")
	private String reviewStatus;

	/**
	 * 评审状态，数据字典
	 */
	@ApiModelProperty(value = "评审状态，数据字典")
	private String reviewStatusName;
	/**
	 * 是否失效
	 */
	@ApiModelProperty(value = "是否失效")
	private Boolean isInvalid;
}
