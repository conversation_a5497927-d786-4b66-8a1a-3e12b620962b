package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectEvaluateUploadPageVo", description = "项目库基本信息表")
public class ProjectEvaluateUploadPageVo extends BaseCrudSlimVo {

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 0 待办 1 已办
	 */
	@ApiModelProperty(value = "0 待办 1 已办")
	private Integer flag;

	/**
	 * 是否为单位登录
	 */
	@ApiModelProperty(value = "是否为单位登录",hidden = true)
	private Integer isUnit;

	/**
	 * 组织id
	 */
	@ApiModelProperty(value = "组织id",hidden = true)
	private Long orgId;

	@ApiModelProperty(value = "项目类型")
	private String projectType;


}
