/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 单位明细
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndicatorOrgDetailListVo", description = "单位明细")
public class IndicatorOrgDetailListVo extends BaseCrudSlimVo {

	/**
	 * 系统编号
	 */
	@ApiModelProperty(value = "系统编号")
	@NotBlank
	private String systemNo;

	/**
	 * 上级组织编码
	 */
	@ApiModelProperty(value = "上级组织编码")
	private String upOrgCode;

	/**
	 * 时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JSONField(format = "yyyy-MM-dd")
	@ApiModelProperty(value = "时间")
	@NotNull
	private LocalDate time;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序 1正序  2倒叙")
	private Integer sort = 1;

	@ApiModelProperty(hidden = true)
	private Integer year;

	@ApiModelProperty(hidden = true)
	private Integer month;



}
