/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import cn.hutool.core.date.DateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 首页任务管理统计
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@ApiModel(value = "HomeProjectStatVo对象", description = "首页任务管理统计")
public class HomeTaskStatVo {


	@ApiModelProperty(value = "日期范围 1 本月 2 本年")
	private Integer dateRange;

	@ApiModelProperty(value = "机构id",hidden = true)
	private  Long orgId;


	@ApiModelProperty(value = "开始时间",hidden = true)
	private LocalDateTime startDate;

	@ApiModelProperty(value = "结束时间",hidden = true)
	private LocalDateTime endDate;

	@ApiModelProperty(value = "类型  1 任务下发 2 任务执行")
	private Integer taskStatType;


}
