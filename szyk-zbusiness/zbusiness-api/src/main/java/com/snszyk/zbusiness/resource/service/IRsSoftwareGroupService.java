package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupDto;
import com.snszyk.zbusiness.resource.dto.RsSoftwareGroupPageDto;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupPageVo;
import com.snszyk.zbusiness.resource.vo.RsSoftwareGroupVo;

import java.util.List;

/**
 * IRsSoftwareGroupService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IRsSoftwareGroupService extends IBaseCrudService<RsSoftwareGroupDto, RsSoftwareGroupVo> {


	IPage<RsSoftwareGroupPageDto> pageList(RsSoftwareGroupPageVo vo);

	boolean updateByStatus(Long id, String groupStatus);

	RsSoftwareGroupDto update(RsSoftwareGroupVo vo);

}
