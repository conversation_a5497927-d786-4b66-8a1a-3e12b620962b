///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.vo;
//
//import com.snszyk.core.crud.vo.BaseCrudSlimVo;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
///**
// * 任务模板实体类
// *
// * <AUTHOR>
// * @since 2023-04-23
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ApiModel(value = "TaskModelVo对象", description = "任务模板")
//public class TaskModelPageVo extends BaseCrudSlimVo {
//
//	/**
//	 * 模板名称
//	 */
//	@ApiModelProperty(value = "模板名称")
//	private String modelName;
//
//	/**
//	 * 所属组织名称
//	 */
//	@ApiModelProperty(value = "所属组织名称")
//	private String companyName;
//
//
//
//}
