package com.snszyk.zbusiness.flow.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: Create in 18:18 2021/4/26 0026
 * @Modified By:
 */
public enum BusinessTypeEnum {
	/**
	 * 项目申报
	 */
	PROJECT_SUBMIT("1"),

	/**
	 * 项目进度提报
	 */
	PROJECT_PROGRESS_SUBMIT("2"),

	/**
	 * 一类项目验收
	 */
	ONE_PROJECT_CHECK("3"),

	/**
	 * 二类项目验收
	 */
	TWO_PROJECT_CHECK("4");

    @Getter
    @Setter
    private String code;

    BusinessTypeEnum(String code) {
        this.code = code;
    }


}
