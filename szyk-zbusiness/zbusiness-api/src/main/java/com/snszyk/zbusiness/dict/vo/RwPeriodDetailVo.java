package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "RwPeriodDetailVo", description = "考核周期")
public class RwPeriodDetailVo extends BaseCrudSlimVo {

	/**
	 * 考核周期id
	 */
	@ApiModelProperty("考核周期id")
	private Long rwPeriodId;
	/**
	 * 期间名称
	 */
	@ApiModelProperty("期间名称")
	private String periodName;
	/**
	 * 周期开始时间
	 */
	@ApiModelProperty("周期开始时间")
	private Date periodStartTime;
	/**
	 * 周期结束时间
	 */
	@ApiModelProperty("周期结束时间")
	private Date periodEndTime;
	/**
	 * 状态：0启用1关闭
	 */
	@ApiModelProperty("状态：0启用1关闭")
	private String periodDetailStatus;

}
