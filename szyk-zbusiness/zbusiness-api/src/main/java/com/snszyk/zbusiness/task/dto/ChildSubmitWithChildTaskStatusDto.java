/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 子任务填报记录及关联子任务状态
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SubmitNoteDto对象", description = "填报记录")
public class ChildSubmitWithChildTaskStatusDto extends BaseCrudDto {


	/**
	 * 子任务id
	 */
	@ApiModelProperty(value = "子任务id")
	private Long childTaskId;
	/**
	 * 填报记录id
	 */
	@ApiModelProperty(value = "填报记录id")
	private Long submitNoteId;

	private String childTaskStatus;

}
