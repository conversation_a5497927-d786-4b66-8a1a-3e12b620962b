/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 科技项目库实体类
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjectTechnologyDto对象", description = "科技项目库")
public class ProjectTechnologyDto extends BaseCrudDto {

	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;
	/**
	 * 批次名
	 */
	@ApiModelProperty(value = "批次名")
	private String batchName;
	/**
	 * 项目负责人
	 */
	@ApiModelProperty(value = "项目负责人")
	private String headPerson;
	/**
	 * 项目联系人
	 */
	@ApiModelProperty(value = "项目联系人")
	private String constructionPerson;
	/**
	 * 项目联系人联系方式
	 */
	@ApiModelProperty(value = "项目联系人联系方式")
	private String constructionPersonTel;
	/**
	 * 建设内容
	 */
	@ApiModelProperty(value = "建设内容")
	private String constructionContent;
	/**
	 * 项目预计总投资
	 */
	@ApiModelProperty(value = "项目预计总投资")
	private BigDecimal totalEstimate;
	/**
	 * 集团审查结论
	 */
	@ApiModelProperty(value = "集团审查结论")
	private String corpApproveResult;
	@ApiModelProperty(value = "集团审查结论名")
	private String corpApproveResultName;
	/**
	 * 计划招标和上下日期
	 */
	@ApiModelProperty(value = "计划招标和上下日期")
	private String planDate;
	/**
	 * 集团审查意见
	 */
	@ApiModelProperty(value = "集团审查意见")
	private String corpApproveRemark;
	/**
	 * 二级审查意见
	 */
	@ApiModelProperty(value = "二级审查意见")
	private String secApproveRemark;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	private Long constructionUnitId;


}
