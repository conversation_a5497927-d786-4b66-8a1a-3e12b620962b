package com.snszyk.zbusiness.flow.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Optional;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum InstanceStatusEnum {

	CHECKING(0, "审核中"),
	PASS(1, "审核通过"),
	RETURN_FLOW(2, "已退回"),
	CANCEL(4 ,"已撤回"),
	DISAGREE(3 ,"待提交"),
	;

	private Integer code;
	private String message;

	InstanceStatusEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	public static InstanceStatusEnum getByCode(Integer code) {
		Optional<InstanceStatusEnum> result = Arrays.stream(values()) // values() 可以获取当前枚举类所有枚举常量
			.filter(t -> t.getCode() == code) // 判断相等的条件
			.findFirst();
		if (result.isPresent()) {
			return result.get();
		} else {
			throw new SecurityException(StrUtil.format("No  matches type value {}", code)); // NOSONAR
		}
	}

}
