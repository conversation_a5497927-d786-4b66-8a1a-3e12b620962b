package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDto;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordPageVo;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordVo;

import java.util.List;

/**
 * 操作记录服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface IRsOperationRecordService extends IBaseCrudService<RsOperationRecordDto, RsOperationRecordVo> {

    /**
     * 分页查询操作记录
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<RsOperationRecordDto> pageList(RsOperationRecordPageVo vo);

    /**
     * 异步保存操作记录
     *
     * @param vo 操作记录
     */
    void saveAsync(RsOperationRecordVo vo);

    /**
     * 根据业务类型和业务ID获取业务数据
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 业务数据
     */
    Object getBusinessData(String businessType, Long businessId);

    /**
     * 根据记录ID获取变更详情
     *
     * @param recordId 记录ID
     * @return 变更详情
     */
    RsOperationRecordDto getDetailWithChanges(Long recordId);

    /**
     * 根据业务ID查询操作记录
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 操作记录列表
     */
    List<RsOperationRecordDto> listByBusinessId(String businessType, Long businessId);
}
