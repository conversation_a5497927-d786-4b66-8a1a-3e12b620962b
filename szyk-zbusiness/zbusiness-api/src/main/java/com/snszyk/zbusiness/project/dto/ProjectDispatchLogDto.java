package com.snszyk.zbusiness.project.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
public class ProjectDispatchLogDto extends BaseCrudDto {

	/**
	 * 项目调度明细id
	 */
	@ApiModelProperty(value = "项目调度明细id")
	private Long projectDispatchDetailId;
	/**
	 * 操作人id
	 */
	@ApiModelProperty(value = "操作人id")
	private Long operatorId;
	/**
	 * 操作人姓名
	 */
	@ApiModelProperty(value = "操作人姓名")
	private String operatorName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 操作人单位id
	 */
	@ApiModelProperty(value = "操作人单位id")
	private Long operatorCompanyId;
	/**
	 * 操作人单位名称
	 */
	@ApiModelProperty(value = "操作人单位名称")
	private String operatorCompanyName;
	/**
	 * 操作，字典
	 */
	@ApiModelProperty(value = "操作")
	private String operate;
	/**
	 * 操作，字典
	 */
	@ApiModelProperty(value = "操作名称")
	private String operateName;
	/**
	 * 操作时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "操作时间")
	private Date operateTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
