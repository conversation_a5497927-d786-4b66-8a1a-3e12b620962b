package com.snszyk.zbusiness.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel
public class ProjectDispatchDto extends BaseCrudDto {

	/**
	 * 调度单号
	 */
	@ApiModelProperty(value = "调度单号")
	private String dispatchNo;
	/**
	 * 调度周期明细id
	 */
	@ApiModelProperty(value = "调度周期明细id")
	private Long dispatchPeriodDetailId;
	/**
	 * 期间名称
	 */
	@ApiModelProperty(value = "期间名称")
	private String periodName;
	/**
	 * 周期开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "周期开始时间")
	private Date periodStartTime;
	/**
	 * 周期结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "周期结束时间")
	private Date periodEndTime;
	/**
	 * 调度截止日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "调度截止日期")
	private Date dispatchDeadline;
	/**
	 * 下发单位id
	 */
	@ApiModelProperty(value = "下发单位id")
	private Long sendCompanyId;
	/**
	 * 下发单位名称
	 */
	@ApiModelProperty(value = "下发单位名称")
	private String sendCompanyName;
	/**
	 * 调度状态，数据字典
	 */
	@ApiModelProperty(value = "调度状态，数据字典")
	private String dispatchStatus;
	/**
	 * 调度状态，数据字典
	 */
	@ApiModelProperty(value = "调度状态名称")
	private String dispatchStatusName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 明细数据
	 */
	@ApiModelProperty(value = "明细数据")
	private List<ProjectDispatchDetailDto> list;
	/**
	 * 是否具有退回权限
	 */
	@ApiModelProperty(value = "是否具有退回权限 true 是 false 否")
	private Boolean backFlag;


	 /**
	 * 是否是当前部门编辑
	 */
	@ApiModelProperty(value = "是否是当前部门编辑 true 是 false 否")
	private Boolean deptFlag;


}
