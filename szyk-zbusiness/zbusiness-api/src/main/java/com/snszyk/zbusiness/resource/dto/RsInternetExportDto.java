/*
 *      Copyright (c,order=1) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.snszyk.common.excel.DateConverter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 互联网资源台账实体类
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
@ApiModel(value = "RsInternetDto对象", description = "互联网资源台账")
public class RsInternetExportDto  {


	/**
	 * 所属组织名称
	 */

	@ExcelProperty(value = "组织名称", order = 1)
	private String fullOrgName;

	/**
	 * 单位级别
	 */
	@ExcelIgnore
	private Integer orgLevel;
	/**
	 * 单位级别
	 */
	@ExcelProperty(value = "单位级别", order = 2)
	private String orgLevelName;

	/**
	 * 系统名称
	 */
	@ExcelProperty(value = "系统名称", order = 3)
	private String systemName;


	/**
	 * 等保级别
	 */
	@ExcelIgnore
	private String securityLevel;

	/**
	 * 等保级别
	 */
	@ExcelProperty(value = "等保级别", order = 4)
	private String securityLevelName;
	/**
	 * 资源类型,数据字典
	 */
	@ExcelProperty(value = "资源类型", order = 5)
	private String internetTypeName;
	/**
	 * 应用类型，数据字典
	 */
	@ExcelProperty(value = "应用类型", order = 6)
	private String applicationTypeName;

	/**
	 * 小程序名称
	 */
	@ExcelProperty(value = "小程序名称", order = 7)
	private String appName;
	/**
	 * 互联网地址
	 */
	@ExcelProperty(value = "互联网地址", order = 8)
	private String internetAddress;

	/**
	 * 内网地址
	 */
	@ExcelProperty(value = "内网地址", order = 9)
	private String networkAddress;

	/**
	 * 域名或url
	 */
	@ExcelProperty(value = "域名或url",order=10)
	private String domainName;

	/**
	 * 是否公有云部署
	 */
	@ExcelIgnore
	private Boolean isPublicCloud;
	/**
	 * 是否公有云部署
	 */
	@ExcelProperty(value = "是否公有云部署",order=11)
	private String isPublicCloudName;

	/**
	 * 公有云供应商
	 */
	@ExcelProperty(value = "公有云供应商",order=12)
	private String publicCloudSupplier;

	/**
	 * 公有云所需资源
	 */
	@ExcelProperty(value = "公有云所需资源", order = 13)
	private String publicCloudResource;

	/**
	 * 联系人
	 */
	@ExcelProperty(value = "负责人", order = 14)
	private String contactPerson;
	/**
	 * 电话号码
	 */
	@ExcelProperty(value = "联系方式", order = 15)
	private String contactPhone;

	/**
	 * 资源状态
	 */
	@ExcelProperty(value = "资源状态", order = 16)
	private String resourceStatusName;

	/**
	 * 接口地址文件
	 */
	@ExcelProperty(value = "上传接口地址", order = 17)
	@ColumnWidth(30)
	private String interfaceAddressListStr;

	/**
	 * 渗透报告文件
	 */
	@ColumnWidth(30)
	@ExcelProperty(value = "上传渗透报告地址", order = 18)
	private String penetrationReportListStr;


//	/**
//	 * 关联类型
//	 */
//	@ExcelProperty(value = "关联类型", order = 13)
//	private String associationTypeName;
//
//	/**
//	 * 关联名称
//	 */
//	@ExcelProperty(value = "关联名称", order = 14)
//	private String associationName;
//	/**
//	 * 关联编号
//	 */
//	@ExcelProperty(value = "关联编号", order = 15)
//	private String associationNo;
	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注", order = 19)
	private String remark;

	@ExcelIgnore
	private Long createUser;

	@ExcelProperty(value = "创建人", order = 20)
	private String createUserName;


	@ExcelProperty(value = "创建日期", order = 21,converter= DateConverter.class)
	@ColumnWidth(20)
	private Date createTime;

	@ExcelIgnore
	private Long updateUser;

	@ExcelProperty(value = "修改人", order = 22)
	private String updateUserName;

	@ColumnWidth(20)
	@ExcelProperty(value = "修改日期", order = 23,converter= DateConverter.class)
	private Date updateTime;



}
