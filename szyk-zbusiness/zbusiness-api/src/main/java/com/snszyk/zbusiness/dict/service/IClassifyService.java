/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.dict.dto.ClassifyDto;
import com.snszyk.zbusiness.dict.vo.ClassifySaveVo;
import com.snszyk.zbusiness.dict.vo.ClassifyVo;

import java.util.List;

/**
 * 知识分类 服务类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
public interface IClassifyService extends IBaseCrudService<ClassifyDto, ClassifyVo> {
	/**
	 * 批量删除
	 *
	 * @param idList-id集合
	 * @return
	 */
	Boolean removeByIds(List<Long> idList);

	/**
	 * 新增或修改
	 *
	 * @param classifyVoList
	 * @return
	 */
	Boolean saveOrUpdateBatch(List<ClassifyVo> classifyVoList);

	/**
	 * 列表查询
	 *
	 * @param collectType
	 * @param classifyName
	 * @return
	 */
	List<ClassifyDto> myList(String collectType, String classifyName);

	/**
	 *
	 * @param ids
	 * @return
	 */
	List<ClassifyDto> listByIds(List<Long> ids);

	/**
	 *
	 * @param menuId
	 * @param collectType
	 * @param classifyStatus
	 * @return
	 */
    List<ClassifyDto> listByMenuId(Long menuId, String collectType, String classifyStatus);

    Boolean updateMenu(Long id);

	Boolean updateVo(ClassifyVo v);
}
