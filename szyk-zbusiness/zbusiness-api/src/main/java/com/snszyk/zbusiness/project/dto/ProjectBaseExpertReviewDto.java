/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 专家评审json
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
@ApiModel(value = "专家评审json", description = "项目分配专家表")
@AllArgsConstructor
public class ProjectBaseExpertReviewDto {

	/**
	 * 专家id
	 */
	@ApiModelProperty(value = "专家id")
	private Long expertId;

	/**
	 * 评审状态，数据字典
	 */
	@ApiModelProperty(value = "评审状态，数据字典")
	private String reviewStatus;
	/**
	 * 是否为线上
	 */
	@ApiModelProperty(value = "是否为线上")
	private Boolean onlineStatus;


}
