package com.snszyk.zbusiness.assess.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class RwSchemeScoreMethodDto extends BaseCrudDto {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标评价id
	 */
	@ApiModelProperty("指标评价id")
	private Long schemeEvaluateId;
	/**
	 * 评分方法
	 */
	@ApiModelProperty("评分方法")
	private String scoreMethod;
}
