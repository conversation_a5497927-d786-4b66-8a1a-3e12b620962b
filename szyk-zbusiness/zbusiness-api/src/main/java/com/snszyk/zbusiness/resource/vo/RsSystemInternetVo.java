/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信息系统互联网明细实体类
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsSystemInternetVo对象", description = "信息系统互联网明细")
public class RsSystemInternetVo extends BaseCrudVo {

	/**
	 * 系统id
	 */
	@ApiModelProperty(value = "系统id")
	private Long systemId;
	/**
	 * 互联网资源id
	 */
	@ApiModelProperty(value = "互联网资源id")
	private Long internetId;


}
