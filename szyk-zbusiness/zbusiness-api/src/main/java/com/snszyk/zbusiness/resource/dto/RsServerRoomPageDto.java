package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class RsServerRoomPageDto extends BaseCrudDto {


	/**
	 * 数据中心机房名称
	 */
	@ApiModelProperty(value = "数据中心机房名称")
	private String roomName;
	/**
	 * 机房编号
	 */
	@ApiModelProperty(value = "机房编号")
	private String roomNo;
	/**
	 * 机房等级，数据字典
	 */
	@ApiModelProperty(value = "机房等级，数据字典")
	private String roomLevel;
	/**
	 * 机房等级，数据字典
	 */
	@ApiModelProperty(value = "机房等级名称")
	private String roomLevelName;
	/**
	 * 机房定位，数据字典
	 */
	@ApiModelProperty(value = "机房定位，数据字典")
	private String roomUse;
	/**
	 * 机房定位，数据字典
	 */
	@ApiModelProperty(value = "机房定位名称")
	private String roomUseName;
	/**
	 * 负责人姓名
	 */
	@ApiModelProperty(value = "负责人姓名")
	private String headPersonName;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "负责人联系方式")
	private String headPersonTel;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 动环系统型号
	 */
	@ApiModelProperty(value = "动环系统型号")
	private String deModel;

	/**
	 * 提交状态，数据字典
	 */
	@ApiModelProperty(value = "提交状态")
	private String submitStatus;
	/**
	 * 提交状态，数据字典
	 */
	@ApiModelProperty(value = "提交状态名称")
	private String submitStatusName;
	/**
	 * 资源状态，数据字典
	 */
	@ApiModelProperty(value = "资源状态名称")
	private String resourceStatus;
	/**
	 * 资源状态，数据字典
	 */
	@ApiModelProperty(value = "资源状态名称")
	private String resourceStatusName;
	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private List<SzykAttachDto> fileList = new ArrayList<>();
//
//	/**
//	 * 编辑权限
//	 */
//	@ApiModelProperty(value = "编辑权限")
//	private Boolean editFlag = false;


}
