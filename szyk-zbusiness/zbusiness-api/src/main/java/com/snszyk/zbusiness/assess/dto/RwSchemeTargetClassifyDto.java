package com.snszyk.zbusiness.assess.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class RwSchemeTargetClassifyDto extends BaseCrudDto {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty("指标分类id")
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项")
	private String addScore;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项名称")
	private String addScoreName;
	/**
	 * 分类分值
	 */
	@ApiModelProperty("分类分值")
	private BigDecimal classifyScore;
}
