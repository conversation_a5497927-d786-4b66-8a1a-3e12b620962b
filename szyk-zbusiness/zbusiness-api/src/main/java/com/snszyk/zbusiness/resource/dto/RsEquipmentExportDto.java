package com.snszyk.zbusiness.resource.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/*
设备台账导入dto
 */
@Data
@ApiModel
public class RsEquipmentExportDto {


	/**
	 * 分类名称
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*设备分类")
	private String classifyName;

	@ExcelIgnore

	private Long classifyId;

	/**
	 * 分类名称//
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*分类编码")
	private String classifyCode;





	/**
	 * 设备名称
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*设备名称")
	private String equipmentName;
	/**
	 * 设备编号
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*设备编码")
	private String equipmentNo;
	/**
	 * 规格型号
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*规格型号")
	private String specification;
	/**
	 * 数量
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*数量")
	private String quantity;
	/**
	 * 计量单位
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*计量单位")
	private String measureName;
	@ExcelIgnore
	private String measure;

	/**
	 * 数据中心机房id
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "数据中心机房名称")
	private String serverroomName;
	/**
	 * 数据中心机房id
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "数据中心机房编号")
	private String serverroomNo;
	/**
	 * 使用地点
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*使用地点")
	private String usePlace;

	/**
	 * 品牌,数据字典
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*品牌")
	private String brandName;
	@ExcelIgnore
	private String brand;

	/**
	 * 供应商id
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*供应商")
	private String supplierName;
	@ExcelIgnore
	private Long supplierId;
	/**
	 * 采购日期
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*采购日期")
//	@DateTimeFormat("yyyy-MM-dd")
	private String purchaseDate;

	/**
	 * 入网日期
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "*入网日期")
//	@DateTimeFormat("yyyy-MM-dd")
	private String networkAccessDate;


	/**
	 * 金额
	 */

	@ColumnWidth(value = 10)
//	@NumberFormat("#.##")//百分比表示，保留两位小数
	@ExcelProperty(value = "*金额(元)")
	private String amount;

	/**
	 * 负责人姓名
	 */
	@ColumnWidth(value = 10)

	@ExcelProperty(value = "*负责人")
	private String headPersonName;
	/**
	 * 使用人姓名
	 */
	@ColumnWidth(value = 10)
	@ExcelProperty(value = "使用人")
	private String usePersonName;
	/**
	 * 采购来源
	 */

	@ExcelProperty(value = "*采购来源")
	private String purchaseSourceName;

	@ExcelIgnore
	private String purchaseSource;

	/**
	 * 关联项目名称
	 */
	@ColumnWidth(value = 20)
	@ExcelProperty(value = "关联项目")
	private String projectName;
	@ExcelIgnore
	private String projectId;

	/**
	 * 设备状态
	 */
	@ExcelProperty(value = "资源状态")
	private String equipmentStatusName;
	@ExcelIgnore
	private Integer equipmentStatus;

	/**
	 * 备注
	 */
	@ExcelProperty(value = "备注")
	private String remark;


}
