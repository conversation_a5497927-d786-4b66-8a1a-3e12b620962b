package com.snszyk.zbusiness.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class ProjectBasePageDto extends BaseCrudDto {
	/**
	 * 立项编号
	 */
	@ApiModelProperty(value = "立项编号")
	private String approvalNo;
	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;

	@ApiModelProperty(value = "批次")
	private String batchNo;
	@ApiModelProperty(value = "批次")
	private String batchName;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类")
	private String specialtyClassification;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类名称")
	private String specialtyClassificationName;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类")
	private String projectClassification;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类名称")
	private String projectClassificationName;
	/**
	 * 列支渠道，字典dc
	 */
	@ApiModelProperty(value = "列支渠道")
	private String distributionChannel;
	/**
	 * 列支渠道，字典dc
	 */
	@ApiModelProperty(value = "列支渠道名称")
	private String distributionChannelName;
	/**
	 * 项目标签，字典pl
	 */
	@ApiModelProperty(value = "项目标签")
	private String projectLabel;
	/**
	 * 项目标签，字典pl
	 */
	@ApiModelProperty(value = "项目标签集合")
	private List<PlProjectLabel> projectLabelList;
	/**
	 * 审查状态，字典review_status
	 */
	@ApiModelProperty(value = "审查状态")
	private String reviewStatus;
	/**
	 * 审查状态，字典review_status
	 */
	@ApiModelProperty(value = "审查状态名称")
	private String reviewStatusName;
	/**
	 * 复审状态
	 */
	@ApiModelProperty(value = "复审状态")
	private String retrialStatus;
	/**
	 * 复审状态
	 */
	@ApiModelProperty(value = "复审状态名称")
	private String retrialStatusName;
	/**
	 * 提报时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "提报时间")
	private Date submitTime;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty(value = "创建人名称")
	private String createUserName;

	@ApiModelProperty(value = "审批流id")
	private Long instanceId;

	@ApiModelProperty(value = "集团审查结论")
	private String corpApproveResult;

	@ApiModelProperty(value = "集团审查结论名称")
	private String corpApproveResultName;

	/**
	 * 集团审查意见
	 */
	@ApiModelProperty(value = "集团审查意见")
	private String corpApproveRemark;

	/**
	 * 二级审查意见
	 */
	@ApiModelProperty(value = "二级审查意见")
	private String secApproveRemark;

	@ApiModelProperty(value = "编辑权限,true可编辑false不可编辑")
	private Boolean editFlag = false;

	@ApiModelProperty(value = "计划资金")
	private BigDecimal planFunds;

	/**
	 * 合同金额
	 */
	@ApiModelProperty(value = "合同金额")
	private BigDecimal contractAmount;

	/**
	 * 投资主体id
	 */
	@ApiModelProperty(value = "投资主体id")
	private Long investmentSubjectId;
	/**
	 * 投资主体名称
	 */
	@ApiModelProperty(value = "投资主体名称")
	private String investmentSubjectName;

	/**
	 * 是否可以删除
	 */
	@ApiModelProperty(value = "是否可以删除")
	private Boolean delFlag = false;


	/**
	 * 项目预计总投资
	 */
	@ApiModelProperty(value = "项目预计总投资")
	private BigDecimal totalEstimate;
	/**
	 * 项目本年度投资
	 */
	@ApiModelProperty(value = "项目本年度投资")
	private BigDecimal currentInvestment;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "计划招标日期")
	private Date planTenderDate;
	/**
	 * 计划开始日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "计划上线日期")
	private Date planCompleteDate;

	@ApiModelProperty(value = "当前节点")
	private String currStepName;

	@ApiModelProperty(value = "补充材料状态  0.-- 1.无需提报 2.待提交 3.已提交 4.已归档")
	private String supplementStatus;

	@ApiModelProperty(value = "补充材料状态名称  0.-- 1.无需提报 2.待提交 3.已提交 4.已归档")
	private String supplementStatusName;

	@ApiModelProperty(value = "是否有归档按钮")
	private Boolean placeOnFileFlag = false;

	@ApiModelProperty(value = "是否有上传补充材料按钮")
	private Boolean supplementUploadFlag = false;

	@ApiModelProperty(value = "可撤回标识")
	private Boolean cancelFlag = false;

	@ApiModelProperty("作废标志")
	private Boolean nullifyFlag = false;
	/**
	 * 申报结束时间
	 */
	@ApiModelProperty(value = "申报结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime finishDateTime;

	@ApiModelProperty(value = "集团还是二级单位 补充材料使用")
	private String supplyLevel;


	@ApiModelProperty(value = "补充材料上传时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime supplementTime;
}
