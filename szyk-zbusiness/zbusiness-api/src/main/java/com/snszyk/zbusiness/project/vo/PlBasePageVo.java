/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目库基本信息表实体类
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlBasePageVo对象", description = "项目库基本信息表")
@Accessors(chain = true)
public class PlBasePageVo extends BaseCrudSlimVo {

	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名车
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;

	/**
	 * 项目阶段，字段pp
	 */
	@ApiModelProperty(value = "项目阶段，字段pp")
	private String projectPhase;
	/**
	 * 项目阶段
	 */
	@ApiModelProperty(value = "项目阶段")
	private String projectPhaseName;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类，字典sc")
	private String specialtyClassification;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类，字典pc")
	private String projectClassification;

	/**
	 * 项目状态，字典project_status
	 */
	@ApiModelProperty(value = "项目状态，字典project_status")
	private String projectStatus;
	/**
	 * 审查状态，字典review_status
	 */
	@ApiModelProperty(value = "审查状态，字典review_status", hidden = true)
	private String reviewStatus;
	/**
	 * 项目标签，字典pl
	 */
	@ApiModelProperty(value = "项目标签，字典pl")
	private String projectLabel;


	@ApiModelProperty(value = "监理单位", hidden = true)
	private String supervisionUnit;

	@ApiModelProperty(value = "合作单位", hidden = true)
	private String cooperationUnit;

	@ApiModelProperty(value = "年份")
	private Integer year;

	@ApiModelProperty(value = "建设内容")
	private String constructionContent;

	@ApiModelProperty(value = "ids")
	private List<Long> ids;

	@ApiModelProperty(value = "批次号")
	private String batchName;

	// ==================== 新增查询条件属性 ====================

	/**
	 * 计划招标开始日期
	 */
	@ApiModelProperty(value = "计划招标开始日期")
	private LocalDate startPlanTenderDate;

	/**
	 * 计划招标结束日期
	 */
	@ApiModelProperty(value = "计划招标结束日期")
	private LocalDate endPlanTenderDate;

	/**
	 * 计划开始日期
	 */
	@ApiModelProperty(value = "计划开始日期")
	private LocalDate startPlanDate;

	/**
	 * 计划结束日期
	 */
	@ApiModelProperty(value = "计划结束日期")
	private LocalDate endPlanDate;

	/**
	 * 计划上线开始日期
	 */
	@ApiModelProperty(value = "计划上线开始日期")
	private LocalDate startPlanCompleteDate;

	/**
	 * 计划上线结束日期
	 */
	@ApiModelProperty(value = "计划上线结束日期")
	private LocalDate endPlanCompleteDate;

	/**
	 * 项目现状及存在问题
	 */
	@ApiModelProperty(value = "项目现状及存在问题")
	private String projectIntroduction;

	/**
	 * 立项依据
	 */
	@ApiModelProperty(value = "立项依据")
	private String projectNecessity;

	/**
	 * 设备投入明细
	 */
	@ApiModelProperty(value = "设备投入明细")
	private String equipmentInputDetails;

	/**
	 * 信息化智能化投入明细
	 */
	@ApiModelProperty(value = "信息化智能化投入明细")
	private String imInputDetails;

	/**
	 * 列支渠道
	 */
	@ApiModelProperty(value = "列支渠道")
	private String distributionChannel;

	/**
	 * 预计总投资最小值
	 */
	@ApiModelProperty(value = "预计总投资最小值（万元）")
	private BigDecimal minTotalEstimate;

	/**
	 * 预计总投资最大值
	 */
	@ApiModelProperty(value = "预计总投资最大值（万元）")
	private BigDecimal maxTotalEstimate;

	/**
	 * 本年度投资最小值
	 */
	@ApiModelProperty(value = "本年度投资最小值（万元）")
	private BigDecimal minCurrentInvestment;

	/**
	 * 本年度投资最大值
	 */
	@ApiModelProperty(value = "本年度投资最大值（万元）")
	private BigDecimal maxCurrentInvestment;

	/**
	 * 资金计划最小值
	 */
	@ApiModelProperty(value = "资金计划最小值（万元）")
	private BigDecimal minPlanFunds;

	/**
	 * 资金计划最大值
	 */
	@ApiModelProperty(value = "资金计划最大值（万元）")
	private BigDecimal maxPlanFunds;

}
