package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "PlDispatchLogVo", description = "项目库-调度记录")
public class PlDispatchLogVo extends BaseCrudSlimVo {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 调度id
	 */
	@ApiModelProperty(value = "调度id")
	private Long dispatchId;
	/**
	 * 调度编号
	 */
	@ApiModelProperty(value = "调度编号")
	private String dispatchNo;
	/**
	 * 调度周期期间名称
	 */
	@ApiModelProperty(value = "调度周期期间名称")
	private String periodName;
	/**
	 * 调度截止日期
	 */
	@ApiModelProperty(value = "调度截止日期")
	private Date dispatchDeadline;
	/**
	 * 提报时间
	 */
	@ApiModelProperty(value = "提报时间")
	private Date submitTime;
	/**
	 * 调度力度
	 */
	@ApiModelProperty(value = "调度力度")
	private String dispatchIntensity;
	/**
	 * 当前状态,项目阶段
	 */
	@ApiModelProperty(value = "当前状态")
	private String projectPhase;
	/**
	 * 本季度进展情况
	 */
	@ApiModelProperty(value = "本季度进展情况")
	private String thisQuarterProgress;
	/**
	 * 推进进度，数据字典
	 */
	@ApiModelProperty(value = "推进进度")
	private String pushProgress;
	/**
	 * 存在主要问题
	 */
	@ApiModelProperty(value = "存在主要问题")
	private String mainQuestion;
	/**
	 * 下季度工作计划
	 */
	@ApiModelProperty(value = "下季度工作计划")
	private String nextQuarterPlan;

}
