package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsOperationRecordDetailDto;
import com.snszyk.zbusiness.resource.vo.RsOperationRecordDetailVo;

import java.util.List;

/**
 * 操作记录详情服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface IRsOperationRecordDetailService extends IBaseCrudService<RsOperationRecordDetailDto, RsOperationRecordDetailVo> {

    /**
     * 根据记录ID查询详情列表
     *
     * @param recordId 记录ID
     * @return 详情列表
     */
    List<RsOperationRecordDetailDto> listByRecordId(Long recordId);

    /**
     * 批量保存详情
     *
     * @param detailList 详情列表
     * @return 是否成功
     */
    boolean saveBatch(List<RsOperationRecordDetailVo> detailList);
}
