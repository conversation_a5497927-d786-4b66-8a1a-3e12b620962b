/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首页任务管理统计
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HomeProjectStatVo对象", description = "首页任务管理统计")
public class HomeTaskStatDto {


	@ApiModelProperty(value = "下发总数")
	private Integer issueCount;

	@ApiModelProperty(value = "下发进行中")
	private Integer issueInProcess;

	@ApiModelProperty(value = "下发已归档")
	private Integer issuePlaceFile;

	@ApiModelProperty(value = "执行总数")
	private Integer executeCount;

	@ApiModelProperty(value = "执行进行中")
	private Integer executeInProcess;

	@ApiModelProperty(value = "执行已提报")
	private Integer executeSubmit;
}
