/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 信息系统授权实体类
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsSystemAuthDto对象", description = "信息系统授权")
public class RsSystemAuthDto extends BaseCrudDto {

	/**
	* 信息系统id
	*/
		@ApiModelProperty(value = "信息系统id")
		private Long systemId;
	/**
	* 授权组织id
	*/
		@ApiModelProperty(value = "授权组织id")
		private Long authOrgId;


}
