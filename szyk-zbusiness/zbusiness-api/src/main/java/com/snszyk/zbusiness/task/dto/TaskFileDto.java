/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.dto;

import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务文档表实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TaskFileDto对象", description = "任务文档表")
public class TaskFileDto extends SzykAttachDto {

	/**
	 * 系统id
	 */
	@ApiModelProperty(value = "系统id")
	private Long taskId;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;
	/**
	 * 业务类型 1通知附件 2 填报模板
	 */
	private String businessType;



}
