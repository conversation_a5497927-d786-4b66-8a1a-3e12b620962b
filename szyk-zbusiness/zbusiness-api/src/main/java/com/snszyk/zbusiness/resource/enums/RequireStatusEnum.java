package com.snszyk.zbusiness.resource.enums;

/**
 * 统谈分签 需求状态
 *
 * <AUTHOR>
 */
public enum RequireStatusEnum {


	SUBMITTED("1", "已提交"),
	RECALL("2", "已撤回"),
	LOCKED("3", "已锁定"),
	FINISHED("4", "已完成"),


	;

	private String code;
	private String message;

	RequireStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
