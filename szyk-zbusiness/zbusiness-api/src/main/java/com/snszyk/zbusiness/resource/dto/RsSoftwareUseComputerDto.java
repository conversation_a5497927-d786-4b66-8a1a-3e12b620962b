package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class RsSoftwareUseComputerDto extends BaseCrudDto {

	/**
	 * 软件使用id
	 */
	@ApiModelProperty(value = "软件使用id")
	private Long useId;
	/**
	 * 计算机编号
	 */
	@ApiModelProperty(value = "计算机编号")
	private String computerNo;
	/**
	 * 计算机品牌
	 */
	@ApiModelProperty(value = "计算机品牌")
	private String computerBrand;
	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptName;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String ownerName;
	/**
	 * 软件安装明细
	 */
	@ApiModelProperty(value = "软件安装明细")
	private List<RsSoftwareUseSoftwareDto> list;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Integer number;

}
