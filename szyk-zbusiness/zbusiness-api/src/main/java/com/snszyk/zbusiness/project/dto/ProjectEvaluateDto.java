package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class ProjectEvaluateDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类")
	private String specialtyClassification;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类名称")
	private String specialtyClassificationName;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类")
	private String projectClassification;
	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类名称")
	private String projectClassificationName;
	/**
	 * 项目阶段，字典pp
	 */
	@ApiModelProperty(value = "项目阶段")
	private String projectPhase;
	/**
	 * 项目阶段名称
	 */
	@ApiModelProperty(value = "项目阶段名称")
	private String projectPhaseName;
	/**
	 * 评价小组id
	 */
	@ApiModelProperty(value = "评价小组id")
	private Long evaluateGroupId;
	/**
	 * 评价小组名称
	 */
	@ApiModelProperty(value = "评价小组名称")
	private String evaluateGroupName;
	/**
	 * 方案id
	 */
	@ApiModelProperty(value = "方案id")
	private Long schemeId;
	/**
	 * 方案名称
	 */
	@ApiModelProperty(value = "方案名称")
	private String schemeName;
	/**
	 * 评价状态，数据字典
	 */
	@ApiModelProperty(value = "评价状态")
	private String evaluateStatus;
	/**
	 * 评价状态，数据字典
	 */
	@ApiModelProperty(value = "评价状态名称")
	private String evaluateStatusName;
	/**
	 * 总分
	 */
	@ApiModelProperty(value = "总分")
	private BigDecimal totalScore;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateCompanyId;
	/**
	 * 发起组织名称
	 */
	@ApiModelProperty(value = "发起组织名称")
	private String initiateCompanyName;
	/**
	 * 自评报告
	 */
	@ApiModelProperty(value = "自评报告")
	private List<SzykAttachDto> fileList = new ArrayList<>();
	/**
	 * 项目评价
	 */
	@ApiModelProperty(value = "项目评价")
	private List<ProjectEvaluateCommonDto> list = new ArrayList<>();


	@ApiModelProperty(value = "是否指派完成：0否1是")
	private Integer sendMember;


	@ApiModelProperty(value = "项目阶段文档")
	private List<PlStageDto> stageList=new ArrayList<>();

	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;


	/**
	 * 满分
	 */
	@ApiModelProperty(value = "满分")
	private BigDecimal fullScore;

	/**
	 * 项目类型
	 */
	@ApiModelProperty(value = "项目类型")
	private String projectType;

	@ApiModelProperty(value = "项目类型")
	private String projectTypeName;
}
