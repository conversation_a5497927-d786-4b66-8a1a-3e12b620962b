/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 项目库项目资金计划
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlFundPlanDto对象", description = "项目库项目资金计划")
public class PlFundPlanDto extends BaseCrudDto {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * WBS元素号
	 */
	@ApiModelProperty(value = "WBS元素号")
	private String wbsNo;
	/**
	 * 列支渠道，字典dc
	 */
	@ApiModelProperty(value = "列支渠道，字典dc")
	private String distributionChannel;
	/**
	 * 列支渠道，字典dc
	 */
	@ApiModelProperty(value = "列支渠道名称")
	private String distributionChannelName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;
	/**
	 * 投资主体id
	 */
	@ApiModelProperty(value = "投资主体id")
	private Long investmentSubjectId;
	/**
	 * 投资主体名称
	 */
	@ApiModelProperty(value = "投资主体名称")
	private String investmentSubjectName;
	/**
	 * 项目预计总投资
	 */
	@ApiModelProperty(value = "项目预计总投资")
	private BigDecimal totalEstimate;
	/**
	 * 项目本年度投资
	 */
	@ApiModelProperty(value = "项目本年度投资")
	private BigDecimal currentInvestment;


}
