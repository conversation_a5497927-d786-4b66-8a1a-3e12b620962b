package com.snszyk.zbusiness.project.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectEvaluateTargetEvaluateDto;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateEvaluatePageVo;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateTargetEvaluateVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * IProjectEvaluateTargetEvaluateService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IProjectEvaluateTargetEvaluateService extends IBaseCrudService<ProjectEvaluateTargetEvaluateDto, ProjectEvaluateTargetEvaluateVo> {

	List<ProjectEvaluateTargetEvaluateDto> listByEvaluateId(Long projectEvaluateId);

	boolean updateByScore(Long id, BigDecimal getScore);

	int deleteByEvaluateId(Long projectEvaluateId);

    List<Long> querySend(ProjectEvaluateEvaluatePageVo vo, String code, String deptId);

	List<ProjectEvaluateTargetEvaluateDto> listByEvaluateIdAndUser(Long id, String code, Long deptId);
}
