package com.snszyk.zbusiness.project.enums;

/**
 * 记录类型
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum OaOperateEnum {

	/**
	 * 下发
	 */
	AGREE("1", "同意"),

	/**
	 * 提报
	 */
	DISAGREE("2", "不同意"),

	/**
	 * 退回
	 */
	RETURN("3", "退回"),
	;

	private String code;
	private String message;

	OaOperateEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
