package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DictLabelClassifyVo", description = "项目标签分类")
public class DictLabelClassifyVo extends BaseCrudSlimVo {

	/**
	 * 父分类id
	 */
	@ApiModelProperty("父分类id")
	private Long parentId;
	/**
	 * 分类名称
	 */
	@ApiModelProperty("分类名称")
	private String name;
}
