/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectExpertDto;
import com.snszyk.zbusiness.project.dto.ProjectExpertListDto;
import com.snszyk.zbusiness.project.dto.ProjectExpertPageDto;
import com.snszyk.zbusiness.project.vo.ProjectBasePageVo;
import com.snszyk.zbusiness.project.vo.ProjectExpertVo;

import java.util.List;

/**
 * 项目分配专家表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface IProjectExpertService extends IBaseCrudService<ProjectExpertDto, ProjectExpertVo> {

    List<ProjectExpertDto> listByExportId(Long expertId);

	/****
     * 根据项目id查询专家
     * @param projectIds
     * @return
     */
    List<ProjectExpertListDto> queryLatestByProjectIds(List<Long> projectIds);
	/****
	 * 根据项目id查询专家
	 * @param projectIds
	 * @return
	 */
	List<ProjectExpertListDto> queryByProjectIds(List<Long> projectIds);

	/**
	 * 专家评审列表
	 *
	 * @param vo

	 * @return
	 */
    IPage<ProjectExpertPageDto> pageList(ProjectBasePageVo vo, Long expertId);

	boolean updateReviewStatus(List<Long> projectExpertIdList, String code);

	/**
	 * 获取最新的专家评审 分配
	 * @param projectId
	 * @return
	 */
	ProjectExpertDto getLatestByExpertAndProject(String projectId, Long expertId);

	/**
	 * 根据项目id删除专家分配
	 * @param projectId
	 * @return
	 */
	Boolean invalidByProjectId(Long projectId);

	boolean updateOnlineStatus(Long id, Boolean paramOnlineStatus);
}
