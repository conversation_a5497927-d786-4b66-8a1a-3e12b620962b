package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "EvaluateTargetClassifyVo", description = "指标分类")
public class EvaluateTargetClassifyPageVo extends BaseCrudSlimVo {

	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态")
	private String classifyStatus;
}
