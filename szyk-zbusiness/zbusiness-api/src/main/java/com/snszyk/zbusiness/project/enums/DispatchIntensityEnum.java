package com.snszyk.zbusiness.project.enums;

/**
 * 调度力度
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum DispatchIntensityEnum {

	/**
	 * 延迟
	 */
	DELAY("1", "延迟"),

	/**
	 * 正常
	 */
	NORMAL("2", "正常"),
	;

	private String code;
	private String message;

	DispatchIntensityEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
