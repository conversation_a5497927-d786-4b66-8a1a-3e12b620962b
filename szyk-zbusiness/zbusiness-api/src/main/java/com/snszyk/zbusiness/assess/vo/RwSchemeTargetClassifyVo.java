package com.snszyk.zbusiness.assess.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "RwSchemeTargetClassifyVo", description = "考核指标分类")
public class RwSchemeTargetClassifyVo extends BaseCrudSlimVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty("指标分类id")
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty("指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty("是否加分项")
	private String addScore;
	/**
	 * 分类分值
	 */
	@ApiModelProperty("分类分值")
	private BigDecimal classifyScore;
}
