/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作记录分页查询VO
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录分页查询VO", description = "操作记录分页查询视图对象")
public class SysOperationRecordPageVo extends BaseCrudSlimVo {

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 系统名称（模糊查询）
     */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /**
     * 更新开始时间
     */
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startUpdateTime;

    /**
     * 更新结束时间
     */
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endUpdateTime;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作人部门ID
     */
    @ApiModelProperty(value = "操作人部门ID")
    private Long operatorDeptId;
}
