package com.snszyk.zbusiness.assess.dto.linkdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 项目申报关联查询
 */

@Data
public class LinkDataProjectReport {
	@ApiModelProperty("单号")
	private String no;

	/**
	 * 项目名
	 */
	@ApiModelProperty("项目名")
	private String projectName;

	/**
	 * 建设单位名
	 */
	@ApiModelProperty("建设单位名")
	private String constructionUnitName;

	/**
	 * 二级单位名
	 */
	@ApiModelProperty("二级单位名")
	private String secondUnitName;

	@ApiModelProperty("二级单位id")
	private Long secondUnitId;
	/**
	 * 提报人
	 */
	@ApiModelProperty("提报人")
	private String submitName;

	@ApiModelProperty("提报人id")
	private Long submitId;

	/**
	 * 提报机构名
	 */
	@ApiModelProperty("提报机构名")
	private String submitOrgName;

	@ApiModelProperty("提报机构id")
	private Long submitOrgId;

	/**
	 * 提报时间
	 */
	@ApiModelProperty("提报时间")
	private Date submitDate;

	/**
	 * 审批状态
	 */
	@ApiModelProperty("审批状态")
	private String approveStatus;

}
