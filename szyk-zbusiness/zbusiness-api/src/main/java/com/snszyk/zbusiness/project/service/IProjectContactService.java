package com.snszyk.zbusiness.project.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectContactDto;
import com.snszyk.zbusiness.project.vo.ProjectContactVo;

/**
 * IProjectContactService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IProjectContactService extends IBaseCrudService<ProjectContactDto, ProjectContactVo> {

	ProjectContactDto getByProjectId(Long projectId);

	int deleteByProjectId(Long projectId);

    Boolean updateContact(Long projectId, String headPerson, String headPersonTel, String constructionPerson, String constructionPersonTel);
}
