/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 互联网资产操作日志查询对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "互联网资产操作日志查询对象", description = "互联网资产操作日志查询对象")
public class InternetAssetOperationLogVo extends BaseCrudVo {

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 系统名称
     */
    @ApiModelProperty(value = "系统名称")
    private String systemName;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 更新开始时间
     */
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startUpdateTime;

    /**
     * 更新结束时间
     */
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endUpdateTime;
}
