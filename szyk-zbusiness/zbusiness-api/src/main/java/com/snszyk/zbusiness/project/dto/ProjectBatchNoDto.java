/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目批次号实体类
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjectBatchNoDto对象", description = "项目批次号")
public class ProjectBatchNoDto extends BaseCrudDto {

	/**
	* 批次号
	*/
		@ApiModelProperty(value = "批次号")
		private Integer nowNo;


}
