/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 指标数据集实体类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndicatorDatasetVo对象", description = "指标数据集")
public class IndicatorDatasetVo extends BaseCrudSlimVo {

	/**
	 * 系统编号
	 */
	@ApiModelProperty(value = "系统编号")
	private String systemNo;
	/**
	 * 指标编码
	 */
	@ApiModelProperty(value = "指标编码")
	private String indicatorCode;
	/**
	 * 单位编码
	 */
	@ApiModelProperty(value = "单位编码")
	private String orgCode;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;
	/**
	 * 月份
	 */
	@ApiModelProperty(value = "月份")
	private Integer month;

	/**
	 * 指标值
	 */
	@ApiModelProperty(value = "指标值")
	private BigDecimal indicatorValue;
	/**
	 * 指标得分
	 */
	@ApiModelProperty(value = "指标得分")
	private BigDecimal indicatorScore;
	/**
	 * 分子
	 */
	@ApiModelProperty(value = "分子")
	private BigDecimal numerator;
	/**
	 * 分母
	 */
	@ApiModelProperty(value = "分母")
	private BigDecimal denominator;

}
