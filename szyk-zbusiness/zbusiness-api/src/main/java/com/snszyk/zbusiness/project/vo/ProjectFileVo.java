package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectFileVo", description = "项目文档表")
public class ProjectFileVo extends BaseCrudSlimVo {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;
	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;
	/**
	 * 不可删除的
	 */
	@ApiModelProperty("不可删除的，1-是，0-否")
	private Long undeletable;
}
