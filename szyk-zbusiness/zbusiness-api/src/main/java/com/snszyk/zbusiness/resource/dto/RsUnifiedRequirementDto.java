/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 统谈分签采购需求实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsUnifiedRequirementDto对象", description = "统谈分签目录明细")
public class RsUnifiedRequirementDto extends BaseCrudDto {

	/**
	 * 统谈分签目录id
	 */
	@ApiModelProperty(value = "统谈分签目录id")
	private Long catalogId;
	/**
	 * 统谈分签目录名称
	 */
	@ApiModelProperty(value = "统谈分签目录名称")
	private String catalogName;
	/**
	 * 统谈分签目录明细id
	 */
	@ApiModelProperty(value = "统谈分签目录明细id")
	private Long catalogDetailId;
	/**
	 * 需求组织id全路径
	 */
	@ApiModelProperty(value = "需求组织id全路径")
	private String fullOrgId;
	/**
	 * 需求组织名称全路径
	 */
	@ApiModelProperty(value = "需求组织名称全路径")
	private String fullOrgName;
	/**
	 * 需求组织id
	 */
	@ApiModelProperty(value = "需求组织id")
	private Long orgId;
	/**
	 * 需求组织名称
	 */
	@ApiModelProperty(value = "需求组织名称")
	private String orgName;
	/**
	 * 软硬件名称
	 */
	@ApiModelProperty(value = "软硬件名称")
	private String name;
	/**
	 * 类型，数据字典
	 */
	@ApiModelProperty(value = "类型，数据字典")
	private String type;
	/**
	 * 类型，数据字典
	 */
	@ApiModelProperty(value = "类型，数据字典")
	private String typeName;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String specification;
	/**
	 * 需求数量
	 */
	@ApiModelProperty(value = "需求数量")
	private Integer requireQuantity;
	/**
	 * 需求原因
	 */
	@ApiModelProperty(value = "需求原因")
	private String requireReason;
	/**
	 * 计划使用日期
	 */
	@JsonFormat(pattern = "yyyy-MM")
	@DateTimeFormat(pattern = "yyyy-MM")
	@ApiModelProperty(value = "计划使用日期")
	private LocalDate planUseDate;
	/**
	 * 联系人id
	 */
	@ApiModelProperty(value = "联系人id")
	private Long contactPersonId;
	/**
	 * 联系人姓名
	 */
	@ApiModelProperty(value = "联系人姓名")
	private String contactPersonName;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String contactPhone;
	/**
	 * 提交日期
	 */
	@ApiModelProperty(value = "提交日期")
	private LocalDate submitDate;
	/**
	 * 需求状态，数据字典
	 */
	@ApiModelProperty(value = "需求状态，数据字典")
	private String requireStatus;

	/**
	 * 需求状态，数据字典
	 */
	@ApiModelProperty(value = "需求状态，数据字典")
	private String requireStatusName;

	@ApiModelProperty(value = "是否可撤回")
	private boolean recallFlag=false;

	@ApiModelProperty(value = "是否可编辑,删除,提交")
	private boolean editFlag=false;

	@ApiModelProperty(value = "是否可需求完成")
	private boolean completeFlag=false;


	@ApiModelProperty(value = "规格型号附件")
	private List<RsUnifiedCatalogFileAttachDto> specificationFiles = new ArrayList<>();

}
