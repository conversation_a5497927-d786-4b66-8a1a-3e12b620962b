package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "ProjectEvaluateTargetClassifyVo", description = "项目后评价指标分类")
public class ProjectEvaluateTargetClassifyVo extends BaseCrudSlimVo {

	/**
	 * 项目后评价id
	 */
	@ApiModelProperty(value = "项目后评价id")
	private Long projectEvaluateId;
	/**
	 * 指标分类id
	 */
	@ApiModelProperty(value = "指标分类id")
	private Long classifyId;
	/**
	 * 指标分类名称
	 */
	@ApiModelProperty(value = "指标分类名称")
	private String classifyName;
	/**
	 * 是否加分项：0否1是
	 */
	@ApiModelProperty(value = "是否加分项")
	private String addScore;
	/**
	 * 分类分值
	 */
	@ApiModelProperty(value = "分类分值")
	private BigDecimal classifyScore;
	/**
	 * 最终得分
	 */
	@ApiModelProperty(value = "最终得分")
	private BigDecimal finalScore;

}
