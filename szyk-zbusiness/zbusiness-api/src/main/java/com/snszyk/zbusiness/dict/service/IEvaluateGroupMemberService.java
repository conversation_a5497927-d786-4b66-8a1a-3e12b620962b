package com.snszyk.zbusiness.dict.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.dict.dto.EvaluateGroupMemberDto;
import com.snszyk.zbusiness.dict.vo.EvaluateGroupMemberVo;

import java.util.List;

/**
 * IEvaluateGroupMemberService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IEvaluateGroupMemberService extends IBaseCrudService<EvaluateGroupMemberDto, EvaluateGroupMemberVo> {

	List<EvaluateGroupMemberDto> listByGroupId(Long groupId);

	List<EvaluateGroupMemberDto> listByAll();

	int deleteByGroupId(Long groupId);

}
