package com.snszyk.zbusiness.assess.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RwResultDetailVo", description = "考核结果")
public class RwResultDetailVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty(value = "体系id")
	private Long schemeId;
	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;
	/**
	 * 被考核组织id
	 */
	@ApiModelProperty(value = "被考核组织id")
	private Long assessedOrgId;
	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;

}
