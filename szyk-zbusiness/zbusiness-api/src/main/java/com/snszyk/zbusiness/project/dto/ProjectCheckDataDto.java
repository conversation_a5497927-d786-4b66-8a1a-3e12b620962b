package com.snszyk.zbusiness.project.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class ProjectCheckDataDto  {


	@ApiModelProperty(value = "校验结果：true通过false不通过")
	private Boolean checkResult = true;

	@ApiModelProperty(value = "错误数据项")
	private List<ProjectDeleteDto> dataList;


}
