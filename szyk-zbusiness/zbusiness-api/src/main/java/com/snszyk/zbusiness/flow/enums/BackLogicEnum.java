package com.snszyk.zbusiness.flow.enums;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum BackLogicEnum {

	NO(0, "退回后不可提交"),
	YES(1, "退回后可提交"),
	;

	private Integer code;
	private String message;

	BackLogicEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
