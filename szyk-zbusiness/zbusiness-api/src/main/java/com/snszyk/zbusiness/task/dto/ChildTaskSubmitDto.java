/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 子任务提报记录实体类
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ChildTaskSubmitDto对象", description = "子任务提报记录")
public class ChildTaskSubmitDto extends BaseCrudDto {

	/**
	 * 子任务id
	 */
	@ApiModelProperty(value = "子任务id")
	private Long childTaskId;
	/**
	 * 填报记录id
	 */
	@ApiModelProperty(value = "填报记录id")
	private Long submitNoteId;
	/**
	 * 任务下发情况id
	 */
	@ApiModelProperty(value = "任务下发情况id")
	private Long taskIssueId;

}
