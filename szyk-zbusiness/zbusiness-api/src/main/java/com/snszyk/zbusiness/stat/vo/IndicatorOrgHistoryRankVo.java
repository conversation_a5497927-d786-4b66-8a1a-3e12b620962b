/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 单位的历史得分趋势
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
@ApiModel(value = "IndicatorOrgHistoryRankVo", description = "单位的历史得分趋势")
public class IndicatorOrgHistoryRankVo  {

	/**
	 * 系统编号
	 */
	@ApiModelProperty(value = "系统编号")
	private String systemNo;


	/**
	 * 历史得分趋势组织编码
	 */
	@ApiModelProperty(value = "历史得分趋势组织编码")
	private String orgCode;

	/**
	 * 历史得分趋势上级组织编码
	 */
	@ApiModelProperty(value = "历史得分趋势上级组织编码")
	private String upOrgCode;
	/**
	 * 时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JSONField(format = "yyyy-MM-dd")
	@ApiModelProperty(value = "时间")
	@NotNull
	private LocalDate time;


	@ApiModelProperty(hidden = true)
	private Integer year;

	@ApiModelProperty(hidden = true)
	private Integer month;



	@ApiModelProperty(hidden = true)
	private Integer orgCount;



}
