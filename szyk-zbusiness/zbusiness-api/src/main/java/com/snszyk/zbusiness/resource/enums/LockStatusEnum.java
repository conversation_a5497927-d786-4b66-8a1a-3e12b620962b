package com.snszyk.zbusiness.resource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2023-12-20-15:40
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum LockStatusEnum {
	/**
	 *
	 */
	LOCK_STATUS_1("1", "未锁定"),
	/**
	 *
	 */
	LOCK_STATUS_2("2", "已锁定"),
	/**
	 *
	 */
	LOCK_STATUS_3("3", "申请解锁"),
	;
	private String code;
	private String message;
}
