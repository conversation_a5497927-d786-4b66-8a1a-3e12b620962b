package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "RsEquipmentClassifyVo", description = "设备台账")
public class RsEquipmentClassifyVo extends BaseCrudSlimVo {

	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类id")
	private Long parentId;
	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;

}
