package com.snszyk.zbusiness.project.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TechProjectTypeEnum {

	NO_TECH(0, "非科技项目"),

	TECH(1, "科技项目"),
	;
	Integer dictKey;

	String dictValue;

	@Override
	public String toString() {
		return this.dictValue;
	}

	public static String getValue(String key) {
		if (ObjectUtil.isEmpty(key)) {
			return null;
		}
		for (TechProjectTypeEnum value : TechProjectTypeEnum.values()) {
			if (value.getDictKey().equals(key)) {
				return value.getDictValue();
			}
		}
		return null;
	}

}
