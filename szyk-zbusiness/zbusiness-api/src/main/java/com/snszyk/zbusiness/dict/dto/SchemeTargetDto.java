package com.snszyk.zbusiness.dict.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class SchemeTargetDto extends BaseCrudDto {

	/**
	 * 体系编号
	 */
	@ApiModelProperty("体系编号")
	private String schemeNo;
	/**
	 * 体系名称
	 */
	@ApiModelProperty("体系名称")
	private String schemeName;
	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@ApiModelProperty("所属组织名称")
	private String companyName;
	/**
	 * 总分
	 */
	@ApiModelProperty("总分")
	private BigDecimal totalScore;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态")
	private String schemeStatus;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty("状态名称")
	private String schemeStatusName;
	/**
	 * 评价指标数据
	 */
	@ApiModelProperty("评价指标数据")
	private List<SchemeTargetCommonDto> list;

	/**
	 *创建人姓名
	 */
	@ApiModelProperty("创建人姓名")
	private String createUserName;

}
