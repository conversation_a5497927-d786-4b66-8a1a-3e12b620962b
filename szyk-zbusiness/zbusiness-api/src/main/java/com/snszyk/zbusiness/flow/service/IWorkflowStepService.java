/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.flow.dto.WorkflowStepDto;
import com.snszyk.zbusiness.flow.vo.WorkflowStepVo;

import java.util.List;

/**
 * 流程节点设置表 服务类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface IWorkflowStepService extends IBaseCrudService<WorkflowStepDto, WorkflowStepVo> {

    Boolean deleteByFlowId(Long flowId);

	Boolean addSteps(Long flowId, List<WorkflowStepVo> steps);

	List<WorkflowStepDto> fetchByFlowId(Long flowId);

	WorkflowStepDto queryStep(Long flowId, Integer flowSort);

	List<WorkflowStepDto> queryStepByUnitLevel(Long id, Integer unitLevel);

	List<WorkflowStepDto> queryStepLeUnitLevel(Long id, Integer unitLevel);

    List<WorkflowStepDto> queryByIds(List<Long> flowIdList,Integer level);

	List<WorkflowStepDto> queryByIds(List<Long> flowIdList);
}
