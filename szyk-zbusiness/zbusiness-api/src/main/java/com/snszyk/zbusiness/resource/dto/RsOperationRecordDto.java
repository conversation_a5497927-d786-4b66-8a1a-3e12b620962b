package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作记录数据传输对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录数据传输对象", description = "操作记录数据传输对象")
public class RsOperationRecordDto extends BaseCrudDto {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作类型名称
     */
    @ApiModelProperty(value = "操作类型名称")
    private String operationTypeName;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 组织全路径ID
     */
    @ApiModelProperty(value = "组织全路径ID")
    private String fullOrgId;

    /**
     * 组织全路径名称
     */
    @ApiModelProperty(value = "组织全路径名称")
    private String fullOrgName;

    /**
     * 变更前数据
     */
    @ApiModelProperty(value = "变更前数据")
    private String oldData;

    /**
     * 变更后数据
     */
    @ApiModelProperty(value = "变更后数据")
    private String newData;

    /**
     * 变更字段列表
     */
    @ApiModelProperty(value = "变更字段列表")
    private String changeFields;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作IP地址
     */
    @ApiModelProperty(value = "操作IP地址")
    private String ipAddress;

    /**
     * 用户代理
     */
    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    /**
     * 变更详情列表
     */
    @ApiModelProperty(value = "变更详情列表")
    private List<RsOperationRecordDetailDto> detailList;

    /**
     * 扩展字段数据
     */
    @ApiModelProperty(value = "扩展字段数据")
    private Map<String, Object> extensionData;

    /**
     * 扩展字段显示值
     */
    @ApiModelProperty(value = "扩展字段显示值")
    private Map<String, String> extensionDisplayValues;
}
