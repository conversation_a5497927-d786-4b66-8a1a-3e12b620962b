package com.snszyk.zbusiness.project.enums;

/**
 * 审核状态
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum EvaluateStatusEnum {

	/**
	 * 未开始   创建之后   未开始状态和评价中 可以上传自评报告
	 */
	NOT_STARTED("1", "未开始"),

	/**
	 * 评价中  上传自评报告后变更为评价中状态
	 */
	EVALUATION("2", "评价中"),

	/**
	 * 已完成   手动点击完成
	 */
	OVER("3", "已完成"),
	;

	private String code;
	private String message;

	EvaluateStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
