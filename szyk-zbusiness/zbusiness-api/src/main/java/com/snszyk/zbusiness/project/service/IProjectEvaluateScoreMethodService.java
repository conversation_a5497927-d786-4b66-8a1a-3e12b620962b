package com.snszyk.zbusiness.project.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.project.dto.ProjectEvaluateScoreMethodDto;
import com.snszyk.zbusiness.project.vo.ProjectEvaluateScoreMethodVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * IProjectEvaluateScoreMethodService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface IProjectEvaluateScoreMethodService extends IBaseCrudService<ProjectEvaluateScoreMethodDto, ProjectEvaluateScoreMethodVo> {

	List<ProjectEvaluateScoreMethodDto> listByEvaluateId(Long projectEvaluateId);

	boolean updateByScore(Long id, BigDecimal score, String scoreCause, Long scorePersonId, String scorePersonName);

	int deleteByEvaluateId(Long projectEvaluateId);

	List<ProjectEvaluateScoreMethodDto> listByEvaluateIdAndTarget(Long id, List<Long> targetIdList);
}
