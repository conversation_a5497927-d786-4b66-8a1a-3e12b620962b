/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录率和活跃率统计角色配置实体类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "IndicatorStatRoleVo对象", description = "登录率和活跃率统计角色配置")
public class IndicatorStatRoleVo extends BaseCrudVo {

	/**
	 * 指标编码
	 */
	@ApiModelProperty(value = "指标编码")
	private String indicatorCode;
	/**
	 * 角色id
	 */
	@ApiModelProperty(value = "角色id")
	private Long roleId;
	/**
	 * 角色类型
	 */
	private String roleType;

}
