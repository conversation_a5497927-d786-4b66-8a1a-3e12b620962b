package com.snszyk.zbusiness.flow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @atuthor
 * @date 2023/3/27
 * @apiNote
 */
@Data
@ApiModel(value = "ReturnFlowVo")
@Accessors(chain = true)
public class ReturnFlowVo {

	@ApiModelProperty(value = "审批流id")
	private List<Long> instanceIds;

	@ApiModelProperty(value = "审核意见")
	private String operateRemark;

	@ApiModelProperty(value = "节点顺序")
	private Integer flowSort;



}
