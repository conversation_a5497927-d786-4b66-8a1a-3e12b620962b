package com.snszyk.zbusiness.project.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "ProjectBaseVo", description = "项目基本信息表")
public class ProjectBaseVo extends BaseCrudSlimVo {
	/**
	 * 立项编号
	 */
	@ApiModelProperty(value = "立项编号")
	private String approvalNo;
	/**
	 * 项目编号
	 */
	@ApiModelProperty(value = "项目编号")
	private String projectNo;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String projectName;
	/**
	 * 公司id
	 */
	@ApiModelProperty(value = "公司id")
	private Long companyId;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;
	/**
	 * 建设单位id
	 */
	@ApiModelProperty(value = "建设单位id")
	private Long constructionUnitId;
	/**
	 * 建设单位名称
	 */
	@ApiModelProperty(value = "建设单位名称")
	private String constructionUnitName;
	/**
	 * 年份
	 */
	@ApiModelProperty(value = "年份")
	private Integer year;

	@ApiModelProperty(value = "批次")
	private String batchNo;

	@ApiModelProperty(value = "批次")
	private String batchName;

	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类")
	private String specialtyClassification;
	/**
	 * 专业分类，字典sc
	 */
	@ApiModelProperty(value = "专业分类-缩写",required = true)
	private String specialtyClassAbbreviation;

	/**
	 * 项目分类，字典pc
	 */
	@ApiModelProperty(value = "项目分类")
	private String projectClassification;
	/**
	 * 列支渠道，字典dc
	 */
	@ApiModelProperty(value = "列支渠道")
	private String distributionChannel;
	/**
	 * 投资主体id
	 */
	@ApiModelProperty(value = "投资主体id")
	private Long investmentSubjectId;

	@ApiModelProperty(value = "审批流id")
	private Long instanceId;
	/**
	 * 投资主体名称
	 */
	@ApiModelProperty(value = "投资主体名称")
	private String investmentSubjectName;
	/**
	 * 项目预计总投资
	 */
	@ApiModelProperty(value = "项目预计总投资")
	private BigDecimal totalEstimate;
	/**
	 * 项目本年度投资
	 */
	@ApiModelProperty(value = "项目本年度投资")
	private BigDecimal currentInvestment;
	/**
	 * 牵头部门id
	 */
	@ApiModelProperty(value = "牵头部门id")
	private Long leadOrgId;
	/**
	 * 牵头部门名称
	 */
	@ApiModelProperty(value = "牵头部门名称")
	private String leadOrgName;
	/**
	 * 计划招标日期
	 */
	@ApiModelProperty(value = "计划招标日期")
	private Date planTenderDate;
	/**
	 * 计划开始日期
	 */
	@ApiModelProperty(value = "计划开始日期")
	private Date planStartDate;
	/**
	 * 计划上线日期
	 */
	@ApiModelProperty(value = "计划上线日期")
	private Date planCompleteDate;
	/**
	 * 审查状态，字典review_status
	 */
	@ApiModelProperty(value = "审查状态")
	private String reviewStatus;
	/**
	 * 复审状态
	 */
	@ApiModelProperty(value = "复审状态")
	private String retrialStatus;
	/**
	 * 提报时间
	 */
	@ApiModelProperty(value = "提报时间")
	private Date submitTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 项目介绍
	 */
	@ApiModelProperty(value = "项目介绍")
	private ProjectIntroduceVo introduceVo;
	/**
	 * 项目联系人
	 */
	@ApiModelProperty(value = "项目联系人")
	private ProjectContactVo contactVo;
	/**
	 * 项目成员
	 */
	@ApiModelProperty(value = "项目成员集合")
	private List<ProjectMemberVo> memberList;
	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private List<Long> fileList;

	@ApiModelProperty(value = "集团审查结论")
	private String corpApproveResult;

	/**
	 * 集团审查意见
	 */
	@ApiModelProperty(value = "集团审查意见")
	private String corpApproveRemark;

	/**
	 * 二级审查意见
	 */
	@ApiModelProperty(value = "二级审查意见")
	private String secApproveRemark;

	@ApiModelProperty(value = "计划资金")
	private BigDecimal planFunds;

	/**
	 * 合同金额
	 */
	@ApiModelProperty(value = "合同金额")
	private BigDecimal contractAmount;

	private String ancestors;

	private Integer approveLevel;

	@ApiModelProperty(value = "补充材料状态  0.-- 1.无需提报 2.待提交 3.已提交 4.已归档")
	private String supplementStatus;
	private String currStepName;

	private Boolean cancelFlag;
	/**
	 * 专家评审信息
	 */
	@ApiModelProperty(value = "专家评审信息")
	private String expertReview;


	@ApiModelProperty(value = "补充材料上传时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime supplementTime;
	@ApiModelProperty(value = "作废时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime nullifyTime;

	@ApiModelProperty(value = "是否需要日志")
	private Boolean doLog=false;

}
