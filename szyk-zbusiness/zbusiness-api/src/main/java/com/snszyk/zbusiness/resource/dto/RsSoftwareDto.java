package com.snszyk.zbusiness.resource.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.zbusiness.project.dto.SzykAttachDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class RsSoftwareDto extends BaseCrudDto {


	/**
	 * 所属组织id
	 */

	@ApiModelProperty(value="所属组织id")
	private Long orgId;
	/**
	 * 所属组织全路径id
	 */

	@ApiModelProperty(value="所属组织全路径id")
	private String fullOrgId;
	/**
	 * 所属组织名称
	 */

	@ApiModelProperty(value="所属组织名称")
	private String orgName;
	/**
	 * 所属组织全路径名称
	 */

	@ApiModelProperty(value="所属组织全路径名称")
	private String fullOrgName;
	/**
	 * 软件分类，数据字典
	 */
	@ApiModelProperty(value = "软件分类")
	private String softwareClassify;
	/**
	 * 软件分类，数据字典
	 */
	@ApiModelProperty(value = "软件分类名称")
	private String softwareClassifyName;
	/**
	 * 软件编号
	 */
	@ApiModelProperty(value = "软件编号")
	private String softwareNo;
	/**
	 * 软件名称
	 */
	@ApiModelProperty(value = "软件名称")
	private String softwareName;
	/**
	 * 软件类型，数据字典
	 */
	@ApiModelProperty(value = "软件类型")
	private String softwareType;
	/**
	 * 软件类型，数据字典
	 */
	@ApiModelProperty(value = "软件类型名称")
	private String softwareTypeName;
	/**
	 * 软件版本
	 */
	@ApiModelProperty(value = "软件版本")
	private String softwareVersion;
	/**
	 * 采购时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "采购时间")
	private Date purchaseTime;
	/**
	 * 采购金额
	 */
	@ApiModelProperty(value = "采购金额")
	private BigDecimal purchaseAmount;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 供应商名称
	 */
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;
	/**
	 * 供应商编号
	 */
	@ApiModelProperty(value = "供应商编号")
	private String supplierNo;
	/**
	 * 许可类型，数据字典
	 */
	@ApiModelProperty(value = "许可类型")
	private String licenseType;
	/**
	 * 许可类型，数据字典
	 */
	@ApiModelProperty(value = "许可类型名称")
	private String licenseTypeName;
	/**
	 * 许可数量
	 */
	@ApiModelProperty(value = "许可数量")
	private Integer licenseQuantity;
	/**
	 * 序列号
	 */
	@ApiModelProperty(value = "序列号")
	private String serialNumber;
	/**
	 * 许可期限开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "许可期限开始时间")
	private Date licenseStartTime;
	/**
	 * 许可期限截至时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "许可期限截至时间")
	private Date licenseEndTime;
	/**
	 * 负责人id
	 */
	@ApiModelProperty(value = "负责人id")
	private Long headPersonId;
	/**
	 * 负责人姓名
	 */
	@ApiModelProperty(value = "负责人姓名")
	private String headPersonName;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "负责人联系方式")
	private String headPersonTel;
	/**
	 * 软件状态
	 */
	@ApiModelProperty(value = "软件状态")
	private String softwareStatus;
	/**
	 * 软件状态
	 */
	@ApiModelProperty(value = "软件状态名称")
	private String softwareStatusName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String softwareRemark;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private String updateUserName;

	/**
	 * 合同
	 */
	@ApiModelProperty(value = "合同")
	private List<SzykAttachDto> contractList = new ArrayList<>();
	/**
	 * 授权证书
	 */
	@ApiModelProperty(value = "授权证书")
	private List<SzykAttachDto> certificateList = new ArrayList<>();

	/**
	 * 是否永久有效
	 */
	@ApiModelProperty(value = "是否永久有效")
	private Boolean priceValidity=false;
}
