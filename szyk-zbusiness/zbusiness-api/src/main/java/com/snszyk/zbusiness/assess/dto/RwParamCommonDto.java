package com.snszyk.zbusiness.assess.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel
public class RwParamCommonDto extends BaseCrudDto {


	/**
	 * 发起组织id
	 */
	@ApiModelProperty(value = "发起组织id")
	private Long initiateOrgId;
	/**
	 * 发起组织名称
	 */
	@ApiModelProperty(value = "发起组织名称")
	private String initiateOrgName;

	/**
	 * 考核周期明细id
	 */
	@ApiModelProperty(value = "考核周期明细id")
	private Long rwPeriodDetailId;
	/**
	 * 考核期间名称
	 */
	@ApiModelProperty(value = "考核期间名称")
	private String periodName;


}
