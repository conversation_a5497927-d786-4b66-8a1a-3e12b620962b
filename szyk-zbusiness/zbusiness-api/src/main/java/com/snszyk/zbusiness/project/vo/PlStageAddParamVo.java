/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目阶段添加参数
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlStageVo对象", description = "项目库项目阶段表")
public class PlStageAddParamVo extends BaseCrudSlimVo {

	@ApiModelProperty(value = "字典的值")
	private String dictValue;


	@ApiModelProperty(value = "已经选择的阶段key", required = false)
	private List<String> haveChooseStageKeyList;

	@ApiModelProperty(value = "项目id", required = false)
	private Long projectId;

	@ApiModelProperty(value = "进度id", required = false)
	private Long projectProgressId;

}
