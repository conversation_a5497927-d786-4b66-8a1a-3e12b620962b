package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel(value = "DictPhaseVo", description = "项目阶段字典表")
public class DictPhaseVo extends BaseCrudSlimVo {

	/**
	 * 字典值
	 */
	@ApiModelProperty("字典值")
	private String dictKey;
	/**
	 * 字典名称
	 */
	@ApiModelProperty("字典名称")
	private String dictValue;
	/**
	 * 是否必要节点，业务字典necessary_type
	 */
	@ApiModelProperty("是否必要节点")
	private String necessaryType;

	@ApiModelProperty("输出文档")
	private String outputDocument;


	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Integer sort;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;
	/**
	 * 状态，业务字典enable_type
	 */
	@ApiModelProperty("状态")
	private String dictStatus;
}
