package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "ProjectDispatchVo", description = "项目调度表")
public class ProjectDispatchVo extends BaseCrudSlimVo {

	/**
	 * 调度单号
	 */
	@ApiModelProperty(value = "调度单号")
	private String dispatchNo;
	/**
	 * 调度周期明细id
	 */
	@ApiModelProperty(value = "调度周期明细id")
	private Long dispatchPeriodDetailId;
	/**
	 * 期间名称
	 */
	@ApiModelProperty(value = "期间名称")
	private String periodName;
	/**
	 * 周期开始时间
	 */
	@ApiModelProperty(value = "周期开始时间")
	private Date periodStartTime;
	/**
	 * 周期结束时间
	 */
	@ApiModelProperty(value = "周期结束时间")
	private Date periodEndTime;
	/**
	 * 调度截止日期
	 */
	@ApiModelProperty(value = "调度截止日期")
	private Date dispatchDeadline;
	/**
	 * 下发单位id
	 */
	@ApiModelProperty(value = "下发单位id")
	private Long sendCompanyId;
	/**
	 * 下发单位名称
	 */
	@ApiModelProperty(value = "下发单位名称")
	private String sendCompanyName;
	/**
	 * 调度状态，数据字典
	 */
	@ApiModelProperty(value = "调度状态，数据字典")
	private String dispatchStatus;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 明细数据
	 */
	@ApiModelProperty(value = "明细数据")
	private List<ProjectDispatchDetailVo> list;

}
