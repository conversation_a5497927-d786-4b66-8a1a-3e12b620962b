/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 基础对象
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@ApiModel(value = "TaskVo对象", description = "任务管理")
public class BaseObjectVo implements Serializable {


	@NotNull(message = "id不能为空")
	@ApiModelProperty(value = "主键id",required = true)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty(value = "填报说明")
	private String submitRemark;



}
