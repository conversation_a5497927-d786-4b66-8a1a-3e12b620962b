/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 驾驶舱项目投资评估
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BoardProjectInvestmentDto", description = "驾驶舱项目投资评估")
public class BoardProjectInvestmentDto {


	@ApiModelProperty(value = "投资估算")
	private BigDecimal projectInvestmentSum;

	@ApiModelProperty(value = "列支渠道的项目数")
	private List<EchartCircleDto> channeList;

}
