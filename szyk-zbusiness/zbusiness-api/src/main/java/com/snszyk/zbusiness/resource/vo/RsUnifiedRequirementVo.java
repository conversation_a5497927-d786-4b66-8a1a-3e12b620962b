/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 统谈分签采购需求实体类
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RsUnifiedRequirementVo对象", description = "统谈分签采购需求")
public class RsUnifiedRequirementVo extends BaseCrudSlimVo {



	/**
	 * 统谈分签目录id
	 */
	@ApiModelProperty(value = "统谈分签目录id",required = true)
	private Long catalogId;
	/**
	 * 统谈分签目录名称
	 */
	@ApiModelProperty(value = "统谈分签目录名称",required = true)
	private String catalogName;
	/**
	 * 统谈分签目录明细id
	 */
	@ApiModelProperty(value = "统谈分签目录明细id",required = true)
	private Long catalogDetailId;
	/**
	 * 需求组织id全路径
	 */
	@ApiModelProperty(value = "需求组织id全路径")
	private String fullOrgId;
	/**
	 * 需求组织名称全路径
	 */
	@ApiModelProperty(value = "需求组织名称全路径")
	private String fullOrgName;
	/**
	 * 需求组织id
	 */
	@ApiModelProperty(value = "需求组织id")
	private Long orgId;
	/**
	 * 需求组织名称
	 */
	@ApiModelProperty(value = "需求组织名称")
	private String orgName;
	/**
	 * 软硬件名称
	 */
	@ApiModelProperty(value = "软硬件名称",required = true)
	private String name;
	/**
	 * 类型，数据字典
	 */
	@ApiModelProperty(value = "类型，数据字典",required = true)
	private String type;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号",required = true)
	private String specification;
	/**
	 * 需求数量
	 */
	@ApiModelProperty(value = "需求数量",required = true)
	private Integer requireQuantity;
	/**
	 * 需求原因
	 */
	@ApiModelProperty(value = "需求原因",required = true)
	private String requireReason;
	/**
	 * 计划使用日期
	 */
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd",timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "计划使用日期",required = true)
	private LocalDate planUseDate;
	/**
	 * 联系人id
	 */
	@ApiModelProperty(value = "联系人id")
	private Long contactPersonId;
	/**
	 * 联系人姓名
	 */
	@ApiModelProperty(value = "联系人姓名")
	private String contactPersonName;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String contactPhone;
	/**
	 * 提交日期
	 */
	@ApiModelProperty(value = "提交日期")
	private LocalDate submitDate;
	/**
	 * 需求状态，数据字典
	 */
	@ApiModelProperty(value = "需求状态，数据字典")
	private String requireStatus;


}
