/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.resource.dto.FieldChangeDetailDto;
import com.snszyk.zbusiness.resource.dto.InternetAssetOperationLogDto;
import com.snszyk.zbusiness.resource.vo.InternetAssetOperationLogVo;

import java.util.List;

/**
 * 互联网资产操作日志服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface IInternetAssetOperationLogService {

    /**
     * 分页查询操作日志
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<InternetAssetOperationLogDto> page(InternetAssetOperationLogVo vo);

    /**
     * 根据业务ID查询操作日志列表
     *
     * @param businessId 业务数据ID
     * @return 操作日志列表
     */
    List<InternetAssetOperationLogDto> listByBusinessId(Long businessId);

    /**
     * 获取操作日志详情
     *
     * @param id 操作日志ID
     * @return 操作日志详情
     */
    InternetAssetOperationLogDto getDetail(Long id);

    /**
     * 获取字段变更详情
     *
     * @param id 操作日志ID
     * @return 字段变更详情列表
     */
    List<FieldChangeDetailDto> getChangeDetails(Long id);

    /**
     * 创建操作日志记录
     *
     * @param businessId 业务数据ID
     * @param businessName 业务数据名称
     * @param operationType 操作类型
     * @param description 操作描述
     * @param oldData 操作前数据
     * @param newData 操作后数据
     * @param orgId 主管单位ID
     * @param orgName 主管单位名称
     * @return 是否成功
     */
    boolean createOperationLog(Long businessId, String businessName, String operationType, 
                              String description, Object oldData, Object newData, 
                              Long orgId, String orgName);
}
