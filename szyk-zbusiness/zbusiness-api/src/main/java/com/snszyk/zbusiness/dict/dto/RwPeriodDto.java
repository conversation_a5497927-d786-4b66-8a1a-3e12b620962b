package com.snszyk.zbusiness.dict.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class RwPeriodDto extends BaseCrudDto {

	/**
	 * 周期类型，字典
	 */
	@ApiModelProperty("周期类型")
	private String periodType;
	/**
	 * 周期类型，字典
	 */
	@ApiModelProperty("周期类型名称")
	private String periodTypeName;
	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Integer year;
	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@ApiModelProperty("所属组织名称")
	private String companyName;
	/**
	 * 开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("开始时间")
	private Date startTime;
	/**
	 * 结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("结束时间")
	private Date endTime;
	/**
	 * 周期状态：0全部开启1部分关闭3全部关闭
	 */
	@ApiModelProperty("周期状态")
	private String periodStatus;
	/**
	 * 周期状态：0全部开启1部分关闭3全部关闭
	 */
	@ApiModelProperty("周期状态名称")
	private String periodStatusName;
	/**
	 * 明细数据
	 */
	@ApiModelProperty("明细数据")
	private List<RwPeriodDetailDto> list;
	/**
	 * 是否被引用 true 是 false 否
	 */
	@ApiModelProperty("是否被引用 true 是 false 否")
	private boolean flag = false;
	/**
	 * 创建人名称
	 */
	@ApiModelProperty("创建人名称")
	private String createUserName;
}
