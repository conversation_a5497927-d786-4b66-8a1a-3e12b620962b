/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.resource.dto.RsEquipmentSystemDto;
import com.snszyk.zbusiness.resource.dto.RsSystemServerDto;
import com.snszyk.zbusiness.resource.vo.RsSystemServerVo;

import java.util.List;

/**
 * 信息系统-服务器信息 服务类
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
public interface IRsSystemServerService extends IBaseCrudService<RsSystemServerDto, RsSystemServerVo> {

	/**
	 * 根据系统id查询
	 * @param systemId
	 * @return
	 */
	List<RsSystemServerDto> listBySytemId(Long systemId);

	boolean batchSaveServer(List<RsSystemServerVo> addServerList);

	/**
	 * 批量更新
	 * @param updateServerList
	 * @return
	 */
	boolean batchUpdateServer(List<RsSystemServerVo> updateServerList);

	/**
	 * 批量删除
	 * @param ids
	 * @return
	 */
	boolean deleteByIds(List<Long> ids);

	boolean deleteBySystemId(Long systemId
	);

	boolean deleteBySystemIds(List<Long> systemIds);

	/**
	 * 查询设备关联的系统
	 * @param equipmentId
	 * @return
	 */
	List<RsEquipmentSystemDto> linkSystemByEqId(Long equipmentId);
}
