package com.snszyk.zbusiness.resource.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作记录分页查询对象
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录分页查询对象", description = "操作记录分页查询对象")
public class RsOperationRecordPageVo extends BaseCrudSlimVo {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务数据名称（系统名称）
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 更新开始时间
     */
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startUpdateTime;

    /**
     * 更新结束时间
     */
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endUpdateTime;

    /**
     * 单位范围
     */
    @ApiModelProperty(value = "单位范围 1本单位的 2 本单位及权属单位的")
    private Integer unitRange = 1;
}
