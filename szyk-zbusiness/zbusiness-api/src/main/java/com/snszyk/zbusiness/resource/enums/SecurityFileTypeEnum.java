package com.snszyk.zbusiness.resource.enums;

/**
 * 备案文件类型
 * 1 等保备案证明
 * 2等保测评报告
 *
 * @create 2022/8/1
 */
public enum SecurityFileTypeEnum {

	FILE_1("1", "等保备案证明"),
	FILE_2("2", "等保测评报告");

	private String code;
	private String message;

	SecurityFileTypeEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
