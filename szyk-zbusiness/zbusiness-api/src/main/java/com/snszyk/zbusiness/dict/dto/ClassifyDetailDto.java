/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 知识分类实体类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ClassifyDetailDto对象", description = "知识分类")
public class ClassifyDetailDto extends BaseCrudDto {

	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;
	/**
	 * 收集方式
	 */
	@ApiModelProperty(value = "收集方式")
	private String collectType;
	/**
	 * 关联菜单id
	 */
	@ApiModelProperty(value = "关联菜单id")
	private List<String> linkMenuIdList;
	/**
	 * 关联菜单名称
	 */
	@ApiModelProperty(value = "关联菜单名称")
	private List<String> linkMenuNameList;
	/**
	 * 状态：0启用1停用
	 */
	@ApiModelProperty(value = "状态：0启用1停用")
	private String classifyStatus;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 序号
	 */
	@ApiModelProperty(value = "序号")
	private Integer sort;

}
