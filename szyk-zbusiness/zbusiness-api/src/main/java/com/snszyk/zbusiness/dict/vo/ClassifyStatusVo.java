/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 知识分类实体类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@ApiModel(value = "ClassifyStatusVo对象", description = "知识分类分页")
@Accessors(chain = true)
public class ClassifyStatusVo extends BaseCrudSlimVo {

	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "启用标识（字典，0-启用，1-停用）")
	String enableType;

}
