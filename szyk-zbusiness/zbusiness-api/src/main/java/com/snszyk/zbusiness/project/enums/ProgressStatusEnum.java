package com.snszyk.zbusiness.project.enums;

/**
 * 项目阶段审核状态
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum ProgressStatusEnum {

	/**
	 * 待提报
	 */
	TO_BE_REPORTED("0", "待提报"),

	/**
	 * 审批中
	 */
	UNDER_REVIEW("1", "审批中"),

	/**
	 * 通过
	 */
	PASS("2", "通过"),

	/**
	 * 退回
	 */
	RETURN("3", "退回"),
	CANCEL("4", "已撤回"),
	;

	private String code;
	private String message;

	ProgressStatusEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}
