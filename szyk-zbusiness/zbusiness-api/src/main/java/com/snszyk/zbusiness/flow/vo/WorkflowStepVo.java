/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程节点设置表实体类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WorkflowStepVo对象", description = "流程节点设置表")
public class WorkflowStepVo extends BaseCrudSlimVo {


	/**
	 * 节点名称
	 */
	@ApiModelProperty(value = "节点名称")
	private String stepName;
	/**
	 * 节点类型：0用户1角色2部门3岗位4其他
	 */
	@ApiModelProperty(value = "节点类型：0用户1角色2部门3岗位4其他")
	private Integer stepType;
	/**
	 * 操作对象id
	 */
	@ApiModelProperty(value = "操作对象id")
	private String operatorId;
	/**
	 * 节点审批模式：COUNTERSIGN会签；ONESIGN或签
	 */
	@ApiModelProperty(value = "节点审批模式：COUNTERSIGN会签；ONESIGN或签")
	private String stepMode;
	/**
	 * 是否提交人自选：0否1是
	 */
	@ApiModelProperty(value = "是否提交人自选：0否1是")
	private Integer submitChoose;
	/**
	 * 筛选部门：0不筛选1本部门2关联上级
	 */
	@ApiModelProperty(value = "筛选部门：0不筛选1本部门2关联上级")
	private Integer screenDept;

	/**
	 * 提报前事件
	 */
	@ApiModelProperty(value = "提报前事件：0无1有")
	private Integer beforeEvent;

	@ApiModelProperty(value = "操作渠道：0本系统1OA")
	private Integer operatorChannel;

	@ApiModelProperty(value = "单位范围")
	private Integer unitLevel;

	@ApiModelProperty(value = "是否文审")
	private Boolean enableDocReview;

	@ApiModelProperty(value = "退回逻辑：0退回后不可提交1退回后可提交")
	private Integer backLogic;


}
