/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目库项目联系人表实体类
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlContactVo对象", description = "项目库项目联系人表")
public class PlContactVo extends BaseCrudSlimVo {

	/**
	 * 项目id
	 */
	@ApiModelProperty(value = "项目id")
	private Long projectId;
	/**
	 * 项目负责人
	 */
	@ApiModelProperty(value = "项目负责人")
	private String headPerson;
	/**
	 * 负责人联系方式
	 */
	@ApiModelProperty(value = "项目负责人联系方式")
	private String headPersonTel;
	/**
	 * 建设单位联系人
	 */
	@ApiModelProperty(value = "建设单位联系人")
	private String constructionPerson;
	/**
	 * 建设单位联系方式
	 */
	@ApiModelProperty(value = "建设单位联系方式")
	private String constructionPersonTel;
	/**
	 * 合作单位联系人
	 */
	@ApiModelProperty(value = "合作单位联系人")
	private String cooperationPerson;
	/**
	 * 合作单位联系方式
	 */
	@ApiModelProperty(value = "合作单位联系方式")
	private String cooperationPersonTel;
	/**
	 * 监理单位联系人
	 */
	@ApiModelProperty(value = "监理单位联系人")
	private String supervisionPerson;
	/**
	 * 监理单位联系方式
	 */
	@ApiModelProperty(value = "监理单位联系方式")
	private String supervisionPersonTel;


}
