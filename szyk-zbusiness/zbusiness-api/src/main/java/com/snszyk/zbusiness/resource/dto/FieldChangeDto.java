/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.resource.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字段变更DTO
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@ApiModel(value = "字段变更DTO", description = "字段变更数据传输对象")
public class FieldChangeDto {

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段标签
     */
    @ApiModelProperty(value = "字段标签")
    private String fieldLabel;

    /**
     * 变更前值
     */
    @ApiModelProperty(value = "变更前值")
    private String oldValue;

    /**
     * 变更后值
     */
    @ApiModelProperty(value = "变更后值")
    private String newValue;

    /**
     * 变更前显示值
     */
    @ApiModelProperty(value = "变更前显示值")
    private String oldDisplayValue;

    /**
     * 变更后显示值
     */
    @ApiModelProperty(value = "变更后显示值")
    private String newDisplayValue;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 是否有变更
     */
    @ApiModelProperty(value = "是否有变更")
    private Boolean hasChanged;

    public FieldChangeDto() {
    }

    public FieldChangeDto(String fieldName, String fieldLabel, String oldValue, String newValue) {
        this.fieldName = fieldName;
        this.fieldLabel = fieldLabel;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.oldDisplayValue = oldValue;
        this.newDisplayValue = newValue;
        this.hasChanged = !java.util.Objects.equals(oldValue, newValue);
    }

    public FieldChangeDto(String fieldName, String fieldLabel, String oldValue, String newValue, 
                         String oldDisplayValue, String newDisplayValue) {
        this.fieldName = fieldName;
        this.fieldLabel = fieldLabel;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.oldDisplayValue = oldDisplayValue;
        this.newDisplayValue = newDisplayValue;
        this.hasChanged = !java.util.Objects.equals(oldValue, newValue);
    }
}
