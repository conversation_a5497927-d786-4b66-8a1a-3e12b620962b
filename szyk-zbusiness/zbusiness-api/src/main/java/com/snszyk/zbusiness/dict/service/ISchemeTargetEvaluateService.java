package com.snszyk.zbusiness.dict.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.dict.dto.SchemeTargetClassifyDto;
import com.snszyk.zbusiness.dict.dto.SchemeTargetEvaluateDto;
import com.snszyk.zbusiness.dict.vo.SchemeTargetEvaluateVo;

import java.util.List;

/**
 * ISchemeTargetEvaluateService
 *
 * <AUTHOR> wangbin
 * @create 2022/8/1
 */
public interface ISchemeTargetEvaluateService extends IBaseCrudService<SchemeTargetEvaluateDto, SchemeTargetEvaluateVo> {

	List<SchemeTargetEvaluateDto> listBySchemeId(Long schemeId);

	List<SchemeTargetEvaluateDto> listAll();

	int deleteBySchemeId(Long schemeId);
}
