/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务删除
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@ApiModel(value = "任务删除", description = "任务删除")
public class TaskDeleteVo {

    @ApiModelProperty("任务ID列表")
    @NotEmpty(message = "任务ID列表不能为空")
    private List<Long> taskIds;
}
