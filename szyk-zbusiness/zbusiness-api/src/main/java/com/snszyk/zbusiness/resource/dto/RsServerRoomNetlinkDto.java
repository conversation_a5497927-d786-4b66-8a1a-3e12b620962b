package com.snszyk.zbusiness.resource.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 数据中心机房-互联链路
 */
@Data
@ApiModel
public class RsServerRoomNetlinkDto extends BaseCrudDto {

	/**
	 * 机房id
	 */
	@ApiModelProperty(value = "机房id")
	private Long roomId;
	/**
	 * 链路类型，数据字典
	 */
	@ApiModelProperty(value = "链路类型，数据字典")
	private String linkType;
	/**
	 * 链路类型，数据字典
	 */
	@ApiModelProperty(value = "链路类型名称")
	private String linkTypeName;
	/**
	 * 链路分类名称，数据字典
	 */
	@ApiModelProperty(value = "链路分类名称")
	private String linkClassifyName;
	/**
	 * 链路分类
	 */
	@ApiModelProperty(value = "链路分类")
	private String linkClassify;
	/**
	 * 链路用途，数据字典
	 */
	@ApiModelProperty(value = "链路用途，数据字典")
	private String linkUse;
	/**
	 * 链路用途，数据字典
	 */
	@ApiModelProperty(value = "链路用途名称")
	private String linkUseName;
	/**
	 * 链路数量
	 */
	@ApiModelProperty(value = "链路数量")
	private Integer linkNumber;
	/**
	 * 链路总带宽
	 */
	@ApiModelProperty(value = "链路总带宽")
	private String linkBandwidth;
	/**
	 * 物理传输通道
	 */
//	@ApiModelProperty(value = "物理传输通道")
//	private String physicalChannel;
//
//	@ApiModelProperty(value = "物理传输通道")
//	private String physicalChannelName;
	/**
	 * 运营商，数据字典
	 */
	@ApiModelProperty(value = "运营商，数据字典")
	private String networkOperator;

	/**
	 * 运营商，数据字典
	 */
	@ApiModelProperty(value = "运营商，数据字典")
	private String networkOperatorName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	/*
	IP地址
	 */

	@ApiModelProperty(value = "IP地址")
	private String ipAddress;

	/**
	 * 网关
	 */
	@ApiModelProperty(value = "网关")
	private String gateway;
	/**
	 * 子网掩码
	 */
	@ApiModelProperty(value = "子网掩码")
	private String subnetMask;
	/**
	 * 本端
	 */
	@ApiModelProperty(value = "本端")
	private String selfSide;
	/**
	 * 对端
	 */
	@ApiModelProperty(value = "对端")
	private String otherSide;

	/**
	 * 所属组织全路径名称
	 */
	@ApiModelProperty(value="所属组织全路径名称")
	private String fullOrgName;

	/**
	 * 数据中心机房名称
	 */
	@ApiModelProperty(value = "数据中心机房名称")
	private String roomName;
	/**
	 * 机房编号
	 */
	@ApiModelProperty(value = "机房编号")
	private String roomNo;

}
