/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;
import com.snszyk.zbusiness.stat.dto.HomeTaskStatDto;
import com.snszyk.zbusiness.stat.vo.HomeTaskStatVo;
import com.snszyk.zbusiness.task.dto.TaskDto;
import com.snszyk.zbusiness.task.dto.TaskPageDto;
import com.snszyk.zbusiness.task.vo.DispatchTaskVo;
import com.snszyk.zbusiness.task.vo.TaskQueryVo;
import com.snszyk.zbusiness.task.vo.TaskVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务管理 服务类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITaskService extends IBaseCrudService<TaskDto, TaskVo> {


	/**
	 * 分页查询
	 * @param v
	 * @return
	 */
	IPage<TaskPageDto> pageList(TaskQueryVo v);

	TaskDto getTaskById(Long taskId);

	/**
	 * 根据子任务id查询转发人物
	 * @param childTaskId
	 * @return
	 */
	TaskDto getTaskByChildTaskId(Long childTaskId);

	List<TaskDto> listByModelId(Long modelId);

	/**
	 * 保存转发的任务
	 * @param dispatchTask
	 * @return
	 */
    TaskDto saveDispatchTask(DispatchTaskVo dispatchTask);

	List<TaskDto> listByModelIds(List<Long> modelIdList );

	TaskDto getByBusinessIdAndBusinessType(Long businessId, String businessType);

	List<TaskDto> listByChildTaskIds(List<Long> childTaskIds);

	/**
	 * 根据关联的业务查询
	 * @param businessIds
	 * @param rwSourceType
	 * @return
	 */
	List<TaskDto> listByBusiness(List<Long> businessIds, String rwSourceType);

	/**
	 * 根据祖籍列表查询
	 * @param taskId
	 * @return
	 */
	List<TaskDto> listByAncestor(Long taskId);

	boolean batchUpdateFileStatus(List<Long> taskIds, LocalDateTime fileTime);

	boolean delByIds(List<Long> collect);

	/**
	 * 首页任务管理统计
	 * @param v
	 * @return
	 */
    HomeTaskStatDto homeTaskStat(HomeTaskStatVo v);

	/**
	 * 驾驶仓任务下发统计 只统计总部的
	 * @return
	 */
	List<EchartCircleDto> headTaskIssueStat(LocalDateTime  startDate, LocalDateTime endDate, Long orgId, Integer isSupervise);

	/**
	 * 超时提报的任务统计
	 *
	 * @param
	 * @param startDate
	 * @param endDate
	 * @param sort
	 * @param isSupervise
	 * @return
	 */
    List<EchartCircleDto> overtimeSubmitTaskStat(LocalDateTime startDate, LocalDateTime endDate, Integer sort, Long orgId, Integer isSupervise);
	/**
	 * 任务退回次数统计
	 *
	 * @param startDate
	 * @param endDate
	 * @param sort
	 * @param
	 * @return
	 */
	List<EchartCircleDto> backTaskStat(LocalDateTime startDate, LocalDateTime endDate, Integer sort, Long orgId);

    List<TaskDto> listByIds(List<Long> taskIds);

    TaskDto getInfoByIdIgnoreDel(Long initTaskId);
}
