package com.snszyk.zbusiness.dict.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "SchemeScoreMethodVo", description = "体系指标评分方法")
public class SchemeScoreMethodVo extends BaseCrudSlimVo {

	/**
	 * 体系id
	 */
	@ApiModelProperty("体系id")
	private Long schemeId;
	/**
	 * 指标评价id
	 */
	@ApiModelProperty("指标评价id")
	private Long schemeEvaluateId;
	/**
	 * 评分方法
	 */
	@ApiModelProperty("评分方法")
	private String scoreMethod;
}
