package com.snszyk.zbusiness.flow.enums;

/**
 * 字典
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum LockStatusEnum {

	ON(0, "开放"),
	CLOSE(1, "关闭"),
	;

	private Integer code;
	private String message;

	LockStatusEnum(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}



}
