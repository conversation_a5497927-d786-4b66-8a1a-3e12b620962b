package com.snszyk.zbusiness.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
@ApiModel(value = "ProjectDispatchReportVo", description = "项目调度表")
public class ProjectDispatchReportVo {

	/**
	 * id
	 */
	@ApiModelProperty(value = "id")
	private Long id;
	/**
	 * 明细数据
	 */
	@Valid
	@ApiModelProperty(value = "明细数据")
	private List<ProjectDispatchDetailVo> list;

}
