/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.project.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 项目审批意见实体类
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjectApprovalOpinionVo对象", description = "项目审批意见")
public class ProjectApprovalOpinionVo extends BaseCrudVo {

	/**
	* 项目id
	*/
		@ApiModelProperty(value = "项目id")
		private Long projectId;
	/**
	* 审批节点
	*/
		@ApiModelProperty(value = "审批节点")
		private Integer flowSort;
	/**
	* 流程id
	*/
		@ApiModelProperty(value = "流程id")
		private Long instanceId;
	/**
	* 审核部门id
	*/
		@ApiModelProperty(value = "审核部门id")
		private Long approveDeptId;
	/**
	* 审核部门名称
	*/
		@ApiModelProperty(value = "审核部门名称")
		private String approveDeptName;
	/**
	* 审核人id
	*/
		@ApiModelProperty(value = "审核人id")
		private String approveUserId;
	/**
	* 审核人姓名
	*/
		@ApiModelProperty(value = "审核人姓名")
		private String approveUserName;
	/**
	* 项目审批意见，数据字典
	*/
		@ApiModelProperty(value = "项目审批意见，数据字典")
		private String approveResult;
	/**
	* 审核意见
	*/
		@ApiModelProperty(value = "审核意见")
		private String approveRemark;
	/**
	* 审批时间
	*/
		@ApiModelProperty(value = "审批时间")
		private LocalDateTime approveTime;


}
