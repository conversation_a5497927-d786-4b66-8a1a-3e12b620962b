/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.stat.dto.*;
import com.snszyk.zbusiness.stat.service.logic.ProjectStatLogicService;
import com.snszyk.zbusiness.stat.vo.HomeProjectStatVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 子任务管理 控制器
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-stat/project-stat")
@Api(value = "项目管理统计", tags = "项目管理统计接口")
public class ProjectStatController extends BaseCrudController {



	private ProjectStatLogicService projectStatLogicService;






	/**
	 * 首页项目管理统计
	 */
	@PostMapping("/homeProjectStat")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "首页项目管理统计", notes = "首页项目管理统计")
	public R<HomeProjectStatDto> homeProjectStat(@RequestBody HomeProjectStatVo v) {
		HomeProjectStatDto dto = projectStatLogicService.homeProjectStat(v);
		return R.data(dto);
	}
	/**
	 * 驾驶舱项目总数
	 */
	@GetMapping("/boardProjectPhaseStat")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱项目总数", notes = "驾驶舱项目总数")
	public R<BoardProjectPhaseDto> boardProjectPhaseStat(@RequestParam @ApiParam(value = "0全部  1 本月  2本年")
														   Integer scope) {
		BoardProjectPhaseDto dto = projectStatLogicService.boardProjectPhaseStat(scope);
		return R.data(dto);
	}
	/**
	 * 驾驶舱投资估算
	 */
	@GetMapping("/projectInvestment")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱投资估算", notes = "驾驶舱项目总数")
	public R<BoardProjectInvestmentDto> projectInvestment(@RequestParam @ApiParam(value = "0全部  1 本月  2本年")
														 Integer scope) {
		BoardProjectInvestmentDto dto = projectStatLogicService.projectInvestment(scope);
		return R.data(dto);
	}
	/**
	 * 驾驶舱项目审查数量
	 */
	@GetMapping("/projectExamine")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱项目审查数量", notes = "驾驶舱项目审查数量")
	public R<BoardExamineDto> projectExamine(@RequestParam @ApiParam(value = "0全部  1 本月  2本年 3本季度")
														  Integer scope) {
		BoardExamineDto dto = projectStatLogicService.projectExamine(scope);
		return R.data(dto);
	}
	/**
	 * 驾驶舱项目后评价
	 */
	@GetMapping("/projectEvaluate")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱项目后评价", notes = "驾驶舱项目后评价")
	public R<BoardEvaluateDto> projectEvaluate(@RequestParam @ApiParam(value = "0全部  1 本月  2本年 3本季度")
											 Integer scope) {
		BoardEvaluateDto dto = projectStatLogicService.projectEvaluate(scope);
		return R.data(dto);
	}
	/**
	 * 驾驶舱项目验收数量
	 */
	@GetMapping("/projectAccept")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱项目验收数量", notes = "驾驶舱项目验收数量")
	public R<Integer> projectAccept(@RequestParam @ApiParam(value = "0全部  1 本月  2本年 3本季度")
											 Integer scope) {
		Integer result = projectStatLogicService.projectAccept(scope);
		return R.data(result);
	}
	/**
	 * 驾驶舱项目审查数量分布
	 */
	@GetMapping("/companyExamine")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱项目审查数量分布", notes = "驾驶舱项目审查数量分布")
	public R<List<BoardCompanyExamineDto>> companyExamine(@RequestParam @ApiParam(value = "0全部  1 本月  2本年 3本季度")
									Integer scope) {
		List<BoardCompanyExamineDto> result = projectStatLogicService.companyExamine(scope);
		return R.data(result);
	}


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
