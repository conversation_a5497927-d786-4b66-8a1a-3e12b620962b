/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.mapper;

import com.snszyk.zbusiness.stat.entity.IndexSystem;
import com.snszyk.zbusiness.stat.vo.IndexSystemVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 指标-系统 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
public interface IndexSystemMapper extends BaseMapper<IndexSystem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param indexSystem
	 * @return
	 */
	List<IndexSystemVo> selectIndexSystemPage(IPage page, IndexSystemVo indexSystem);

}
