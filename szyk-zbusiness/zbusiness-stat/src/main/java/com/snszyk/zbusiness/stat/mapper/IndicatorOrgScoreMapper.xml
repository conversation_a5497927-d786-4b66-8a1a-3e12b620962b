<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndicatorOrgScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorOrgScoreResultMap" type="com.snszyk.zbusiness.stat.entity.IndicatorOrgScore">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="system_no" property="systemNo"/>
        <result column="org_code" property="orgCode"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="score" property="score"/>
    </resultMap>
    <delete id="deleteByMonth">
        delete
        from indicator_org_score
        where year = #{year}
        and month = #{month}
    </delete>


    <select id="selectIndicatorOrgScorePage" resultMap="indicatorOrgScoreResultMap">
        select * from indicator_org_score where is_deleted = 0
    </select>


    <select id="orgAvgRank" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgRankDto">
        select
        avg(t.score) as score
        from indicator_org_score t
        left join szyk_dept t2 on t2.dept_code=t.org_code and t2.is_deleted=0
        left join szyk_dept t4 on t4.dept_code=t2.parent_code and t4.is_deleted=0
        where t.is_deleted = 0
        <if test="v.systemNo!=null and v.systemNo !=''">
            and t.system_no = #{v.systemNo}
        </if>
        and t4.dept_code=#{v.upOrgCode}
        and t.year=#{v.year} and t.month=#{v.month}

    </select>
    <select id="orgHistoryScore" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto">
        select
        t.score as score,
        concat(t.year,'年',t.month,'月') as time,
        t.year,
        t.month,
        tavg.score as lastScore
        from indicator_org_score t
        left join(
            select t1.year,t1.month, round(avg(t1.score),2) as score
            from indicator_org_score t1
            where t1.is_deleted = 0
            <if test="v.systemNo!=null and v.systemNo!=''">
                and t1.system_no = #{v.systemNo}
            </if>
        and ((t1.year = #{v.year} AND t1.month BETWEEN 1 AND #{v.month})
        OR (t1.year = #{v.year} - 1 AND t1.month BETWEEN 1 AND 12))
            group by t1.year,t1.month
        ) tavg on t.year =tavg.year and t.month=tavg.month
        where t.is_deleted = 0
        <if test="v.systemNo!=null and v.systemNo !=''">
            and t.system_no = #{v.systemNo}
        </if>
        and t.org_code=#{v.orgCode}
        and ((t.year = #{v.year} AND t.month BETWEEN 1 AND #{v.month})
        OR (t.year = #{v.year} - 1 AND t.month BETWEEN 1 AND 12))
        order by t.year  ,t.month
    </select>

    <select id="analyzeReportOrg" resultType="com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto">

        select t.system_no,
               t.org_code,
               t.year,
               t.month,
               t.score,
               count(distinct t3.indicator_code)                 as indicatorNum,
               t1.score                                          as lastMonthScore,
            round(
               case
                   when t1.score is null then null
                   else
                       (t.score - t1.score) / t1.score * 100 end,2) as chainBase

        from indicator_org_score t
                 left join indicator_org_score t1
                           on t1.is_deleted = 0 and t.system_no = t1.system_no and t1.org_code = t.org_code
                               and t1.month = #{lastMonth} and t1.year = #{lastYear}
                 left join indicator_dataset t2
                           on t2.system_no = t.system_no and t2.org_code = t.org_code and t2.is_deleted = 0 and t2.month
                               = t.month and t2.year = t.year and t2.is_deleted = 0
         and t2.denominator != 0 and t2.denominator is not null
            left join indicator_library t3 on t3.indicator_code=t2.indicator_code     and t3.use_type=3
        where t.is_deleted = 0
          and t.year = #{year}
          and t.month = #{month}

        group by t.system_no, t.org_code

    </select>
    <select id="analyzeReportSystem" resultType="com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto">

        select t.system_no,
               t.org_code,
               t.year,
               t.month,
               t.score,
               count(distinct t3.indicator_code)                 as indicatorNum,
               t1.score                                          as lastMonthScore,
            round(
               case
                   when t1.score is null then null
                   else
                       (t.score - t1.score) / t1.score * 100 end ,2)as chainBase
        from indicator_org_score t
                 left join indicator_org_score t1
                           on t1.is_deleted = 0 and t.system_no = t1.system_no and t1.org_code = t.org_code
                               and t1.month = #{lastMonth} and t1.year = #{lastYear}
                 left join indicator_dataset t2
                           on t2.system_no = t.system_no and t2.org_code = t.org_code and t2.is_deleted = 0 and t2.month
                               = t.month and t2.year = t.year and t2.is_deleted = 0 and t.org_code='00000000'
            left join indicator_library t3 on t3.indicator_code=t2.indicator_code   and t3.use_type=3
        where t.is_deleted = 0
          and t.year = #{year}
          and t.month = #{month}
            and t.system_no !='AAAA'
        and t.org_code ='00000000'
        group by t.system_no

    </select>

    <select id="getAllSysIndicatorNum" resultType="com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto">
        select t.org_code,
               count(distinct t2.indicator_code) as indicatorNum
        from indicator_org_score t
                 left join indicator_dataset t2
                           on t2.system_no = t.system_no and t2.is_deleted = 0 and t2.org_code=t.org_code
        and t2.year=t.year and t2.month=t.month
            left join indicator_library t3 on t3.indicator_code=t2.indicator_code  and t3.is_deleted=0
        where t.is_deleted = 0
            and t.year = #{year}
            and t.month = #{month}
            and t2.denominator != 0 and t2.denominator is not null
            and t3.use_type=3
        group by t.org_code
    </select>
    <select id="analyzeZeroOrgScore" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgScoreDto">
        select t.system_no,
               '00000000'                                  as org_code,
               t.year,
               t.month,
               round(sum(t.indicator_score*t1.weighted)*10/sum(t1.weighted),2) as score
        from indicator_dataset t
            left join indicator_library t1 on t1.is_deleted=0 and t1.indicator_code=t.indicator_code
        where t.is_deleted = 0
          and t.year = #{year}
          and t.month = #{month}
          and t.org_code='00000000'
            and t1.use_type=3
        group by t.system_no
    </select>

</mapper>
