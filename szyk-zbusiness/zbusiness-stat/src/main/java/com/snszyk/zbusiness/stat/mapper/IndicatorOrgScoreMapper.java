/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgScoreDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto;
import com.snszyk.zbusiness.stat.entity.IndicatorOrgScore;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgScoreVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单位分数 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface IndicatorOrgScoreMapper extends BaseMapper<IndicatorOrgScore> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param indicatorOrgScore
	 * @return
	 */
	List<IndicatorOrgScoreVo> selectIndicatorOrgScorePage(IPage page, IndicatorOrgScoreVo indicatorOrgScore);


	/**
	 * 单位的历史得分趋势
	 * @param v
	 * @return
	 */
	List<IndicatorOrgHistoryRankDto> orgHistoryScore(@Param("v") IndicatorOrgHistoryRankVo v);

	/**
	 * 分析单位排名
	 * @return
	 */
	List<IndicatorReportOrgDto> analyzeReportOrg(@Param("year") Integer year, @Param("month") Integer month,
												 @Param("lastYear") Integer lastYear, @Param("lastMonth") Integer lastMonth
	);

	/**
	 * 分析单位排名
	 * @param year
	 * @param month
	 * @return
	 */
	List<IndicatorReportSystemDto> analyzeReportSystem(@Param("year") Integer year, @Param("month") Integer month,
													   @Param("lastYear") Integer lastYear, @Param("lastMonth") Integer lastMonth);


	/**
	 * 获取所有系统指标的数量
	 * @param year
	 * @param month
	 * @param lastYear
	 * @param lastMonth
	 * @return
	 */
	List<IndicatorReportOrgDto> getAllSysIndicatorNum(@Param("year") int year, @Param("month") int month, @Param("lastYear") int lastYear, @Param("lastMonth") int lastMonth);

	List<IndicatorOrgScoreDto> analyzeZeroOrgScore( @Param("year") int year, @Param("month") int month);

	boolean deleteByMonth(@Param("year") Integer year, @Param("month") Integer month);
}
