/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.utils.DictUtil;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import com.snszyk.zbusiness.resource.dto.RsSoftwareDeleteDto;
import com.snszyk.zbusiness.resource.vo.RsEquipmentDeleteVo;
import com.snszyk.zbusiness.stat.dto.IndicatorLibraryDto;
import com.snszyk.zbusiness.stat.dto.IndicatorLibrarySystemDto;
import com.snszyk.zbusiness.stat.enums.IndicatorSystemEnum;
import com.snszyk.zbusiness.stat.enums.IndicatorUseTypeEnum;
import com.snszyk.zbusiness.stat.service.IIndicatorLibraryService;
import com.snszyk.zbusiness.stat.vo.IndicatorLibraryPageVo;
import com.snszyk.zbusiness.stat.vo.IndicatorLibraryVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.snszyk.zbusiness.dict.enums.DictBizEnum.XXJSGK_MODULE;

/**
 * 指标库 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@AllArgsConstructor
@Service
public class IndicatorLibraryLogicService extends BaseCrudLogicService<IndicatorLibraryDto, IndicatorLibraryVo> {

	private final IIndicatorLibraryService indicatorLibraryService;

	private final IDeptService deptService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.indicatorLibraryService;
	}

	/**
	 * 分页
	 * @param v
	 * @return
	 */
	public IPage<IndicatorLibraryDto> pageList(IndicatorLibraryPageVo v) {
		IPage<IndicatorLibraryDto> indicatorLibraryDtoIPage = indicatorLibraryService.pageList(v);
		//评价维度的字典
		Map<String, String> assessmentMap = DictUtil.getMap(DictBizEnum.ASSESSMENT_DIMENSION);
		List<IndicatorLibraryDto> records = indicatorLibraryDtoIPage.getRecords();
		if (!CollectionUtils.isEmpty(records)) {
			for (IndicatorLibraryDto indicatorLibraryDto : records) {
				indicatorLibraryDto.setAssessmentDimensionName(assessmentMap.get(indicatorLibraryDto.getAssessmentDimension()));
			}
		}
		return indicatorLibraryDtoIPage;
	}


	/**
	 * 保存或更新
	 * @param v
	 * @return
	 */
	public IndicatorLibraryDto saveOrUpdate(IndicatorLibraryVo v) {
		Integer useType = v.getUseType();
		if (v.getModelName() == null) {
			v.setModelName("");
		}

		//指标的编号不可以重复
		Integer validNoRepeat = this.indicatorLibraryService.validNoRepeat(v.getIndicatorCode(), v.getId());
		if (validNoRepeat > 0) {
			throw new ServiceException("指标编号不可重复");
		}
		//指标的名称不可以重复
		Integer validNameRepeat = this.indicatorLibraryService.validNameRepeat(v.getSystemNo(), v.getIndicatorName(), v.getId());
		if (validNameRepeat > 0) {
			throw new ServiceException("指标名称不可重复");
		}
		IndicatorLibraryDto save = indicatorLibraryService.save(v);
		if (!Objects.equals(useType, IndicatorUseTypeEnum.EVALUATE.getCode())) {
			boolean result = indicatorLibraryService.clearWeighted(save.getId());
			if (!result) {
				throw new ServiceException("清空权重失败");
			}
		}
		return save;
	}


	/**
	 * 根据ID获取数据
	 * @param id
	 * @return
	 */

	public IndicatorLibraryDto detail(Long id) {
		IndicatorLibraryDto indicatorLibraryDto = indicatorLibraryService.detail(id);
		if (indicatorLibraryDto == null) {
			throw new ServiceException("指标不存在");
		}

		return indicatorLibraryDto;
	}

	public List<RsSoftwareDeleteDto> delete(RsEquipmentDeleteVo vo) {
		List<RsSoftwareDeleteDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			return result;
		}
		for (Long id : vo.getIdList()) {
			RsSoftwareDeleteDto deleteDto = new RsSoftwareDeleteDto();
			IndicatorLibraryDto dto = indicatorLibraryService.fetchById(id);
			deleteDto.setName(dto.getIndicatorName());
			Boolean flag = indicatorLibraryService.deleteById(id);
			deleteDto.setResult(flag);
			result.add(deleteDto);
		}
		return result;
	}

	/**
	 * 根据系统名称查询指标
	 * @param systemName
	 * @return
	 */
	public List<IndicatorLibrarySystemDto> systemList(String systemName) {
		return indicatorLibraryService.getIndicatorSystemListByName(systemName);
	}

	/**
	 * 指标系统树形查询
	 * @return
	 */
	public List<IndicatorLibrarySystemDto> systemTree() {
		return indicatorLibraryService.systemTree();
	}

	/**
	 * 根据系统编号查询模块列表
	 * @param systemCode
	 * @return
	 */
	public List<IndicatorLibrarySystemDto> listModuleBySystemCode(String systemCode) {
		if (!Objects.equals(IndicatorSystemEnum.XXJSGK.getCode(), systemCode)) {
			return new ArrayList<>();
		}
		List<IndicatorLibrarySystemDto> indicatorModuleList = indicatorLibraryService.getIndicatorModuleList(XXJSGK_MODULE.getCode());
		return indicatorModuleList;


	}
}
