package com.snszyk.zbusiness.stat.indicatorHandle;

import com.snszyk.system.service.IUserService;
import com.snszyk.zbusiness.stat.enums.IndicatorEnum;
import com.snszyk.zbusiness.stat.enums.IndicatorScoreTypeEnum;
import com.snszyk.zbusiness.stat.scoreStrategy.AbstractScoringStrategy;
import com.snszyk.zbusiness.stat.scoreStrategy.ScoringStrategyFactory;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;

import static com.snszyk.common.utils.CommonUtil.getEndOfMonth;
import static com.snszyk.common.utils.CommonUtil.getStartOfMonth;
import static com.snszyk.zbusiness.stat.enums.IndicatorParamEnum.USER_LOGIN_RATE_PARAM;

/**
 * 登录率指标处理
 *
 */
@Service
@Getter
public class LoginRateHandle extends AbstractIndicatorHandle {

	@Autowired
	private ScoringStrategyFactory scoringStrategyFactory;
	@Autowired
	private IUserService userService;

	/**
	 * 指标编码
	 */
	protected final String indicatorCode = IndicatorEnum.LOGIN_RATE.getCode();
	/**
	 * 指标名称
	 */
	protected final String indicatorName = IndicatorEnum.LOGIN_RATE.getMessage();


	/**
	 * 指标参数
	 */
	protected  final String paramTypeCode = USER_LOGIN_RATE_PARAM.getCode();

	@Override
	protected void handleLog(LocalDate date) {
		//每个组织的用户
		logService.extractUser(getStartOfMonth(date), getEndOfMonth(date));
		//每个组织的登录用户
		logService.extractLoginUser(getStartOfMonth(date), getEndOfMonth(date));
		//每个组织的登录率
		int year = date.getYear();
		Month month = date.getMonth();
		int monthValue = month.getValue();
		logService.extractLoginRate(year, monthValue);
	}

	/**
	 * 每个单位的指标的得分
	 * 用户活跃率为80%，最大值为90%，最小值为40%，则本单位得分为: (80%-40%) *10分/ (90%-40%) =8分
	 * @param date
	 */
	@Override
	protected void handleDataSet(LocalDate date) {
		//max min
		int year = date.getYear();
		int monthValue = date.getMonthValue();
		AbstractScoringStrategy scoringStrategy = scoringStrategyFactory.getScoringStrategy(IndicatorScoreTypeEnum.STRATEGY_ONE.getCode());
		scoringStrategy.genDataSet(year, monthValue, indicatorCode, paramTypeCode);

	}


	@Override
	protected String getIndicatorDesc() {
		return this.getIndicatorCode()+this.getIndicatorName()+":";
	}

}
