/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.stat.dto.LoginLogDto;
import com.snszyk.zbusiness.stat.service.logic.IndicatorLogLogicService;
import com.snszyk.zbusiness.stat.vo.LoginLogQVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 指标业务日志 控制器
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-stat/indicatorlog")
@Api(value = "指标业务日志", tags = "指标业务日志接口")
public class IndicatorLogController extends BaseCrudController {


    private final IndicatorLogLogicService baseService;

    @Override
    protected BaseCrudLogicService fetchBaseLogicService() {
        return null;
    }
	/**
	 * 登录指标日志
	 */
	@PostMapping("/loginLog")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "登录指标日志", notes = "登录指标日志")
	public R<IPage<LoginLogDto>> loginLog(@RequestBody  @Valid LoginLogQVo v) {
		IPage<LoginLogDto> pageQueryResult = baseService.loginLog(v);
		return R.data(pageQueryResult);
	}

}
