/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.utils.CommonUtil;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IUserBehaviorService;
import com.snszyk.system.service.IUserOnlineDurationService;
import com.snszyk.system.service.IUserService;
import com.snszyk.zbusiness.rpc.service.ILogQueryService;
import com.snszyk.zbusiness.rpc.vo.LoginLogVo;
import com.snszyk.zbusiness.stat.dto.IndicatorLogDto;
import com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto;
import com.snszyk.zbusiness.stat.dto.IndicatorMaxMinScoreDto;
import com.snszyk.zbusiness.stat.dto.LoginLogDto;
import com.snszyk.zbusiness.stat.enums.IndicatorConst;
import com.snszyk.zbusiness.stat.enums.IndicatorEnum;
import com.snszyk.zbusiness.stat.enums.IndicatorParamEnum;
import com.snszyk.zbusiness.stat.indicatorHandle.AbstractIndicatorHandle;
import com.snszyk.zbusiness.stat.service.IIndicatorLogService;
import com.snszyk.zbusiness.stat.vo.IndicatorLogVo;
import com.snszyk.zbusiness.stat.vo.LoginLogQVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 指标业务日志 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@AllArgsConstructor
@Service
public class IndicatorLogLogicService extends BaseCrudLogicService<IndicatorLogDto, IndicatorLogVo> {

	private final IIndicatorLogService indicatorLogService;
	private final IUserService userService;
	private final IUserBehaviorService userBehaviorService;
	private final IUserOnlineDurationService durationService;
	private final SzykRedis redis;
	private final IDeptService deptService;

	private final ILogQueryService logsQueryService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.indicatorLogService;
	}

	/**
	 * 提取用户
	 * @param startOfMonth
	 * @param endOfMonth
	 */
	public void extractUser(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		List<IndicatorLogExtractDto> list = userService.indicatorCountUserByOrg(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.USER_COUNT_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.LOGIN_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(startOfMonth.getYear());
				vo.setMonth(startOfMonth.getMonthValue());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取登录的用户
	 * @param startOfMonth
	 * @param endOfMonth
	 */

	public void extractLoginUser(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		List<IndicatorLogExtractDto> list = userBehaviorService.indicatorCountUserLoginByOrg(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.USER_LOGIN_COUNT_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.LOGIN_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(startOfMonth.getYear());
				vo.setMonth(startOfMonth.getMonthValue());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取用户的登录率
	 */
	public void extractLoginRate(Integer year, Integer month) {
		List<IndicatorLogExtractDto> list = indicatorLogService.genLoginRateLog(year, month);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.USER_LOGIN_RATE_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.LOGIN_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				//分子分母
				vo.setNumerator(dto.getNumerator());
				vo.setDenominator(dto.getDenominator());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 某个指标的最大最小数
	 *
	 * @param indicatorCode
	 * @param paramCode
	 */

	public IndicatorMaxMinScoreDto IndicatorMaxMinScoreDto(String indicatorCode, String paramCode, Integer year, Integer month) {
		return indicatorLogService.IndicatorMaxMinScoreDto(indicatorCode, paramCode, year, month);
	}

	/**
	 * 提取督办次数log
	 */
	public void extractSuperviseLog(LocalDate date) {
		int year = date.getYear();
		int month = date.getMonthValue();
		LocalDateTime startOfMonth = CommonUtil.getStartOfMonth(date);
		LocalDateTime endOfMonth = CommonUtil.getEndOfMonth(date);
		List<IndicatorLogExtractDto> list = indicatorLogService.genSuperviseLog(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.SUPERVISE_TIMES_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.SUPERVISE_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 任务退回率
	 */
	public void extractTaskBackRateLog(LocalDate date) {
		int year = date.getYear();
		int month = date.getMonthValue();
		LocalDateTime startOfMonth = CommonUtil.getStartOfMonth(date);
		LocalDateTime endOfMonth = CommonUtil.getEndOfMonth(date);
		List<IndicatorLogExtractDto> list = indicatorLogService.genTaskBackRateLog(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.TASK_BACK_RATE_PARAM.getCode());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.TASK_BACK_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				//分子分母
				vo.setNumerator(dto.getNumerator());
				vo.setDenominator(dto.getDenominator());
				if (dto.getDenominator() == null) {
					continue;
				}
				if (BigDecimal.ZERO.equals(dto.getDenominator())) {
					continue;
				}
				BigDecimal divide = dto.getNumerator().divide(dto.getDenominator(), 4, BigDecimal.ROUND_HALF_UP);
				vo.setParamValue(divide.toString());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取活跃的用户数量
	 * @param startOfMonth
	 * @param endOfMonth
	 */
	public void extractActiveUser(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		//登录的日志
		List<IndicatorLogDto> loginDtoList = indicatorLogService.listLoingRateByTime(startOfMonth.getYear(), startOfMonth.getMonthValue());
		//活跃的日志
		LocalDateTime paramTime = null;
		LocalDateTime now = LocalDateTime.now();
		int year = now.getYear();
		int monthValue = now.getMonthValue();
		//如果是本月的
		if (year == startOfMonth.getYear() && monthValue == startOfMonth.getMonthValue()) {
			paramTime = now;
		} else {
			//如果是历史的月份的
			DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			String localDateTime = df.format(endOfMonth);
			LocalDateTime ldt = LocalDateTime.parse(localDateTime, df);
			paramTime = ldt;
		}
		List<IndicatorLogExtractDto> list = userBehaviorService.indicatorActiveUserByOrg(paramTime, startOfMonth.getYear(), startOfMonth.getMonthValue());
		List<String> activeOrgCodeList = list.stream().map(e -> e.getOrgCode()).collect(Collectors.toList());
		//登录有的.活跃没有的活跃率置为0
		loginDtoList.stream().filter(e -> !activeOrgCodeList.contains(e.getOrgCode())).forEach(e -> {
			IndicatorLogExtractDto dto = new IndicatorLogExtractDto();
			dto.setOrgCode(e.getOrgCode());
			dto.setCount(BigDecimal.ZERO);
			list.add(dto);
		});
		List<IndicatorLogVo> saveList = new ArrayList<>();
		if (Func.isNotEmpty(list)) {
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.USER_ACTIVE_COUNT_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.ACTIVE_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(startOfMonth.getYear());
				vo.setMonth(startOfMonth.getMonthValue());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}

	}

	/**
	 * 提取用户的活跃率
	 * @param year
	 * @param month
	 */
	public void extractUserActiveRate(int year, int month) {
		List<IndicatorLogExtractDto> list = indicatorLogService.indicatorUserActiveRateByOrg(year, month);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.USER_ACTIVE_RATE_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.ACTIVE_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				//分子分母
				vo.setNumerator(dto.getNumerator());
				vo.setDenominator(dto.getDenominator());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}


//	/**
//	 * 项目提报质量
//	 */
//	public void extractProjectApproveFinishRateLog(Integer year, Integer month) {
//		//提报的
//		extracted(year, month, CommonConstant.PROJECT_INDEX_REPORT_COUNT, IndicatorParamEnum.PROJECT_REPORT_PARAM.getCode());
//		//退回的
//		extracted(year, month, CommonConstant.PROJECT_INDEX_RETURN_COUNT, IndicatorParamEnum.PROJECT_RETURN_PARAM.getCode());
//		//撤回的
//		extracted(year, month, CommonConstant.PROJECT_INDEX_CANCEL_COUNT, IndicatorParamEnum.PROJECT_CANCEL_PARAM.getCode());
//
//		List<IndicatorLogExtractDto> list = indicatorLogService.indicatorProjectFinishRateByOrg(year, month);
//		if (Func.isNotEmpty(list)) {
//			List<IndicatorLogVo> saveList = new ArrayList<>();
//			for (IndicatorLogExtractDto dto : list) {
//				if (dto.getCount() == null) {
//					continue;
//				}
//				IndicatorLogVo vo = new IndicatorLogVo();
//				vo.setParamType(IndicatorParamEnum.PROJECT_QUALITY_PARAM.getCode());
//				BigDecimal count = dto.getCount();
//				vo.setParamValue(count.toString());
//				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
//				vo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
//				vo.setOrgCode(dto.getOrgCode());
//				vo.setYear(year);
//				vo.setMonth(month);
//				//分子分母
//				vo.setNumerator(dto.getNumerator());
//				vo.setDenominator(dto.getDenominator());
//				saveList.add(vo);
//			}
//			if (Func.isNotEmpty(saveList)) {
//				indicatorLogService.saveDataBatch(saveList);
//			}
//		}
//	}

	private void extracted(Integer year, Integer month, String redisKey, String paramCode) {
		Set<Object> hKeys = redis.hKeys(redisKey);
		if (Func.isNotEmpty(hKeys)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (Object key : hKeys) {
				String value = redis.hGet(redisKey, key);
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(paramCode);
				vo.setParamValue(String.valueOf(value));
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
				vo.setOrgCode(String.valueOf(key));
				vo.setYear(year);
				vo.setMonth(month);
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取任务完成率
	 */

	public boolean extractTaskFinishRateLog(LocalDate date) {
		int year = date.getYear();
		int month = date.getMonthValue();
		LocalDateTime startOfMonth = CommonUtil.getStartOfMonth(date);
		LocalDateTime endOfMonth = CommonUtil.getEndOfMonth(date);
		List<IndicatorLogExtractDto> list = indicatorLogService.genTaskFinishRateLog(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.TASK_FINISH_RATE_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.TASK_FINISH_RATE.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				//分子分母
				vo.setNumerator(dto.getNumerator());
				vo.setDenominator(dto.getDenominator());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
		return true;
	}

	/**
	 * 处理项目质量指标
	 * @param type  1 提报  2 退回 3 撤回
	 * @param orgId
	 * @return
	 */

	public boolean handleProjectReport(Integer type, Long orgId) {

		LocalDate now = LocalDate.now();
		int year = now.getYear();
		Month month = now.getMonth();
		int monthValue = month.getValue();
		Dept dept = SysCache.getDept(orgId);
		if (dept == null || dept.getDeptCode() == null) {
			return false;
		}
		IndicatorLogDto reportLog = indicatorLogService.getProjectReportLog(dept.getDeptCode(), year, monthValue,
			IndicatorParamEnum.PROJECT_REPORT_PARAM.getCode(), IndicatorEnum.PROJECT_QUALITY.getCode());
		return true;
	}

	public void extractOrgZeroUser(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		Integer count = userService.allCount();
		IndicatorLogVo vo = new IndicatorLogVo();
		vo.setParamType(IndicatorParamEnum.USER_COUNT_PARAM.getCode());
		vo.setParamValue(count.toString());
		vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
		vo.setIndicatorCode(IndicatorEnum.LOGIN_RATE.getCode());
		vo.setOrgCode(IndicatorConst.ZERO_ORG_CODE);
		vo.setYear(startOfMonth.getYear());
		vo.setMonth(startOfMonth.getMonthValue());
		indicatorLogService.save(vo);
	}

	/**
	 * 提取项目提报数量
	 * @param startOfMonth
	 * @param endOfMonth
	 */
	public void extractProjectReprot(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		List<IndicatorLogExtractDto> list = indicatorLogService.genProjectReportLog(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.PROJECT_REPORT_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(startOfMonth.getYear());
				vo.setMonth(startOfMonth.getMonthValue());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取项目退回数量
	 * @param startOfMonth
	 * @param endOfMonth
	 */
	public void extractProjectBack(LocalDateTime startOfMonth, LocalDateTime endOfMonth) {
		List<IndicatorLogExtractDto> list = indicatorLogService.genProjectBackLog(startOfMonth, endOfMonth);
		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.PROJECT_RETURN_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(startOfMonth.getYear());
				vo.setMonth(startOfMonth.getMonthValue());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 * 提取项目提报质量
	 */
	public void extractProjectQuality(Integer year, Integer month) {
		List<IndicatorLogExtractDto> list = indicatorLogService.genProjectQualityLog(year, month);

		if (Func.isNotEmpty(list)) {
			List<IndicatorLogVo> saveList = new ArrayList<>();
			for (IndicatorLogExtractDto dto : list) {
				IndicatorLogVo vo = new IndicatorLogVo();
				vo.setParamType(IndicatorParamEnum.PROJECT_QUALITY_PARAM.getCode());
				vo.setParamValue(dto.getCount().toString());
				vo.setSystemNo(AbstractIndicatorHandle.CURRENT_SYSTEM_NO);
				vo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
				vo.setOrgCode(dto.getOrgCode());
				vo.setYear(year);
				vo.setMonth(month);
				vo.setDenominator(dto.getDenominator());
				vo.setNumerator(dto.getNumerator());
				saveList.add(vo);
			}
			if (Func.isNotEmpty(saveList)) {
				indicatorLogService.saveDataBatch(saveList);
			}
		}
	}

	/**
	 *登录日志
	 * @param v
	 * @return
	 */
	public IPage<LoginLogDto> loginLog(LoginLogQVo v) {
		if (v.getOrgId() == null) {
			v.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
		}
		//时间
		if (v.getLoginStartDate() != null) {
			v.setLoginStartDateTime(v.getLoginStartDate().atStartOfDay());
		}
		if (v.getLoginEndDate() != null) {
			v.setLoginEndDateTime(v.getLoginEndDate().atTime(23, 59, 59));
		}
		//查询条件 信息技术
		if (Objects.equals(v.getSystemNo(), "XXJSGK")) {
			IPage<LoginLogDto> page = userBehaviorService.loginLog(v);
			return page;
		}
		//安全管控
		//单位名查询
		if (Objects.equals(v.getSystemNo(), "AQGK")) {
			//查询本机以及下级的orgCode
			List<String> orgCodeList;
			//山能登录&&搜索条件为空
			if (Objects.equals(v.getOrgId(), deptService.getTopDept().getId())) {
				orgCodeList = new ArrayList<>();
			} else {
				orgCodeList = deptService.listCodeByParentIdAndName(v.getOrgId(), null);
			}
			LoginLogVo loginLogVo = new LoginLogVo();
			loginLogVo.setOrgCode(orgCodeList);
			if (v.getLoginStartDate() != null) {
				loginLogVo.setLoginStartTime(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(v.getLoginStartDateTime()));
			}
			if (v.getLoginEndDate() != null) {
				loginLogVo.setLoginEndTime(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(v.getLoginEndDateTime()));
			}
			loginLogVo.setPageNumber(Math.toIntExact(v.getCurrent()));
			loginLogVo.setPageSize(Math.toIntExact(v.getSize()));
			loginLogVo.setUserType(v.getUserType());
			loginLogVo.setUserCode(v.getUserCode());
			loginLogVo.setUserName(v.getUserName());
			IPage<JSONObject> jsonObjectIPage = logsQueryService.loginLog(loginLogVo);
			List<LoginLogDto> list = new ArrayList<>();
			if (Func.isNotEmpty(jsonObjectIPage)) {
				for (JSONObject jsonObject : jsonObjectIPage.getRecords()) {
					LoginLogDto dto = new LoginLogDto();
					dto.setUserCode(jsonObject.getString("user_code"));
					dto.setUserName(jsonObject.getString("user_name"));
					dto.setUserType(jsonObject.getString("user_type"));
					dto.setOperateTime(LocalDateTime.parse(jsonObject.getString("login_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
					dto.setOrgName(jsonObject.getString("org_name"));
					dto.setSystemNo("AQGK");
					list.add(dto);
				}
			}
			Page<LoginLogDto> page = new Page<>();
			page.setTotal(jsonObjectIPage.getTotal());
			page.setCurrent(jsonObjectIPage.getCurrent());
			page.setSize(jsonObjectIPage.getSize());
			page.setRecords(list);
			return page;
		}
		return new Page<>();
	}
}
