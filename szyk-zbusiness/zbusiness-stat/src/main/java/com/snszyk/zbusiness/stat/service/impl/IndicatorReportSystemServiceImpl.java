/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto;
import com.snszyk.zbusiness.stat.entity.IndicatorReportSystem;
import com.snszyk.zbusiness.stat.mapper.IndicatorReportSystemMapper;
import com.snszyk.zbusiness.stat.service.IIndicatorReportSystemService;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgDetailListVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorReportSystemVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 系统得分表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@AllArgsConstructor
@Service
public class IndicatorReportSystemServiceImpl extends BaseCrudServiceImpl<IndicatorReportSystemMapper, IndicatorReportSystem, IndicatorReportSystemDto, IndicatorReportSystemVo> implements IIndicatorReportSystemService {


	@Override
	public IPage<IndicatorReportSystemDto> systemRankPage(IndicatorOrgRankVo v) {
		return baseMapper.systemRankPage(v);
	}

	@Override
	public BigDecimal systemAvgRank(IndicatorOrgRankVo vo) {
		return baseMapper.systemAvgRank(vo);
	}

	@Override
	public List<IndicatorOrgHistoryRankDto> systemHistoryScore(IndicatorOrgHistoryRankVo v) {
		return baseMapper.systemHistoryScore(v);
	}

	/**系统排名-单位明细
	 *
	 * @param v
	 * @return
	 */
	@Override
	public IPage<IndicatorReportOrgDto> orgDetailList(IndicatorOrgDetailListVo v) {
		return baseMapper.orgDetailList( v);
	}

	@Override
	public boolean saveDataBatch(List<IndicatorReportSystemVo> copy) {
		if (Func.isNotEmpty(copy)) {
			List<IndicatorReportSystem> list = BeanUtil.copy(copy, IndicatorReportSystem.class);
			return super.saveBatch(list);
		}
		return false;
	}

	@Override
	public boolean removeByDate(int year, int monthValue) {
		return lambdaUpdate().eq(IndicatorReportSystem::getYear, year).eq(IndicatorReportSystem::getMonth, monthValue).remove();
	}

	@Override
	public boolean deleteByMonth(Integer year, Integer month) {
		if(year==null||month==null){
			return false;
		}
		return baseMapper.deleteByMonth(year,month);
	}

}
