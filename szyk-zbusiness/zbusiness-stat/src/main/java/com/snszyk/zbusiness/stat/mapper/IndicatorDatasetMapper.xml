<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndicatorDatasetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorDatasetResultMap" type="com.snszyk.zbusiness.stat.entity.IndicatorDataset">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="system_no" property="systemNo"/>
        <result column="indicator_code" property="indicatorCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="indicator_value" property="indicatorValue"/>
    </resultMap>
    <delete id="deleteByMonth">
        delete
        from indicator_dataset
        where year = #{year}
        and month = #{month}

    </delete>


    <select id="selectIndicatorDatasetPage" resultMap="indicatorDatasetResultMap">
        select * from indicator_dataset where is_deleted = 0
    </select>

    <select id="indicatorDetailList" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDetailDto">
        select t.id,
        t.system_no,
        t.indicator_code,
        t.org_code,
        t.year,
        t.month,
        t1.model_name,
        round(if(t.system_no='XXJSGK',t.indicator_value*100, t.indicator_value),2) as indicator_value,
        t.indicator_score,
        t1.indicator_name,
        t2.dept_name as orgName,
        t2.ancestor_name,
        t1.calculation_formula,
        t1.weighted,
        t3.dict_value as systemName,
        t1.scoring_type,
        t4.dict_value as scoringTypeName,
        t5.dict_value as modelNameName,
        t6.dict_value as indicatorName,
        t.numerator,
        t.denominator,
        t7.dict_value as useTypeName,
        t1.use_type,
        rank() over (
            order by t.indicator_score desc
        ) as 'rank'
        from indicator_dataset t
        left join indicator_library t1 on t1.indicator_code=t.indicator_code and t1.is_deleted=0
        left join szyk_dept t2 on t2.dept_code=t.org_code
        left join szyk_dept udept on t2.unit_id=udept.id
        left join szyk_dict_biz t3 on t3.dict_key=t.system_no and t3.code='system_code' and t3.is_deleted=0
        left join szyk_dict_biz t4 on t4.dict_key=t1.scoring_type and t4.code='scoring_type' and t4.is_deleted=0
        left join szyk_dict_biz t5 on t5.dict_key=t1.model_name and t5.code='xxjsgk_module' and t5.is_deleted=0
        left join szyk_dict_biz t6 on t6.dict_key=t1.indicator_code and t6.code='indicator_code' and t6.is_deleted=0
        left join szyk_dict_biz t7 on t7.dict_key=t1.use_type and t7.code='indicator_use_type' and t7.is_deleted=0
        where t.is_deleted = 0
        and (t2.is_deleted=0 or t.org_code='00000000')
        and t.org_code!='00000001'
        and t.denominator is not null and t.denominator != 0
        <if test="v.orgCode!=null and v.orgCode!=''">
            and t.org_code=#{v.orgCode}
        </if>
        <if test="v.systemNo !=null and v.systemNo!='' ">
            and (t.system_no=#{v.systemNo} or t1.model_name =#{v.systemNo})
        </if>
        and t.year=#{v.year}
        and t.month=#{v.month}
        <if test="v.assessmentDimension !=null and v.assessmentDimension!=''">
            and t1.assessment_dimension =#{v.assessmentDimension}
        </if>
        <if test="v.indicatorName!=null and v.indicatorName!=''">
            and t1.indicator_name like concat('%',#{v.indicatorName},'%')
        </if>
        <if test="v.indicatorName!=null and v.indicatorName!=''">
            and t1.indicator_name like concat('%',#{v.indicatorName},'%')
        </if>
        <if test="v.orgName!=null and v.orgName!=''">
            and t2.dept_name like concat('%',#{v.orgName},'%')
        </if>
        <if test="v.useType!=null ">
            and t1.use_type =#{v.useType}
        </if>
        <if test="v.sort==1">
            order by t.indicator_score desc
        </if>
        <if test="v.sort==2">
            order by t.indicator_score asc
        </if>
        <if test="v.sort==null">
          order by  udept.sort asc,t2.sort asc,t2.unit_level asc, t2.id asc, t2.sort  ASC,t.indicator_score desc,t.id desc

        </if>
    </select>
    <select id="analyzeDatasetOne" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select t.system_no,
               t.indicator_code,
               t.org_code,
               t.year,
               t.month,
               t.param_value                                                    as indicatorValue,
               t.numerator,
               t.denominator,
               (t.param_value - #{minValue}) * 10 / (#{maxValue} - #{minValue}) as indicatorScore
        from indicator_log t
        where t.indicator_code = #{indicatorCode}
          and t.month = #{month}
          and t.year = #{year}
          and t.is_deleted = 0
          and t.param_type = #{paramCode}
         and t.denominator is not null and t.denominator != 0
        order by t.id desc

    </select>
    <select id="analyzeOrgSystemScore" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgScoreDto">
        select t.system_no,  t.org_code, t.year, t.month,
               round(sum(t.indicator_score*t1.weighted)*10/sum(t1.weighted),2) as score from
            indicator_dataset t
                left join indicator_library t1 on t1.indicator_code=t.indicator_code and t1.is_deleted=0
        where t.is_deleted=0
          and t.year=#{year}
          and t.month=#{month}
            and t1.use_type=3

        group by t.system_no,t.org_code

    </select>
    <select id="analyzeOrgAllSystemScore" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgScoreDto">
        select t.system_no,
               t.org_code,
               t.year,
               t.month,
               'AAAA'                                                                 as systemNo,
               round(sum(t.indicator_score * t1.weighted) * 10 / sum(t1.weighted), 2) as score
        from indicator_dataset t
                 left join indicator_library t1 on t1.indicator_code = t.indicator_code and t1.is_deleted=0
        where t.is_deleted = 0
          and t.year = #{year}
          and t.month = #{month}
          and t.org_code != '00000000'
            and t1.use_type=3
        group by t.org_code

    </select>
    <select id="analyzeDatasetTwo" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select t.system_no,
               t.indicator_code,
               t.org_code,
               t.year,
               t.month,
               t.numerator,
               t.denominator,
               t.param_value as                                                      indicatorValue,
               10 - (t.param_value - #{minValue}) * 10 / (#{maxValue} - #{minValue}) indicatorScore
        from indicator_log t
                 left join
             szyk_dept t1 on t.org_code = t1.dept_code and t1.is_deleted = 0
        where t.indicator_code = #{indicatorCode}
          and t.month = #{month}
          and t.year = #{year}
          and t.is_deleted = 0
          and t.param_type = #{paramCode}
          and t1.is_deleted = 0
          and t1.hide = 0
          and t1.dept_category in (1, 3)
          and t1.dept_status = 1
          and t.denominator is not null and t.denominator != 0
        order by t.id desc
    </select>
    <select id="analyzeDatasetThree" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select
            t.system_no,
            t.indicator_code,
            t.org_code,
            t.year,
            t.month,
            t.numerator,
            t.denominator,
            t.param_value                                               as indicatorValue,
            param_value*10 as indicatorScore
        from indicator_log t
        where t.indicator_code = #{indicatorCode}
          and t.month = #{month}
          and t.year = #{year}
          and t.is_deleted = 0
          and t.param_type = #{paramCode}
          and t.denominator is not null and t.denominator != 0
        order by t.id desc
    </select>
    <select id="analyzeDatasetFour" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select t.system_no,
               t.indicator_code,
               t.org_code,
               t.year,
               t.month,
               t.numerator,
               t.denominator,
               t.param_value                    as indicatorValue,
               10-t.param_value*10 as indicatorScore
        from indicator_log t
        where t.indicator_code = #{indicatorCode}
          and t.month = #{month}
          and t.year = #{year}
          and t.is_deleted = 0
          and t.param_type = #{paramCode}
          and t.denominator is not null and t.denominator != 0
        order by t.id desc
    </select>

    <select id="analyzeDatasetFive" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select
            t.system_no,
            t.indicator_code,
            t.org_code,
            t.year,
            t.month,
            t.numerator,
            t.denominator,
            t.param_value                                               as indicatorValue,
            if(t.param_value=1,10,0) as indicatorScore
        from indicator_log t
        where t.indicator_code = #{indicatorCode}
          and t.month = #{month}
          and t.year = #{year}
          and t.is_deleted = 0
          and t.param_type = #{paramCode}
          and t.denominator is not null and t.denominator != 0
        order by t.id desc
    </select>
    <select id="analyzeZeroOrgDataSet" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDatasetDto">
        select t.system_no,
               t.indicator_code,
               t.year,
               t.month,
               sum(t.numerator)                                                as numerator,
               sum(t.denominator)                                              as denominator,
               round(sum(t.indicator_score) / count(distinct t1.dept_code), 2) as indicator_score,
               round(sum(t.numerator) / sum(t.denominator), 4)                 as indicator_value,
               '00000000'                                                      as org_code
        from indicator_dataset t
                 left join szyk_dept t1
                           on t1.dept_code = t.org_code and t1.is_deleted = 0 and t1.dept_status = 1 and t1.hide = 0
                 left join indicator_library t2 on t2.indicator_code = t.indicator_code and t2.is_deleted = 0
        where t.is_deleted = 0
          and t.year = #{year}
          and t.month = #{month}
          and t.org_code != '00000001'
          and t1.dept_category = 1
          and t.system_no = 'XXJSGK'
        group by t.indicator_code
    </select>
    <select id="rankIndicatorDetailList" resultType="com.snszyk.zbusiness.stat.dto.IndicatorDetailDto">
        select t.id,
        t.system_no,
        t1.indicator_code,
        t.org_code,
        t.year,
        t.month,
        t1.model_name,
        round(if(t.system_no='XXJSGK',t.indicator_value*100, t.indicator_value),2) as indicator_value,
        t.indicator_score,
        t1.indicator_name,
        t2.dept_name as orgName,
        t2.ancestor_name,
        t1.calculation_formula,
        t1.weighted,
        t3.dict_value as systemName,
        t1.scoring_type,
        t4.dict_value as scoringTypeName,
        t5.dict_value as modelNameName,
        t6.dict_value as indicatorName,
        t.numerator,
        t.denominator,
        t7.dict_value as useTypeName,
        t1.use_type,
        rank() over (
        order by t.indicator_score desc
        ) as 'rank'
        from indicator_library t1
        left join  indicator_dataset t on t1.indicator_code=t.indicator_code and t1.is_deleted=0 and t.is_deleted=0 and t.org_code!='00000001'
        and t.denominator is not null and t.denominator != 0
         and t.org_code=#{v.orgCode}

        and t.year=#{v.year}
        and t.month=#{v.month}
        left join szyk_dept t2 on t2.dept_code=t.org_code
        left join szyk_dept udept on t2.unit_id=udept.id
        left join szyk_dict_biz t3 on t3.dict_key=t1.system_no and t3.code='system_code' and t3.is_deleted=0
        left join szyk_dict_biz t4 on t4.dict_key=t1.scoring_type and t4.code='scoring_type' and t4.is_deleted=0
        left join szyk_dict_biz t5 on t5.dict_key=t1.model_name and t5.code='xxjsgk_module' and t5.is_deleted=0
        left join szyk_dict_biz t6 on t6.dict_key=t1.indicator_code and t6.code='indicator_code' and t6.is_deleted=0
        left join szyk_dict_biz t7 on t7.dict_key=t1.use_type and t7.code='indicator_use_type' and t7.is_deleted=0
        where t1.is_deleted = 0
        and t1.use_type=3
        <if test="v.systemNo !=null and v.systemNo!='' ">
            and (t1.system_no=#{v.systemNo} or t1.model_name =#{v.systemNo})
        </if>
        <if test="v.assessmentDimension !=null and v.assessmentDimension!=''">
            and t1.assessment_dimension =#{v.assessmentDimension}
        </if>
        <if test="v.indicatorName!=null and v.indicatorName!=''">
            and t1.indicator_name like concat('%',#{v.indicatorName},'%')
        </if>
        <if test="v.indicatorName!=null and v.indicatorName!=''">
            and t1.indicator_name like concat('%',#{v.indicatorName},'%')
        </if>
        <if test="v.orgName!=null and v.orgName!=''">
            and t2.dept_name like concat('%',#{v.orgName},'%')
        </if>
        <if test="v.useType!=null ">
            and t1.use_type =#{v.useType}
        </if>
        <if test="v.sort==1">
            order by t.indicator_score desc,t1.is_deleted asc
        </if>
        <if test="v.sort==2">
            order by t.indicator_score asc,t1.is_deleted asc
        </if>

    </select>
    <select id="allActiveUser" resultType="java.lang.Integer">
        select count(1)
        from szyk_user_activity
        where #{paramTime} BETWEEN DATE_FORMAT(interval_start, '%Y-%m-%d 00:00:00') and DATE_FORMAT(interval_end, '%Y-%m-%d 23:59:59')
          and add_count >= interval_num
    </select>
    <select id="allUser" resultType="java.lang.Integer">

        select count(distinct t.id)
        from szyk_user_dept t
                 left join szyk_user t1 on t.user_id = t1.id
                 left join szyk_dept t2 on t.dept_id = t2.id
        where t1.is_deleted = 0
          and t1.status = 1
          and t2.is_deleted = 0
          and t2.dept_status = 1
          and t2.hide = 0
    </select>
    <select id="allLoginUser" resultType="java.lang.Integer">

        select count(distinct t1.id) as count
        from szyk_user_behavior t
                 left join szyk_user_dept t1 on t1.dept_id = t.dept_id and t1.user_id = t.user_id
                 left join szyk_user t2 on t2.id = t.user_id
                 left join szyk_dept t3 on t3.id = t.dept_id
        where t2.is_deleted = 0
          and t2.status = 1
          and t2.account != 'SZYKxm'
          and t.create_time &gt;= #{startOfMonth}
          and t.create_time &lt;= #{endOfMonth}
          and t3.is_deleted = 0
          and t3.dept_status = 1
          and t3.hide = 0

    </select>
</mapper>
