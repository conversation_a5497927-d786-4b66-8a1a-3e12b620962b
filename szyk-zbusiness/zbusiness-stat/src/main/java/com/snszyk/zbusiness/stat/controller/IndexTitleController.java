///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.stat.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//
//import com.snszyk.core.crud.controller.BaseCrudController;
//import com.snszyk.core.crud.dto.BaseCrudDto;
//import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.stat.service.logic.IndexTitleLogicService;
//import com.snszyk.zbusiness.stat.vo.IndexTitleVo;
//
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//
///**
// * 指标-主题 控制器
// *
// * <AUTHOR>
// * @since 2023-10-12
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("zbusiness-stat/indextitle")
//@Api(value = "指标-主题", tags = "指标-主题接口")
//public class IndexTitleController extends BaseCrudController {
//
//	// private final IIndexTitleService indexTitleService;
//
//    private final IndexTitleLogicService indexTitleLogicService;
//
//    @Override
//    protected BaseCrudLogicService fetchBaseLogicService() {
//        return indexTitleLogicService;
//    }
//
//    /**
//     * 保存
//     */
//    @PostMapping("/save")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "保存", notes = "IndexTitleVo")
//    public R<BaseCrudDto> save(@RequestBody IndexTitleVo v) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().save(v);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 分页
//     */
//    @GetMapping("/page")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "分页", notes = "IndexTitleVo")
//    public R<IPage<BaseCrudDto>> page(IndexTitleVo v) {
//        IPage<BaseCrudDto> pageQueryResult = this.fetchBaseLogicService().page(v);
//        return R.data(pageQueryResult);
//    }
//
//    /**
//    * 列表
//    */
//    @GetMapping("/list")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "列表", notes = "IndexTitleVo")
//    public R<List<BaseCrudDto>> list(IndexTitleVo v) {
//        List<BaseCrudDto> listQueryResult = this.fetchBaseLogicService().list(v);
//        return R.data(listQueryResult);
//    }
//
//    /**
//     * 获取单条
//     */
//    @GetMapping("/fetchOne")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "获取单条数据", notes = "IndexTitleVo")
//    public R<BaseCrudDto> fetchOne(IndexTitleVo v) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchOne(v);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 根据ID获取数据
//     */
//    @Override
//    @GetMapping("/fetchById")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "根据ID获取数据", notes = "id")
//    public R<BaseCrudDto> fetchById(Long id) {
//        BaseCrudDto baseCrudDto = this.fetchBaseLogicService().fetchById(id);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 删除
//     */
//    @Override
//    @PostMapping("/deleteById")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "删除", notes = "id")
//    public R<Boolean> deleteById(Long id) {
//        Boolean result = this.fetchBaseLogicService().deleteById(id);
//        return R.data(result);
//    }
//
//}
