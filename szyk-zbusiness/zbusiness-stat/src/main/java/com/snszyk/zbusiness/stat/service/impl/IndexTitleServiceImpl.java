/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.impl;

import com.snszyk.zbusiness.stat.entity.IndexTitle;
import com.snszyk.zbusiness.stat.dto.IndexTitleDto;
import com.snszyk.zbusiness.stat.vo.IndexTitleVo;
import com.snszyk.zbusiness.stat.mapper.IndexTitleMapper;
import com.snszyk.zbusiness.stat.service.IIndexTitleService;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 指标-主题 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@AllArgsConstructor
@Service
public class IndexTitleServiceImpl extends BaseCrudServiceImpl<IndexTitleMapper, IndexTitle, IndexTitleDto, IndexTitleVo> implements IIndexTitleService {


}
