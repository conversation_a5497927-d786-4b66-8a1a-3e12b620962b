/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.service.IDeptService;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto;
import com.snszyk.zbusiness.stat.service.IIndicatorReportSystemService;
import com.snszyk.zbusiness.stat.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统得分表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@AllArgsConstructor
@Service
public class IndicatorReportSystemLogicService extends BaseCrudLogicService<IndicatorReportSystemDto, IndicatorReportSystemVo> {

	/**
	 * 山能的org
	 */
	public static final String TOP_ORG_CODE = "00000001";
	private final IIndicatorReportSystemService indicatorReportSystemService;

	private final IDeptService deptService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.indicatorReportSystemService;
    }

	/**
	 * 单位排名分页
	 * @param v
	 * @return
	 */
	public IPage<IndicatorReportSystemDto> systemRankPage(IndicatorOrgRankVo v) {

		LocalDate time = v.getTime();
		if (time == null) {
			time = LocalDate.now();
		}
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		IPage<IndicatorReportSystemDto> page = indicatorReportSystemService.systemRankPage(v);
		List<IndicatorReportSystemDto> records = page.getRecords();
		records.forEach(record -> {
			BigDecimal lastMonthScore = record.getLastMonthScore();
			BigDecimal score = record.getScore();
			record.setChainBaseStr(record.getChainBase()==null ? "" : record.getChainBase().toString());
			//上月为0 本月不为0 无穷大
			if(lastMonthScore!=null&&score!=null&&new BigDecimal("0").compareTo(lastMonthScore)==0&&new BigDecimal("0").compareTo(score)<0){
				record.setChainBaseStr("∞");
			}
			if(Objects.equals(lastMonthScore,score)){
				record.setChainBaseStr("/");
			}
		});
		return page;
	}

	/**
	 * 系统的平均分
	 * @param v
	 * @return
	 */
	public BigDecimal systemAvgRank(IndicatorOrgRankVo v) {
		LocalDate time = v.getTime();
		if (time == null) {
			time = LocalDate.now();
		}
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		BigDecimal avg = indicatorReportSystemService.systemAvgRank(v);
		if (avg == null) {
			return null;
		}
		avg = avg.setScale(2, BigDecimal.ROUND_HALF_UP);
		return avg;
	}

	/**
	 * 系统的历史得分趋势
	 * @param v
	 * @return
	 */
	public List<IndicatorOrgHistoryRankDto> systemHistoryScore(IndicatorOrgHistoryRankVo v) {
		LocalDate time = v.getTime();
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		List<IndicatorOrgHistoryRankDto> indicatorOrgHistoryRankDtos = indicatorReportSystemService.systemHistoryScore(v);
		return indicatorOrgHistoryRankDtos.stream().limit(12).collect(Collectors.toList());
	}

	/**
	 * 系统排名-单位明细
	 * @param v
	 * @return
	 */
	public IPage<IndicatorReportOrgDto> orgDetailList(IndicatorOrgDetailListVo v) {
		LocalDate time = v.getTime();
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		//默认上级为山东能源集团
		if(Func.isEmpty(v.getUpOrgCode())){
			v.setUpOrgCode(TOP_ORG_CODE);
		}
 		IPage<IndicatorReportOrgDto> page = indicatorReportSystemService.orgDetailList(v);
		List<IndicatorReportOrgDto> records = page.getRecords();
		records.forEach(record -> {
			BigDecimal lastMonthScore = record.getLastMonthScore();
			BigDecimal score = record.getScore();
			record.setChainBaseStr(record.getChainBase() == null ? "" : record.getChainBase().toString());
			if (lastMonthScore != null && score != null) {
				//上月为0 本月不为0 无穷大
				if (new BigDecimal("0").compareTo(lastMonthScore) == 0 && new BigDecimal("0").compareTo(score) < 0) {
					record.setChainBaseStr("∞");
				}
				//两月相同为--
				if (Objects.equals(lastMonthScore, score)) {
					record.setChainBaseStr("/");
				}
			}
		});
		return page;
	}


}
