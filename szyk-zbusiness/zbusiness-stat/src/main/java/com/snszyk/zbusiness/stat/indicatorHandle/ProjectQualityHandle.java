package com.snszyk.zbusiness.stat.indicatorHandle;

import com.snszyk.zbusiness.stat.enums.IndicatorEnum;
import com.snszyk.zbusiness.stat.enums.IndicatorScoreTypeEnum;
import com.snszyk.zbusiness.stat.scoreStrategy.AbstractScoringStrategy;
import com.snszyk.zbusiness.stat.scoreStrategy.ScoringStrategyFactory;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;

import static com.snszyk.common.utils.CommonUtil.getEndOfMonth;
import static com.snszyk.common.utils.CommonUtil.getStartOfMonth;
import static com.snszyk.zbusiness.stat.enums.IndicatorParamEnum.PROJECT_QUALITY_PARAM;

/**
 * 项目的提报质量
 * （当月申报结束的项目被退回累计次数/当月申报结束的项目提报累计次数）*100
 *
 */
@Service
@Getter
public class ProjectQualityHandle extends AbstractIndicatorHandle {

	/**
	 * 指标参数
	 */
	protected  final String paramTypeCode = PROJECT_QUALITY_PARAM.getCode();
	@Autowired
	private ScoringStrategyFactory scoringStrategyFactory;

	/**
	 * 指标编码
	 */
	protected  final String indicatorCode = IndicatorEnum.PROJECT_QUALITY.getCode();
	/**
	 * 指标名称
	 */
	protected  final String indicatorName = IndicatorEnum.PROJECT_QUALITY.getMessage();;


	/**
	 * 项目数量
	 * @param date
	 */
	@Override
	protected void handleLog(LocalDate date) {
		//提报数量
		logService.extractProjectReprot(getStartOfMonth(date), getEndOfMonth(date));
		//退回数量
		logService.extractProjectBack(getStartOfMonth(date), getEndOfMonth(date));
		int year = date.getYear();
		Month month = date.getMonth();
		int monthValue = month.getValue();
		//提报质量
		logService.extractProjectQuality(year, monthValue);
	}

	/**
	 * 每个单位的指标的得分
	 * @param date
	 */
	@Override
	protected void handleDataSet(LocalDate date) {
		int year = date.getYear();
		int monthValue = date.getMonthValue();
		AbstractScoringStrategy scoringStrategy = scoringStrategyFactory.getScoringStrategy(IndicatorScoreTypeEnum.STRATEGY_FOUR.getCode());
		scoringStrategy.genDataSet(year,monthValue,indicatorCode,paramTypeCode);
	}
	@Override
	protected String getIndicatorDesc() {
		return this.getIndicatorCode() + ":"+this.getIndicatorName();
	}
}
