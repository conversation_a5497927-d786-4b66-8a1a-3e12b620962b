/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.service.IDeptService;
import com.snszyk.zbusiness.resource.service.IRsEquipmentService;
import com.snszyk.zbusiness.resource.service.IRsServerRoomService;
import com.snszyk.zbusiness.resource.service.IRsSystemService;
import com.snszyk.zbusiness.stat.dto.HeadOwnerResourceStatDto;
import com.snszyk.zbusiness.stat.dto.HomeResourceStatDto;
import com.snszyk.zbusiness.stat.vo.HomeProjectStatVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 资源管理统计
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@AllArgsConstructor
@Service
public class ResourceStatLogicService {


    private final IRsEquipmentService equipmentService;
    private final IRsSystemService systemService;
    private final IRsServerRoomService roomService;
    private final IDeptService deptService;


    /**
     *首页资源管理统计
     * @param v
     * @return
     */
    public HomeResourceStatDto homeResourceStat(HomeProjectStatVo v) {
        if (v.getOrgId() == null) {
            v.setOrgId(Long.valueOf(AuthUtil.getDeptId()));
        }
        //设备台账
        Integer equipmentCount = equipmentService.countStat(v);
        //信息系统台账
        Integer systemCount = systemService.countStat(v);
        //数据中心机房
        Integer roomCount = roomService.countStat(v);
        HomeResourceStatDto dto = new HomeResourceStatDto();
        dto.setEquipmentCount(equipmentCount);
        dto.setSystemCount(systemCount);
        dto.setServerRoomCount(roomCount);
        return dto;
    }

    /**
     * 驾驶仓总部和权属单位的资源管理统计
     * @return
     */
    public HeadOwnerResourceStatDto headOwnerResourceStat() {
        DeptDTO headDept = deptService.getHeadDept();
        HeadOwnerResourceStatDto equipmentStat = equipmentService.countHeadOwenrStat(headDept.getId());
        HeadOwnerResourceStatDto systemStat = systemService.countHeadOwenrStat(headDept.getId());
        HeadOwnerResourceStatDto roomStat = roomService.countHeadOwenrStat(headDept.getId());
        HeadOwnerResourceStatDto build = HeadOwnerResourceStatDto.builder().equipmentCount(equipmentStat.getEquipmentCount())
                .equipmentHeadCount(equipmentStat.getEquipmentHeadCount())
                .equipmentOwnerCount(equipmentStat.getEquipmentOwnerCount())
                .systemCount(systemStat.getSystemCount())
                .systemOwnerCount(systemStat.getSystemOwnerCount())
                .systemHeadCount(systemStat.getSystemHeadCount())
                .serverRoomCount(roomStat.getServerRoomCount())
                .serverRoomHeadCount(roomStat.getServerRoomHeadCount())
                .serverRoomOwnerCount(roomStat.getServerRoomOwnerCount()).build();
        return build;
    }
}
