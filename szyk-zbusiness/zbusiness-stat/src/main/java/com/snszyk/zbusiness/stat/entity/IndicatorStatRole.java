/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录率和活跃率统计角色配置实体类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorStatRole extends BaseCrudEntity {

	/**
	* 指标编码
	*/
	private String indicatorCode;
	/**
	* 角色id
	*/
	private Long roleId;
	/**
	 * 角色名
	 */
	private String roleName;

	/**
	 * 角色类型
	 */
	private String roleType;

}
