package com.snszyk.zbusiness.stat.uitl;

import com.snszyk.zbusiness.stat.dto.DateRangeDto;
import com.snszyk.zbusiness.stat.enums.DateRangeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.temporal.TemporalAdjusters;

import static java.time.LocalTime.MIN;

/**
 * 统计的工具类
 */
public class StatUtil {


	/**
	 * 获取本月和本年的时间范围
	 * @param  scope  DateRangeEnum
	 * @return
	 */
	public static DateRangeDto getMonthYearRange(Integer scope) {
		//本月1
		LocalDateTime startDate = null;
		LocalDateTime endDate = null;
		// 获取当前月第一天及最后一天
		//本月
		if (DateRangeEnum.CURRENT_MONTH.getCode().equals(scope)) {
			startDate = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth())), MIN);
			endDate = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);
			return DateRangeDto.builder().startDate(startDate)
				.endDate(endDate)
				.build();
		}
		//本年2
		// 获取今年第一天及最后一天
		if (DateRangeEnum.CURRENT_YEAR.getCode().equals(scope)) {
			startDate = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), MIN);
			endDate = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
			return DateRangeDto.builder().startDate(startDate)
				.endDate(endDate)
				.build();
		}
		//本季度3
		// 获取季度第一天及最后一天
		if (DateRangeEnum.CURRENT_QUARTER.getCode().equals(scope)) {
			DateRangeDto quarterRange = getQuarterRange();
			return quarterRange;
			// 获取当前季度的开始日期和结束日期
		}
		return new DateRangeDto();
	}

	/**
	 * 获取本季度的范围
	 * @return
	 */
	private static DateRangeDto getQuarterRange() {
		LocalDate today = LocalDate.now();
		Month month = today.getMonth();
		Month firstMonthOfQuarter = month.firstMonthOfQuarter();
		Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
		LocalDateTime startDate = LocalDateTime.of(today.getYear(), firstMonthOfQuarter, 1, 0, 0, 0);
		LocalDateTime endDate = LocalDateTime.of(today.getYear(), endMonthOfQuarter,
			endMonthOfQuarter.length(today.isLeapYear()), 23, 59, 59);
		return DateRangeDto.builder().startDate(startDate)
			.endDate(endDate)
			.build();
	}
	/**
	 * 获取上个季度的范围
	 * @return
	 */
	public static DateRangeDto getLastQuarterRange() {
		LocalDate today = LocalDate.now();
		Month month = today.getMonth();
		Month firstMonthOfQuarter = month.firstMonthOfQuarter();
		Month end = firstMonthOfQuarter.minus(1);
		Month first = Month.of(end.getValue() -2);
		LocalDateTime startDate = LocalDateTime.of(today.getYear(), first, 1, 0, 0, 0);
		LocalDateTime endDate = LocalDateTime.of(today.getYear(), end,
			end.length(today.isLeapYear()), 23, 59, 59);
		return DateRangeDto.builder().startDate(startDate)
			.endDate(endDate)
			.build();
	}
	/**
	 * 获取上月的时间范围
	 * @return
	 */
	public static DateRangeDto getLastMonth() {


		LocalDateTime localDateTime = LocalDateTime.now();
//		TODO 需要改成上月的
		LocalDateTime startDate = LocalDateTime.of(LocalDate.from(localDateTime.with(TemporalAdjusters.firstDayOfMonth())), LocalTime.MIN);
		LocalDateTime endDate = LocalDateTime.of(LocalDate.from(localDateTime.with(TemporalAdjusters.lastDayOfMonth())), LocalTime.MAX);
		return DateRangeDto.builder().startDate(startDate)
			.endDate(endDate)
			.build();
	}

	public static String getStartOrEndDayOfQuarter(LocalDate today, Boolean isFirst) {
		LocalDate resDate = LocalDate.now();
		if (today == null) {
			today = resDate;
		}
		Month month = today.getMonth();
		Month firstMonthOfQuarter = month.firstMonthOfQuarter();
		Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
		if (isFirst) {
			resDate = LocalDate.of(today.getYear(), firstMonthOfQuarter, 1);
		} else {
			resDate = LocalDate.of(today.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(today.isLeapYear()));
		}
		return resDate.toString();
	}

	public static void main(String[] args) {
		String startOrEndDayOfQuarter = getStartOrEndDayOfQuarter(LocalDate.now(), false);
	}
}
