<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndexTitleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indexTitleResultMap" type="com.snszyk.zbusiness.stat.entity.IndexTitle">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="system_id" property="systemId"/>
        <result column="title" property="title"/>
    </resultMap>


    <select id="selectIndexTitlePage" resultMap="indexTitleResultMap">
        select * from index_title where is_deleted = 0
    </select>

</mapper>
