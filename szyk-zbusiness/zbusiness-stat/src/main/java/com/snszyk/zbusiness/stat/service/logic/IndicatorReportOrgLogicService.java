/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.service.IDeptService;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.service.IIndicatorReportOrgService;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorReportOrgVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单位得分表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@AllArgsConstructor
@Service
public class IndicatorReportOrgLogicService extends BaseCrudLogicService<IndicatorReportOrgDto, IndicatorReportOrgVo> {

	/**
	 * 全部的系统编码
	 */
	public static final String SYSTEM_ALL_NO = "AAAA";
	private final IIndicatorReportOrgService indicatorReportOrgService;

	private final IDeptService deptService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.indicatorReportOrgService;
	}

	/**
	 * 单位排名分页
	 * @param v
	 * @return
	 */
	public IPage<IndicatorReportOrgDto> orgRankPage(IndicatorOrgRankVo v) {
		//全部时的系统编码AAAA
		if (StringUtil.isEmpty(v.getSystemNo())) {
			v.setSystemNo(SYSTEM_ALL_NO);
		}
		//上级组织
		if (v.getUpOrgCode() == null) {
			v.setUpOrgCode("00000001");
		}
		LocalDate time = v.getTime();
		if (time == null) {
			time = LocalDate.now();
		}
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());

		IPage<IndicatorReportOrgDto> page = indicatorReportOrgService.orgRankPage(v);
		List<IndicatorReportOrgDto> records = page.getRecords();
		records.forEach(record -> {
			if(StringUtil.isBlank(record.getSystemNo())){
				record.setSystemNo(v.getSystemNo());
			}
			BigDecimal lastMonthScore = record.getLastMonthScore();
			BigDecimal score = record.getScore();
			record.setChainBaseStr(record.getChainBase() == null ? "" : record.getChainBase().toString());
			if (lastMonthScore != null && score != null) {
				//上月为0 本月不为0 无穷大
				if (new BigDecimal("0").compareTo(lastMonthScore) == 0 && new BigDecimal("0").compareTo(score) < 0) {
					record.setChainBaseStr("∞");
				}
				//两月相同为--
				if (Objects.equals(lastMonthScore, score)) {
					record.setChainBaseStr("/");
				}
			}
		});
		return page;
	}

	/**
	 * 单位的平均分
	 * @param v
	 * @return
	 */
	public BigDecimal orgAvgRank(IndicatorOrgRankVo v) {
		//全部时的系统编码AAAA
		if (StringUtil.isEmpty(v.getSystemNo())) {
			v.setSystemNo(SYSTEM_ALL_NO);
		}
		//上级组织
		if (v.getUpOrgCode() == null) {
			DeptDTO headDept = deptService.getHeadDept();
			v.setUpOrgCode(headDept.getDeptCode());
		}
		LocalDate time = v.getTime();
		if (time == null) {
			time = LocalDate.now();
		}
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		BigDecimal avg = indicatorReportOrgService.orgAvgRank(v);

		if (avg == null) {
			return null;
		}
		avg = avg.setScale(2, BigDecimal.ROUND_HALF_UP);
		return avg;
	}

	/**
	 * 单位的历史得分趋势
	 * @param v
	 * @return
	 */
	public List<IndicatorOrgHistoryRankDto> orgHistoryScore(IndicatorOrgHistoryRankVo v) {
		if (StringUtil.isBlank(v.getUpOrgCode())) {
			v.setUpOrgCode("00000001");
		}
		LocalDate time = v.getTime();
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		Integer count = deptService.listUnitCountByParentCode(v.getUpOrgCode());
		if (count == null || count == 0) {
			return new ArrayList<>();
		}
		v.setOrgCount(count);
		List<IndicatorOrgHistoryRankDto> indicatorOrgHistoryRankDtos = indicatorReportOrgService.orgHistoryScore(v);
		return indicatorOrgHistoryRankDtos.stream().limit(12).collect(Collectors.toList());
	}

	/**
	 * 单位排名-系统明细
	 * @param v   时间 单位 系统编码(全部)
	 * @return
	 */
	public IPage<IndicatorReportOrgDto> systemDetailList(IndicatorOrgRankVo v) {
		LocalDate time = v.getTime();
		if (time == null) {
			time = LocalDate.now();
		}
		v.setYear(time.getYear());
		v.setMonth(time.getMonthValue());
		if (Objects.equals(v.getSystemNo(), SYSTEM_ALL_NO)) {
			v.setSystemNo("");
		}
		IPage<IndicatorReportOrgDto> page = indicatorReportOrgService.systemDetailList(v);
		List<IndicatorReportOrgDto> records = page.getRecords();
		records.forEach(record -> {
			BigDecimal lastMonthScore = record.getLastMonthScore();
			BigDecimal score = record.getScore();
			record.setChainBaseStr(record.getChainBase() == null ? "" : record.getChainBase().toString());
			if (lastMonthScore != null && score != null) {
				//上月为0 本月不为0 无穷大
				if (new BigDecimal("0").compareTo(lastMonthScore) == 0 && new BigDecimal("0").compareTo(score) < 0) {
					record.setChainBaseStr("∞");
				}
				//两月相同为--
				if (Objects.equals(lastMonthScore, score)) {
					record.setChainBaseStr("/");
				}
			}
		});
		return page;
	}

}
