package com.snszyk.zbusiness.stat.scoreStrategy;

import com.snszyk.zbusiness.stat.dto.IndicatorMaxMinScoreDto;
import com.snszyk.zbusiness.stat.enums.IndicatorScoreTypeEnum;
import com.snszyk.zbusiness.stat.service.logic.IndicatorDatasetLogicService;
import com.snszyk.zbusiness.stat.service.logic.IndicatorLogLogicService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description:计分规则
 * @Version: 1.0
 * @Create: 2021/1/12 15:17
 */
@Service
@Slf4j
public class ScoringOneStrategy extends AbstractScoringStrategy {
	@Autowired
	public IndicatorLogLogicService logService;
	@Autowired
	public IndicatorDatasetLogicService dataSetService;

	@Getter
	private final String scoringType = IndicatorScoreTypeEnum.STRATEGY_ONE.getCode();
	@Getter
	private final String scoringTypeName= IndicatorScoreTypeEnum.STRATEGY_ONE.getMessage();

	@Override
	public boolean genDataSet(Integer year, Integer month, String indicatorCode, String paramCode) {
		IndicatorMaxMinScoreDto maxMin = logService.IndicatorMaxMinScoreDto(indicatorCode,
			paramCode, year, month);
		if (maxMin == null) {
			log.error(year+"年"+month+"月 指标"+indicatorCode+" 参数param"+paramCode+"无最大最小值" );
			return false;
		}
		dataSetService.extractDatasetOne(maxMin, year, month,indicatorCode,paramCode);
		return true;
	}
}
