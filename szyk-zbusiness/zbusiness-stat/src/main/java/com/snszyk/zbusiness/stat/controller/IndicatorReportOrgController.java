/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto;
import com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto;
import com.snszyk.zbusiness.stat.service.logic.IndicatorReportOrgLogicService;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgHistoryRankVo;
import com.snszyk.zbusiness.stat.vo.IndicatorOrgRankVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;


/**
 * 单位得分表 控制器
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-stat/indicatorreportorg")
@Api(value = "单位得分表", tags = "单位得分报表接口")
public class IndicatorReportOrgController extends BaseCrudController {


	private final IndicatorReportOrgLogicService indicatorReportOrgLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return indicatorReportOrgLogicService;
	}
	/**
	 * 分页
	 */
	@GetMapping("/orgRankPage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "单位排名分页", notes = "")
	public R<IPage<IndicatorReportOrgDto>> orgRankPage(@Valid IndicatorOrgRankVo v) {
		IPage<IndicatorReportOrgDto> pageQueryResult = indicatorReportOrgLogicService.orgRankPage(v);
		return R.data(pageQueryResult);
	}
	/**
	 * 单位排名-系统明细
	 */
	@GetMapping("/systemDetailList")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "单位排名-系统明细", notes = "")
	public R<IPage<IndicatorReportOrgDto>> systemDetailList(@Valid IndicatorOrgRankVo v) {
		IPage<IndicatorReportOrgDto> pageQueryResult = indicatorReportOrgLogicService.systemDetailList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 单位的历史得分趋势
	 */
	@GetMapping("/orgHistoryScore")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "单位的历史得分趋势", notes = "IndicatorOrgScoreVo")
	public R<List<IndicatorOrgHistoryRankDto>> orgHistoryScore(@Valid IndicatorOrgHistoryRankVo v) {
		List<IndicatorOrgHistoryRankDto> pageQueryResult = indicatorReportOrgLogicService.orgHistoryScore(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 单位的平均分
	 */
	@GetMapping("/orgAvgRank")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "单位的平均分", notes = "IndicatorOrgScoreVo")
	public R<BigDecimal> orgAvgRank(@Valid IndicatorOrgRankVo v) {
		v.setSize(-1);
		BigDecimal result = indicatorReportOrgLogicService.orgAvgRank(v);
		return R.data(result);
	}

}
