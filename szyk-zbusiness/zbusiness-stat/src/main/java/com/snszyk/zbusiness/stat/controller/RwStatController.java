/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.assess.dto.RwResultQueryParamDto;
import com.snszyk.zbusiness.stat.dto.BoardRwRankDto;
import com.snszyk.zbusiness.stat.service.logic.RwStatLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考核管理统计 控制器
 *
 * <AUTHOR>
 * @since 2023-10-7
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-stat/project-task")
@Api(value = "考核管理统计", tags = "考核管理统计接口")
public class RwStatController extends BaseCrudController {


	private RwStatLogicService rwStatLogicService;


	/**
	 * 驾驶舱考核排名
	 */
	@GetMapping("/rwRank")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱考核排名", notes = "驾驶舱考核排名")
	public R<List<BoardRwRankDto>> rwRank(@RequestParam @ApiParam(value = "1 前十  2末十") Integer sort,
										 @RequestParam @ApiParam(value = "周期") Long rwPeriodDetailId) {
		List<BoardRwRankDto> dto = rwStatLogicService.rwRank(sort, rwPeriodDetailId);
		return R.data(dto);
	}

	/**
	 *  驾驶舱考核排名默认参数
	 */
	@PostMapping("/rwRankDefaultParam")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "驾驶舱考核排名默认参数", notes = "驾驶舱考核排名默认参数")
	public R<RwResultQueryParamDto> rwRankDefaultParam() {
		RwResultQueryParamDto dto = rwStatLogicService.rwRankDefaultParam();
		return R.data(dto);
	}


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
