/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.stat.dto.IndicatorDetailDto;
import com.snszyk.zbusiness.stat.service.logic.IndicatorDatasetLogicService;
import com.snszyk.zbusiness.stat.vo.IndicatorDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 指标数据集 控制器
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-stat/indicatordataset")
@Api(value = "指标数据集", tags = "指标数据集接口")
public class IndicatorDatasetController extends BaseCrudController {


    private final IndicatorDatasetLogicService indicatorDatasetLogicService;

    @Override
    protected BaseCrudLogicService fetchBaseLogicService() {
        return indicatorDatasetLogicService;
    }

	/**
	 * 指标明细
	 */
	@GetMapping("/indicatorDetailList")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "指标明细", notes = "指标明细")
	public R<IPage<IndicatorDetailDto>> indicatorDetailList(@Valid IndicatorDetailVo v) {
		IPage<IndicatorDetailDto> pageQueryResult = indicatorDatasetLogicService.indicatorDetailList(v);
		return R.data(pageQueryResult);
	}
	/**
	 * 指标明细
	 */
	@GetMapping("/orgRankIndicatorDetailList")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "排名的指标明细", notes = "排名的指标明细")
	public R<IPage<IndicatorDetailDto>> orgRankIndicatorDetailList(@Valid IndicatorDetailVo v) {
		IPage<IndicatorDetailDto> pageQueryResult = indicatorDatasetLogicService.orgRankIndicatorDetailList(v);
		return R.data(pageQueryResult);
	}
}
