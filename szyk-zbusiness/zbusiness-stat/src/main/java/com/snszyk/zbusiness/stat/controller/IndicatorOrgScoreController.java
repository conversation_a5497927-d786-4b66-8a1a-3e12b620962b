///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.stat.controller;
//
//import com.snszyk.core.crud.controller.BaseCrudController;
//import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
//import com.snszyk.zbusiness.stat.service.logic.IndicatorOrgScoreLogicService;
//import io.swagger.annotations.Api;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//
///**
// * 单位分数 控制器
// *
// * <AUTHOR>
// * @since 2023-12-11
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("zbusiness-stat/indicatororgscore")
//@Api(value = "单位分数", tags = "单位分数接口")
//public class IndicatorOrgScoreController extends BaseCrudController {
//
//	private final IndicatorOrgScoreLogicService indicatorOrgScoreLogicService;
//	@Override
//	protected BaseCrudLogicService fetchBaseLogicService() {
//		return indicatorOrgScoreLogicService;
//	}
//
//
//}
