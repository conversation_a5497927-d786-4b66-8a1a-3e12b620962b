package com.snszyk.zbusiness.stat.indicatorHandle;

import com.snszyk.zbusiness.stat.enums.IndicatorEnum;
import com.snszyk.zbusiness.stat.enums.IndicatorScoreTypeEnum;
import com.snszyk.zbusiness.stat.scoreStrategy.AbstractScoringStrategy;
import com.snszyk.zbusiness.stat.scoreStrategy.ScoringStrategyFactory;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

import static com.snszyk.zbusiness.stat.enums.IndicatorParamEnum.TASK_FINISH_RATE_PARAM;

/**
 * 任务及时办结完成率指标处理
 *
 */
@Service
@Getter
public class TaskFinishRateHandle extends AbstractIndicatorHandle {

	/**
	 * 指标参数
	 */
	protected  final String paramTypeCode = TASK_FINISH_RATE_PARAM.getCode();
	@Autowired
	private ScoringStrategyFactory scoringStrategyFactory;

	/**
	 * 指标编码
	 */
	protected  final String indicatorCode = IndicatorEnum.TASK_FINISH_RATE.getCode();
	/**
	 * 指标名称
	 */
	protected  final String indicatorName = "任务及时办结完成率回率";


	@Override
	protected void handleLog(LocalDate date) {
		logService.extractTaskFinishRateLog(date);
	}

	/**
	 * 每个单位的指标的得分
	 * @param date
	 */
	@Override
	protected void handleDataSet(LocalDate date) {
		int year = date.getYear();
		int monthValue = date.getMonthValue();
		AbstractScoringStrategy scoringStrategy = scoringStrategyFactory.getScoringStrategy(IndicatorScoreTypeEnum.STRATEGY_THREE.getCode());
		scoringStrategy.genDataSet(year,monthValue,indicatorCode,paramTypeCode);
	}

	@Override
	protected String getIndicatorDesc() {
		 	return this.getIndicatorCode()+this.getIndicatorName()+":";
	}
}
