<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndicatorLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorLogResultMap" type="com.snszyk.zbusiness.stat.entity.IndicatorLog">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="system_no" property="systemNo"/>
        <result column="indicator_code" property="indicatorCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="param_type" property="paramType"/>
        <result column="param_value" property="paramValue"/>
    </resultMap>
    <delete id="deleteByMonth">
        delete
        from indicator_log
        where year = #{year}
        and month = #{month}

    </delete>

    <select id="selectIndicatorLogPage" resultMap="indicatorLogResultMap">
        select *
        from indicator_log
        where is_deleted = 0
    </select>

    <select id="genLoginRateLog"
            resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">
        select t.org_code,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) ), 4) as numerator,
               ROUND( sum(t.param_value), 4) as denominator,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) / sum(t.param_value)), 4) as count
        from indicator_log t
                 left join indicator_log t1 on t1.system_no = t.system_no and
                                               t1.year = t.year and
                                               t1.month = t.month and
                                               t1.is_deleted = 0 and
                                               t1.param_type = 2 and
                                               t1.org_code = t.org_code
        where t.is_deleted = 0
          and t.param_type = 1
          and t.year = #{year}
          and t.month = #{month}
          and t.system_no = 'XXJSGK'
          and t.param_value>0
        group by t.org_code

    </select>
    <select id="IndicatorMaxMinScoreDto" resultType="com.snszyk.zbusiness.stat.dto.IndicatorMaxMinScoreDto">
        select MAX(CAST(t.param_value AS DECIMAL(10, 4))) AS max,
               MIN(CAST(t.param_value AS DECIMAL(10, 4))) AS min
        from indicator_log t
        where t.is_deleted = 0
          and t.param_type = #{paramCode}
          and t.year = #{year}
          and t.month = #{month}
          and t.indicator_code = #{indicatorCode}
          and t.system_no = 'XXJSGK'
    </select>

    <select id="genSuperviseLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">
        select count(t.id) as count, t2.dept_code as orgCode
        from m_child_task t

                 left join m_task t1 on t1.id = t.task_id
                 left join szyk_dept t2
                           on t.responsible_org_id = t2.id and t2.is_deleted = 0 and t2.hide = 0 and t2.dept_status = 1
        where t.is_deleted = 0
          and t.create_time between #{start} and #{end}
          and t1.task_type = 1
          and t2.dept_code is not null
          and t2.dept_category = 1
          and t1.is_supervise = 1
        GROUP BY t2.dept_code

    </select>

    <select id="genTaskBackLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        SELECT t.dept_code                                        AS orgCode,
               sum(if(t3.back_number is null, 0, t3.back_number)) as numerator,
               sum(case
                       when t3.back_number is null then 1
                       when t3.back_number > 0 and t3.child_task_status = 3 then t3.back_number
                       when t3.back_number > 0 and t3.child_task_status != 3 then t3.back_number + 1
                       else 0
                   end
               )                                                  as denominator
        FROM szyk_dept t
                 left join szyk_dept t1
                           on t1.unit_id = t.id and t1.is_deleted = 0 and t1.dept_status = 1 and t1.hide = 0
                 LEFT JOIN m_task_issue t3
                           ON t3.responsible_org_id = t1.id
                 left join m_child_task t2
                           on t2.issue_id = t3.id and t2.is_deleted = 0
                 left join szyk_dept t4
                           on t4.id = t2.issue_org_id and t4.is_deleted = 0 and t4.dept_status = 1 and t4.hide = 0

        WHERE t.is_deleted = 0
          and t.dept_code is not null
          and t.dept_status = 1
          and t.hide = 0
          and t.dept_category = 1
          and t2.is_deleted = 0
          and t3.id is not null
          and t3.is_deleted = 0
          and t2.is_supervise = 0
          and t2.file_status = 1
          and t2.child_task_status != 1
          and t2.file_time between #{startOfMonth} and #{endOfMonth}
          and t4.unit_id != t.id
        GROUP BY t.dept_code;

    </select>
    <select id="genUserActiveRateLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        select t.org_code,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) ), 4) as numerator   ,
               ROUND(sum(t.param_value), 4) as denominator,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) / sum(t.param_value)), 4) as count
        from indicator_log t
                 left join indicator_log t1 on t1.system_no = t.system_no and
                                               t1.year = t.year and
                                               t1.month = t.month and
                                               t1.is_deleted = 0 and
                                               t1.param_type = 7 and
                                               t1.org_code = t.org_code
        where t.is_deleted = 0
          and t.param_type = 1
          and t.year = #{year}
          and t.month = #{month}
          and t.system_no = 'XXJSGK'
          and t1.id is not null
        group by t.org_code
    </select>
    <select id="genTaskFinishRateLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        SELECT t.dept_code                                                      AS orgCode,
                sum(if(t2.submit_date &lt;= t2.limit_date and t2.child_task_status =2, 1, 0))
                    as numerator,
                count(t2.id) as denominator,
               sum(if(t2.submit_date &lt;= t2.limit_date and t2.child_task_status =2, 1, 0)) / count(t2.id) as count
        FROM szyk_dept t
                 left join szyk_dept t1 on t1.unit_id=t.id and t1.is_deleted = 0 and t1.dept_status = 1 and t1.hide=0
                 left join m_child_task t2 on t2.responsible_org_id = t1.id and t2.is_deleted = 0 and t2.limit_date BETWEEN #{startOfMonth} and #{endOfMonth}
            left join  szyk_dept t3 on t3.id = t2.issue_org_id and t3.is_deleted = 0 and t3.dept_status = 1 and t3.hide=0
        where t.is_deleted = 0
          and t.hide = 0
          and t.dept_status = 1
          and t.dept_category = 1
          and t2.id is not null
          and t2.is_supervise = 0
            and t3.unit_id !=t.id
        GROUP by t.dept_code

    </select>
    <select id="indicatorProjectFinishRateByOrg"
            resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">


    </select>
    <select id="genProjectReportLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        SELECT t.dept_code  AS orgCode,
               count(t2.id) as count
        FROM szyk_dept t

                 left join bpm_instance_log t2
                           on t2.operator_dept_id = t.id and t2.is_deleted = 0 and t2.operate_type = 1
                               and t2.cross_level = 1
                 left join bpm_instance t3
                           on t3.id = t2.bpm_instance_id and t3.is_deleted = 0
                 left join project_base t4
                           on t4.instance_id = t3.id and t4.is_deleted = 0

        where t.is_deleted = 0
          and t.hide = 0
          and t.dept_status = 1
          and t.dept_category = 1
          and t2.id is not null
          and t2.batch_no_time between #{startOfMonth} and #{endOfMonth}
        GROUP by t.dept_code

    </select>
    <select id="genProjectBackLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        SELECT t.dept_code  AS orgCode,
               count(t2.id) as count
        FROM szyk_dept t
                 left join bpm_instance_log t2
                           on t2.target_dept_id = t.id and t2.is_deleted = 0 and t2.operate_type = 3
                               and t2.cross_level = 1
                 left join bpm_instance t3
                           on t3.id = t2.bpm_instance_id and t3.is_deleted = 0
                 left join project_base t4
                           on t4.instance_id = t3.id and t4.is_deleted = 0

        where t.is_deleted = 0
          and t.hide = 0
          and t.dept_status = 1
          and t.dept_category = 1
          and t2.id is not null
          and t2.batch_no_time between #{startOfMonth} and #{endOfMonth}
        GROUP by t.dept_code


    </select>
    <select id="genProjectQualityLog" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">

        select t.org_code,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) ), 4) as numerator   ,
               ROUND(sum(t.param_value), 4) as denominator,
               ROUND(if(count(t1.id) = 0, 0, sum(t1.param_value) / sum(t.param_value)), 4) as count
        from indicator_log t
                 left join indicator_log t1 on t1.system_no = t.system_no and
                                               t1.year = t.year and
                                               t1.month = t.month and
                                               t1.is_deleted = 0 and
                                               t1.param_type = 10 and
                                               t1.org_code = t.org_code
        where t.is_deleted = 0
          and t.param_type = 9
          and t.year = #{year}
          and t.month = #{month}
          and t.system_no = 'XXJSGK'
        group by t.org_code
    </select>

    <select id="listLoginRateByTime" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogDto">
        select t.org_code,
               t.year,
               t.month,
               t.param_value
        from indicator_log t
        where t.is_deleted = 0
          and t.param_type = 3
          and t.year = #{year}
          and t.month = #{month}
          and t.system_no = 'XXJSGK'
    </select>
</mapper>
