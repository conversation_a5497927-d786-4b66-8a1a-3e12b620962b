/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标-主题实体类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndexTitle extends BaseCrudEntity {

	/**
	* 系统id
	*/
	private Long systemId;
	/**
	* 主题名称
	*/
	private String title;


}
