/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.snszyk.zbusiness.stat.dto.IndexSystemDto;
import com.snszyk.zbusiness.stat.vo.IndexSystemVo;
import com.snszyk.zbusiness.stat.service.IIndexSystemService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 指标-系统 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@AllArgsConstructor
@Service
public class IndexSystemLogicService extends BaseCrudLogicService<IndexSystemDto, IndexSystemVo> {

    private final IIndexSystemService indexSystemService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.indexSystemService;
    }
}
