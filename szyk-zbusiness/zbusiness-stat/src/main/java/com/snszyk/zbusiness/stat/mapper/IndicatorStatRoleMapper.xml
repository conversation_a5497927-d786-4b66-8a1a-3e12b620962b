<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndicatorStatRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="indicatorStatRoleResultMap" type="com.snszyk.zbusiness.stat.entity.IndicatorStatRole">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="indicator_code" property="indicatorCode"/>
        <result column="role_id" property="roleId"/>
    </resultMap>


    <select id="selectIndicatorStatRolePage" resultMap="indicatorStatRoleResultMap">
        select * from indicator_stat_role where is_deleted = 0
    </select>

</mapper>
