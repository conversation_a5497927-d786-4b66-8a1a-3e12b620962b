/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.logic;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.stat.dto.IndicatorStatRoleDto;
import com.snszyk.zbusiness.stat.service.IIndicatorStatRoleService;
import com.snszyk.zbusiness.stat.vo.IndicatorStatRoleVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 登录率和活跃率统计角色配置 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@AllArgsConstructor
@Service
public class IndicatorStatRoleLogicService extends BaseCrudLogicService<IndicatorStatRoleDto, IndicatorStatRoleVo> {

    private final IIndicatorStatRoleService indicatorStatRoleService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.indicatorStatRoleService;
    }
}
