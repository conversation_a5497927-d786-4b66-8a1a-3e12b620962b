<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.stat.mapper.IndicatorReportSystemMapper">

    <delete id="deleteByMonth">
        delete
        from indicator_report_system
        where year = #{year}
          and month = #{month}
    </delete>


    <select id="systemRankPage" resultType="com.snszyk.zbusiness.stat.dto.IndicatorReportSystemDto">
        select
        tr.rank ,
        t.id, t.system_no, t.year, t.month, t.score, t.last_month_score, t.chain_base, t.indicator_num, t.create_user,
        t.create_dept, t.create_time, t.update_user, t.update_time, t.status, t.is_deleted,
        t1.dict_value as systemName
        from indicator_report_system t
        left join szyk_dict_biz t1 on t1.code='system_code' and t1.is_deleted=0 and t1.dict_key =t.system_no
        left join (SELECT rank() over (order by tt.score desc) as rank ,tt.system_no from indicator_report_system tt where tt.year =#{v.year} and tt.`month`=#{v.month} and tt.is_deleted=0) tr on tr.system_no=t.system_no
        where t.is_deleted = 0
        <if test="v.systemNo!=null and v.systemNo !=''">
            and t.system_no = #{v.systemNo}
        </if>
        and t.year=#{v.year} and t.month=#{v.month}
        group by t.system_no,t.year,t.month
        <if test="v.sort==1">
            order by score desc
        </if>
        <if test="v.sort==2">
            order by score asc
        </if>


    </select>
    <select id="systemAvgRank" resultType="java.math.BigDecimal">
        select avg(IFNULL(t.score,0))  as score
        from indicator_report_system t
        where t.is_deleted = 0
        <if test="v.systemNo!=null and v.systemNo !=''">
            and t.system_no = #{v.systemNo}
        </if>
        and t.year=#{v.year} and t.month=#{v.month}

    </select>

    <select id="systemHistoryScore" resultType="com.snszyk.zbusiness.stat.dto.IndicatorOrgHistoryRankDto">
        select
        t.score as score,
        concat(t.year,'年',t.month,'月') as time,
        t.year,
        t.month,
        tavg.score as avgScore
        from indicator_report_system t
        left join(
        select t1.year,t1.month, round(avg(t1.score),2) as score
        from indicator_report_system t1
        where t1.is_deleted = 0

        and ((t1.year = #{v.year} AND t1.month BETWEEN 1 AND #{v.month})
        OR (t1.year = #{v.year} - 1 AND t1.month BETWEEN 1 AND 12))
        group by t1.year,t1.month
        ) tavg on t.year =tavg.year and t.month=tavg.month
        where t.is_deleted = 0
        and t.system_no = #{v.systemNo}
        and ((t.year = #{v.year} AND t.month BETWEEN 1 AND #{v.month})
        OR (t.year = #{v.year} - 1 AND t.month BETWEEN 1 AND 12))
        order by t.year ,t.month
    </select>
    <select id="orgDetailList" resultType="com.snszyk.zbusiness.stat.dto.IndicatorReportOrgDto">
        select
        rank() over (
            order by score desc
        ) as 'rank',
        t.id, t.system_no, t.year, t.month, t.score, t.last_month_score, t.chain_base, t.indicator_num,
        t.system_no,t.org_code,
        t2.dept_name as orgName,
        t1.dict_value as systemName
        from indicator_report_org t
        left join szyk_dict_biz t1 on t1.code='system_code' and t1.is_deleted=0 and t1.dict_key =t.system_no
        left join szyk_dept t2 on t2.dept_code=t.org_code and t2.is_deleted=0
        where t.is_deleted = 0
        <if test="v.systemNo!=null and v.systemNo!=''">
            and t.system_no = #{v.systemNo}
        </if>
        and t.year=#{v.year} and t.month=#{v.month}

        and ( t2.parent_code=#{v.upOrgCode}

        <if test="v.upOrgCode=='00000001'">
            or t2.parent_code='00002000'
        </if>
            )
        <if test="v.sort==1">
            order by score desc
        </if>
        <if test="v.sort==2">
            order by score asc
        </if>
        <if test="v.sort==null">
            order by score desc
        </if>
    </select>
</mapper>
