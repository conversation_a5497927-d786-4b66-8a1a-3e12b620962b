/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.stat.dto.IndicatorLibraryDto;
import com.snszyk.zbusiness.stat.dto.IndicatorLibrarySystemDto;
import com.snszyk.zbusiness.stat.entity.IndicatorLibrary;
import com.snszyk.zbusiness.stat.enums.IndicatorSystemEnum;
import com.snszyk.zbusiness.stat.mapper.IndicatorLibraryMapper;
import com.snszyk.zbusiness.stat.service.IIndicatorLibraryService;
import com.snszyk.zbusiness.stat.vo.IndicatorLibraryPageVo;
import com.snszyk.zbusiness.stat.vo.IndicatorLibraryVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.snszyk.zbusiness.dict.enums.DictBizEnum.SYSTEM_CODE;
import static com.snszyk.zbusiness.dict.enums.DictBizEnum.XXJSGK_MODULE;

/**
 * 指标库 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@AllArgsConstructor
@Service
public class IndicatorLibraryServiceImpl extends BaseCrudServiceImpl<IndicatorLibraryMapper, IndicatorLibrary, IndicatorLibraryDto, IndicatorLibraryVo> implements IIndicatorLibraryService {


	@Override
	public IPage<IndicatorLibraryDto> pageList(IndicatorLibraryPageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 校验指标编码是否重复
	 * @param indicatorCode
	 * @param id
	 * @return
	 */
	@Override
	public Integer validNoRepeat(String indicatorCode, Long id) {
		return this.lambdaQuery().eq(IndicatorLibrary::getIndicatorCode, indicatorCode)
			.ne(id != null, IndicatorLibrary::getId, id).count();
	}

	/**
	 * 校验指标名称是否重复
	 *
	 * @param systemNo
	 * @param indicatorName
	 * @param id
	 * @return
	 */
	@Override
	public Integer validNameRepeat(String systemNo, String indicatorName, Long id) {
		return this.lambdaQuery().eq(IndicatorLibrary::getIndicatorName, indicatorName)
			.eq(IndicatorLibrary::getSystemNo, systemNo)
			.ne(id != null, IndicatorLibrary::getId, id).count();
	}

	/**
	 * 查询指标系统
	 * @param systemName
	 * @return
	 */
	@Override
	public List<IndicatorLibrarySystemDto> getIndicatorSystemListByName(String systemName) {
		return baseMapper.getIndicatorSystemListByName(SYSTEM_CODE.getCode(), systemName);
	}


	/**
	 * 查询指标系统
	 * @param moduleCode
	 * @return
	 */
	@Override
	public List<IndicatorLibrarySystemDto> getIndicatorModuleList(String moduleCode) {
		return baseMapper.getIndicatorModuleList(moduleCode);
	}

	/**
	 * 指标详情
	 * @param id
	 * @return
	 */
	@Override
	public IndicatorLibraryDto detail(Long id) {
		return this.baseMapper.detail(id);
	}

	/**
	 * 信息系统树形
	 * @return
	 */
	@Override
	public List<IndicatorLibrarySystemDto> systemTree() {
		List<IndicatorLibrarySystemDto> resultList = new ArrayList<>();
		IndicatorLibrarySystemDto indicatorLibrarySystemDto = new IndicatorLibrarySystemDto();
		resultList.add(indicatorLibrarySystemDto);
		indicatorLibrarySystemDto.setSystemNo("");
		indicatorLibrarySystemDto.setSystemName("全部系统");
		indicatorLibrarySystemDto.setType(CommonConstant.ONE);
		indicatorLibrarySystemDto.setHasChildren(false);
		List<IndicatorLibrarySystemDto> systemList = getIndicatorSystemListByName(null);
		if (Func.isNotEmpty(systemList)) {
			Integer allTotal = systemList.stream().map(e -> e.getIndicatorCount()).reduce(Integer::sum).get();
			indicatorLibrarySystemDto.setIndicatorCount(allTotal);
			indicatorLibrarySystemDto.setChildren(systemList);
			indicatorLibrarySystemDto.setHasChildren(true);
			List<IndicatorLibrarySystemDto> librarySystemDtos = systemList.stream().filter(e -> IndicatorSystemEnum.XXJSGK.getCode().equals(e.getSystemNo())).collect(Collectors.toList());
			if (!librarySystemDtos.isEmpty()) {
				IndicatorLibrarySystemDto xxjsgk = librarySystemDtos.get(0);
				List<IndicatorLibrarySystemDto> xxjsgkModuleList = getIndicatorModuleList(XXJSGK_MODULE.getCode());
				if (Func.isNotEmpty(xxjsgkModuleList)) {
					xxjsgk.setHasChildren(true);
					xxjsgk.setChildren(xxjsgkModuleList);
				}
			}
		}
		return resultList;
	}

	@Override
	public List<IndicatorLibraryDto> queryBySystemNo(String systemNo) {
		List<IndicatorLibrary> list = this.lambdaQuery().eq(IndicatorLibrary::getSystemNo, systemNo).list();
		//如果list不为空
		if (Func.isNotEmpty(list)) {
			return BeanUtil.copyProperties(list, IndicatorLibraryDto.class);
		}
		return null;
	}

	@Override
	public IndicatorLibraryDto getByCode(String indicatorCode) {
		return BeanUtil.copy(this.lambdaQuery().eq(IndicatorLibrary::getIndicatorCode, indicatorCode).one(), IndicatorLibraryDto.class);

	}

	@Override
	public boolean clearWeighted(Long id) {
		return this.lambdaUpdate().eq(IndicatorLibrary::getId, id).set(IndicatorLibrary::getWeighted, null
		).update();

	}
}
