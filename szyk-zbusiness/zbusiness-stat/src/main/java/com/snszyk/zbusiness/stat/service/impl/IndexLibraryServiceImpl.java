/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.stat.service.impl;

import com.snszyk.zbusiness.stat.entity.IndexLibrary;
import com.snszyk.zbusiness.stat.dto.IndexLibraryDto;
import com.snszyk.zbusiness.stat.vo.IndexLibraryVo;
import com.snszyk.zbusiness.stat.mapper.IndexLibraryMapper;
import com.snszyk.zbusiness.stat.service.IIndexLibraryService;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 指标库 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@AllArgsConstructor
@Service
public class IndexLibraryServiceImpl extends BaseCrudServiceImpl<IndexLibraryMapper, IndexLibrary, IndexLibraryDto, IndexLibraryVo> implements IIndexLibraryService {


}
