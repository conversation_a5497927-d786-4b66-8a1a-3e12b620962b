/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.person.service.logic;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUnit;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.*;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.dto.UserDeptDTO;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.TreeNodeNew;
import com.snszyk.system.entity.User;
import com.snszyk.system.enums.DeptCategoryEnum;
import com.snszyk.system.mapper.DeptMapper;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IUserDeptService;
import com.snszyk.system.service.IUserService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.system.vo.DeptIncludePersonCountVO;
import com.snszyk.zbusiness.person.dto.*;
import com.snszyk.zbusiness.person.enums.DepartContactEnum;
import com.snszyk.zbusiness.person.enums.PersonExceptionEnum;
import com.snszyk.zbusiness.person.service.IPersonalBaseService;
import com.snszyk.zbusiness.person.service.IPersonalDetailService;
import com.snszyk.zbusiness.person.vo.PersonalBaseEditVo;
import com.snszyk.zbusiness.person.vo.PersonalBaseListVo;
import com.snszyk.zbusiness.person.vo.PersonalBaseVo;
import com.snszyk.zbusiness.person.vo.PersonalPageVo;
import com.snszyk.zbusiness.rpc.service.IDataOceanService;
import com.snszyk.zbusiness.rpc.vo.PersonConditionVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员基本信息表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@AllArgsConstructor
@Service
public class PersonalBaseLogicService extends BaseCrudLogicService<PersonalBaseDto, PersonalBaseVo> {

	private final IPersonalBaseService personalBaseService;

	private final IPersonalDetailService personalDetailService;

	private final IDeptService deptService;

	private final DeptMapper deptMapper;

	private final IDataOceanService dataOceanService;

	private final IUserService userService;
	private final IUserDeptService userDeptService;
	;


	public IPage<PersonalBasePageDto> dataPage(PersonalPageVo vo) {
		//获取当前用户的单位id
		//v1.4这里改为只能查看本层级的单位或虚拟单位的
		Long unitId = SysCache.getUnitDept(Long.valueOf(AuthUtil.getDeptId())).getId();
		if (Func.isEmpty(vo.getOrgId())) {
			vo.setOrgId(unitId);
		}
		Long orgId = vo.getOrgId();

		List<Long> orgList = new LinkedList<>();
		//查询该单位下的所有虚拟单位
		List<Dept> depts = deptService.queryVirtualUnit(orgId, SysCache.getDept(orgId).getUnitId());
		if (CollectionUtils.isNotEmpty(depts)) {
			List<Long> collect = depts.stream().map(Dept::getId).collect(Collectors.toList());
			orgList.addAll(collect);
		}
		orgList.add(orgId);
		// 查询人员基本信息
		IPage<PersonalBasePageDto> page = personalBaseService.dataPage(vo, orgList);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return null;
		}
		page.getRecords().forEach(t -> {
			/*if (Func.isNotEmpty(t.getDepartContact())) {
				t.setDepartContactName(DepartContactEnum.getByCode(t.getDepartContact()).getDictValue());
			}*/
			//组装联络人信息
			String contactPerson = t.getDepartContact();
			if (StringUtil.isNotBlank(contactPerson)) {
				List<String> contactPersonNameList = new LinkedList<>();
				List<String> contactPersonList = Func.toStrList(contactPerson);
				contactPersonList.forEach(key -> {
					if (StringUtil.isNotBlank(key)) {
						String value = DictBizCache.getValue(DictBizEnum.CONTACT_PERSON, key);
						if(!contactPersonNameList.contains(value)){
							contactPersonNameList.add(value);
						}
					}
				});
				t.setDepartContactName(String.join(",", contactPersonNameList));
			}
			//是否开通账号
			User user = UserCache.getUser(SzykConstant.ADMIN_TENANT_ID, t.getJobNo());
			if (Func.isNotEmpty(user)) {
				t.setAccountOpen(DepartContactEnum.YES.getDictValue());
			}
			// 工作类别
			if (Func.isNotEmpty(t.getJobCategory())) {
				t.setJobCategoryName(DictBizCache.getValue(DictBizEnum.JOB_CATEGORY, t.getJobCategory()));
			}
			// 岗位类别
			if (Func.isNotEmpty(t.getPostCategory())) {
				t.setPostCategoryName(DictBizCache.getValue(DictBizEnum.POST_CATEGORY, t.getPostCategory()));
			}
			//增加编辑权限
			//如果当前单位的unit和当前登录人一致
//			if (unitId.equals(SysCache.getDept(t.getManageOrgId()).getUnitId())) {
			t.setEditFlag(1);
//			}
			t.setManageOrgName(SysCache.getDeptName(t.getManageOrgId()));
		});
		return page;
	}

	public IPage<PersonalBaseDto> pageList(PersonalBaseListVo vo) {
		IPage<PersonalBaseDto> result = personalBaseService.pageList(vo);

		if (ObjectUtil.isEmpty(result) || CollectionUtils.isEmpty(result.getRecords())) {
			return null;
		}


		return result;
	}


	@Transactional(rollbackFor = Exception.class)
	public BaseCrudDto save(PersonalBaseEditVo v) {
		//不可编辑
		v.setDepartContact(null);
		PersonalBaseDto personalBaseDto = personalBaseService.fetchById(v.getId());
		PersonalBaseVo vo = BeanUtil.copyProperties(personalBaseDto, PersonalBaseVo.class);
		BeanUtil.copyProperties(v, vo);
		return personalBaseService.update(vo);
	}

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.personalBaseService;
	}

	/**
	 * 获取上级公司
	 *
	 * @param deptId
	 * @return
	 */
	@Transactional(readOnly = true)
	public DeptDTO getSuperiorDept(Long deptId) {
		Dept dept = deptService.getById(deptId);
		if (ObjectUtil.isEmpty(dept) || 0 == dept.getParentId()) {
			return null;
		}
		Dept parentDept = deptService.getById(dept.getParentId());
		return BeanUtil.copy(parentDept, DeptDTO.class);
	}

	/**
	 * 详情查询
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public PersonDto getById(Long id) {
		// 查询人员基本信息
		PersonalBaseDto personalBaseDto = personalBaseService.fetchById(id);
		if (personalBaseDto == null) {
			return null;
		}
		//组装联络人信息
		List<UserDeptDTO> userDeptDTOS = userDeptService.listByUserCode(personalBaseDto.getJobNo());
		String contactPersonStr = userDeptDTOS.stream().map(UserDeptDTO::getContactPerson).filter(StringUtil::isNotBlank).distinct().collect(Collectors.joining(","));
		if (Func.isNotBlank(contactPersonStr)) {
			List<String> contactPersonNameList = new LinkedList<>();
			List<String> contactPersonList = Func.toStrList(contactPersonStr);
			contactPersonList.forEach(key -> {
				contactPersonNameList.add(DictBizCache.getValue(DictBizEnum.CONTACT_PERSON, key));
			});
			personalBaseDto.setDepartContactName(String.join(",", contactPersonNameList));
		}
		//是否开通账号
		User user = UserCache.getUser(SzykConstant.ADMIN_TENANT_ID, personalBaseDto.getJobNo());
		if (Func.isNotEmpty(user)) {
			personalBaseDto.setAccountOpen(DepartContactEnum.YES.getDictValue());
		}

		if (Func.isNotEmpty(personalBaseDto.getTitles())) {
			personalBaseDto.setTitlesName(DictBizCache.getValue(DictBizEnum.TITLES, personalBaseDto.getTitles()));
		}
		if (Func.isNotEmpty(personalBaseDto.getRank())) {
			personalBaseDto.setRankName(DictBizCache.getValue(DictBizEnum.RANK, personalBaseDto.getRank()));
		}
		if (Func.isNotEmpty(personalBaseDto.getJobCategory())) {
			personalBaseDto.setJobCategoryName(DictBizCache.getValue(DictBizEnum.JOB_CATEGORY, personalBaseDto.getJobCategory()));
		}
		if (Func.isNotEmpty(personalBaseDto.getPostCategory())) {
			personalBaseDto.setPostCategoryName(DictBizCache.getValue(DictBizEnum.POST_CATEGORY, personalBaseDto.getPostCategory()));
		}
		//截取时间为日期
		if (StringUtil.isNoneBlank(personalBaseDto.getRzsj())) {
			if (personalBaseDto.getRzsj().length() > 10) {
				personalBaseDto.setRzsj(personalBaseDto.getRzsj().substring(0, 10));
			}
		}
		if (StringUtil.isNoneBlank(personalBaseDto.getCjgzsj())) {
			if (personalBaseDto.getCjgzsj().length() > 10) {
				personalBaseDto.setCjgzsj(personalBaseDto.getCjgzsj().substring(0, 10));
			}
		}

		//获取岗位从业年限 = 当前时间 - 岗位从业开始时间
		if (StringUtil.isNoneBlank(personalBaseDto.getPostStartDate())) {
			String postStartDate = personalBaseDto.getPostStartDate();
			String dateFormat = "yyyy-MM-dd";
			if (postStartDate.length() > 10) {
				postStartDate = postStartDate.substring(0, 10);
			} else if (postStartDate.length() == 8) {
				dateFormat = "yyyyMMdd";
			}
			Long days = cn.hutool.core.date.DateUtil.between(DateUtil.parse(postStartDate, dateFormat), new Date(), DateUnit.DAY);

			Integer years = new BigDecimal(days).divide(new BigDecimal("365"), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
			personalBaseDto.setPostYears(years);
			personalBaseDto.setPostStartDate(DateUtil.formatDate(DateUtil.parse(postStartDate, dateFormat)));
		}
		personalBaseDto.setManageOrgName(SysCache.getDeptName(personalBaseDto.getManageOrgId()));
		personalBaseDto.setDepartName(deptService.queryByCode(personalBaseDto.getDepartCode()).getDeptName());
		// 查询人员详情
		PersonalDetailDto personalDetailDto = BeanUtil.copyProperties(personalBaseDto, PersonalDetailDto.class);
		PersonDto personDto = new PersonDto();
		personDto.setBaseDto(personalBaseDto);
		personDto.setDetailDto(personalDetailDto);
		hasAuth(personDto);

		return personDto;
	}

	private void hasAuth(PersonDto personDto) {
		//管理员
		List<String> currentRole = DeptScopeUtil.getCurrentRole();
		if (currentRole.contains("administrator")||currentRole.contains("admin")) {
			return;
		}
		PersonalBaseDto baseDto = personDto.getBaseDto();
		if (baseDto == null) {
			return;
		}
		Long manageOrgId = baseDto.getManageOrgId();

		//数据权限
		List<Long> longs = DeptScopeUtil.verticalDeptList();
		longs.stream().filter(e -> e.equals(manageOrgId)).findFirst().orElseThrow(() -> new ServiceException("无权查看"));
		//菜单权限
		List<String> currentMenu = DeptScopeUtil.getCurrentMenu();
		if (!currentMenu.contains("信息化从业人员-详情")) {
			throw new ServiceException("无权查看");
		}
	}

	/**
	 * 删除
	 *
	 * @param idList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteByIds(List<Long> idList) {
		// 删除人员信息
		return personalBaseService.deleteByIds(idList);
	}


	public void personExport(PersonalPageVo vo, HttpServletResponse response) {
		String sheetName = "sheet1";

		String fileName = "信息系统维护-人员管理数据表-" + DateUtil.format(new Date(), "yyyyMMdd");

		if (Func.isEmpty(vo.getOrgId())) {
			Long unitId = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId())).getUnitId();
			vo.setOrgId(unitId);
		}
		List<PersonExportDto> resultList = new ArrayList<>();
		Long orgId = vo.getOrgId();
		List<Long> orgList = new LinkedList<>();
		//查询该单位下的所有虚拟单位
		List<Dept> depts = deptService.queryVirtualUnit(orgId, SysCache.getDept(orgId).getUnitId());
		if (CollectionUtils.isNotEmpty(depts)) {
			List<Long> collect = depts.stream().map(Dept::getId).collect(Collectors.toList());
			orgList.addAll(collect);
		}
		orgList.add(orgId);
		// 查询人员信息
		List<PersonalBaseDto> personBaseList = personalBaseService.dataList(vo, orgList, vo.getPersonIds());
		if (CollectionUtils.isNotEmpty(personBaseList)) {
			List<String> deptCodeList = personBaseList.stream().map(PersonalBaseDto::getDepartCode).distinct().collect(Collectors.toList());
			List<DeptDTO> deptDTOList = deptService.listByDeptCodeList(deptCodeList);
			//部门的code map
			Map<String, DeptDTO> deptCodeMap = deptDTOList.stream().collect(Collectors.toMap(Dept::getDeptCode, e -> e, (e1, e2) -> e2));
			resultList = BeanUtil.copyProperties(personBaseList, PersonExportDto.class);
			for (int i = 0; i < resultList.size(); i++) {
				PersonExportDto dto = resultList.get(i);
				dto.setSerialNumber((long) i + 1);
				//组装联络人信息
				String contactPerson = dto.getDepartContact();
				if (StringUtil.isNotBlank(contactPerson)) {
					List<String> contactPersonNameList = new LinkedList<>();
					List<String> contactPersonList = Func.toStrList(contactPerson);
					contactPersonList.forEach(key -> {
						if (StringUtil.isNotBlank(key)) {
							contactPersonNameList.add(DictBizCache.getValue(DictBizEnum.CONTACT_PERSON, key));
						}
					});
					dto.setDepartContactName(String.join(",", contactPersonNameList));
				}

				if (Func.isNotEmpty(dto.getTitles())) {
					dto.setTitles(DictBizCache.getValue(DictBizEnum.TITLES, dto.getTitles()));
				}
				if (Func.isNotEmpty(dto.getRank())) {
					dto.setRank(DictBizCache.getValue(DictBizEnum.RANK, dto.getRank()));
				}
				if (Func.isNotEmpty(dto.getJobCategory())) {
					dto.setJobCategory(DictBizCache.getValue(DictBizEnum.JOB_CATEGORY, dto.getJobCategory()));
				}
				if (Func.isNotEmpty(dto.getPostCategory())) {
					dto.setPostCategory(DictBizCache.getValue(DictBizEnum.POST_CATEGORY, dto.getPostCategory()));
				}
				//截取时间为日期
				if (StringUtil.isNoneBlank(dto.getRzsj())) {
					if (dto.getRzsj().length() > 10) {
						dto.setRzsj(dto.getRzsj().substring(0, 10));
					}
				}
				if (StringUtil.isNoneBlank(dto.getCjgzsj())) {
					if (dto.getCjgzsj().length() > 10) {
						dto.setCjgzsj(dto.getCjgzsj().substring(0, 10));
					}
				}

				//获取岗位从业年限 = 当前时间 - 岗位从业开始时间
				if (StringUtil.isNoneBlank(dto.getPostStartDate())) {
					String postStartDate = dto.getPostStartDate();
					String dateFormat = "yyyy-MM-dd";
					if (postStartDate.length() > 10) {
						postStartDate = postStartDate.substring(0, 10);
					} else if (postStartDate.length() == 8) {
						dateFormat = "yyyyMMdd";
					}
					Long days = cn.hutool.core.date.DateUtil.between(DateUtil.parse(postStartDate, dateFormat), new Date(), DateUnit.DAY);

					Integer years = new BigDecimal(days).divide(new BigDecimal("365"), 0, BigDecimal.ROUND_HALF_DOWN).intValue();
					dto.setPostYears(years);
					dto.setPostStartDate(DateUtil.formatDate(DateUtil.parse(postStartDate, dateFormat)));
				}
				dto.setManageOrgName(SysCache.getDeptName(dto.getManageOrgId()));
				DeptDTO deptDTO = deptCodeMap.get(dto.getDepartCode());
				if (deptDTO != null) {
					dto.setDepartName(deptDTO.getDeptName());
				}
			}
			// 导出
			ExcelUtil.export(response, fileName + DateUtil.time(), sheetName, resultList, PersonExportDto.class);
			// EasyExcelUtil.exportExcel(response, sheetName, fileName, PersonExportDto.class, resultList);
		}

	}

	public List<DeptIncludePersonCountVO> treeIncludePersonCount(String tenantId, Long parentId) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		List<TreeNodeNew> deptList = new LinkedList<>();
		if (Func.isEmpty(parentId)) {
			//获取当前用户的id作为
			Dept dept = SysCache.getUnitDept(Long.valueOf(AuthUtil.getDeptId()));
			//查询当前用户的上级组织
			TreeNodeNew treeNodeNew = new TreeNodeNew();
			treeNodeNew.setTitle(dept.getDeptName());
			treeNodeNew.setKey(dept.getId());
			treeNodeNew.setValue(dept.getId());
			treeNodeNew.setId(dept.getId());
			treeNodeNew.setParentId(dept.getParentId());
			treeNodeNew.setHasChildren(false);
			treeNodeNew.setIsLeaf(true);
			List<Dept> list = deptService.queryUnitByParentId(dept.getId());
			if (CollectionUtil.isNotEmpty(list)) {
				treeNodeNew.setHasChildren(true);
				treeNodeNew.setIsLeaf(false);
			}
			deptList.add(treeNodeNew);
		} else {
			deptList = deptMapper.lazyUnitTree(tenantId, parentId, null);
		}

		if (ObjectUtil.isEmpty(deptList)) {
			return null;
		}
		List<DeptIncludePersonCountVO> list = BeanUtil.copyProperties(deptList, DeptIncludePersonCountVO.class);

//		list.forEach(d -> {
//			StringBuilder sb = new StringBuilder();
//			// 获取获取子部门ID集合
//
//			Integer personCount = personalBaseService.countByOrgIdsNew(d.getId());
//			d.setPersonCount(personCount);
//			sb.append(d.getTitle()).append("(").append(personCount).append(")");
//			d.setTitle(sb.toString());
//		});

		/*List<DeptIncludePersonCountVO> topDeptList = list.stream()
			.filter(v -> v.getParentId() == 0)
			.collect(Collectors.toList());

		topDeptList.forEach(d -> {
			d.setChildren(this.getChildren(d.getId(), list));

			d.setHasChildren(d.getChildren().size() > 0);
		});
		return topDeptList;*/
		return list;
	}


//	public List<DeptIncludePersonCountVO> getChildren(Long id, List<DeptIncludePersonCountVO> list) {
//		List<DeptIncludePersonCountVO> childList = new ArrayList<>();
//		list.forEach(d -> {
//			if (id.equals(d.getParentId())) {
//				childList.add(d);
//			}
//		});
//		if (ObjectUtil.isEmpty(childList)) {
//			return Collections.EMPTY_LIST;
//		}
//
//		childList.forEach(d -> {
//			d.setChildren(getChildren(d.getId(), list));
//			d.setHasChildren(d.getChildren().size() > 0);
//		});
//
//		return childList;
//	}

	public IPage<PersonalBaseDto> batchAddPage(PersonConditionVo v) {
		List<String> users = new LinkedList<>();
		Long manageOrgId = v.getManageOrgId();
		//获取当前部门的人员信息
		if (Func.isEmpty(manageOrgId)) {
			manageOrgId = Long.valueOf(AuthUtil.getDeptId());
		}
		Dept dept = SysCache.getDept(manageOrgId);
		Long orgId = dept.getUnitId();
		//单位中增加虚拟单位的用户
		List<Long> orgList = new LinkedList<>();
		//查询该单位下的所有虚拟单位
		List<Dept> depts = deptService.queryVirtualUnit(orgId, SysCache.getDept(orgId).getUnitId());
		if (CollectionUtils.isNotEmpty(depts)) {
			List<Long> collect = depts.stream().map(Dept::getId).collect(Collectors.toList());
			orgList.addAll(collect);
		}
		orgList.add(orgId);
		return dataOceanService.conditionPerson(v, users);
	}

	@Transactional
	public Boolean batchAdd(List<PersonalBaseVo> list, Long manageOrgId) {

		if (manageOrgId == null) {
			Dept unitDept = SysCache.getUnitDept(Long.valueOf(AuthUtil.getDeptId()));
			if (unitDept != null) {
				manageOrgId = unitDept.getId();
			} else {
				manageOrgId = Long.valueOf(AuthUtil.getDeptId());
			}
		}
		if (CollectionUtil.isEmpty(list)) {
			throw new ServiceException(PersonExceptionEnum.SUBMIT_NULL.getMessage());
		}
		/*Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		dept = SysCache.getDept(dept.getUnitId());

		Dept finalDept = dept;*/
		Long finalManageOrgId = manageOrgId;
		list.forEach(vo -> {
			//校验重复数据
			PersonalBaseDto personalBaseDto = personalBaseService.queryByJobNo(vo.getJobNo());
			if (Func.isNotEmpty(personalBaseDto)) {
				throw new ServiceException(vo.getPersonalName() + "已存在，请勿重复导入!");
			}
			vo.setManageOrgId(finalManageOrgId);
			vo.setManageOrgName(SysCache.getDept(finalManageOrgId).getDeptName());
			//根据人员所在部门编码，查询所属单位信息
			/*Dept depart = deptService.queryByCode(vo.getDepartCode());
			if (Func.isEmpty(depart)) {
				vo.setManageOrgId(finalDept.getId());
				vo.setManageOrgName(finalDept.getDeptName());
			} else {
				List<String> departList = Arrays.asList(depart.getAncestors().split(StringPool.COMMA));
				Collections.reverse(departList);
				//查询所在部门的上级单位信息
				for (String departId : departList) {
					Dept deptN = deptService.getById(Long.valueOf(departId));
					Integer deptCategory = deptN.getDeptCategory();
					if (DeptCategoryEnum.UNIT.getCode().equals(deptCategory) || DeptCategoryEnum.VIRTUAL_UNIT.getCode().equals(deptCategory)) {
						//如果是单位或者虚拟单位则赋值
						vo.setManageOrgId(deptN.getId());
						vo.setManageOrgName(deptN.getDeptName());
						break;
					}
				}
			}*/
			personalBaseService.save(vo);
		});
		return true;
	}

	public Boolean handleDept() {
		//查询所有信息
		List<PersonalBaseDto> list = personalBaseService.queryList();
		for (PersonalBaseDto vo : list) {
			//根据人员所在部门编码，查询所属单位信息
			Dept depart = deptService.queryByCode(vo.getDepartCode());
			//如果当前组织不存在
			if (Func.isEmpty(depart)) {
				continue;
			}
			List<String> departList = Arrays.asList(depart.getAncestors().split(StringPool.COMMA));
			Collections.reverse(departList);
			//查询所在部门的上级单位信息
			for (String departId : departList) {
				Dept dept = deptService.getById(Long.valueOf(departId));
				Integer deptCategory = dept.getDeptCategory();
				if (DeptCategoryEnum.UNIT.getCode().equals(deptCategory) || DeptCategoryEnum.VIRTUAL_UNIT.getCode().equals(deptCategory)) {
					//如果是单位或者虚拟单位则赋值
					vo.setManageOrgId(dept.getId());
					vo.setManageOrgName(dept.getDeptName());
					break;
				}
			}
			PersonalBaseVo baseVo = BeanUtil.copyProperties(vo, PersonalBaseVo.class);
			personalBaseService.save(baseVo);
		}
		return true;
	}

	public Boolean updateFromDataOcean(String jobNo) {
		if (StringUtil.isEmpty(jobNo)) {
			return true;
		}
		//查询这个人所在的数据湖信息
		PersonalBaseDto dto = dataOceanService.queryByJobNo(jobNo);
		if (Func.isNotEmpty(dto)) {
			//更新人员信息
			PersonalBaseDto personalBaseDto = personalBaseService.queryByJobNo(jobNo);
			//不更新组织
			Long manageOrgId = personalBaseDto.getManageOrgId();
			String manageOrgName = personalBaseDto.getManageOrgName();
			String departCode = personalBaseDto.getDepartCode();
			String departName = personalBaseDto.getDepartName();
			cn.hutool.core.bean.BeanUtil.copyProperties(dto, personalBaseDto, CopyOptions.create().setIgnoreNullValue(true));
			personalBaseDto.setManageOrgId(manageOrgId);
			personalBaseDto.setManageOrgName(manageOrgName);
			personalBaseDto.setDepartCode(departCode);
			personalBaseDto.setDepartName(departName);
			personalBaseService.save(BeanUtil.copy(personalBaseDto, PersonalBaseVo.class));
		}
		return true;

	}
}
