<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.person.mapper.PersonalBaseMapper">

    <!-- 通用查询映射结果 -->
    <select id="selectPageByUnit" resultType="com.snszyk.zbusiness.person.entity.PersonalBase">
        SELECT DISTINCT
        t.id,
        t.manage_org_id,
        t.manage_org_name,
        t.secondary_org_id,
        t.secondary_org_name,
        t.personal_name,
        t.job_no,
        t.birth_date,
        t.jg,
        t.jkzk,
        t.depart_code,
        t.depart_name,
        t.phone_no,
        t.job_status,
        t.mz,
        t.rzsj,
        t.cjgzsj,
        t.gl,
        t.hyzk,
        t.sex,
        t.education,
        t.university,
        t.subject,
        t.titles,
        t.portfolio,
        t.`rank`,
        t.job_category,
        t.post_category,
        t.post,
        t.post_start_date,
        t.post_years,
        t.gz,
        t.main_technical_capability,
        t.create_user,
        t.create_dept,
        t.create_time,
        t.update_user,
        t.update_time,
        t.status,
        t.is_deleted,
        group_concat(distinct t3.contact_person) as departContact
        FROM
        personal_base t
        left join szyk_user t2 on (t.job_no = t2.account and t2.is_deleted=0)
        LEFT JOIN szyk_user_dept t1 ON (t.manage_org_id = t1.dept_id and t2.id = t1.user_id )
        LEFT JOIN szyk_dept dept ON (t.manage_org_id = dept.id and dept.is_deleted=0   and dept.hide=0 )
        left join szyk_user_dept t3 on t2.id =t3.user_id
        left join szyk_dept t4 on t4.id =t.secondary_org_id and t4.is_deleted=0 and t4.hide=0 and t4.dept_status=1
        left join szyk_dept t5 on t5.dept_code =t.depart_code and t5.is_deleted=0 and t5.hide=0 and t5.dept_status=1
        <where>
            and t.is_deleted = 0
            <if test="vo.manageOrgName!=null and vo.manageOrgName!=''">
                and t.manage_org_name like concat('%',#{vo.manageOrgName},'%')
            </if>
            <if test="vo.personalName!=null and vo.personalName!=''">
                and t.personal_name like concat('%',#{vo.personalName},'%')
            </if>
            <if test="vo.unitRange == null or vo.unitRange == 1 ">
                and t.manage_org_id in
                <foreach item="id" collection="orgIds" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <if test="vo.personIds != null and vo.personIds.size>0 ">
                and t.id in
                <foreach item="id" collection="personIds" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="vo.unitRange==2">
                and (dept.ancestors like concat('%',#{vo.orgId},'%') or
                t.manage_org_id = #{vo.orgId}
                )
            </if>
            <if test="vo.jobCategory !=null and vo.jobCategory !='' ">
                and t.job_category =#{vo.jobCategory}
            </if>
            <if test="vo.postCategory !=null and vo.postCategory !='' ">
                and t.post_category=#{vo.postCategory}
            </if>
            <if test="vo.departContact !=null and vo.departContact !='' ">
                and t3.contact_person like concat('%',#{vo.departContact},'%')
            </if>
            group by t.id ,dept.sort,t1.contact_person,t5.id,t.depart_name
            order by t4.sort asc , dept.sort asc, t5.sort asc,t.depart_name asc,t5.id asc,departContact desc,t.id asc
        </where>
    </select>


    <resultMap id="personalBaseResultMap" type="com.snszyk.zbusiness.person.entity.PersonalBase">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="manage_org_id" property="manageOrgId"/>
        <result column="manage_org_name" property="manageOrgName"/>
        <result column="secondary_org_id" property="secondaryOrgId"/>
        <result column="secondary_org_name" property="secondaryOrgName"/>
        <result column="job_no" property="jobNo"/>
        <result column="personal_name" property="personalName"/>
        <result column="birth_date" property="birthDate"/>
        <result column="depart_name" property="departName"/>
        <result column="phone_no" property="phoneNo"/>
        <result column="depart_contact" property="departContact"/>
        <result column="job_status" property="jobStatus"/>
    </resultMap>


    <select id="selectPersonalBasePage" resultMap="personalBaseResultMap">
        select *
        from personal_base
        where is_deleted = 0
    </select>

</mapper>
