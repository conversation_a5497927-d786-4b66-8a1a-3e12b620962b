/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.person.service.logic;

import com.snszyk.zbusiness.person.dto.PersonalDetailDto;
import com.snszyk.zbusiness.person.service.IPersonalDetailService;
import com.snszyk.zbusiness.person.vo.PersonalDetailVo;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 人员明细表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@AllArgsConstructor
@Service
public class PersonalDetailLogicService extends BaseCrudLogicService<PersonalDetailDto, PersonalDetailVo> {

    private final IPersonalDetailService personalDetailService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.personalDetailService;
    }
}
