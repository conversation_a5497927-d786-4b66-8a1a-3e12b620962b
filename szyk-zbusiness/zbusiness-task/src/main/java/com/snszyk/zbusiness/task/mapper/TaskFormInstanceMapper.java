///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.mapper;
//
//import com.snszyk.zbusiness.task.entity.TaskFormInstance;
//import com.snszyk.zbusiness.task.vo.TaskFormInstanceVo;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import java.util.List;
//
///**
// * 任务表单实例 Mapper 接口
// *
// * <AUTHOR>
// * @since 2023-04-24
// */
//public interface TaskFormInstanceMapper extends BaseMapper<TaskFormInstance> {
//
//	/**
//	 * 自定义分页
//	 *
//	 * @param page
//	 * @param taskFormInstance
//	 * @return
//	 */
//	List<TaskFormInstanceVo> selectTaskFormInstancePage(IPage page, TaskFormInstanceVo taskFormInstance);
//
//}
