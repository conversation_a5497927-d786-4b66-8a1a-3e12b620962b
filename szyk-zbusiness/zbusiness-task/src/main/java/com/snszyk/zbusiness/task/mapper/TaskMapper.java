/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.stat.dto.EchartCircleDto;
import com.snszyk.zbusiness.stat.dto.HomeTaskStatDto;
import com.snszyk.zbusiness.stat.vo.HomeTaskStatVo;
import com.snszyk.zbusiness.task.dto.TaskDto;
import com.snszyk.zbusiness.task.entity.Task;
import com.snszyk.zbusiness.task.vo.TaskVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface TaskMapper extends BaseMapper<Task> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param task
	 * @return
	 */
	List<TaskVo> selectTaskPage(IPage page, TaskVo task);

	/**
	 * 任务下发统计
	 * @param v
	 * @return
	 */
    HomeTaskStatDto issueStat(@Param("v") HomeTaskStatVo v);

	/**
	 * 任务执行统计
	 * @param v
	 * @return
	 */
	HomeTaskStatDto  executeStat(@Param("v") HomeTaskStatVo v);

	/**
	 * 驾驶仓任务下发统计 只统计总部的
	 *
	 * @param orgId
	 * @param isSupervise
	 * @return
	 */
	List<EchartCircleDto> headTaskIssueStat(@Param("startDate") LocalDateTime startDate,
											@Param("endDate") LocalDateTime endDate,
											@Param("orgId") Long orgId, @Param("isSupervise") Integer isSupervise);

	/**
	 * 超时提报任务统计
	 *
	 * @param startDate
	 * @param endDate
	 * @param sort
	 * @param orgId
	 * @param isSupervise
	 * @return
	 */
    List<EchartCircleDto> overtimeSubmitTaskStat(@Param("startDate") LocalDateTime startDate,
												 @Param("endDate") LocalDateTime endDate,
												 @Param("sort") Integer sort,
												 @Param("orgId") Long orgId,
												 @Param("isSupervise") Integer isSupervise);

	/**
	 * 任务退回次数统计
	 *
	 * @param startDate
	 * @param endDate
	 * @param sort
	 * @param orgId
	 * @return
	 */
	List<EchartCircleDto> backTaskStat(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("sort") Integer sort, @Param("orgId") Long orgId);

    TaskDto getByIdIgnoreDel(@Param("id") Long id);
}
