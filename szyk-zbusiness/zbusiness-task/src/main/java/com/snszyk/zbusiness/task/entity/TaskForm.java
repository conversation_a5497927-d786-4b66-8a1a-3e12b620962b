/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务表单实体类
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@TableName("m_task_form")
@EqualsAndHashCode(callSuper = true)
public class TaskForm extends BaseCrudEntity {

	/**
	* 模板id
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long modelId;
	/**
	* 字段名称
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String attrName;
	/**
	* 字段类型
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String attrType;
	/**
	* 是否必填，0否1是
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer isRequire;
	/**
	* 待选值数据源
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String selectData;
	/**
	* 精度
	*/
	@TableField(value = "`precision`", updateStrategy = FieldStrategy.IGNORED)
	private Integer precision;
	/**
	* 日期类型
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dataType;
	/**
	* 字段长度
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer fieldLength;
	/**
	* 排序
	*/
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer sort;


}
