/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务模板实体类
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@TableName("m_task_model")
@EqualsAndHashCode(callSuper = true)
public class TaskModel extends BaseCrudEntity {

	/**
	 * 模板名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String modelName;
	/**
	 * 所属组织id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long companyId;
	/**
	 * 所属组织名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String companyName;
	/**
	 * 状态
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String modelStatus;


}
