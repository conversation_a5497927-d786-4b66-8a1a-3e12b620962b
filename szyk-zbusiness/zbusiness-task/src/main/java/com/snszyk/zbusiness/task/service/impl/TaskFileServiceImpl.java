/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service.impl;

import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.zbusiness.task.dto.TaskFileDto;
import com.snszyk.zbusiness.task.entity.TaskFile;
import com.snszyk.zbusiness.task.mapper.TaskFileMapper;
import com.snszyk.zbusiness.task.service.ITaskFileService;
import com.snszyk.zbusiness.task.vo.TaskFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务文档表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@AllArgsConstructor
@Service
public class TaskFileServiceImpl extends BaseCrudServiceImpl<TaskFileMapper, TaskFile, TaskFileDto, TaskFileVo> implements ITaskFileService {


	private final IAttachService attachService;

	/**
	 * 根据业务id和文件类型查询list
	 *
	 * @param taskId
	 * @param taskFileTypeEnum
	 * @return
	 */
//	@Override
//	public List<TaskFileDto> listByTaskIdAndType(Long taskId, String taskFileTypeEnum) {
//		List<TaskFile> list = this.lambdaQuery().eq(TaskFile::getTaskId, taskId)
//			.eq(TaskFile::getBusinessType, taskFileTypeEnum).list();
//		return BeanUtil.copy(list, TaskFileDto.class);
//	}

	/**
	 * 根据id批量删除
	 *
	 * @param needDeleteIds
	 * @return
	 */
	@Override
	public boolean deleteByIds(List<Long> needDeleteIds) {
		int result = baseMapper.deleteBatchIds(needDeleteIds);
		if (result <= CommonConstant.ZERO) {
			throw new ServiceException("删除文件失败!");
		}
		return true;
	}

	/**
	 * 根据任务id查询附件列表
	 *
	 * @param taskId
	 * @return
	 */
//	@Override
//	public List<TaskFileDto> listByTaskId(Long taskId) {
//
//		LambdaQueryWrapper<TaskFile> wrapper = Wrappers.lambdaQuery(TaskFile.class).eq(TaskFile::getTaskId, taskId);
//		List<TaskFile> list = list(wrapper);
//		if (CollectionUtil.isEmpty(list)) {
//			return new ArrayList<>();
//		}
//		List<TaskFileDto> fileDtoList = BeanUtil.copy(list, TaskFileDto.class);
//		return fileDtoList;
//	}

//	/**
//	 * 根据任务id删除附件
//	 *
//	 * @param taskId
//	 * @return
//	 */
//	@Override
//	public int deleteByTaskId(Long taskId) {
//		LambdaQueryWrapper<TaskFile> wrapper = Wrappers.lambdaQuery(TaskFile.class).eq(TaskFile::getTaskId, taskId);
//		remove(wrapper);
//		return CommonConstant.ONE;
//	}

	/**
	 * 根据任务id和类型删除附件
	 *
	 * @param taskIds
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean deleteByTaskIdsAndType(List<Long> taskIds, List<String> businessTypes) {


		List<TaskFile> list = this.lambdaQuery().in(TaskFile::getTaskId, taskIds).in(TaskFile::getBusinessType, businessTypes).list();
		if (CollectionUtil.isEmpty(list)) {
			return true;
		}
		List<Long> ids = list.stream().map(BaseCrudEntity::getId).collect(Collectors.toList());
		boolean b = this.deleteByIds(ids);
		return b;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void batchUpdate(List<TaskFileVo> fileList) {
		if (CollectionUtil.isEmpty(fileList)) {
			return;
		}
		List<TaskFile> copyList = BeanUtil.copy(fileList, TaskFile.class);
		boolean saved = SpringUtil.getBean(TaskFileServiceImpl.class).updateBatchById(copyList);
		if (!saved) {
			throw new ServiceException("保存文件失败");
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void batchSave(List<TaskFileVo> fileList) {
		if (CollectionUtil.isEmpty(fileList)) {
			return;
		}
		List<TaskFile> copyList = BeanUtil.copy(fileList, TaskFile.class);
		boolean saved = SpringUtil.getBean(TaskFileServiceImpl.class).saveBatch(copyList);
		if (!saved) {
			throw new ServiceException("保存文件失败");
		}
	}

	/**
	 * 据业务ids和文件类型查询list
	 *
	 * @param businessIds
	 * @param fileTypeList
	 * @return
	 */

	@Override
	public List<TaskFileDto> listByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeList) {
		if (CollectionUtil.isEmpty(businessIds) || CollectionUtil.isEmpty(fileTypeList)) {
			return new ArrayList<>();
		}
		List<TaskFile> list = this.lambdaQuery().in(TaskFile::getTaskId, businessIds).in(TaskFile::getBusinessType, fileTypeList).list();
		List<TaskFileDto> fileDtoList = BeanUtil.copy(list, TaskFileDto.class);
		if (CollectionUtil.isEmpty(fileDtoList)) {
			return fileDtoList;
		}
		supplyAttachDetail(fileDtoList);

		return fileDtoList;
	}

	/**
	 *查询基本信息
	 * @param businessIds
	 * @param fileTypeList
	 * @return
	 */
	@Override
	public List<TaskFileDto> listBaseByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeList) {
		if (CollectionUtil.isEmpty(businessIds) || CollectionUtil.isEmpty(fileTypeList)) {
			return new ArrayList<>();
		}
		List<TaskFile> list = this.lambdaQuery().in(TaskFile::getTaskId, businessIds).in(TaskFile::getBusinessType, fileTypeList).list();
		List<TaskFileDto> fileDtoList = BeanUtil.copy(list, TaskFileDto.class);
		if (CollectionUtil.isEmpty(fileDtoList)) {
			return fileDtoList;
		}
		return fileDtoList;
	}

	/**
	 * 据业务ids和文件类型查询文件下详情列表
	 *
	 * @param businessIds
	 * @param fileTypeList
	 * @return
	 */

	@Override
	public List<TaskFileDto> listDetailByBusinessIdsAndType(List<Long> businessIds, List<String> fileTypeList) {
		List<TaskFileDto> taskFileDtoList = listByBusinessIdsAndType(businessIds, fileTypeList);
		if (CollectionUtil.isEmpty(taskFileDtoList)) {
			return new ArrayList<>();
		}
		supplyAttachDetail(taskFileDtoList);
		return taskFileDtoList;

	}

	/**
	 * 补充附件详细信息
	 *
	 * @param taskFileDtoList
	 */
	@Override
	public void supplyAttachDetail(List<TaskFileDto> taskFileDtoList) {
		List<Long> attachIds = taskFileDtoList.stream().map(TaskFileDto::getAttachId).collect(Collectors.toList());
		List<Attach> attachList = attachService.listByFileIds(attachIds);
		if (CollectionUtil.isEmpty(attachIds)) {
			return;
		}
		Map<Long, Attach> attachIdMap = attachList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
		for (TaskFileDto dto : taskFileDtoList) {
			Attach attach = attachIdMap.get(dto.getAttachId());
			if (attach != null) {
				dto.setLink(attach.getLink());
				dto.setOriginalName(attach.getOriginalName());
				dto.setName(attach.getName());
				dto.setDomain(attach.getDomain());
			}

		}
	}

}
