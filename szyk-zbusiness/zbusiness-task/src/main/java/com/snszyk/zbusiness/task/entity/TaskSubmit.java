/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务提报记录实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@TableName("m_task_submit")
@EqualsAndHashCode(callSuper = true)
public class TaskSubmit extends BaseCrudEntity {

	/**
	 * 任务id
	 */
	private Long taskId;
	/**
	 * 填报记录id
	 */
	private Long submitNoteId;
	/**
	 * 任务下发情况id
	 */
	private Long taskIssueId;

}
