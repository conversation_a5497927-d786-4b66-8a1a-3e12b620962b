/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 任务管理实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@TableName("m_task")
@EqualsAndHashCode(callSuper = true)
public class Task extends BaseCrudEntity {

	/**
	 * 任务编号
	 */
	private String taskNo;
	/**
	 * 类型：新增/转发
	 */
	private String taskType;
	/**
	 * 关联子任务id
	 */
	private Long childTaskId;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 相关要求
	 */
	private String requirement;
	/**
	 * 优先级，数据字典
	 */
	private String priority;
	/**
	 * 下发机构id
	 */
	private Long issueOrgId;
	/**
	 * 下发机构名称
	 */
	private String issueOrgName;
	/**
	 * 发起人id
	 */
	private Long initiatorId;
	/**
	 * 发起人姓名
	 */
	private String initiatorName;
	/**
	 * 是否引用模板
	 */
	private Integer useModel;
	/**
	 * 模板id
	 */
	private Long modelId;
	/**
	 * 模板名称
	 */
	private String modelName;
	/**
	 * 模板实例id
	 */
	private Long modelInstantId;
	/**
	 * 限定办结日期
	 */
	private LocalDateTime limitDate;
	/**
	 * 任务状态
	 */
	private String taskStatus;
	/**
	 * 关联业务id
	 */
	private String businessId;
	/**
	 * 关联业务类型
	 */
	private String businessType;
	/**
	 * 解析模板：是/否
	 */
	private String readModel;
	/**
	 * 模板表头
	 */
	private String modelHead;
	/**
	 * 任务祖级列表
	 */
	private String ancestors;
	/**
	 * 归档状态，0未归档1已归档
	 */
	private Integer fileStatus;

	@ApiModelProperty(value = "是否督办(0 否1 是)")
	private Integer isSupervise;

	@ApiModelProperty(value = "归档时间")
	private LocalDateTime fileTime;

	/**
	 * 撤销原因
	 */
	@ApiModelProperty(value = "撤销原因")
	private String revokeReason;
}
