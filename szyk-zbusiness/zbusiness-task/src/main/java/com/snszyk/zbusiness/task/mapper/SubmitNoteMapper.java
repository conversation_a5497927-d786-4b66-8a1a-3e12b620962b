/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.task.dto.SubmitNoteDto;
import com.snszyk.zbusiness.task.entity.SubmitNote;
import com.snszyk.zbusiness.task.vo.SubmitNotePageVo;
import com.snszyk.zbusiness.task.vo.SubmitNoteVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 填报记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface SubmitNoteMapper extends BaseMapper<SubmitNote> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param submitNote
	 * @return
	 */
	List<SubmitNoteVo> selectSubmitNotePage(IPage page, SubmitNoteVo submitNote);

	/**
	 * 根据任务id查询填报内容
	 *
	 * @param taskId
	 * @param issueId
	 * @return
	 */
	List<SubmitNoteDto> listSubmitNoteListByTaskId(@Param("taskId") Long taskId, @Param("issueId") Long issueId);

	/**
	 * 根据子任务id查询填报内容
	 * @param childTaskId
	 * @return
	 */
	List<SubmitNoteDto> listSubmitNoteListByChildTaskId(@Param("childTaskId") Long childTaskId);

	/**
	 *
	 * 查询子级的提报内容
	 * @param page
	 * @param vo
	 * @return
	 */
	Page<SubmitNoteDto> childSubmitNotePage(IPage page, @Param("vo") SubmitNotePageVo vo);

	/**
	 * 自己的填报内容分页
	 * @param page
	 * @param vo
	 * @return
	 */

	Page<SubmitNote> selfSubmitNotePage(IPage page, @Param("vo") SubmitNotePageVo vo);


}
