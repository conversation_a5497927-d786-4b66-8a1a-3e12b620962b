/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.system.vo.UserVO;
import com.snszyk.zbusiness.assess.dto.LinkDataDto;
import com.snszyk.zbusiness.assess.dto.linkData4TaskDto;
import com.snszyk.zbusiness.dict.dto.CommonDeleteDto;
import com.snszyk.zbusiness.task.dto.*;
import com.snszyk.zbusiness.task.service.logic.TaskLogicService;
import com.snszyk.zbusiness.task.vo.*;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;


/**
 * 任务管理 控制器
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-task/task")
@Api(value = "任务管理", tags = "任务管理接口")
@Validated
public class TaskController extends BaseCrudController {


	private final TaskLogicService taskLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {

		return taskLogicService;
	}

	/**
	 * 任务下发
	 */
	@PostMapping("/issue")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "任务下发", notes = "TaskVo")
	public R<TaskDto> issue(@RequestBody @Valid TaskVo v) {
		TaskDto dto = taskLogicService.issue(v);
		return R.data(dto);
	}

	/**
	 * 任务下发责任人校验
	 */
	@PostMapping("/issueValid")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "任务下发责任人校验", notes = "TaskVo")
	public R<List<TaskIssueValidDto>> issueValid(@RequestBody List<TaskIssueVo> list) {
		List<TaskIssueValidDto> result = taskLogicService.issueValid(list);
		return R.data(result);
	}

	/**
	 * 任务退回
	 */
	@PostMapping("/back")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "任务退回", notes = "TaskVo")
	public R<Integer> taskBack(@RequestBody @Valid TaskBackVo v) {
		Integer r = taskLogicService.taskBack(v);
		return R.data(r);
	}

	/**
	 * 任务撤销
	 */
	@PostMapping("/revoke")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "任务撤销", notes = "任务撤销")
	public R<Boolean> taskRevoke(@RequestBody @Valid TaskRevokeVo v) {
		Boolean result = taskLogicService.taskRevoke(v);
		return R.data(result);
	}

	/**
	 * 任务归档
	 */
	@PostMapping("/placeOnFile")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "任务归档", notes = "任务归档")
	public R<Boolean> placeOnFile(@RequestBody BaseObjectVo v) {
		Boolean dto = taskLogicService.placeOnFile(v);
		return R.data(dto);
	}

	/**
	 * 分页
	 */
	@PostMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "TaskVo")
	public R<IPage<TaskPageDto>> page(@RequestBody TaskQueryVo v) {
		IPage<TaskPageDto> pageQueryResult = taskLogicService.pageList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 子级的填报内容分页
	 */
	@PostMapping("/childSubmitNotePage")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "填报内容分页,子级提报的", notes = "填报内容分页")
	public R<IPage<SubmitNoteDto>> childSubmitNotePage(@RequestBody SubmitNotePageVo v) {
		IPage<SubmitNoteDto> pageQueryResult = taskLogicService.childSubmitNotePage(v);
		return R.data(pageQueryResult);
	}


	/**
	 * 任务详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "任务详情", notes = "id")
	public R<TaskDto> detail(Long id) {
		TaskDto taskDto = taskLogicService.detail(id);
		return R.data(taskDto);
	}


	@ApiOperationSupport(order = 6)
	@ApiOperation("导出填报内容")
	@PostMapping("/exportNoteContent")
	public void export(@RequestBody SubmitNoteExportParamVo vo, HttpServletRequest request, HttpServletResponse response) {
		taskLogicService.exportNoteContent(vo, request, response);
	}


	@GetMapping("/getByBusinessIdAndBusinessType")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据业务id和业务类型查询子任务", notes = "childTaskId")
	public R<TaskDto> getByBusinessIdAndBusinessType(Long businessId, String businessType) {
		TaskDto taskDto = taskLogicService.getByBusinessIdAndBusinessType(businessId, businessType);
		return R.data(taskDto);
	}

	/**
	 * excel解析
	 */
	@PostMapping("/parseExcel")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "excel模板解析", notes = "TaskVo")
	public R<TaskTemplateParseDto> parseExcel(@RequestParam Long menuId, @RequestParam MultipartFile file) throws IOException {
		TaskTemplateParseDto result = taskLogicService.parseExcel(menuId, file);
		return R.data(result);
	}

	/**
	 * 关联查询
	 */
	@GetMapping("/linkData")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "关联查询（督办使用）", notes = "关联查询（督办使用）")
	public R<List<LinkDataDto>> linkData(@RequestParam(required = false) String businessId, @RequestParam String businessType) {
		List<LinkDataDto> result = taskLogicService.linkData(businessId, businessType);
		return R.data(result);
	}

	@PostMapping("/getUsers")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "查询责任人", notes = "查询责任人 v1.4根据业务范围查询")
	public R<IPage<UserVO>> getUsers(@RequestBody TaskUserVo vo) {
		IPage<UserVO> result = taskLogicService.getTaskUsers(vo.getDeptIds(), vo.getUserName(), vo.getContact(), vo.getCurrent(), vo.getSize());
		return R.data(result);
	}


	@PostMapping("/getUsersByRole")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "一键选择联络人", notes = "一键选择联络人")
	public R<List<UserVO>> oneChooseContactPerson(@RequestBody TaskUserRoleVo vo) {

		List<UserVO> result = taskLogicService.oneChooseContactPerson(vo.getDeptIds());
		return R.data(result);
	}

	/**
	 * 退回记录查询
	 */
	@GetMapping("/listBackHistory")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "退回记录查询", notes = "TaskVo")
	public R<List<TaskBackHistoryDto>> listBackHistory(@RequestParam String issueId) {
		List<TaskBackHistoryDto> r = taskLogicService.listBackHistory(Long.valueOf(issueId));
		return R.data(r);
	}


	/**
	 * 进度跟踪排序
	 */
	@PostMapping("/issueSort")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "进度跟踪排序", notes = "TaskVo")
	public R<List<TaskIssueSortVo>> issueSort(@RequestBody List<TaskIssueSortVo> taskIssueVoList) {
		List<TaskIssueSortVo> r = taskLogicService.issueSort(taskIssueVoList);
		return R.data(r);
	}


	@PutMapping("/updateBusinessInfo/{id}")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "修改业务关联信息", notes = "修改业务关联信息")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "businessId", value = "业务ids", required = true, dataType = "String", paramType = "query"),
		@ApiImplicitParam(name = "businessType", value = "业务类型", required = true, dataType = "String", paramType = "query")
	})
	public R<TaskDto> updateBusinessInfo(@NotNull @PathVariable Long id, @NotBlank String businessId, @NotBlank String businessType) {
		return R.data(taskLogicService.updateBusinessInfo(id, businessId, businessType));
	}


	@GetMapping("/linkData4task")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "关联查询（任务使用）", notes = "关联查询（任务使用）")
	public R<List<linkData4TaskDto>> linkData4task(@RequestParam(required = true) @NotNull @ApiParam("id") Long id) {
		List<linkData4TaskDto> result = taskLogicService.linkData4TaskDto(id);
		return R.data(result);
	}

	@PostMapping("/deleteTask")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "删除任务", notes = "删除已撤销状态的任务及其关联的子任务和任务下发")
	public R<List<CommonDeleteDto>> deleteTask(@RequestBody @Valid TaskDeleteVo v) {
		List<CommonDeleteDto> result = taskLogicService.deleteTasks(v.getTaskIds());
		return R.data(result);
	}
}
