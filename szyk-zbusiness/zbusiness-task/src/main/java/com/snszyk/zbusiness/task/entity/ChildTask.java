/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 子任务管理实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@TableName("m_child_task")
@EqualsAndHashCode(callSuper = true)
	public class ChildTask extends BaseCrudEntity {

	/**
	* 任务id
	*/
	private Long taskId;
	/**
	* 下发id
	*/
	private Long issueId;
	/**
	* 子任务编号
	*/
	private String childTaskNo;
	/**
	* 标题
	*/
	private String title;
	/**
	* 相关要求
	*/
	private String requirement;
	/**
	* 优先级，数据字典
	*/
	private String priority;
	/**
	* 下发机构id
	*/
	private Long issueOrgId;
	/**
	* 下发机构名称
	*/
	private String issueOrgName;
	/**
	* 发起人id
	*/
	private Long initiatorId;
	/**
	* 发起人姓名
	*/
	private String initiatorName;
	/**
	* 任务责任人id
	*/
	private Long responsibleId;
	/**
	* 任务责任人姓名
	*/
	private String responsibleName;
	/**
	* 任务责任机构id
	*/
	private Long responsibleOrgId;
	/**
	* 任务责任机构名称
	*/
	private String responsibleOrgName;
	/**
	* 是否引用模板
	*/
	private Integer useModel;
	/**
	* 模板id
	*/
	private Long modelId;
	/**
	* 模板名称
	*/
	private String modelName;
	/**
	* 模板实例id
	*/
	private Long modelInstantId;
	/**
	* 限定办结日期
	*/
	private LocalDateTime limitDate;
	/**
	* 提报日期
	*/
	private LocalDateTime submitDate;
	/**
	* 退回原因
	*/
	private String backReason;
	/**
	* 子任务状态
	*/
	private String childTaskStatus;


	/**
	 * 关联业务id
	 */
	@ApiModelProperty(value = "关联业务id", hidden = true)
	private String businessId;
	/**
	 * 关联业务类型
	 */
	@ApiModelProperty(value = "关联业务类型", hidden = true)
	private String businessType;
	/**
	 * 解析模板：是/否
	 */
	@ApiModelProperty(value = "解析模板：是/否", hidden = true)
	private String readModel;
	/**
	 * 模板表头
	 */
	@ApiModelProperty(value = "模板表头", hidden = true)
	private String modelHead;

	/**
	 * 任务祖级列表
	 */
	@ApiModelProperty(value = "任务祖级列表")
	private String ancestors;
	/**
	 * 归档状态，0未归档1已归档
	 */
	@ApiModelProperty(value = "归档状态，0未归档1已归档")
	private Integer fileStatus;

	@ApiModelProperty(value = "是否督办(0 否1 是)")
	private Integer isSupervise;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "归档时间")
	private LocalDateTime fileTime;

	/**
	 * 撤销原因
	 */
	@ApiModelProperty(value = "撤销原因")
	private String revokeReason;
}
