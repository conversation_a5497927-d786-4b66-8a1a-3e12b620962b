/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service.logic;

import com.snszyk.zbusiness.task.dto.TaskSubmitDto;
import com.snszyk.zbusiness.task.vo.TaskSubmitVo;
import com.snszyk.zbusiness.task.service.ITaskSubmitService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.crud.service.IBaseCrudService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * 任务提报记录 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@AllArgsConstructor
@Service
public class TaskSubmitLogicService extends BaseCrudLogicService<TaskSubmitDto, TaskSubmitVo> {

    private final ITaskSubmitService taskSubmitService;

    @Override
    protected IBaseCrudService fetchBaseService() {
        return this.taskSubmitService;
    }
}
