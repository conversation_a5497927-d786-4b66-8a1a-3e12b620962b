///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.zbusiness.task.service.impl;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
//import com.snszyk.core.log.exception.ServiceException;
//import com.snszyk.core.tool.utils.BeanUtil;
//import com.snszyk.core.tool.utils.CollectionUtil;
//import com.snszyk.zbusiness.task.dto.TaskFormInstanceDto;
//import com.snszyk.zbusiness.task.entity.TaskFormInstance;
//import com.snszyk.zbusiness.task.mapper.TaskFormInstanceMapper;
//import com.snszyk.zbusiness.task.service.ITaskFormInstanceService;
//import com.snszyk.zbusiness.task.vo.TaskFormInstanceVo;
//import lombok.AllArgsConstructor;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 任务表单实例 服务实现类
// *
// * <AUTHOR>
// * @since 2023-04-24
// */
//@AllArgsConstructor
//@Service
//public class TaskFormInstanceServiceImpl extends BaseCrudServiceImpl<TaskFormInstanceMapper, TaskFormInstance, TaskFormInstanceDto, TaskFormInstanceVo> implements ITaskFormInstanceService {
//
//
//	@Override
//	public void batchSave(List<TaskFormInstanceVo> formList) {
//		List<TaskFormInstance> instanceList = BeanUtil.copy(formList, TaskFormInstance.class);
//		boolean saved = saveBatch(instanceList);
//		if(!saved){
//			throw new ServiceException("保存填报内容失败!");
//		}
//	}
//
//	@Override
//	public void batchUpdate(List<TaskFormInstanceVo> formList) {
//		List<TaskFormInstance> instanceList = BeanUtil.copy(formList, TaskFormInstance.class);
//		boolean saved = updateBatchById(instanceList);
//		if(!saved){
//			throw new ServiceException("修改填报内容失败!");
//		}
//	}
//
//	/**
//	 * 根据模版实例id查询
//	 * @param modelInstantId
//	 * @return
//	 */
//	@Override
//	public List<TaskFormInstanceDto> listByModelInstanceId(Long modelInstantId) {
//		LambdaQueryWrapper<TaskFormInstance> wrapper = Wrappers.lambdaQuery(TaskFormInstance.class).eq(TaskFormInstance::getModelInstantId, modelInstantId);
//		List<TaskFormInstance> list = list(wrapper);
//		if(CollectionUtil.isEmpty(list)){
//			return new ArrayList<>();
//		}
//		return BeanUtil.copy(list, TaskFormInstanceDto.class);
//	}
//
//	@Override
//	public void batchDeleteByIds(List<Long> idList) {
//		boolean removed = removeByIds(idList);
//		if(!removed){
//			throw new ServiceException("修改表单内容失败!");
//		}
//
//	}
//}
