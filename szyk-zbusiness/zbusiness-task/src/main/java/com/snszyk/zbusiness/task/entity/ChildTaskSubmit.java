/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 子任务提报记录实体类
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@TableName("m_child_task_submit")
@EqualsAndHashCode(callSuper = true)
public class ChildTaskSubmit extends BaseCrudEntity {

	/**
	* 子任务id
	*/
	private Long childTaskId;
	/**
	* 填报记录id
	*/
	private Long submitNoteId;
	/**
	 * 任务下发情况id
	 */
	private Long taskIssueId;


}
