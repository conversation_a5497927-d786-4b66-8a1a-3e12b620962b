/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务表单实例实体类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@TableName("m_task_form_instance")
@EqualsAndHashCode(callSuper = true)
public class TaskFormInstance extends BaseCrudEntity {

	/**
	* 任务模板实例id
	*/
	private Long modelInstantId;
	/**
	* 字段名称
	*/
	private String attrName;
	/**
	* 字段类型
	*/
	private String attrType;
	/**
	* 是否必填，0否1是
	*/
	private Integer isRequire;
	/**
	* 待选值数据源
	*/
	private String selectData;
	/**
	* 精度
	*/
	@TableField(value = "`precision`", updateStrategy = FieldStrategy.IGNORED)
	private Integer precision;
	/**
	* 日期类型
	*/
	private String dataType;
	/**
	* 字段长度
	*/
	private Integer fieldLength;
	/**
	* 排序
	*/
	private Integer sort;


}
