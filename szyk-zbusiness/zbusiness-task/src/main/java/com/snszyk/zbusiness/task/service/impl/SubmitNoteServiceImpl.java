/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.zbusiness.task.dto.SubmitNoteDto;
import com.snszyk.zbusiness.task.entity.SubmitNote;
import com.snszyk.zbusiness.task.mapper.SubmitNoteMapper;
import com.snszyk.zbusiness.task.service.ISubmitNoteService;
import com.snszyk.zbusiness.task.vo.SubmitNotePageVo;
import com.snszyk.zbusiness.task.vo.SubmitNoteVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 填报记录 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@AllArgsConstructor
@Service
public class SubmitNoteServiceImpl extends BaseCrudServiceImpl<SubmitNoteMapper, SubmitNote, SubmitNoteDto, SubmitNoteVo> implements ISubmitNoteService {


	/**
	 * 根据任务id查询填报列表
	 *
	 * @param taskId
	 * @param issueId
	 * @return
	 */
	@Override
	public List<SubmitNoteDto> listSubmitNoteListByTaskId(Long taskId, Long issueId) {
		return baseMapper.listSubmitNoteListByTaskId(taskId,issueId);
	}

	/**
	 * 根据子任务id查询填报列表
	 *
	 * @param childTaskId
	 * @return
	 */
	@Override
	public List<SubmitNoteDto> listSubmitNoteListByChildTaskId(Long childTaskId) {
		return baseMapper.listSubmitNoteListByChildTaskId(childTaskId);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean deleteByIds(List<Long> ids) {
		if (CollectionUtil.isEmpty(ids)) {
			return true;
		}
		boolean b = this.removeByIds(ids);
		if (!b) {
			throw new ServiceException("删除填报记录失败!");
		}
		return true;
	}

	/**
	 * 根据id查询填报记录
	 *
	 * @param noteIdList
	 * @return
	 */
	@Override
	public List<SubmitNoteDto> listByIds(List<Long> noteIdList) {
		if (CollectionUtil.isEmpty(noteIdList)) {
			return new ArrayList<>();
		}
		List<SubmitNote> list = lambdaQuery().in(SubmitNote::getId, noteIdList).list();

		return BeanUtil.copy(list, SubmitNoteDto.class);
	}

	/**
	 * 批量更新填报记录
	 *
	 * @param submitNoteVoList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean updateBatch(List<SubmitNoteVo> submitNoteVoList) {
		List<SubmitNote> submitNoteList = BeanUtil.copy(submitNoteVoList, SubmitNote.class);
		boolean updated = SpringUtil.getBean(SubmitNoteServiceImpl.class).updateBatchById(submitNoteList);
		for (int i = 0; i < submitNoteVoList.size(); i++) {
			// TODO: 2023/5/16  cesh
			submitNoteVoList.get(i).setId(submitNoteList.get(i).getId());

		}
		if (!updated) {
			throw new ServiceException("修改填报记录失败!");
		}

		return updated;
	}




	/**
	 * 自己的填报内容分页
	 *
	 * @param v
	 * @return
	 */
	@Override
	public IPage<SubmitNoteDto> selfSubmitNotePage(SubmitNotePageVo v) {
		Page<SubmitNote> page = new Page<>(v.getCurrent(), v.getSize());
		IPage<SubmitNote> pageResult = baseMapper.selfSubmitNotePage(page, v);
		return pageResult.convert(e -> BeanUtil.copy(e, SubmitNoteDto.class));
	}

	/**
	 * 分页查询
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public IPage<SubmitNoteDto> childSubmitNotePage(SubmitNotePageVo vo) {
		Page<SubmitNote> page = new Page<>(vo.getCurrent(), vo.getSize());
		Page<SubmitNoteDto> notePage = baseMapper.childSubmitNotePage(page, vo);
		IPage<SubmitNoteDto> convert = notePage.convert(e -> BeanUtil.copy(e, SubmitNoteDto.class));
		return convert;
	}

	/**
	 * 批量更新填报记录
	 *
	 * @param submitNoteVoList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean batchSave(List<SubmitNoteVo> submitNoteVoList) {
		List<SubmitNote> submitNoteList = BeanUtil.copy(submitNoteVoList, SubmitNote.class);
		boolean updated = SpringUtil.getBean(SubmitNoteServiceImpl.class).saveBatch(submitNoteList);
		for (int i = 0; i < submitNoteVoList.size(); i++) {
			// TODO: 2023/5/16  cesh
			submitNoteVoList.get(i).setId(submitNoteList.get(i).getId());

		}
		if (!updated) {
			throw new ServiceException("修改填报记录失败!");
		}

		return updated;
	}



}
