/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.task.mapper;

import com.snszyk.zbusiness.task.entity.TaskFile;
import com.snszyk.zbusiness.task.vo.TaskFileVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 任务文档表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface TaskFileMapper extends BaseMapper<TaskFile> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param taskFile
	 * @return
	 */
	List<TaskFileVo> selectTaskFilePage(IPage page, TaskFileVo taskFile);

}
