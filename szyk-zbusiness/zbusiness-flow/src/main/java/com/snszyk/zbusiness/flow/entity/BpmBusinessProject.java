/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批流业务-项目表实体类
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BpmBusinessProject extends BaseCrudEntity {

	/**
	* 流程实例id
	*/
	private Long instanceId;
	private Long projectId;
	/**
	* 项目名称
	*/
	private String projectName;
	/**
	* 二级审查意见
	*/
	private String secApproveRemark;
	/**
	* 集团审查结论
	*/
	private String corpApproveResult;
	/**
	* 集团审查意见
	*/
	private String corpApproveRemark;


}
