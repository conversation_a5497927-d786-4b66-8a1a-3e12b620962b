<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.flow.mapper.BpmBusinessProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bpmBusinessProjectResultMap" type="com.snszyk.zbusiness.flow.entity.BpmBusinessProject">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="instance_id" property="instanceId"/>
        <result column="project_name" property="projectName"/>
        <result column="sec_approve_remark" property="secApproveRemark"/>
        <result column="corp_approve_result" property="corpApproveResult"/>
        <result column="corp_approve_remark" property="corpApproveRemark"/>
    </resultMap>


    <select id="selectBpmBusinessProjectPage" resultMap="bpmBusinessProjectResultMap">
        select * from bpm_business_project where is_deleted = 0
    </select>

</mapper>
