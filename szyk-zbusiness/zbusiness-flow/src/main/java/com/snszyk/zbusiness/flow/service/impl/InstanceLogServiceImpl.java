/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.mp.utils.PageUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.flow.dto.InstanceLogDto;
import com.snszyk.zbusiness.flow.dto.InstancePageDto;
import com.snszyk.zbusiness.flow.entity.InstanceLog;
import com.snszyk.zbusiness.flow.mapper.InstanceLogMapper;
import com.snszyk.zbusiness.flow.service.IInstanceLogService;
import com.snszyk.zbusiness.flow.vo.InstanceLogVo;
import com.snszyk.zbusiness.flow.vo.InstancePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import static com.snszyk.zbusiness.dict.enums.DictBizEnum.DOC_REVIEW;

/**
 * 审核日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@AllArgsConstructor
@Service
public class InstanceLogServiceImpl extends BaseCrudServiceImpl<InstanceLogMapper, InstanceLog, InstanceLogDto, InstanceLogVo> implements IInstanceLogService {


	@Override
	public IPage<InstanceLogDto> pageListGroupByInstanceId(List<Long> businessIdList, String businessType, String operatorId, Long current, Long size) {

		Page<InstanceLog> page = this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(businessIdList), InstanceLog::getBusinessId, businessIdList)
			.eq(ObjectUtil.isNotEmpty(businessType), InstanceLog::getBusinessType, businessType)
			.eq(ObjectUtil.isNotEmpty(operatorId), InstanceLog::getOperatorId, operatorId)
			.groupBy(InstanceLog::getBpmInstanceId)
			.orderByAsc(InstanceLog::getCreateTime)
			.page(new Page<>(current, size));

		if (page == null || ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(current, size);
		}
		return PageUtil.toPage(page, InstanceLogDto.class);

	}

	@Override
	public Boolean deleteByInstanceId(Long instanceId) {
		return this.lambdaUpdate()
			.eq(InstanceLog::getBpmInstanceId, instanceId)
			.remove();
	}

	@Override
	public IPage<InstancePageDto> logPage(InstancePageVo vo, Long userId) {
		return this.baseMapper.logPage(vo, userId);
	}

	@Override
	public List<InstanceLogDto> queryByInstanceId(Long id) {
		List<InstanceLogDto> dtos = new LinkedList<>();
		List<InstanceLog> list = this.lambdaQuery().eq(InstanceLog::getBpmInstanceId, id)
			.orderByAsc(InstanceLog::getOperateTime).list();
		if(CollectionUtil.isNotEmpty(list)){
			dtos = list.stream().map(entity -> BeanUtil.copyProperties(entity,InstanceLogDto.class)).collect(Collectors.toList());
			for (InstanceLogDto dto : dtos) {
				dto.setDocReviewName(DictBizCache.getValue(DOC_REVIEW.getCode(),dto.getDocReview()));
			}
		}
		return dtos;
	}

	@Override
	public List<InstanceLogDto> queryByProcessId(Long processId) {
		return this.lambdaQuery().eq(InstanceLog::getInstanceProcessId, processId)
			.list().stream().map(entity -> BeanUtil.copyProperties(entity,InstanceLogDto.class)).collect(Collectors.toList());
	}

    @Override
    public List<InstanceLogDto> listByInstanceIds(List<Long> instanceIds) {
		if (CollectionUtil.isEmpty(instanceIds)) {
			return Collections.emptyList();
		}
		return this.lambdaQuery().in(InstanceLog::getBpmInstanceId, instanceIds).isNull(InstanceLog::getBatchNo)
			.list().stream().map(entity -> BeanUtil.copyProperties(entity,InstanceLogDto.class)).collect(Collectors.toList());
    }

	@Override
	public Boolean updateBatchNo(List<Long> logIds, String batchNo, LocalDateTime batchNoTime) {
		return this.lambdaUpdate().in(InstanceLog::getId, logIds)
			.set(InstanceLog::getBatchNo, batchNo)
			.set(InstanceLog::getBatchNoTime, batchNoTime)
			.update();
	}
}
