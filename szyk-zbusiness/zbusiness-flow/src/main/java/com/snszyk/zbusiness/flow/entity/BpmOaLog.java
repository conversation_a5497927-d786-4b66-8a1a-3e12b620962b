/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * oa接口日志记录实体类
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BpmOaLog extends BaseCrudEntity {

	/**
	 * oa流程id
	 */
	private String oaRequestid;
	/**
	 * 请求类型：0请求1回调
	 */
	private String requestType;
	/**
	 * 请求数据
	 */
	private String requestParam;
	/**
	 * 请求时间
	 */
	private LocalDateTime requestTime;
	/**
	 * 响应数据
	 */
	private String reponseParam;
	/**
	 * 响应时间
	 */
	private LocalDateTime reponseTime;
	/**
	 * 报错标识：0正常1异常
	 */
	private Integer errorFlag;


	private String asyncResult;


}
