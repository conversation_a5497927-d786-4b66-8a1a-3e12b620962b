<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.flow.mapper.WorkflowStepMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="workflowStepResultMap" type="com.snszyk.zbusiness.flow.entity.WorkflowStep">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="flow_id" property="flowId"/>
        <result column="step_name" property="stepName"/>
        <result column="step_type" property="stepType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="step_mode" property="stepMode"/>
        <result column="apply_choose" property="applyChoose"/>
        <result column="apply_dept" property="applyDept"/>
        <result column="flow_sort" property="flowSort"/>
    </resultMap>


    <select id="selectWorkflowStepPage" resultMap="workflowStepResultMap">
        select * from bpm_workflow_step where is_deleted = 0
    </select>

</mapper>
