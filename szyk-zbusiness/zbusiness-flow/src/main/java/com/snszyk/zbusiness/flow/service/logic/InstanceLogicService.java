/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.service.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.config.FileConfig;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.enums.DeptCategoryEnum;
import com.snszyk.system.service.*;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.system.utils.DictUtil;
import com.snszyk.system.utils.IndexCacheUtil;
import com.snszyk.task.constant.MsgContentConstant;
import com.snszyk.task.constant.MsgTitleConstant;
import com.snszyk.task.constant.MsgUrlConstant;
import com.snszyk.task.dto.SzykMsgGenDto;
import com.snszyk.task.dto.SzykTaskDto;
import com.snszyk.task.dto.SzykTaskGenDto;
import com.snszyk.task.enums.NoticeNextOperationEnum;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.service.ISzykMsgService;
import com.snszyk.task.service.ISzykTaskService;
import com.snszyk.zbusiness.dict.dto.DictCommonDto;
import com.snszyk.zbusiness.dict.dto.DictMajorDto;
import com.snszyk.zbusiness.dict.enums.DictEnum;
import com.snszyk.zbusiness.dict.service.IDictCommonService;
import com.snszyk.zbusiness.dict.service.IDictMajorService;
import com.snszyk.zbusiness.flow.dto.*;
import com.snszyk.zbusiness.flow.entity.Workflow;
import com.snszyk.zbusiness.flow.enums.*;
import com.snszyk.zbusiness.flow.service.*;
import com.snszyk.zbusiness.flow.util.ApprovalOperateFactory;
import com.snszyk.zbusiness.flow.vo.*;
import com.snszyk.zbusiness.knowledge.service.IBaseLogicService;
import com.snszyk.zbusiness.project.dto.*;
import com.snszyk.zbusiness.project.enums.ExceptionEnum;
import com.snszyk.zbusiness.project.enums.ProjectDocReviewEnum;
import com.snszyk.zbusiness.project.enums.ProjectFileEnum;
import com.snszyk.zbusiness.project.enums.ReviewStatusEnum;
import com.snszyk.zbusiness.project.service.*;
import com.snszyk.zbusiness.rpc.service.IOaService;
import com.snszyk.zbusiness.stat.dto.IndicatorLogDto;
import com.snszyk.zbusiness.stat.enums.*;
import com.snszyk.zbusiness.stat.service.IIndicatorLogService;
import com.snszyk.zbusiness.stat.vo.IndicatorLogVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作流实例 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Slf4j
@AllArgsConstructor
@Service
public class InstanceLogicService extends BaseCrudLogicService<InstanceDto, InstanceVo> {

	public static final String HEAD_CONTACT = "集团联络人";
	public static final String HEAD_LEADER = "集团分管领导";
	private final Integer corLenvel = 1;
	private final Integer secLenvel = 2;
	private final String nullStr = "/";
	private final Long nullProcessId = -1l;
	private final String leaderRoleId = "1684748990305972225";
	private final Integer leaderFlag = 1;
	private final String contractPerson = "PROJECT";

	private final IWorkflowService workflowService;
	private final IWorkflowStepService workflowStepService;
	private final IInstanceService instanceService;
	private final IInstanceProcessService instanceProcessService;
	private final IInstanceLogService instanceLogService;
	private final IDeptService deptService;
	private final IUserService userService;
	private final IUserDeptService userDeptService;
	private final BpmInstanceFileLogicService instanceFileLogicService;
	private final IAttachService attachService;
	private final IProjectBaseService projectBaseService;
	private final IProjectProgressService projectProgressService;
	private final IUserRoleService userRoleService;
	private final ISzykTaskService szykTaskService;
	private final ISzykMsgService szykMsgService;
	private final IOaService oaService;
	private final IDictBizService dictBizService;
	private final IBaseLogicService baseLogicService;
	private final IIndicatorLogService indicatorLogService;

	private final SzykRedis redis;
	private final IProjectExpertService projectExpertService;
	private final IPersonExpertService personExpertService;

	private final IDictMajorService dictMajorService;

	private final IDictCommonService dictCommonService;

	private final IProjectVersionService projectionVersionService;
	private final FileConfig fileConfig;

	private final IProjectFileService projectFilesService;
	private final IPlBaseService plBaseService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.instanceService;
	}

	public StepChooseDto getFirstStep(String businessType) {
		StepChooseDto stepChooseDto = new StepChooseDto();

		String deptId = AuthUtil.getUser().getDeptId();
		//获取当前用户部门层级
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		dept = SysCache.getDept(dept.getUnitId());
		//先根据业务类型获取审批流
		Workflow workflow = new Workflow();
		List<WorkflowDto> workflowList = workflowService.queryByBusinessType(businessType);
		if (CollectionUtil.isEmpty(workflowList) || workflowList.size() == 0) {
			throw new ServiceException(FlowExceptionEnum.NO_FLOW.getMessage());
		}
		if (workflowList.size() == 1) {
			workflow = BeanUtil.copyProperties(workflowList.get(0), Workflow.class);
		}
		//如果是多个的话，再根据当前用户层级判断
		if (workflowList.size() > 1) {

			throw new ServiceException(FlowExceptionEnum.MANY_FLOW.getMessage());
		}
		//获取第一个审批节点信息
		List<WorkflowStepDto> workflowStepDtoList = workflowStepService.queryStepByUnitLevel(workflow.getId(), dept.getUnitLevel());
		//根据业务判断
		if (CollectionUtil.isEmpty(workflowStepDtoList)) {
			workflowStepDtoList = workflowStepService.fetchByFlowId(workflow.getId());
		}
		WorkflowStepDto workflowStepDto = workflowStepDtoList.get(0);
		stepChooseDto.setFlowId(workflowStepDto.getFlowId());
		//如果下个节点是分管领导的话
		if (workflowStepDto.getOperatorId().equals(leaderRoleId) && workflowStepDto.getUnitLevel() > 4) {
			stepChooseDto.setUserType(leaderFlag);
		}
		//第一个节点的审批人
		stepChooseDto.setSubmitChoose(SubmitChooseEnum.AUTO.getCode());
		//获取用户
		List<StepUserDto> users = getUsersByType(null, workflowStepDto.getScreenDept(), workflowStepDto.getUnitLevel(), workflowStepDto.getStepType(), workflowStepDto.getOperatorId(), dept, null);
		stepChooseDto.setUsers(users);
		return stepChooseDto;

	}

	private List<StepUserDto> getUsersByType(Long instanceId, Integer screenDept, Integer unitLevel, Integer stepType, String operatorId, Dept dept, Integer flowSort) {
		List<StepUserDto> list = null;
		//条件组装
		String deptCdtn = "";
		if (screenDept.equals(ScreenDeptEnum.SELF_DEPT.getCode())) {
			deptCdtn = String.valueOf(dept.getId());
		}
		if (screenDept.equals(ScreenDeptEnum.UP_DEPT.getCode())) {
			deptCdtn = String.valueOf(dept.getParentId());
		}
		if (screenDept.equals(ScreenDeptEnum.NO_DEPT.getCode())) {
			Dept target = SysCache.getUpDeptByLevel(dept.getId(), unitLevel);
			deptCdtn = String.valueOf(target.getId());
		}

		//角色判断
		if (String.valueOf(stepType).equals(StepTypeEnum.ROLE.getCode())) {
			list = chooseRoleUsers(deptCdtn, operatorId);//
		}
		if (String.valueOf(stepType).equals(StepTypeEnum.USER.getCode())) {
			if (operatorId.equals(UserObjectEnum.LAST_USER.getCode()) && Func.isNotEmpty(instanceId)) {
				//获取上一个提交的用户
				list = chooseLastUser(instanceId, flowSort);
			}
		}
		//如果是联络人
		if (String.valueOf(stepType).equals(StepTypeEnum.CONTACT.getCode())) {
			list = chooseProjectUser(deptCdtn, operatorId);
		}
		return list;
	}

	private List<StepUserDto> chooseProjectUser(String deptId, String code) {
		List<User> userList = userService.queryContactUser(Long.valueOf(deptId), code);
		if (CollectionUtil.isNotEmpty(userList)) {
			return userList.stream().map(user -> {
				StepUserDto dto = new StepUserDto();
				dto.setStepUserName(user.getRealName());
				dto.setStepUserId(String.valueOf(user.getId()));
				dto.setStepDeptId(Long.valueOf(deptId));
				dto.setStepDeptName(SysCache.getDeptName(Long.valueOf(deptId)));
				return dto;
			}).collect(Collectors.toList());
		}
		return null;
	}

	private List<StepUserDto> chooseLastUser(Long instanceId, Integer nowFlowSort) {
		List<StepUserDto> list = new LinkedList<>();
		List<InstanceProcessDto> instanceProcessDtos = instanceProcessService.queryProcessByFlowSort(instanceId, nowFlowSort - 1);
		if (CollectionUtil.isEmpty(instanceProcessDtos)) {
			throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
		}

		InstanceProcessDto instanceProcessDto = instanceProcessDtos.get(0);
		StepUserDto stepUserDto = BeanUtil.copyProperties(instanceProcessDto, StepUserDto.class);
		if (Func.isNotEmpty(stepUserDto.getStepDeptId())) {
			stepUserDto.setStepDeptName(SysCache.getDeptName(stepUserDto.getStepDeptId()));
		}

		list.add(stepUserDto);
		return list;
	}

	public StepChooseDto getNextStep(NextProcessVo vo) {
		StepChooseDto nextProcessDto = new StepChooseDto();
		//获取当前用户部门层级
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		dept = SysCache.getDept(dept.getUnitId());
		//获取其中一个的流程
		InstanceDto instanceDto = instanceService.fetchById(vo.getInstanceIds().get(0));
		nextProcessDto.setFlowId(instanceDto.getFlowId());
		if (instanceDto.getCurrFlowSort().equals(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue()))) {
			//如果是发起人节点
			nextProcessDto.setSubmitChoose(SubmitChooseEnum.AUTO.getCode());
		} else {
			//获取当前节点
			InstanceProcessDto nowProcessDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
			//判断下是否提交人自选
			Integer submitChoose = nowProcessDto.getSubmitChoose();
			nextProcessDto.setSubmitChoose(submitChoose);

			//如果需要提报人自选
			if (submitChoose.equals(SubmitChooseEnum.HAND.getCode())) {
				//获取下个节点信息
				List<InstanceProcessDto> dtos = instanceProcessService.queryGreaterProcess(instanceDto.getId(), instanceDto.getCurrFlowSort());
				if (CollectionUtil.isEmpty(dtos)) {
					throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
				}
				InstanceProcessDto instanceProcessDto = dtos.get(0);
				//如果下个节点是OA审核
				if (instanceProcessDto.getOperatorChannel().equals(OperatorChannelEnum.OA.getCode())) {
					nextProcessDto.setUserType(leaderFlag);
				}

				if (ObjectUtil.isNotEmpty(instanceProcessDto)) {
					List<StepUserDto> users = getUsersByType(instanceDto.getId(), instanceProcessDto.getScreenDept(), instanceProcessDto.getUnitLevel(), instanceProcessDto.getStepType(), instanceProcessDto.getOperatorId(), dept, nowProcessDto.getFlowSort());
					nextProcessDto.setUsers(users);
				}
			}
		}
		return nextProcessDto;
	}

	private List<StepUserDto> chooseRoleUsers(String deptCdtn, String roleId) {
		//先查询带有相同单位的组织信息
		/*List<Dept> depts = deptService.queryDeptByUnitId(deptCdtn);
		if (CollectionUtil.isEmpty(depts)) {
			return null;
		}
		List<Long> ids = depts.stream().map(Dept::getId).collect(Collectors.toList());*/
		//查询带有单位权限的和对应角色的人员
		List<Long> userIds = userRoleService.queryUserByDeptRole(deptCdtn, Long.valueOf(roleId));
		Dept dept = SysCache.getDept(Long.valueOf(deptCdtn));
		if (CollectionUtil.isNotEmpty(userIds)) {
			List<StepUserDto> dtos = userIds.stream().map(userId -> {

				StepUserDto stepUserDto = new StepUserDto();
				stepUserDto.setStepUserId(String.valueOf(userId));
				User user = UserCache.getUser(userId);
				if (user != null) {
					stepUserDto.setStepUserName(user.getRealName());
				}
				stepUserDto.setStepDeptId(dept.getId());
				stepUserDto.setStepDeptName(dept.getDeptName());
				return stepUserDto;
			}).collect(Collectors.toList());
			return dtos;
		}
		return null;
	}

	@Transactional
	public Boolean initiateFlow(InitiateFlowVo vo) {
		LocalDateTime nowTime = LocalDateTime.now();
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		dept = SysCache.getDept(dept.getUnitId());
		// 消息埋点使用
		WorkflowDto workflow = null;
		if (vo.getFlowId() != null) {
			workflow = workflowService.fetchById(vo.getFlowId());

		}
		Integer count = 0;
		List<Long> instanceIds = new ArrayList<>();
		for (InitiateFlowBusinessVo businessVo : vo.getBusinessVo()) {
			InstanceDto instanceDto = new InstanceDto();
			Boolean createFlag = true;
			//如果流程id不为空
			if (ObjectUtil.isNotEmpty(businessVo.getInstanceId())) {
				instanceDto = instanceService.fetchById(businessVo.getInstanceId());
				if (ObjectUtil.isEmpty(instanceDto)) {
					throw new ServiceException(FlowExceptionEnum.LACK_FLOW.getMessage());
				}
				//判断状态是否是已撤回
				if (instanceDto.getCurStatus().equals(InstanceStatusEnum.CANCEL.getCode())) {
					//为了保证唯一性，删除之前已撤回的审批流
					instanceService.deleteById(instanceDto.getId());

				} else if (instanceDto.getCurStatus().equals(InstanceStatusEnum.RETURN_FLOW.getCode())) {
					//如果是已退回的
					createFlag = false;
				} else {
					throw new ServiceException(FlowExceptionEnum.WRONG_FLOW_STATUS.getMessage());
				}
			}
			//如果是退回后重新提报重新提报
			if (!createFlag) {

				//获取下个节点信息
				List<InstanceProcessDto> dtos = instanceProcessService.queryGreaterProcess(instanceDto.getId(), instanceDto.getCurrFlowSort());
				if (CollectionUtil.isEmpty(dtos)) {
					throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
				}
				//List<InstanceProcessVo> instanceProcessVos = com.snszyk.core.tool.utils.BeanUtil.copy(dtos, InstanceProcessVo.class);
				//instanceProcessVos.stream().forEach(e -> e.setDocReview(ProjectDocReviewEnum.TODO_REVIEW.getCode()));

				//instanceProcessService.saveBatch(instanceProcessVos);

				//获取第一个审批节点
				InstanceProcessDto instanceProcessDto = dtos.get(0);
				//获取第二个审批节点为下个审批节点
				InstanceProcessDto nextProcessDto = null;
				if (dtos.size() > 1) {
					nextProcessDto = dtos.get(1);
				}
				instanceDto.setCurrProcessId(instanceProcessDto.getId());
				instanceDto.setCurrStepName(instanceProcessDto.getStepName());
				instanceDto.setCurrUserId(instanceProcessDto.getStepUserId());
				instanceDto.setCurrFlowSort(instanceProcessDto.getFlowSort());
				instanceDto.setCurrDeptId(instanceProcessDto.getStepDeptId());
				instanceDto.setCurrDeptName(SysCache.getDeptName(instanceProcessDto.getStepDeptId()));
				instanceDto.setCurBeforeEvent(instanceProcessDto.getBeforeEvent());
				instanceDto.setCurCompleteEvent(instanceProcessDto.getCompleteEvent());
				instanceDto.setCurOperatorChannel(instanceProcessDto.getOperatorChannel());
				instanceDto.setInitLevel(instanceProcessDto.getUnitLevel());
				if (Func.isNotEmpty(nextProcessDto)) {
					instanceDto.setNextProcessId(nextProcessDto.getId());
					instanceDto.setNextStepName(nextProcessDto.getStepName());
				} else {
					instanceDto.setNextProcessId(nullProcessId);
					instanceDto.setNextStepName(nullStr);
				}
				//更新节点信息
				//获取发起人节点
				InstanceProcessDto nowProcessDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), 0);

				nowProcessDto.setApproveTime(nowTime);
				nowProcessDto.setApproveStatus(ApproveStatusEnum.PASS.getCode());
				instanceProcessService.save(BeanUtil.copyProperties(nowProcessDto, InstanceProcessVo.class));

				vo.setStepDeptId(instanceProcessDto.getStepDeptId());
				vo.setStepDeptName(SysCache.getDeptName(instanceProcessDto.getStepDeptId()));
				vo.setStepUserId(instanceProcessDto.getStepUserId());
				vo.setStepUserName(instanceProcessDto.getStepUserName());


				//更新第一个审批节点信息
				/*instanceProcessDto.setStepUserId(vo.getStepUserId());
				instanceProcessDto.setStepUserName(vo.getStepUserName());
				instanceProcessDto.setStepDeptId(vo.getStepDeptId());
				instanceProcessDto.setStepDeptName(vo.getStepDeptName());*/
				//instanceProcessDto.setSubmitTime(nowTime);
				instanceProcessService.save(BeanUtil.copyProperties(instanceProcessDto, InstanceProcessVo.class));
				//增加日志
				createInstanceLog(instanceDto, "", nowProcessDto, instanceProcessDto);
				//重新提报需要清空流程中的分管领导审查意见
				instanceProcessService.clearOperateRemark(businessVo.getInstanceId());
				//v1.5重新提报 节点都改为待文审状态
				instanceProcessService.clearProjectDocReview(instanceDto.getId(), ProjectDocReviewEnum.TODO_REVIEW.getCode());
				//v1.5重新提报 是否可文审的状态还原// 20240420 文审意见和 文审状态还原
				instanceProcessService.clearEnableDocReview(instanceDto.getId(), workflow.getId(), dept.getUnitLevel());
			}
			//如果是新增的审批流
			if (createFlag) {
				WorkflowDto workflowDto = workflowService.fetchById(vo.getFlowId());
				//获取发起流程的节点信息
				List<WorkflowStepDto> workflowStepDtos = workflowStepService.queryStepLeUnitLevel(workflow.getId(), dept.getUnitLevel());

				if (CollectionUtil.isEmpty(workflowStepDtos)) {
					workflowStepDtos = workflowStepService.fetchByFlowId(workflow.getId());
				}
				InstanceVo instanceVo = initiateInstance(workflowDto);
				List<InstanceProcessVo> processes = initiateProcesses(workflowStepDtos);
				Long businessId = businessVo.getBusinessId();
				//实例化工作流
				instanceVo.setBusinessId(businessId);
				//instanceVo.setFlowNo("BPM" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN));

				instanceVo.setFlowNo("BPM" + DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmm") + String.format("%02d", count));
				ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceVo.getBusinessType());
				String description = approvalOperate.getDescription(businessId);
				instanceVo.setDescription(description);
				instanceDto = instanceService.save(instanceVo);
				//实例化工作流流程
				//维护发起人节点
				InstanceProcessDto nextProcessDto = null;

				//处理其他节点
				for (int i = 0; i < processes.size(); i++) {
					InstanceProcessVo entity = processes.get(i);
					entity.setInstanceId(instanceDto.getId());
					if (i == 0) {
						//第一个节点要根据流程配置获取节点人
						//如果未选，自动获取下个节点审核信息
						List<StepUserDto> users = getUsersByType(instanceDto.getId(), entity.getScreenDept(), entity.getUnitLevel(), entity.getStepType(), entity.getOperatorId(), dept, entity.getFlowSort());
						//如果没有跑出异常
						if (CollectionUtil.isEmpty(users)) {
							throw new ServiceException(FlowExceptionEnum.ENOUGH_APPROVAL_USER.getMessage());
						}
						vo.setStepDeptId(users.get(0).getStepDeptId());
						vo.setStepDeptName(users.get(0).getStepDeptName());
						vo.setStepUserId(users.stream().map(StepUserDto::getStepUserId).distinct().collect(Collectors.joining(",")));
						vo.setStepUserName(users.stream().map(StepUserDto::getStepUserName).distinct().collect(Collectors.joining(",")));

						entity.setStepUserId(vo.getStepUserId());
						entity.setStepUserName(vo.getStepUserName());
						entity.setStepDeptId(vo.getStepDeptId());
						entity.setStepDeptName(SysCache.getDeptName(vo.getStepDeptId()));
						//设置提交人为当前提交人
						entity.setSubmitUserId(instanceVo.getApplyUserId());
						entity.setSubmitUserName(instanceVo.getApplyUserName());
						entity.setSubmitDeptId(dept.getId());
						entity.setSubmitDeptName(dept.getDeptName());
						entity.setSubmitTime(LocalDateTime.now());
					}
					InstanceProcessDto processDto = instanceProcessService.save(entity);
					if (i == 0) {
						instanceDto.setCurrProcessId(processDto.getId());
						instanceDto.setCurrStepName(processDto.getStepName());
						instanceDto.setCurrUserId(vo.getStepUserId());
						instanceDto.setCurrFlowSort(entity.getFlowSort());
						instanceDto.setCurrDeptId(vo.getStepDeptId());
						instanceDto.setCurrDeptName(SysCache.getDeptName(vo.getStepDeptId()));
						instanceDto.setCurBeforeEvent(entity.getBeforeEvent());
						instanceDto.setCurCompleteEvent(entity.getCompleteEvent());
						instanceDto.setCurOperatorChannel(entity.getOperatorChannel());
						instanceDto.setInitLevel(entity.getUnitLevel());
						nextProcessDto = processDto;
					}
					if (i == 1) {
						instanceDto.setNextProcessId(processDto.getId());
						instanceDto.setNextStepName(processDto.getStepName());
					}
				}
				//增肌日志
				createFirstProcess(instanceDto, nextProcessDto);
			}
			ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
			instanceDto.setDescription(approvalOperate.getDescription(businessVo.getBusinessId()));
			//维护第一个审批节点
			instanceDto.setCurStatus(InstanceStatusEnum.CHECKING.getCode());
			instanceDto.setFlowStatus(FlowStatusEnum.RUN.getCode());
			InstanceDto save = instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));
			instanceIds.add(save.getId());

			//执行发起流程业务逻辑
			//执行审批通过的方法
			Map<String, Object> params = new HashMap<>();
			params.put("currStepName", instanceDto.getCurrStepName());
			approvalOperate.intiInstance(instanceDto.getBusinessId(), instanceDto.getId(), params, createFlag);
			//更新审批层级
			//如果是新增的审批流
			if (createFlag) {
				approvalOperate.updateLevel(instanceDto.getBusinessId(), dept.getUnitLevel());
			}

			count++;
			// 消息埋点
			Long businessId = businessVo.getBusinessId();
			//消息埋点 -项目申报
			if (workflow != null) {
				if (ObjectUtil.isNotEmpty(businessId)) {
					if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(workflow.getBusinessType())) {
						handleProjectSubmitTask(vo.getStepUserId(), vo.getStepDeptId(), businessId);
						if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
							handleProjectExert(instanceDto);
						}
						//消息埋点-项目验收
					} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(workflow.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(workflow.getBusinessType())) {
						handleProjectCheckTask(vo.getStepUserId(), vo.getStepDeptId(), businessId);
					}
				}
			}
			//指标埋点
//			handleApproveFinishRate(instanceDto.getBusinessId(), dept.getId(), instanceDto.getCurrProcessId());
		}
		//记录日志
		List<InitiateFlowBusinessVo> businessVo = vo.getBusinessVo();
		List<Long> businessIds = businessVo.stream().map(e -> e.getBusinessId()).collect(Collectors.toList());
		projectionVersionService.projectVersionLog(businessIds, Long.valueOf(AuthUtil.getDeptId()),
			AuthUtil.getUserId());
		if (!Func.isEmpty(instanceIds)) {
			Long instanceId = instanceIds.get(0);
			InstanceDto instanceDto = instanceService.fetchById(instanceId);
			//如果是项目申报
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
				//添加项目的批次
				projectBaseService.handleCreateBatchNo(instanceIds);
			}
		}
		return true;
	}

	/**
	 * 项目申报待办
	 *
	 * @param stepUserId
	 * @param setpDeptId
	 */

	private void handleProjectSubmitTask(String stepUserId, Long setpDeptId, Long businessId) {
		if (Func.isBlank(stepUserId) || businessId == null) {
			return;
		}
		//根据业务id查询项目信息
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(businessId);
		if (projectBaseDto != null) {

			SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
			szykTaskGenDto.setTitle(MsgTitleConstant.PROJECT_REPORT);
			szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_REPORT.getCode());
			szykTaskGenDto.setBusinessId(businessId);
			szykTaskGenDto.setUrl(MsgUrlConstant.PROJECT_SUBMIT + "?deptScopeId=" + setpDeptId);
			szykTaskGenDto.setDeptId(setpDeptId);
			szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_EXAMINE.getCode());
			szykTaskGenDto.setReceiverId(stepUserId);
			szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.PROJECT_REPORT_DETAIL, businessId));
			szykTaskGenDto.setContent(String.format(MsgContentConstant.EXAMINE, projectBaseDto.getProjectName()));
			szykTaskGenDto.setOnlyDingMsg(MsgContentConstant.ONLY_DING_MSG);
			//判断是否是集团联络员,集团的一天只发送一次
			if (canSendToContact(szykTaskGenDto, false)) {
				szykTaskService.genTodoTask(szykTaskGenDto, true);
			} else {
				szykTaskService.genTodoTask(szykTaskGenDto, false);

			}
			//v1.6 项目申报指标埋点
//			handleProjectIndex(IndexLibraryEnum.PROJECT_SUBMIT, projectBaseDto.getCompanyId(), projectBaseDto.getId(),
//				LocalDateTime.ofInstant(projectBaseDto.getSubmitTime().toInstant(), ZoneId.systemDefault()));
//           v1.7 项目审查审批完结率 埋点
			handleProjectIndex(projectBaseDto.getConstructionUnitId(), CommonConstant.ONE);
		}
	}

	/***
	 * v1.7 指标的提报质量
	 * @param constructionUnitId
	 * @param type   1 提报 2 退回  3 撤销
	 */
	private void handleProjectIndex(Long constructionUnitId, Integer type) {

		if (constructionUnitId == null) {
			return;
		}
		Dept dept = SysCache.getDept(constructionUnitId);
		if (dept == null) {
			return;
		}
		Long unitId = dept.getUnitId();
		Dept unit = SysCache.getDept(unitId);
		if (unit == null) {
			return;
		}
		Object o;
		switch (type) {
			case 1:
				o = redis.hGet(CommonConstant.PROJECT_INDEX_REPORT_COUNT, unit.getDeptCode());
				if (o == null) {
					redis.hSet(CommonConstant.PROJECT_INDEX_REPORT_COUNT, unit.getDeptCode(), "1");
				} else {
					long l = Long.valueOf(o.toString()) + 1;
					redis.hSet(CommonConstant.PROJECT_INDEX_REPORT_COUNT, unit.getDeptCode(), Long.toString(l));
				}
				break;
			case 2:
				o = redis.hGet(CommonConstant.PROJECT_INDEX_RETURN_COUNT, unit.getDeptCode());
				if (o == null) {
					redis.hSet(CommonConstant.PROJECT_INDEX_RETURN_COUNT, unit.getDeptCode(), "1");
				} else {
					long l = Long.valueOf(o.toString()) + 1;
					redis.hSet(CommonConstant.PROJECT_INDEX_RETURN_COUNT, unit.getDeptCode(), Long.toString(l));
				}
				break;
			case 3:
				o = redis.hGet(CommonConstant.PROJECT_INDEX_CANCEL_COUNT, unit.getDeptCode());
				if (o == null) {
					redis.hSet(CommonConstant.PROJECT_INDEX_CANCEL_COUNT, unit.getDeptCode(), "1");
				} else {
					long l = Long.valueOf(o.toString()) + 1;
					redis.hSet(CommonConstant.PROJECT_INDEX_CANCEL_COUNT, unit.getDeptCode(), Long.toString(l));
				}
				break;
			default:
				break;
		}
	}

	/**
	 * 项目审查审批完结率 埋点
	 *
	 * @param
	 */
//	private void handleApproveFinishRate(Long businessId, Long currentDeptId, Long nextProcessId) {
//		try {
//			//下一节点信息
//			InstanceProcessDto nextProcessDto = null;
//			if (nextProcessId != null) {
//				nextProcessDto = instanceProcessService.fetchById(nextProcessId);
//			}
//			//下一节点的部门
//			Long nextDeptId = null;
//			if (nextProcessDto != null) {
//				nextDeptId = nextProcessDto.getStepDeptId();
//			}
//
//			LocalDate now = LocalDate.now();
//			int year = now.getYear();
//			int monthValue = now.getMonthValue();
//
//			//当前部门
//			Dept currentDept = SysCache.getDept(currentDeptId);
//			if (currentDept != null) {
//				String currentDeptCode = currentDept.getDeptCode();
//				//处理完成的数据
//				IndicatorLogDto currentLog = indicatorLogService.getByProjectInfo(currentDeptCode, year, monthValue,
//					IndicatorParamEnum.PROJECT_CANCEL_PARAM.getCode(),
//					IndicatorEnum.PROJECT_QUALITY.getCode(),
//					IndicatorSystemEnum.XXJSGK.getCode());
//				Object o = redis.get("indicator:PROJECT_APPROVE_FINISH_RATE:" + year + ":" + monthValue + ":" + currentDeptCode + ":" + businessId);
//				//如果超过的7天
//				if (o != null) {
//					if (currentLog == null) {
//						IndicatorLogVo approveLogVo = new IndicatorLogVo();
//						approveLogVo.setYear(year);
//						approveLogVo.setMonth(monthValue);
//						approveLogVo.setOrgCode(currentDeptCode);
//						approveLogVo.setParamType(IndicatorParamEnum.PROJECT_CANCEL_PARAM.getCode());
//						approveLogVo.setParamValue("1");
//						approveLogVo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
//						approveLogVo.setSystemNo(IndicatorSystemEnum.XXJSGK.getCode());
//						indicatorLogService.save(approveLogVo);
//					} else {
//						String paramValue = currentLog.getParamValue();
//						int paramValueInteger = Integer.parseInt(paramValue);
//						paramValueInteger++;
//						currentLog.setParamValue(Integer.toString(paramValueInteger));
//						indicatorLogService.save(com.snszyk.core.tool.utils.BeanUtil.copy(currentLog, IndicatorLogVo.class));
//					}
//				}
//			}
//			//总的数量  下一个节点的数量
//			Dept nextDept = SysCache.getDept(nextDeptId);
//			if (nextDept == null) {
//				log.error("下一节点不存在");
//				return;
//			}
//			//总的数量
//			String nextOrgCode = nextDept.getDeptCode();
//			IndicatorLogDto nextLog = indicatorLogService.getByProjectInfo(nextOrgCode, year, monthValue,
//				IndicatorParamEnum.PROJECT_RETURN_PARAM.getCode(),
//				IndicatorEnum.PROJECT_QUALITY.getCode(),
//				IndicatorSystemEnum.XXJSGK.getCode());
//			if (nextLog == null) {
//				IndicatorLogVo nextLogVo = new IndicatorLogVo();
//				nextLogVo.setYear(year);
//				nextLogVo.setMonth(monthValue);
//				nextLogVo.setOrgCode(nextOrgCode);
//				nextLogVo.setParamType(IndicatorParamEnum.PROJECT_RETURN_PARAM.getCode());
//				nextLogVo.setParamValue("1");
//				nextLogVo.setIndicatorCode(IndicatorEnum.PROJECT_QUALITY.getCode());
//				nextLogVo.setSystemNo(IndicatorSystemEnum.XXJSGK.getCode());
//				indicatorLogService.save(nextLogVo);
//			} else {
//				String paramValue = nextLog.getParamValue();
//				Integer paramValueInteger = Integer.valueOf(paramValue);
//				paramValueInteger++;
//				nextLog.setParamValue(paramValueInteger.toString());
//				indicatorLogService.save(com.snszyk.core.tool.utils.BeanUtil.copy(nextLog, IndicatorLogVo.class));
//			}
//			redis.setEx("indicator:PROJECT_APPROVE_FINISH_RATE:" + year + ":" + monthValue + ":" + nextOrgCode + ":" + businessId,
//				DateUtil.now(), Duration.ofMinutes(7 * 24 * 60 * 60));
//		} catch (Exception e) {
//			log.error("项目审查审批完结率 埋点失败!" + e.getMessage(), e);
//		}
//	}

	/**
	 * 指标分析-处理撤回
	 *
	 * @param businessId
	 * @param deptId
	 * @param applyTime
	 */
	private void handleApproveFinishRateCancel(Long businessId, Long deptId, LocalDateTime applyTime) {
		try {
			Dept dept = SysCache.getDept(deptId);
			if (dept == null) {
				log.error("handleApproveFinishRateCancel,deptId:" + deptId + "部门不存在");
			}
			int year = applyTime.getYear();
			int monthValue = applyTime.getMonthValue();
			String deptCode = dept.getDeptCode();
			//删除redis 总数使用
			redis.del("indicator:PROJECT_APPROVE_FINISH_RATE:" + year + ":" + monthValue + ":" + deptCode + ":" + businessId);
			IndicatorLogDto nextLog = indicatorLogService.getByProjectInfo(deptCode, year, monthValue,
				IndicatorParamEnum.PROJECT_RETURN_PARAM.getCode(),
				IndicatorEnum.PROJECT_QUALITY.getCode(),
				IndicatorSystemEnum.XXJSGK.getCode());
			if (nextLog == null) {
				log.error("handleApproveFinishRateCancel:撤销的原指标日志不存在:businesId:" + businessId);
			}
			String paramValue = nextLog.getParamValue();
			int paramInt = Integer.parseInt(paramValue);
			paramInt--;
			if (paramInt <= 0) {
				paramInt = 0;
			}
			nextLog.setParamValue(String.valueOf(paramInt));
			indicatorLogService.save(com.snszyk.core.tool.utils.BeanUtil.copy(nextLog, IndicatorLogVo.class));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 项目管理指标统计
	 *
	 * @param libraryEnum 指标id
	 * @param companyId   公司id
	 * @param businessId  业务id 项目id
	 * @param submitDate  提报日期
	 */
	public void handleProjectIndex(IndexLibraryEnum libraryEnum, Long companyId, Long businessId, LocalDateTime submitDate) {
		if (submitDate == null) {
			submitDate = LocalDateTime.now();
		}
		if (libraryEnum == null || companyId == null || businessId == null || submitDate == null) {
			return;
		}
		try {
			IndexCacheUtil.pushToRedis(Long.valueOf(IndexTitleEnum.PROJECT_TITLE.getCode()), Long.valueOf(libraryEnum.getCode()),
				businessId, companyId, submitDate);
		} catch (Exception e) {
			log.error("项目管理指标统计失败!");
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 项目验收待办
	 *
	 * @param stepUserId
	 * @param stepDeptId
	 */

	private void handleProjectCheckTask(String stepUserId, Long stepDeptId, Long businessId) {

		if (Func.isBlank(stepUserId) || businessId == null) {
			return;
		}
		//根据业务id查询项目信息
		ProjectProgressDto projectAcceptDto = projectProgressService.fetchById(businessId);
		if (projectAcceptDto != null) {
			SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
			szykTaskGenDto.setTitle(MsgTitleConstant.PROJECT_ACCEPTANCE);
			szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_ACCEPT.getCode());
			szykTaskGenDto.setBusinessId(businessId);
			szykTaskGenDto.setUrl(MsgUrlConstant.PROJECT_ACCEPT + "?deptScopeId=" + stepDeptId);
			szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_EXAMINE.getCode());
			szykTaskGenDto.setReceiverId(stepUserId);
			szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.PROJECT_ACCEPT_DETAIL, businessId));
			szykTaskGenDto.setContent(String.format(MsgContentConstant.ACCEPT_EXAMINE, projectAcceptDto.getProjectName()));
			szykTaskGenDto.setOnlyDingMsg(MsgContentConstant.ONLY_DING_MSG_ACCEPT);
			szykTaskGenDto.setDeptId(stepDeptId);
			//判断是否是集团联络员,集团的一天只发送一次
			if (canSendToContact(szykTaskGenDto, true)) {
				szykTaskService.genTodoTask(szykTaskGenDto, true);
			} else {
				szykTaskService.genTodoTask(szykTaskGenDto, false);
			}
		}
	}

	/**
	 * @param szykTaskGenDto
	 * @param isAccept       是否为项目验收
	 * @return
	 */
	private boolean canSendToContact(SzykTaskGenDto szykTaskGenDto, boolean isAccept) {
		//是否属于集团
		boolean jilian = false;
		Long deptId = szykTaskGenDto.getDeptId();
		Dept dept = SysCache.getDept(deptId);
		LocalDateTime startDate = LocalDateTimeUtil.beginOfDay(LocalDateTime.now());
		LocalDateTime endDate = LocalDateTimeUtil.endOfDay(LocalDateTime.now());
		List<SzykTaskDto> existList = szykTaskService.listByParam(szykTaskGenDto.getBusinessType(), szykTaskGenDto.getReceiverId(), null, szykTaskGenDto.getContent(), startDate, endDate);
		if (dept.getParentId() == 0l) {
			jilian = true;
		}
		//之前已经发送过
		if (CollectionUtil.isNotEmpty(existList)) {
			//项目验收
			if (isAccept) {
				if (jilian) {
					return false;

				} else {
					return true;
				}
			} else {
				//项目 提报
				return false;
			}
		}
		return true;
	}


	private Boolean createFirstProcess(InstanceDto instanceDto, InstanceProcessDto nextProcessDto) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		LocalDateTime nowDate = LocalDateTime.now();
		Long id = instanceDto.getId();
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		dept = SysCache.getDept(dept.getUnitId());
		InstanceProcessVo vo = new InstanceProcessVo();
		vo.setInstanceId(id);
		vo.setStepName(FirstProcessEnum.STEP_NAME.getValue());
		vo.setStepType(Integer.valueOf(FirstProcessEnum.STEP_TYPE.getValue()));
		vo.setOperatorId(String.valueOf(user.getId()));
		vo.setStepMode(FirstProcessEnum.STEP_MODE.getValue());
		vo.setSubmitChoose(Integer.valueOf(FirstProcessEnum.SUBMIT_CHOOSE.getValue()));
		vo.setScreenDept(Integer.valueOf(FirstProcessEnum.SCREEN_DEPT.getValue()));
		vo.setFlowSort(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue()));
		vo.setStepUserId(String.valueOf(user.getId()));
		vo.setStepUserName(user.getRealName());
		vo.setStepDeptId(dept.getId());
		vo.setStepDeptName(dept.getDeptName());
		vo.setApproveUserId(String.valueOf(user.getId()));
		vo.setApproveUserName(user.getRealName());
		vo.setApproveStatus(ApproveStatusEnum.PASS.getCode());
		vo.setApproveTime(nowDate);
		vo.setBeforeEvent(Integer.valueOf(FirstProcessEnum.BEFORE_EVENT.getValue()));
		vo.setUnitLevel(dept.getUnitLevel());

		InstanceProcessDto dto = instanceProcessService.save(vo);
		//处理发起节点日志

		InstanceLogVo logVo = new InstanceLogVo();
		logVo.setBpmInstanceId(id);
		logVo.setInstanceProcessId(dto.getId());
		logVo.setFlowSort(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue()));
		logVo.setBusinessId(instanceDto.getBusinessId());
		logVo.setBusinessType(instanceDto.getBusinessType());
		logVo.setOperatorId(user.getId());
		logVo.setOperatorName(user.getRealName());
		logVo.setOperateTime(nowDate);
		logVo.setOperatorDeptId(dept.getId());
		logVo.setOperatorDeptName(dept.getDeptName());
		logVo.setOperateType(ApproveStatusEnum.PASS.getCode());
		logVo.setCrossLevel(CommonConstant.ZERO);
		if (Func.isNotEmpty(nextProcessDto)) {
			logVo.setTargetDeptId(nextProcessDto.getStepDeptId());
			//判断当前的单位层级和下一个单位层级是否是同级
			Dept nextDept = SysCache.getDept(nextProcessDto.getStepDeptId());
			if (!dept.getUnitLevel().equals(nextDept.getUnitLevel())) {
				//如果不同级,则流程跨级
				logVo.setCrossLevel(CommonConstant.ONE);
			}
		}

		instanceLogService.save(logVo);

		return true;
	}


	private InstanceVo initiateInstance(WorkflowDto workflowDto) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		//dept = SysCache.getDept(dept.getUnitId());
		InstanceVo instance = new InstanceVo();
		instance.setFlowName(workflowDto.getFlowName());
		instance.setFlowId(workflowDto.getId());
		instance.setBusinessType(workflowDto.getBusinessType());
		instance.setApplyTime(LocalDateTime.now());
		instance.setApplyUserId(user.getId());
		instance.setApplyUserName(user.getRealName());
		instance.setApplyUserDept(dept.getId().toString());
		instance.setApplyUserDeptName(dept.getDeptName());
		return instance;

	}

	private List<InstanceProcessVo> initiateProcesses(List<WorkflowStepDto> workflowStepDtos) {
		return workflowStepDtos.stream().map(dto -> {
			InstanceProcessVo process = BeanUtil.copyProperties(dto, InstanceProcessVo.class);
			process.setId(null);
			process.setCompleteEvent(CompleteEventEnum.NO.getCode());
			process.setDocReview(ProjectDocReviewEnum.TODO_REVIEW.getCode());
			return process;
		}).collect(Collectors.toList());
	}


	@Transactional
	public Boolean approved(ApproveVo vo) {
		try {
			return doApprovedByLock(vo, AuthUtil.getUserId(), Long.valueOf(AuthUtil.getDeptId()));
		} catch (Exception e) {
			throw new BusinessException(e.getMessage());
		}
	}

	public Boolean doApprovedByLock(ApproveVo vo, Long userId, Long deptId) throws Exception {
		//先校验是否锁定
		verifyInstance(vo.getInstanceIds());
		//然后流程加锁
		lockInstance(vo.getInstanceIds(), AuthUtil.getUserId(), LockStatusEnum.CLOSE.getCode());
		//执行审批逻辑
		try {
			doApproved(vo, userId, deptId);
		} catch (Exception e) {
			throw e;
		} finally {
			//解锁
			lockInstance(vo.getInstanceIds(), AuthUtil.getUserId(), LockStatusEnum.ON.getCode());
		}
		return true;
	}


	/**
	 * 审批日志
	 *
	 * @param instanceDto
	 * @param operateRemark
	 * @param nowProcessDto
	 * @param nextProcessDto
	 * @return
	 */
	private Boolean createInstanceLog(InstanceDto instanceDto, String operateRemark, InstanceProcessDto nowProcessDto, InstanceProcessDto nextProcessDto) {
		InstanceLogVo logVo = new InstanceLogVo();
		logVo.setBpmInstanceId(instanceDto.getId());
		logVo.setInstanceProcessId(nowProcessDto.getId());
		logVo.setFlowSort(nowProcessDto.getFlowSort());
		logVo.setBusinessId(instanceDto.getBusinessId());
		logVo.setBusinessType(instanceDto.getBusinessType());
		logVo.setOperatorId(Long.valueOf(nowProcessDto.getApproveUserId()));
		logVo.setOperatorName(nowProcessDto.getApproveUserName());
		logVo.setOperateTime(nowProcessDto.getApproveTime());
		logVo.setOperatorDeptId(nowProcessDto.getStepDeptId());
		logVo.setOperatorDeptName(SysCache.getDeptName(nowProcessDto.getStepDeptId()));
		logVo.setOperateType(nowProcessDto.getApproveStatus());
		logVo.setOperateRemark(operateRemark);
		//0420 文审
		logVo.setDocReview(nowProcessDto.getDocReview());
		logVo.setDocReviewComment(nowProcessDto.getDocReviewComment());
		logVo.setCrossLevel(CommonConstant.ZERO);
		if (Func.isNotEmpty(nextProcessDto)) {
			logVo.setTargetDeptId(nextProcessDto.getStepDeptId());
			//判断当前的单位层级和下一个单位层级是否是同级
			Dept dept = SysCache.getDept(nowProcessDto.getStepDeptId());
			Dept nextDept = SysCache.getDept(nextProcessDto.getStepDeptId());
			if (!dept.getUnitLevel().equals(nextDept.getUnitLevel())) {
				//如果不同级,则流程跨级
				logVo.setCrossLevel(CommonConstant.ONE);
			}
		}

		InstanceLogDto dto = instanceLogService.save(logVo);
		//处理附件
		//查询临时附件
		List<BpmInstanceFileDto> dtos = instanceFileLogicService.queryTemp(instanceDto.getId(), FileBusinessTypeEnum.TEMP.getCode());
		// 附件
		if (!CollectionUtils.isEmpty(dtos)) {
			dtos.forEach(fileDto -> {
				fileDto.setBusinessId(dto.getId());
				fileDto.setBusinessType(FileBusinessTypeEnum.LOG.getCode());
			});
			instanceFileLogicService.updateBatch(dtos);

		}

		return true;
	}

	public List<ReturnStepDto> getReturnProcess(NextProcessVo vo) {
		//查询operatord是project的已审批节点
		InstanceDto instanceDto = instanceService.fetchById(vo.getInstanceIds().get(0));
		return instanceProcessService.queryLowProcess(instanceDto.getId(), instanceDto.getCurrFlowSort());
	}


	@Transactional
	public Boolean returnFlow(ReturnFlowVo vo) {
		try {
			return doReturnByLock(vo, AuthUtil.getUserId());
		} catch (Exception e) {
			throw new BusinessException(e.getMessage());
		}
	}

	public Boolean doReturnByLock(ReturnFlowVo vo, Long userId) throws Exception {
		//先校验是否锁定
		verifyInstance(vo.getInstanceIds());
		//然后流程加锁
		lockInstance(vo.getInstanceIds(), AuthUtil.getUserId(), LockStatusEnum.CLOSE.getCode());
		//执行审批逻辑
		try {
			doReturnFlow(vo, userId);
		} catch (Exception e) {
			throw e;
		} finally {
			//解锁
			lockInstance(vo.getInstanceIds(), AuthUtil.getUserId(), LockStatusEnum.ON.getCode());
		}
		return true;
	}


	@Transactional
	public Boolean doReturnFlow(ReturnFlowVo vo, Long userId) throws Exception {
		Boolean isLeader = false;
		User user = UserCache.getUser(userId);
		//获取当前审核的业务范围
		String deptId = AuthUtil.getDeptId();
		//批量这里采取单条处理的方式
		for (Long instanceId : vo.getInstanceIds()) {
			//获取实例信息
			InstanceDto instanceDto = instanceService.fetchById(instanceId);
			//这里需要加下校验。如果当前审批的审批人中没有审核人，则报错提醒
			if (instanceDto.getCurrFlowSort().equals(0)) {
				throw new ServiceException("项目：" + instanceDto.getDescription() + "，当前是发起人节点，需要重新提报");
			}
			if (!instanceDto.getCurrUserId().contains(String.valueOf(userId))) {
				throw new ServiceException("项目：" + instanceDto.getDescription() + "，已被其他联络员审核过了，无需重复审核");
			}
			Long currDeptId = instanceDto.getCurrDeptId();
			//获取当前节点信息
			InstanceProcessDto nowProcessDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
			//判断当前节点是分管领导节点
			isLeader = isLeader(nowProcessDto.getOperatorId());

			//变更节点审核信息
			nowProcessDto.setApproveTime(LocalDateTime.now());
			nowProcessDto.setApproveUserId(String.valueOf(userId));
			nowProcessDto.setApproveStatus(ApproveStatusEnum.RETURN_FLOW.getCode());
			nowProcessDto.setApproveUserName(user.getRealName());
			nowProcessDto.setOperateRemark(vo.getOperateRemark());
			nowProcessDto.setContactApprovalStatus(ContactApprovalStatusEnum.TODO.getCode());
			instanceProcessService.save(BeanUtil.copyProperties(nowProcessDto, InstanceProcessVo.class));

			//获取回退节点信息
			//获取小于当前节点的联络人节点信息
			//List<InstanceProcessDto> returnStepDtos = instanceProcessService.queryLowStep(instanceDto.getId(), instanceDto.getCurrFlowSort());
			List<InstanceProcessDto> returnStepDtos = instanceProcessService.queryLowCheckStep(instanceDto.getId(), instanceDto.getCurrFlowSort());
			if (CollectionUtil.isEmpty(returnStepDtos)) {
				throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
			}
			//去掉相同联络人节点中，大节点的，保留每个层级的初始联络人
			Integer returnStep = 0;
			InstanceProcessDto nextProcessDto = returnStepDtos.get(returnStep);
			InstanceProcessDto nextTwoProcessDto = null;
			//需要排除自己回退给自己的情况
			//如果退到的节点是当前人，且属于一个业务范围的，则需要退回到下个节点
			if ((!nextProcessDto.getFlowSort().equals(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue())))
				&& nextProcessDto.getApproveUserId().equals(String.valueOf(userId))
				&& StringUtil.isNotBlank(deptId) && nextProcessDto.getStepDeptId().equals(Long.valueOf(deptId))
				&& returnStepDtos.size() > 1 && nextProcessDto.getOperatorId().equals(nowProcessDto.getOperatorId())) {
				returnStep = 1;
				nextProcessDto = returnStepDtos.get(returnStep);
			}
			if (returnStepDtos.size() > returnStep + 1) {
				nextTwoProcessDto = returnStepDtos.get(returnStep + 1);
			}

			instanceDto.setCurrProcessId(nextProcessDto.getId());
			instanceDto.setCurrStepName(nextProcessDto.getStepName());
			instanceDto.setCurrUserId(nextProcessDto.getStepUserId());
			instanceDto.setCurrFlowSort(nextProcessDto.getFlowSort());
			instanceDto.setCurrDeptId(nextProcessDto.getStepDeptId());
			instanceDto.setCurrDeptName(SysCache.getDeptName(nextProcessDto.getStepDeptId()));
			instanceDto.setCurBeforeEvent(nextProcessDto.getBeforeEvent());
			instanceDto.setCurCompleteEvent(nextProcessDto.getCompleteEvent());
			instanceDto.setCurOperatorChannel(nextProcessDto.getOperatorChannel());
			if (isLeader) {
				instanceDto.setCurReceive(StringUtil.isBlank(vo.getOperateRemark()) ? "" : vo.getOperateRemark());
			} else {
				instanceDto.setCurReceive("");
			}

			if (Func.isNotEmpty(nextTwoProcessDto)) {
				instanceDto.setNextProcessId(nextTwoProcessDto.getId());
				instanceDto.setNextStepName(nextTwoProcessDto.getStepName());
			} else {
				instanceDto.setNextProcessId(nullProcessId);
				instanceDto.setNextStepName(nullStr);
			}

			//变更实例信息
			instanceDto.setCurStatus(InstanceStatusEnum.RETURN_FLOW.getCode());
			if (nextProcessDto.getFlowSort().equals(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue()))) {
				//如果退回到发起人，审批流程结束
				instanceDto.setFlowStatus(FlowStatusEnum.END.getCode());
			}
			//新增流程日志
			createInstanceLog(instanceDto, vo.getOperateRemark(), nowProcessDto, nextProcessDto);
			instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));

			ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
			approvalOperate.doReturn(instanceDto.getBusinessId());
			//更新当前节点信息
			Map<String, Object> params = new HashMap<>();
			params.put("currStepName", instanceDto.getCurrStepName());
			approvalOperate.processDone(instanceDto.getBusinessId(), instanceDto.getId(), params);
			//更新审批层级
			/*Dept dept = SysCache.getDept(instanceDto.getCurrDeptId());
			dept = SysCache.getDept(dept.getUnitId());
			ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
			approvalOperate.updateLevel(instanceDto.getBusinessId(), dept.getUnitLevel());*/
			//3.本审批人 退回,本人的待办完成
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
				szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_REPORT, instanceDto.getBusinessId(), userId, currDeptId);
			} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
				szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_ACCEPT, instanceDto.getBusinessId(), userId, currDeptId);
			}
			//如果退回到发起人，则执行退回通过的方法
			// 1.退回给发起人的时候需要 发送消息提醒
			//审核人ids
			String approveUserId = nextProcessDto.getApproveUserId();
			Long stepDeptId = nextProcessDto.getStepDeptId();
			if (nextProcessDto.getFlowSort().equals(Integer.valueOf(FirstProcessEnum.FLOW_SORT.getValue()))) {
				//埋点 消息提醒 审批人退回给发起人
				//如果退回给发起人的话，单位id设置为发起人当时的组织id
				if (StringUtil.isNotBlank(approveUserId)) {
					//指标埋点
//					handleApproveFinishRate(instanceDto.getBusinessId(), nowProcessDto.getStepDeptId(), null);
					if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectReportReturnInitorMsg(instanceDto, instanceDto.getCreateDept(), approveUserId);

					} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectCheckReturnInitorMsg(instanceDto, instanceDto.getCreateDept(), approveUserId);
					}
				}
			} else {
				//指标埋点
//				handleApproveFinishRate(instanceDto.getBusinessId(), nowProcessDto.getStepDeptId(), instanceDto.getCurrProcessId());
				//2.没有退回给发起人时需要发送待办消息 待办埋点 发送给处理退回操作的审批人
				//业务类型 项目申报
				if (StringUtil.isNotBlank(approveUserId)) {
					if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectReportReturnTask(instanceDto, stepDeptId, nextProcessDto.getStepUserId());
					} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectAcceptReturnTask(instanceDto, stepDeptId, nextProcessDto.getStepUserId());
					}
				}
			}

		}
		return true;
	}


	/**
	 * 项目申报回退,生成待办任务
	 *
	 * @param instanceDto
	 * @param approveUserId
	 */
	private void handleProjectReportReturnTask(InstanceDto instanceDto, Long deptId, String approveUserId) {


		//遍历接收人

		//根据业务id查询项目信息
		SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
		szykTaskGenDto.setTitle(MsgTitleConstant.PROJECT_BACK);
		szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_REPORT.getCode());
		szykTaskGenDto.setBusinessId(instanceDto.getBusinessId());
		szykTaskGenDto.setUrl(MsgUrlConstant.PROJECT_SUBMIT);
		szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_HANDLE.getCode());
		szykTaskGenDto.setReceiverId(approveUserId);
		szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.PROJECT_REPORT_DETAIL, instanceDto.getBusinessId()));
		szykTaskGenDto.setContent(String.format(MsgContentConstant.PROJECT_BACK, instanceDto.getDescription()));
		szykTaskGenDto.setOnlyDingMsg(MsgContentConstant.ONLY_DING_MSG);
		szykTaskGenDto.setDeptId(deptId);

		if (canSendToContact(szykTaskGenDto, false)) {
			szykTaskService.genTodoTask(szykTaskGenDto, true);
		} else {
			szykTaskService.genTodoTask(szykTaskGenDto, false);

		}


	}

	/**
	 * 项目验收回退,生成待办任务
	 *
	 * @param instanceDto
	 * @param approveUserId
	 */
	private void handleProjectAcceptReturnTask(InstanceDto instanceDto, Long stepDeptId, String approveUserId) {

		//根据业务id查询项目信息
		SzykTaskGenDto szykTaskGenDto = new SzykTaskGenDto();
		szykTaskGenDto.setTitle(MsgTitleConstant.ACCEPTANCE_BACK);
		szykTaskGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_ACCEPT.getCode());
		szykTaskGenDto.setBusinessId(instanceDto.getBusinessId());
		szykTaskGenDto.setUrl(MsgUrlConstant.PROJECT_ACCEPT + "?deptScopeId=" + stepDeptId);
		szykTaskGenDto.setNextOperation(NoticeNextOperationEnum.TO_HANDLE.getCode());
		szykTaskGenDto.setReceiverId(approveUserId);
		szykTaskGenDto.setDetailUrl(String.format(MsgUrlConstant.PROJECT_ACCEPT_DETAIL, instanceDto.getBusinessId()));
		szykTaskGenDto.setContent(String.format(MsgContentConstant.PROJECT_ACCEPT_BACK, instanceDto.getDescription()));
		szykTaskGenDto.setOnlyDingMsg(MsgContentConstant.ONLY_DING_MSG_ACCEPT);
		szykTaskGenDto.setDeptId(stepDeptId);

		if (canSendToContact(szykTaskGenDto, true)) {
			szykTaskService.genTodoTask(szykTaskGenDto, true);
		} else {
			szykTaskService.genTodoTask(szykTaskGenDto, false);

		}


	}

	/**
	 * 项目验收回退给发起人 消息提醒
	 *
	 * @param instanceDto
	 * @param approveUserId
	 */
	private void handleProjectCheckReturnInitorMsg(InstanceDto instanceDto, Long stepDeptId, String approveUserId) {
		String[] receiverIds = approveUserId.split(",");
		//遍历接收人
		for (String receiverId : receiverIds) {
			//根据业务id查询项目信息
			SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
			szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_ACCEPT.getCode());
			szykMsgDto.setBusinessId(instanceDto.getBusinessId());
			szykMsgDto.setUrl(String.format(MsgUrlConstant.PROJECT_ACCEPT_DETAIL, instanceDto.getBusinessId()) + "?deptScopeId=" + stepDeptId);
			szykMsgDto.setReceiverId(Long.valueOf(receiverId));
			szykMsgDto.setDeptId(stepDeptId);
			szykMsgDto.setContent(String.format(MsgContentConstant.PROJECT_ACCEPT_BACK_TO_INIT, instanceDto.getDescription()));
			szykMsgService.genMsg(szykMsgDto);
		}
	}

	/**
	 * 项目申报回退给发起人 消息提醒
	 *
	 * @param instanceDto
	 * @param approveUserId
	 */
	private void handleProjectReportReturnInitorMsg(InstanceDto instanceDto, Long stepDeptId, String approveUserId) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		String[] receiverIds = approveUserId.split(",");
		//遍历接收人
		for (String receiverId : receiverIds) {
			//根据业务id查询项目信息
			SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
			szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_REPORT.getCode());
			szykMsgDto.setBusinessId(instanceDto.getBusinessId());
			szykMsgDto.setUrl(String.format(MsgUrlConstant.PROJECT_REPORT_DETAIL, instanceDto.getBusinessId()) + "?deptScopeId=" + stepDeptId);
			szykMsgDto.setReceiverId(Long.valueOf(receiverId));
			szykMsgDto.setDeptId(stepDeptId);
			szykMsgDto.setContent(String.format(MsgContentConstant.PROJECT_BACK_TO_INIT, instanceDto.getDescription(), user.getRealName()));
			szykMsgService.genMsg(szykMsgDto);
		}
		//v1.7 指标 退回
		handleProjectIndex(stepDeptId, CommonConstant.TWO);
	}

	public IPage<InstancePageDto> approvePage(InstancePageVo vo) {
		// 专业分类
		List<DictMajorDto> majorList = dictMajorService.listByDictStatus(null);
		Map<String, DictMajorDto> majorMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(majorList)) {
			majorMap.putAll(majorList.stream().collect(Collectors.toMap(DictMajorDto::getDictKey, Function.identity())));
		}
		// 项目分类
		List<DictCommonDto> pcList = dictCommonService.listByDictStatus(DictEnum.PC.getCode(), null);
		Map<String, DictCommonDto> pcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(pcList)) {
			pcMap.putAll(pcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		// 列支渠道
		List<DictCommonDto> dcList = dictCommonService.listByDictStatus(DictEnum.DC.getCode(), null);
		Map<String, DictCommonDto> dcMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(dcList)) {
			dcMap.putAll(dcList.stream().collect(Collectors.toMap(DictCommonDto::getDictKey, Function.identity())));
		}
		SzykUser user = AuthUtil.getUser();

		IPage<InstancePageDto> pages = null;
		//获取当前业务范围
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		//文审状态
		List<DictBiz> docReviewEnumList = dictBizService.getList(com.snszyk.zbusiness.dict.enums.DictBizEnum.DOC_REVIEW.getCode());
		Map<String, DictBiz> docReviewEnumMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(docReviewEnumList)) {
			docReviewEnumMap.putAll(docReviewEnumList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}
		if (ObjectUtil.isNotEmpty(vo.getApplyStartTime())) {
			vo.setApplyStartTime(DateUtil.beginOfDay(vo.getApplyStartTime()));
		}
		if (ObjectUtil.isNotEmpty(vo.getApplyEndTime())) {
			vo.setApplyEndTime(DateUtil.endOfDay(vo.getApplyEndTime()));
		}
		if (vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) {
			//如果是待我审批的
			pages = instanceService.approvePage(vo, user.getUserId(), Long.valueOf(deptId));

		}
		if (vo.getApproveType().equals(ApproveTypeEnum.APPROVED.getCode())) {
			//如果是我已审批的
			pages = instanceProcessService.approvePage(vo, user.getUserId(), Long.valueOf(deptId));
		}
		if (vo.getApproveType().equals(ApproveTypeEnum.INIT_APPROVE.getCode())) {
			//如果是我发起的
			pages = instanceService.initPage(vo, user.getUserId(), Long.valueOf(deptId));
			//根据
			if (CollectionUtil.isNotEmpty(pages.getRecords()) && pages.getRecords().size() > 0) {
				pages.getRecords().forEach(record -> {
					//如果处于审核中且查询上个节点为发起人
					List<InstanceProcessDto> instanceProcessDtos = instanceProcessService.queryLastProcess(record.getId(), record.getCurrFlowSort());
					if (record.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode()) && CollectionUtil.isNotEmpty(instanceProcessDtos) && instanceProcessDtos.size() == 1 && instanceProcessDtos.get(0).getFlowSort() == 0) {
						record.setCancelFlag(true);
					}
				});
			}
		}
		if (CollectionUtil.isNotEmpty(pages.getRecords()) && pages.getRecords().size() > 0) {
			pages.getRecords().forEach(record -> {
				//如果是已发起或已办理，且状态还属于审核中或已退回的，状态拼接上节点名称
				if ((!vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) && (record.getCurStatus().equals(InstanceStatusEnum.RETURN_FLOW.getCode()) || record.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode())) && StringUtil.isNotBlank(record.getCurStepName())) {
					record.setCurStatusName(DictBizCache.getValue(DictBizEnum.REVIEW_STATUS, record.getCurStatus()) + "(" + record.getCurStepName() + ")");
				} else {
					record.setCurStatusName(DictBizCache.getValue(DictBizEnum.REVIEW_STATUS, record.getCurStatus()));
				}

				if (ObjectUtil.isNotEmpty(record.getApproveStatus())) {
					record.setApproveStatusName(ApproveStatusEnum.getByCode(record.getApproveStatus()).getMessage());
				}
				if (ObjectUtil.isNotEmpty(record.getCorpApproveResult())) {
					record.setCorpApproveResult(DictBizCache.getValue(DictBizEnum.PROJECT_APPROVAL_OPINION, record.getCorpApproveResult()));
				}
				if (vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) {
					//设置审查意见标识
					if (ObjectUtil.isNotEmpty(record.getBeforeEvent()) &&
						record.getBeforeEvent().equals(BeforeEventEnum.YES.getCode()) &&
						(record.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode())
							|| (record.getCurStatus().equals(InstanceStatusEnum.RETURN_FLOW.getCode()) &&
							record.getBackLogic().equals(BackLogicEnum.YES.getCode())))) {
						record.setCheckFlag(dept.getUnitLevel());
					}
				}
				//设置文审状态名
				String docReview = record.getDocReview();
				if (StringUtil.isNotBlank(docReview)) {
					DictBiz dictBiz = docReviewEnumMap.get(docReview);
					if (dictBiz != null) {
						record.setDocReviewName(dictBiz.getDictValue());
					}
				}

				//获取最新组织信息
				if (Func.isNotEmpty(record.getSubmitDeptId())) {
					record.setSubmitDeptName(SysCache.getDeptName(record.getSubmitDeptId()));
				}
				//单独处理
				//针对当前登录用户的业务范围是山东能源的做以下展示调整:公司名称是“山东能源”的，建设单位只显示部门就可以，展示的是山能的总部的数据。
				//公司名称是二三四级单位的，建设单位展示到单位级别，不显示部门。
				Dept dtoDept = SysCache.getDept(record.getConstructionUnitId());
				if (DeptScopeUtil.isHeadPerson() && Objects.equals(record.getCompanyName(), "山东能源")) {
					//如果是集团的，不显示部门

					Optional.ofNullable(dtoDept).ifPresent(e -> record.setConstructionUnitName(e.getDeptName()));
				}
				if (DeptScopeUtil.isHeadPerson() && !Objects.equals(record.getCompanyName(), "山东能源")) {
					//公司名称是二三四级单位的，建设单位展示到单位级别，不显示部门。
					if (dtoDept != null) {
						record.setConstructionUnitName(SysCache.getDeptName(dtoDept.getUnitId()));
					}
				}
				//字典值

				// 专业分类
				if (!ObjectUtils.isEmpty(majorMap) && !ObjectUtils.isEmpty(record.getSpecialtyClassification()) && majorMap.containsKey(record.getSpecialtyClassification())) {
					record.setSpecialtyClassificationName(majorMap.get(record.getSpecialtyClassification()).getDictValue());
				}
				// 项目分类
				if (!ObjectUtils.isEmpty(pcMap) && !ObjectUtils.isEmpty(record.getProjectClassification()) && pcMap.containsKey(record.getProjectClassification())) {
					record.setProjectClassificationName(pcMap.get(record.getProjectClassification()).getDictValue());
				}
				// 列支渠道
				if (!ObjectUtils.isEmpty(dcMap) && !ObjectUtils.isEmpty(record.getDistributionChannel()) && dcMap.containsKey(record.getDistributionChannel())) {
					record.setDistributionChannelName(dcMap.get(record.getDistributionChannel()).getDictValue());
				}

			});
		}
		//处理专家评审
		if (vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) {
			handleExpertReview(pages);
		}
		return pages;
	}

	/**
	 * 处理专家评审
	 *
	 * @param pages
	 */
	private void handleExpertReview(IPage<InstancePageDto> pages) {
		//只有集团的人可以看专家评审的信息
		Dept loginDept = DeptScopeUtil.getLoginDept();
		Long parentId = loginDept.getParentId();
		if (!Objects.equals(parentId, 0L)) {
			return;
		}

		Map<String, String> reviewMap = DictUtil.getMap(com.snszyk.zbusiness.dict.enums.DictBizEnum.EXPERT_REVIEW_STATUS);
		List<InstancePageDto> records = pages.getRecords();
		if (CollectionUtil.isNotEmpty(records)) {
			//评审信息
			List<String> jsonList = records.stream().map(InstancePageDto::getExpertReview).filter(StringUtil::isNotBlank).collect(Collectors.toList());
			//转为ProjectBaseExpertReviewDto的list
			List<List<ProjectBaseExpertReviewDto>> reviewDtoList = jsonList.stream().map(e -> JSON.parseArray(e, ProjectBaseExpertReviewDto.class)).collect(Collectors.toList());
			//专家的ids
			List<Long> expertIds = new ArrayList<>();

			for (List<ProjectBaseExpertReviewDto> reviewDtos : reviewDtoList) {
				for (ProjectBaseExpertReviewDto reviewDto : reviewDtos) {
					Long expertId = reviewDto.getExpertId();
					if (expertId != null) {
						expertIds.add(expertId);
					}

				}
			}
			List<PersonExpertDto> expertDtoList = personExpertService.listByIds(expertIds);
			//专家的map
			Map<Long, PersonExpertDto> expertDtoMap = expertDtoList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k1));
			for (InstancePageDto record : records) {
				//设置是否可以一键转发评审 当前节点为集团项目联络员 下一节点为 集团分管领导 则有//
				if (Objects.equals(record.getCurStepName(), HEAD_CONTACT) && Objects.equals(record.getNextStepName(), HEAD_LEADER)) {
					record.setSendExpertFlag(true);
				}
				//当前是退回的,当前节点是在集团联络人
				Integer curStatus = record.getCurStatus();
				if (Objects.equals(curStatus, InstanceStatusEnum.RETURN_FLOW.getCode()) && Objects.equals(record.getCurStepName(), HEAD_CONTACT)) {
					record.setSendExpertFlag(true);
				}
				String expertReview = record.getExpertReview();
				if (StringUtil.isBlank(expertReview)) {
					continue;
				}
				List<ProjectBaseExpertReviewDto> reviewDtos = JSON.parseArray(expertReview, ProjectBaseExpertReviewDto.class);
				//转为ProjectExpertListDto
				List<ProjectExpertListDto> projectExpertListDtos = new ArrayList<>();
				for (ProjectBaseExpertReviewDto e : reviewDtos) {
					PersonExpertDto personExpertDto = expertDtoMap.get(e.getExpertId());
					if (personExpertDto != null) {
						ProjectExpertListDto projectExpertListDto = new ProjectExpertListDto();
						projectExpertListDto.setExpertId(e.getExpertId());
						projectExpertListDto.setUserName(personExpertDto.getUserName());
						projectExpertListDto.setReviewStatus(e.getReviewStatus());
						projectExpertListDto.setUserId(personExpertDto.getUserId());
						projectExpertListDto.setOrgId(personExpertDto.getOrgId());
						projectExpertListDto.setReviewStatus(e.getReviewStatus());
						projectExpertListDto.setOnlineStatus(e.getOnlineStatus());
						projectExpertListDto.setProjectId(record.getBusinessId());
						projectExpertListDto.setReviewStatusName(reviewMap.get(e.getReviewStatus()));
						projectExpertListDtos.add(projectExpertListDto);
					}
					if (CollectionUtil.isNotEmpty(projectExpertListDtos)) {
						//评审信息
						record.setExpertReviewList(projectExpertListDtos);
					}
				}
			}
		}
	}

	private List<WorkflowStepDto> getStep(String businessType) {
		List<WorkflowDto> workflowDtos = workflowService.queryByBusinessType(businessType);
		if (CollectionUtil.isEmpty(workflowDtos)) {
			throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
		}
		List<WorkflowStepDto> workflowStepDtoList = workflowStepService.fetchByFlowId(workflowDtos.get(0).getId());
		if (CollectionUtil.isEmpty(workflowStepDtoList)) {
			throw new ServiceException(FlowExceptionEnum.WRONG_DATA.getMessage());
		}

		return workflowStepDtoList;
	}

	@Transactional
	public Boolean cancel(Long instanceId) {


		//修改流程实例
		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		//这里加一下校验，判断下是否可以撤回
		ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
		Boolean checkResult = approvalOperate.checkCancel(instanceDto.getBusinessId());

		if (!checkResult) {
			throw new ServiceException("当前流程状态不可撤回");
		}
		instanceDto.setCurStatus(InstanceStatusEnum.CANCEL.getCode());
		instanceDto.setFlowStatus(FlowStatusEnum.END.getCode());
		//变更实例信息
		instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));
		//调用业务撤回逻辑

		approvalOperate.doCancel(instanceDto.getBusinessId());
		String businessType = instanceDto.getBusinessType();
		String currUserIds = instanceDto.getCurrUserId();
		Long currDeptId = instanceDto.getCurrDeptId();
		Long businessId = instanceDto.getBusinessId();
		if (StringUtil.isNotBlank(currUserIds)) {
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(businessType)) {
				handleCancelProjectReport(instanceDto, currUserIds, currDeptId, businessId);
			} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
				handleCancelProjectAccept(instanceDto, currUserIds, currDeptId, businessId);
			}
		}
		//v1.7 指标统计-撤回处理
		handleProjectIndex(instanceDto.getCurrDeptId(), CommonConstant.THREE);
		return true;
	}

	/**
	 * 项目申报审批撤回处理
	 * 1.审批人接收消息提醒
	 * 2.审批人的待办消失
	 *
	 * @param instanceDto
	 * @param currUserIds
	 * @param businessId
	 */

	private void handleCancelProjectReport(InstanceDto instanceDto, String currUserIds, Long currentDeptId, Long businessId) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		//项目撤回消息提醒埋点
		//审批人ids
		String[] split = currUserIds.split(",");
		String deptId = AuthUtil.getDeptId();
		Dept dept = deptService.getById(deptId);
		String deptName = "";
		if (dept != null) {
			deptName = dept.getDeptName();
		}
		List<String> userIdList = Arrays.stream(split).distinct().collect(Collectors.toList());
		Dept unit = SysCache.getDept(dept.getUnitId());
		String orgName = "";
		if (!Objects.equals(unit.getDeptName(), deptName)) {
			orgName = unit.getDeptName() + "/" + deptName;
		} else {
			orgName = deptName;
		}
		for (String receiverId : userIdList) {
			SzykMsgGenDto szykMsgGenDto = new SzykMsgGenDto();
			szykMsgGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_REPORT.getCode());
			szykMsgGenDto.setBusinessId(businessId);
			szykMsgGenDto.setUrl(String.format(MsgUrlConstant.PROJECT_REPORT_DETAIL, businessId) + "?deptScopeId=" + currentDeptId);
			szykMsgGenDto.setDeptId(currentDeptId);
			szykMsgGenDto.setNextOperation(NoticeNextOperationEnum.VIEW_DETAIL.getCode());
			szykMsgGenDto.setReceiverId(Long.valueOf(receiverId));
			szykMsgGenDto.setContent(String.format(MsgContentConstant.PROJECT_WITHDRAW, orgName, user.getRealName(), instanceDto.getDescription()));


			//1.审批人接收提醒消息
			szykMsgService.genMsg(szykMsgGenDto);
			//2.审批人的待办消失
			szykTaskService.cancelTask(ToDoBusinessTypeEnum.PROJECT_REPORT, businessId, Long.valueOf(receiverId));
		}
	}

	/**
	 * 项目验收审批撤回处理
	 * 1.审批人接收消息提醒
	 * 2.审批人的待办消失
	 *
	 * @param instanceDto
	 * @param currUserIds
	 * @param businessId
	 */
	private void handleCancelProjectAccept(InstanceDto instanceDto, String currUserIds, Long currentDeptId, Long businessId) {
		User user = UserCache.getUser(AuthUtil.getUserId());
		//项目撤回消息提醒埋点
		//审批人ids
		String[] split = currUserIds.split(",");
		String deptId = AuthUtil.getDeptId();
		Dept dept = deptService.getById(deptId);
		String deptName = "";
		if (dept != null) {
			deptName = dept.getDeptName();
		}
		Dept unit = SysCache.getDept(dept.getUnitId());
		for (String receiverId : split) {
			SzykMsgGenDto szykMsgGenDto = new SzykMsgGenDto();
			szykMsgGenDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_ACCEPT.getCode());
			szykMsgGenDto.setBusinessId(businessId);
			szykMsgGenDto.setUrl(String.format(MsgUrlConstant.PROJECT_ACCEPT_DETAIL, businessId) + "?deptScopeId=" + currentDeptId);

			szykMsgGenDto.setNextOperation(NoticeNextOperationEnum.VIEW_DETAIL.getCode());
			szykMsgGenDto.setReceiverId(Long.valueOf(receiverId));
			szykMsgGenDto.setDeptId(currentDeptId);

			szykMsgGenDto.setContent(String.format(MsgContentConstant.PROJECT_ACCEPT_WITHDRAW, unit.getDeptName() + "/" + deptName, user.getRealName(), instanceDto.getDescription()));

			//1.审批人接收提醒消息
			szykMsgService.genMsg(szykMsgGenDto);
			//2.审批人的待办消失
			szykTaskService.cancelTask(ToDoBusinessTypeEnum.PROJECT_ACCEPT, businessId, Long.valueOf(receiverId));
		}
	}

	public List<InstanceStepDto> getInstance(Long instanceId) {
		Map<String, String> docReviewName = DictUtil.getMap(com.snszyk.zbusiness.dict.enums.DictBizEnum.DOC_REVIEW);
		//获取业务流程
		InstanceDto instanceDto = instanceService.fetchById(instanceId);

		//获取审批日志
		List<InstanceLogDto> logList = instanceLogService.queryByInstanceId(instanceDto.getId());
		//获取流程节点
		List<InstanceProcessDto> processList = instanceProcessService.queryByInstanceId(instanceDto.getId());
		Map<Integer, InstanceProcessDto> processDtoMap = processList.stream().collect((Collectors.toMap(InstanceProcessDto::getFlowSort, Function.identity())));
		//组装数据
		List<InstanceStepDto> dtos = new LinkedList<>();
		Integer lastSort = null;
		if (CollectionUtil.isNotEmpty(logList)) {
			for (int i = 0; i < logList.size(); i++) {
				InstanceLogDto dto = logList.get(i);
				if (i == logList.size() - 1) {
					lastSort = dto.getFlowSort();
				}

				InstanceStepDto stepDto = BeanUtil.copyProperties(dto, InstanceStepDto.class);
				InstanceProcessDto instanceProcessDto = processDtoMap.get(dto.getFlowSort());
				stepDto.setStepName(instanceProcessDto.getStepName());
//				//文审状态和文审意见
//				stepDto.setDocReview(instanceProcessDto.getDocReview());//
//				stepDto.setDocReviewComment(instanceProcessDto.getDocReviewComment());
//				if (StringUtil.isNotBlank(instanceProcessDto.getDocReview())) {
//					stepDto.setDocReviewName(docReviewName.get(instanceProcessDto.getDocReview()));
//				}
				if (instanceProcessDto.getOperatorId().equals(leaderRoleId)) {
					stepDto.setUserType(leaderFlag);
				}

				//获取审批附件
				// 附件
				List<BpmInstanceFileDto> fileDtoList = instanceFileLogicService.listByBusinessId(dto.getId());
				if (!CollectionUtils.isEmpty(fileDtoList)) {
					// 附件ID
					List<Long> attachIds = fileDtoList.stream().map(BpmInstanceFileDto::getAttachId).collect(Collectors.toList());

					List<Attach> attachList = attachService.listByFileIds(attachIds);
					if (!CollectionUtils.isEmpty(attachList)) {
						stepDto.setFileList(Objects.requireNonNull(com.snszyk.core.tool.utils.BeanUtil.copy(attachList, SzykAttachDto.class)));
					}
				}
				if (Func.isNotEmpty(dto.getOperatorDeptId())) {
					stepDto.setOperatorDeptName(SysCache.getDeptName(dto.getOperatorDeptId()));
				}
				dtos.add(stepDto);
			}
		}
		//获取大于节点的数据
		if (instanceDto.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode())) {
			//审核中的需要拼装流程节点
			Integer finalLastSort = lastSort;
			processList = processList.stream().filter(x -> x.getFlowSort() > finalLastSort).collect(Collectors.toList());
			for (int i = 0; i < processList.size(); i++) {
				InstanceProcessDto instanceProcessDto = processList.get(i);
				InstanceStepDto stepDto = new InstanceStepDto();
				stepDto.setFlowSort(instanceProcessDto.getFlowSort());
				if (instanceProcessDto.getOperatorId().equals(leaderRoleId)) {
					stepDto.setUserType(leaderFlag);
				}
				stepDto.setStepName(instanceProcessDto.getStepName());
				if (i == 0) {
					stepDto.setOperatorName(instanceProcessDto.getStepUserName());
					stepDto.setOperatorDeptName(SysCache.getDeptName(instanceProcessDto.getStepDeptId()));
				}
				if (Func.isNotEmpty(instanceProcessDto.getStepDeptId())) {
					stepDto.setOperatorDeptName(SysCache.getDeptName(instanceProcessDto.getStepDeptId()));
				}
				dtos.add(stepDto);
			}
		}
		return dtos;
	}

	public Integer getBeforeEvent(String businessType) {

		Long userId = AuthUtil.getUserId();
		Long deptId = Long.valueOf(AuthUtil.getDeptId());
		//如果其中有一条需要填写审查意见，则显示按钮
		List<InstanceDto> dtos = instanceService.getBeforeEvent(businessType, userId, deptId);

		if (CollectionUtil.isNotEmpty(dtos)) {
			//遍历循环是否有需要填写审查意见的
			for (InstanceDto instanceDto : dtos) {
				if ((ObjectUtil.isNotEmpty(instanceDto.getCurBeforeEvent()) && instanceDto.getCurBeforeEvent().equals(BeforeEventEnum.YES.getCode()))) {
					//判断几级单位
					Dept dept = SysCache.getDept(deptId);
					dept = SysCache.getDept(dept.getUnitId());
					return dept.getUnitLevel();
				}
			}
		}
		return 0;
	}

	public InstanceCheckDataDto checkData(IdVo vo) {
		InstanceCheckDataDto dto = new InstanceCheckDataDto();
		List<WrongDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vo.getIdList())) {
			throw new ServiceException(ExceptionEnum.NOT_PARAMS.getMessage());
		}

		List<InstanceDto> list = instanceService.listByIds(vo.getIdList());
		if (CollectionUtils.isEmpty(list)) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		for (InstanceDto instanceDto : list) {
			WrongDto wrongDto = new WrongDto();
			wrongDto.setName(instanceDto.getFlowNo());
			wrongDto.setProjectName(instanceDto.getDescription());
			// 校验-是否填完审查
			//获取当前用户所在单位的层级
			Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
			dept = SysCache.getDept(dept.getUnitId());
			Integer unitLevel = dept.getUnitLevel();
			ProjectBaseDto projectBaseDto = projectBaseService.fetchById(instanceDto.getBusinessId());

			//如果需要校验事件
			if (instanceDto.getCurBeforeEvent().equals(BeforeEventEnum.YES.getCode())) {
				//获取当前审批节点
				InstanceProcessDto curDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
				if (corLenvel.equals(unitLevel) && (StringUtil.isEmpty(projectBaseDto.getCorpApproveResult())
					/*|| StringUtil.isEmpty(projectBaseDto.getCorpApproveRemark())*/)) {
					//如果是集团单位
					dto.setCheckResult(false);
					wrongDto.setResult(false);
					wrongDto.setMessage(FlowExceptionEnum.ENOUGH_APPROVAL_OPINION.getMessage());
					//查询提报单位
					wrongDto.setName(curDto.getSubmitDeptName());
					result.add(wrongDto);
					continue;
				}

				if (secLenvel.equals(unitLevel) && (StringUtil.isEmpty(projectBaseDto.getSecApproveRemark()))) {
					//如果是二级单位
					dto.setCheckResult(false);
					wrongDto.setResult(false);
					wrongDto.setMessage(FlowExceptionEnum.ENOUGH_APPROVAL_OPINION.getMessage());
					//查询提报单位
					wrongDto.setName(curDto.getSubmitDeptName());
					result.add(wrongDto);
					continue;
				}
			}
		}
		dto.setDataList(result);
		return dto;
	}

	@Transactional
	public Boolean uploadFile(FlowFileVo vo) {
		List<Long> fileList = vo.getIdList();
		//先删除之前的附件
		instanceFileLogicService.deleteByBusiness(vo.getInstanceId(), FileBusinessTypeEnum.TEMP.getCode());
		//再保存新的附件
		if (!CollectionUtils.isEmpty(fileList)) {
			fileList.forEach(fileId -> {
				BpmInstanceFileVo fileVo = new BpmInstanceFileVo();
				fileVo.setBusinessId(vo.getInstanceId());
				fileVo.setBusinessType(FileBusinessTypeEnum.TEMP.getCode());
				fileVo.setAttachId(fileId);
				instanceFileLogicService.save(fileVo);
			});
			// 自动收集
			baseLogicService.autoCollectKnowledge(vo.getIdList(), vo.getInstanceId(), null);
		}
		return true;
	}

	public List<SzykAttachDto> getInstanceFile(Long instanceId) {
		List<BpmInstanceFileDto> fileDtoList = instanceFileLogicService.queryTemp(instanceId, FileBusinessTypeEnum.TEMP.getCode());
		// 附件
		if (!CollectionUtils.isEmpty(fileDtoList)) {
			// 附件ID
			List<Long> attachIds = fileDtoList.stream().map(BpmInstanceFileDto::getAttachId).collect(Collectors.toList());
			List<Attach> sourceList = attachService.listByFileIds(attachIds);
			if (CollectionUtil.isNotEmpty(sourceList)) {
				return com.snszyk.core.tool.utils.BeanUtil.copy(sourceList, SzykAttachDto.class);
			}
		}
		return null;
	}


	/*@Transactional
	public Boolean disagreeFlow(DisagreeFlowVo disagreeFlowVo) {
		return doDisagreeFlow(disagreeFlowVo, AuthUtil.getUserId());
	}

	@Transactional
	public Boolean doDisagreeFlow(DisagreeFlowVo disagreeFlowVo, Long userId) {
		User user = UserCache.getUser(userId);
		disagreeFlowVo.getInstanceIds().forEach(id -> {
			//结束流程
			//获取实例信息
			InstanceDto instanceDto = instanceService.fetchById(id);
			//结束流程
			instanceDto.setCurStatus(InstanceStatusEnum.DISAGREE.getCode());
			instanceDto.setFlowStatus(FlowStatusEnum.END.getCode());
			instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));
			//获取当前节点信息
			InstanceProcessDto nowProcessDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
			//变更节点审核信息
			nowProcessDto.setApproveTime(LocalDateTime.now());
			nowProcessDto.setApproveUserId(String.valueOf(userId));
			nowProcessDto.setApproveUserName(user.getRealName());
			nowProcessDto.setApproveStatus(ApproveStatusEnum.DISAGREE_FLOW.getCode());
			nowProcessDto.setOperateRemark(disagreeFlowVo.getOperateRemark());
			instanceProcessService.save(BeanUtil.copyProperties(nowProcessDto, InstanceProcessVo.class));
			//新增流程日志
			createInstanceLog(instanceDto, disagreeFlowVo.getOperateRemark(), nowProcessDto);
		});
		return true;
	}*/


	@Transactional
	public Boolean doApproved(ApproveVo vo, Long userId, Long deptId) throws Exception {

		Boolean isLeader = false;
		//这里加个校验
		if (SubmitChooseEnum.HAND.getCode().equals(vo.getSubmitChoose())) {
			if (StringUtil.isEmpty(vo.getStepUserId()) || StringUtil.isEmpty(vo.getStepUserName()) || StringUtil.isEmpty(vo.getStepDeptName()) || Func.isEmpty(vo.getStepDeptId())) {
				throw new ServiceException(FlowExceptionEnum.ENOUGH_APPROVAL_USER.getMessage());
			}
		}
		User user = UserCache.getUser(userId);
		Dept dept = SysCache.getDept(deptId);
		Dept finalDept = SysCache.getDept(dept.getUnitId());
		//批量这里采取单条处理的方式
		Integer curOperatorChannel = null;
		Integer flowLevel = null;
		if (Func.isNotEmpty(vo.getStepDeptId())) {
			flowLevel = SysCache.getDept(vo.getStepDeptId()).getUnitLevel();
		}
		Long submitUserId = null;

		List<Long> oaInstanceIds = new LinkedList<>();
		String businessType = "";
		for (Long instanceId : vo.getInstanceIds()) {
			//获取实例信息
			InstanceDto instanceDto = instanceService.fetchById(instanceId);
			businessType = instanceDto.getBusinessType();
			//这里需要加下校验。如果当前审批的审批人中没有审核人，则报错提醒
			if (instanceDto.getCurrFlowSort().equals(0)) {
				throw new ServiceException("项目：" + instanceDto.getDescription() + "，当前是发起人节点，需要重新提报");
			}
			if (!instanceDto.getCurrUserId().contains(String.valueOf(userId))) {
				throw new ServiceException("项目：" + instanceDto.getDescription() + "，已被其他联络员办理过了，无需重复办理");
			}
			//v1.5首先将待办变更为已办,然后再 生成新的待办, 防止待办变更错误
			// 项目申报埋点通知 已办
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
				//消息埋点
				Long businessId = instanceDto.getBusinessId();
				if (ObjectUtil.isNotEmpty(businessId)) {
					//2.审批人提交后 已办
					szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_REPORT, businessId, userId, deptId);
					//其他人的待办删除

				}
			} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
				//项目验收 埋点通知 已办
				Long businessId = instanceDto.getBusinessId();
				if (ObjectUtil.isNotEmpty(businessId)) {
					//2.审批人提交后 已办
					szykTaskService.finishTask(ToDoBusinessTypeEnum.PROJECT_ACCEPT, businessId, userId, deptId);
				}
			}
			//获取当前节点信息
			InstanceProcessDto nowProcessDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
			//判断当前节点是分管领导节点
			isLeader = isLeader(nowProcessDto.getOperatorId());
			//v1.5同步文审状态
			instanceProcessService.syncUpdateDocReview(instanceDto.getId(), nowProcessDto);//
			nowProcessDto.setEnableDocReview(false);
			//变更节点审核信息
			nowProcessDto.setApproveTime(LocalDateTime.now());
			nowProcessDto.setApproveUserId(String.valueOf(user.getId()));
			nowProcessDto.setApproveUserName(user.getRealName());
			nowProcessDto.setApproveStatus(ApproveStatusEnum.PASS.getCode());
			nowProcessDto.setOperateRemark(vo.getOperateRemark());
			instanceProcessService.save(BeanUtil.copyProperties(nowProcessDto, InstanceProcessVo.class));

			//获取下个节点信息
			InstanceProcessDto nextProcessDto = null;
			//获取下第二个节点
			InstanceProcessDto nextTwoProcessDto = null;

			List<InstanceProcessDto> dtos = instanceProcessService.queryGreaterProcess(instanceDto.getId(), instanceDto.getCurrFlowSort());
			if (CollectionUtil.isNotEmpty(dtos)) {
				nextProcessDto = dtos.get(0);
				if (dtos.size() > 1) {
					nextTwoProcessDto = dtos.get(1);
				}
			}


			Map<String, Object> params = new HashMap<>();

			//变更下个节点信息
			if (ObjectUtil.isNotEmpty(nextProcessDto)) {

				//执行审批通过的方法
				ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
				approvalOperate.handleBusiness(instanceDto.getBusinessId());


				//先判断本次是否选择下个审批人
				if (vo.getSubmitChoose().equals(SubmitChooseEnum.AUTO.getCode())) {
					//如果未选，自动获取下个节点审核信息
					List<StepUserDto> users = getUsersByType(instanceId, nextProcessDto.getScreenDept(), nextProcessDto.getUnitLevel(), nextProcessDto.getStepType(), nextProcessDto.getOperatorId(), finalDept, nowProcessDto.getFlowSort());
					//如果没有跑出异常
					if (CollectionUtil.isEmpty(users)) {
						throw new ServiceException(FlowExceptionEnum.ENOUGH_APPROVAL_USER.getMessage());
					}

					vo.setStepDeptId(users.get(0).getStepDeptId());
					vo.setStepDeptName(users.get(0).getStepDeptName());
					vo.setStepUserId(users.stream().map(StepUserDto::getStepUserId).collect(Collectors.joining(",")));
					vo.setStepUserName(users.stream().map(StepUserDto::getStepUserName).collect(Collectors.joining(",")));

				}
				nextProcessDto.setStepUserId(vo.getStepUserId());
				nextProcessDto.setStepUserName(vo.getStepUserName());
				nextProcessDto.setStepDeptId(vo.getStepDeptId());
				nextProcessDto.setStepDeptName(SysCache.getDeptName(vo.getStepDeptId()));
				//下个节点的审批层级小于当前审批层级，则下个节点信息需要更新提交信息
				Dept nextDept = SysCache.getDept(vo.getStepDeptId());
				if (nextDept.getUnitLevel() < finalDept.getUnitLevel()) {
					nextProcessDto.setSubmitUserId(user.getId());
					nextProcessDto.setSubmitUserName(user.getName());
					nextProcessDto.setSubmitDeptId(finalDept.getId());
					nextProcessDto.setSubmitDeptName(finalDept.getDeptName());
					nextProcessDto.setSubmitTime(nowProcessDto.getApproveTime());
				} else {
					nextProcessDto.setSubmitUserId(nowProcessDto.getSubmitUserId());
					nextProcessDto.setSubmitUserName(nowProcessDto.getSubmitUserName());
					nextProcessDto.setSubmitDeptId(nowProcessDto.getSubmitDeptId());
					nextProcessDto.setSubmitDeptName(nowProcessDto.getSubmitDeptName());
					nextProcessDto.setSubmitTime(nowProcessDto.getSubmitTime());
				}

				instanceProcessService.save(BeanUtil.copyProperties(nextProcessDto, InstanceProcessVo.class));
				instanceDto.setCurrProcessId(nextProcessDto.getId());
				instanceDto.setCurrStepName(nextProcessDto.getStepName());
				instanceDto.setCurrUserId(vo.getStepUserId());
				instanceDto.setCurrFlowSort(nextProcessDto.getFlowSort());
				instanceDto.setCurrDeptId(vo.getStepDeptId());
				instanceDto.setCurrDeptName(SysCache.getDeptName(vo.getStepDeptId()));
				instanceDto.setCurBeforeEvent(nextProcessDto.getBeforeEvent());
				instanceDto.setCurCompleteEvent(nextProcessDto.getCompleteEvent());
				instanceDto.setCurOperatorChannel(nextProcessDto.getOperatorChannel());
				//变更实例信息
				instanceDto.setCurStatus(InstanceStatusEnum.CHECKING.getCode());
				//领导人审批节点的需要带到下个节点
				if (isLeader) {
					instanceDto.setCurReceive(StringUtil.isBlank(vo.getOperateRemark()) ? "" : vo.getOperateRemark());
				} else {
					instanceDto.setCurReceive("");
				}
				if (Func.isNotEmpty(nextTwoProcessDto)) {
					instanceDto.setNextProcessId(nextTwoProcessDto.getId());
					instanceDto.setNextStepName(nextTwoProcessDto.getStepName());
				} else {
					instanceDto.setNextProcessId(nullProcessId);
					instanceDto.setNextStepName(nullStr);
				}

				params.put("currStepName", instanceDto.getCurrStepName());
				//如果提交到OA，则不发消息
				if (nextProcessDto.getOperatorChannel().equals(OperatorChannelEnum.SYSTEM.getCode())) {
					//埋点3 不是最后一个节点 生成下个审批人的待办
					if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectSubmitTask(vo.getStepUserId(), vo.getStepDeptId(), instanceDto.getBusinessId());
						//消息埋点-项目验收
					} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
						handleProjectCheckTask(vo.getStepUserId(), vo.getStepDeptId(), instanceDto.getBusinessId());
					}
				}
				if (nextProcessDto.getOperatorChannel().equals(OperatorChannelEnum.OA.getCode())) {
					//这里默认发给oa的只有一个人
					submitUserId = Long.valueOf(nextProcessDto.getStepUserId());
					if (Func.isEmpty(curOperatorChannel)) {
						curOperatorChannel = nextProcessDto.getOperatorChannel();
					}
					if (Func.isNotEmpty(curOperatorChannel) && !curOperatorChannel.equals(nextProcessDto.getOperatorChannel())) {
						throw new ServiceException(FlowExceptionEnum.NOT_SAME_OA_FLOW.getMessage());
					}
					oaInstanceIds.add(instanceId);
				}

			}

			//如果已经到了最后一个节点
			if (ObjectUtil.isEmpty(nextProcessDto)) {
				//结束流程
				instanceDto.setCurStatus(InstanceStatusEnum.PASS.getCode());
				instanceDto.setFlowStatus(FlowStatusEnum.END.getCode());

				//处理业务
				//执行审批通过的方法
				ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
				//doApproval
				approvalOperate.doApproval(instanceDto.getBusinessId());

				params.put("currStepName", "--");
				//1.消息 提醒埋点  审批流程结束 发送消息给 项目申报发起人
				//最初的发起人
				Long applyUserId = instanceDto.getApplyUserId();
				Long applyDept = Long.valueOf(instanceDto.getApplyUserDept());
				//获取除了集团外的所有审批节点的联络人
//				List<InstanceProcessDto> userList = instanceProcessService.queryByUser(instanceId);
				if (applyUserId != null) {
					if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
						//添加项目的发起人
						/*List<InstanceProcessDto> applyList = userList.stream().filter(e -> Objects.equals(String.valueOf(applyUserId), String.valueOf(e.getStepUserId())) && Objects.equals(String.valueOf(applyDept), String.valueOf(e.getStepDeptId()))).collect(Collectors.toList());
						if (CollectionUtil.isEmpty(applyList)) {
							//如果节点上没有发起人.则添加上发起人
							InstanceProcessDto applyProcessDto = new InstanceProcessDto();
							applyProcessDto.setStepUserId(String.valueOf(applyUserId));
							applyProcessDto.setStepDeptId(applyDept);
							userList.add(applyProcessDto);
						}*/
//						if (CollectionUtil.isNotEmpty(userList)) {
						//查询项目的审查意见
						//v1.5只发送给项目的发起人
						ProjectBaseDto baseDto = projectBaseService.fetchById(instanceDto.getBusinessId());
						String value = DictBizCache.getValue(DictBizEnum.PROJECT_APPROVAL_OPINION, baseDto.getCorpApproveResult());
//							userList.forEach(entity -> {
						SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
						szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_REPORT.getCode());
						szykMsgDto.setBusinessId(instanceDto.getBusinessId());
						szykMsgDto.setUrl(String.format(MsgUrlConstant.PROJECT_REPORT_DETAIL, instanceDto.getBusinessId()) + "?deptScopeId=" + applyDept);
						szykMsgDto.setReceiverId(Long.valueOf(applyUserId));
						szykMsgDto.setDeptId(applyDept);
						szykMsgDto.setContent(String.format(MsgContentConstant.EXAMINE_FINISH, instanceDto.getDescription(), value));
						szykMsgService.genMsg(szykMsgDto);
						// v1.6 项目申报通过的埋点
						handleProjectIndex(IndexLibraryEnum.PROJECT_SUBMIT_PASS, baseDto.getCompanyId(), baseDto.getId(),
							LocalDateTime.ofInstant(baseDto.getSubmitTime().toInstant(), ZoneId.systemDefault()));

//							}
//							);
//						}
					} else if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType()) || BusinessTypeEnum.TWO_PROJECT_CHECK.getCode().equals(instanceDto.getBusinessType())) {
						SzykMsgGenDto szykMsgDto = new SzykMsgGenDto();
						szykMsgDto.setBusinessType(ToDoBusinessTypeEnum.PROJECT_ACCEPT.getCode());
						szykMsgDto.setBusinessId(instanceDto.getBusinessId());
						szykMsgDto.setUrl(String.format(MsgUrlConstant.PROJECT_ACCEPT_DETAIL, instanceDto.getBusinessId()) + "?deptScopeId=" + applyDept);
						szykMsgDto.setDeptId(applyDept);
						szykMsgDto.setReceiverId(applyUserId);
						szykMsgDto.setContent(String.format(MsgContentConstant.ACCEPT_EXAMINE_FINISH, instanceDto.getDescription(), (InstanceStatusEnum.PASS.getMessage())));
						szykMsgService.genMsg(szykMsgDto);
					}
				}
			}
			//新增流程日志
			createInstanceLog(instanceDto, vo.getOperateRemark(), nowProcessDto, nextProcessDto);//123123
			// 清空此process的文审
//			instanceProcessService.clearProcessDocReview(nowProcessDto.getId());
			//变更实例信息
			instanceService.save(BeanUtil.copyProperties(instanceDto, InstanceVo.class));
			//更新审批层级
			ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(instanceDto.getBusinessType());
			Dept depart = SysCache.getDept(instanceDto.getCurrDeptId());
			depart = SysCache.getDept(depart.getUnitId());
			//更新当前节点信息

			approvalOperate.processDone(instanceDto.getBusinessId(), instanceDto.getId(), params);
			//更新当前审核到的等级
			approvalOperate.updateLevel(instanceDto.getBusinessId(), depart.getUnitLevel());

//			if (!ObjectUtil.isEmpty(nextProcessDto)) {
//				//如果不是最后一个节点
//				//指标埋点
//				handleApproveFinishRate(instanceDto.getBusinessId(), nowProcessDto.getStepDeptId(), instanceDto.getCurrProcessId());
//			} else {
//				handleApproveFinishRate(instanceDto.getBusinessId(), nowProcessDto.getStepDeptId(), null);
//
//			}

			//v1.8如果下一个节点是集团联络人, 更新专家的状态为未转发
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
				handleProjectExert(instanceDto);
			}
		}
		//发送oa申请
		if (CollectionUtil.isNotEmpty(oaInstanceIds)) {
			//重新排序
			List<Long> businessIds = instanceService.queryInstanceBySort(oaInstanceIds);
			//获取提交oa的数据集
			ApprovalOperateService approvalOperate = ApprovalOperateFactory.getBusinessType(businessType);
			JSONArray dataArray = approvalOperate.createOaData(businessIds);

			if (Func.isNotEmpty(dataArray) && dataArray.size() > 0 && Func.isNotEmpty(flowLevel)) {
				User submitUser = userService.getById(submitUserId);
				Integer requestId = oaService.createProjectFlow(flowLevel, "", user.getAccount(), dept.getDeptName(), submitUser.getAccount(), dataArray);
				if (requestId == null) {
					throw new BusinessException("提交OA失败!");
				}
				//更新oa返回id
				instanceService.updateRequestId(vo.getInstanceIds(), requestId);
			}
		}
		//******** 生成项目编号
		//同一批审批通过的项目的排序码按照建设单位的组织排序进行顺序生成，
		// 例如本次200个项目中兖矿的兴隆庄煤矿的两个项目和鲍店煤矿的一个项目，
		// 兴隆庄的排在前面就是001、002，鲍店的项目是003，按照这个顺序依次生成，
		// 建设单位相同的，按照提报时间升序排序
		if (!Func.isEmpty(vo.getInstanceIds())) {
			Long l = vo.getInstanceIds().get(0);
			InstanceDto instanceDto = instanceService.fetchById(l);
			//如果是项目申报
			if (BusinessTypeEnum.PROJECT_SUBMIT.getCode().equals(instanceDto.getBusinessType())) {
				//项目编号
//				projectBaseService.handleProjectNo(vo.getInstanceIds());
				//补充材料的待办
				projectBaseService.handleSupplyTask(vo.getInstanceIds());
				//生成批次
				projectBaseService.handleCreateBatchNo(vo.getInstanceIds());
				projectBaseService.handleBatchNoAdd(l);

				//项目提报质量的日志
				//根据当前审批结束的项目批次，回写所有该批次项目申
				//报日志的批次和批次下发时间，即填充项目审批日志表中删除的内容:号
				//batch_no（批次）和batch_no_time（批次下发时间）为空
				//的日志信息；
				handleBatchNoLog(vo.getInstanceIds());
			}
		}
		return true;
	}

	/**
	 * 项目提报质量的日志
	 * 1.项目申报结束后会触发以下逻辑：带格式的:缩进:首行缩进:2字符
	 * 根据当前审批结束的项目批次，回写所有该批次项目申
	 * 报日志的批次和批次下发时间，即填充项目审批日志表中删除的内容:号
	 * batch_no（批次）和batch_no_time（批次下发时间）为空
	 * 的日志信息；
	 * 2.每日项目提报质量指标定时任务计算逻辑如下：
	 * 查询项目审批日志表中batch_no_time（批次下发时间）
	 * 为本月的，按照单位统计提报上级单位的次数和被上级单位删除的内容:：
	 * 退回的次数，计算公式（被上级单位退回的次数/提报上级删除的内容:1.（1）项目申报结束后存在不一致的批次号
	 * 单位的次数），最后计算得出每个单位本月的项目提报质量。批次，只统计当前批次。，例如：当前下发第二批项目，
	 * <p>
	 * 四、特殊情况说明删除的内容:（2）
	 * 1.项目提报质量按照batch_no_time（批次下发时间）
	 * 统计，不管本月通过几个批次，都算到当月。
	 * 2.多个批次内都发生了提报，则按批次分段统计项目提报
	 * 质量。例如：部分项目提报到集团后，集团联络人进行了退
	 * 回，建设单位本批不再重新提报这些项目，下个批次或未来
	 * 其他批次再提报，根据每次项目申报结束后生成的批次和下
	 * 三、项目审批日志表结构
	 * 发时间，分段统计项目提报质量，即当前批次统计过的日志，
	 * 下个批次不统计在内。
	 *
	 * @param instanceIds
	 */
	private void handleBatchNoLog(List<Long> instanceIds) {
		if (com.snszyk.core.tool.utils.CollectionUtil.isEmpty(instanceIds)) {
			log.error("handleBatchNoLog instanceIds is empty");
			return;
		}
		Long instanceId = instanceIds.get(0);
		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		if (instanceDto == null) {
			log.error("instanceDto 不存在,instanceId{}", instanceId);
			return;
		}

		Long businessId = instanceDto.getBusinessId();
		ProjectBaseDto projectBaseDto = projectBaseService.fetchByIdIgnoreDelete(businessId);
		if (projectBaseDto == null) {
			log.error("projectBaseDto 不存在,projectId{}", businessId);
			return;
		}
		//只要申报结束的
		if (!Objects.equals(projectBaseDto.getReviewStatus(), ReviewStatusEnum.PASS.getCode())) {
			return;
		}
		String batchNo = projectBaseDto.getBatchNo();
		Integer year = projectBaseDto.getYear();
		if (StringUtil.isBlank(batchNo)) {
			log.error("batchNo 不存在,projectId{}", businessId);
			return;
		}
		List<ProjectBaseDto> projectBaseDtos = projectBaseService.listByBatchNo(year, batchNo);
		List<Long> updateInstanceIds = projectBaseDtos.stream().map(ProjectBaseDto::getInstanceId).collect(Collectors.toList());
		projectBaseService.handleInstanceLogBatchNo(updateInstanceIds, batchNo, LocalDateTime.now());
	}


	/**
	 * 如果下一个节点是集团联络人, 更新专家的状态为未转发
	 */
	private void handleProjectExert(InstanceDto instanceDto) {
		String currStepName = instanceDto.getCurrStepName();
		String nextStepName = instanceDto.getNextStepName();
		Long businessId = instanceDto.getBusinessId();
		if (Objects.equals(currStepName, HEAD_CONTACT) && Objects.equals(nextStepName, HEAD_LEADER)) {
			//如果下一个节点是集团联络人, 更新专家的状态为未转发
			projectBaseService.clearExpertReview(businessId);
			projectExpertService.invalidByProjectId(businessId);
		}
	}

	private Boolean isLeader(String operateUserId) {
		if (operateUserId.equals(leaderRoleId)) {
			return true;
		}
		return false;
	}


	public List<String> getStepList(String businessType) {
		List<String> typeList = new LinkedList<>();
		typeList.add(businessType);
		if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(businessType)) {
			typeList.add(BusinessTypeEnum.TWO_PROJECT_CHECK.getCode());
		}
		List<Long> flowIdList = workflowService.getByBusinessType(typeList);
		//根据id获取节点
		if (CollectionUtil.isNotEmpty(flowIdList)) {
			Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
			List<WorkflowStepDto> steps = workflowStepService.queryByIds(flowIdList, dept.getUnitLevel());
			if (CollectionUtil.isNotEmpty(steps)) {
				//去重
				List<String> collect = steps.stream().map(WorkflowStepDto::getStepName).distinct().collect(Collectors.toList());
				//增加发起人节点
				collect.add(FirstProcessEnum.STEP_NAME.getValue());
				return collect;
			}
		}
		return null;
	}

	/**
	 * 获取当前节点的文审状态详情
	 *
	 * @param instanceId
	 * @return
	 */
	public ProcessDocReviewDto docReviewDetail(Long instanceId) {
		ProcessDocReviewDto result = new ProcessDocReviewDto();
		result.setInstanceId(instanceId);

		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		if (instanceDto == null) {
			return result;
		}
		//当前的节点
		Long currProcessId = instanceDto.getCurrProcessId();
		result.setProcessId(currProcessId);
		if (currProcessId == null) {
			return result;
		}
		InstanceProcessDto instanceProcessDto = instanceProcessService.fetchById(currProcessId);

		if (instanceProcessDto == null) {
			return result;
		}
		//赋值
		result.setDocReview(instanceProcessDto.getDocReview());
		result.setEnableDocReview(instanceProcessDto.isEnableDocReview());
		result.setDocReviewComment(instanceProcessDto.getDocReviewComment());
		// 文审状态
		List<DictBiz> enumList = dictBizService.getList(com.snszyk.zbusiness.dict.enums.DictBizEnum.DOC_REVIEW.getCode());
		Map<String, DictBiz> enumMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(enumList)) {
			enumMap.putAll(enumList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}
		String docReview = instanceProcessDto.getDocReview();
		if (StringUtil.isNotBlank(docReview)) {
			DictBiz dictBiz = enumMap.get(docReview);
			if (dictBiz != null) {
				result.setDocReviewName(dictBiz.getDictValue());
			}
		}

		return result;
	}

	/**
	 * 更新文审状态
	 *
	 * @param vo
	 * @return
	 */
	public Boolean updateDocReview(ProcessDocReviewVo vo) {
		Long processId = vo.getProcessId();
		if (processId == null) {
			return false;
		}
		InstanceProcessDto instanceProcessDto = instanceProcessService.fetchById(processId);
		//判断当前是否可文审
		if (!instanceProcessDto.isEnableDocReview()) {
			throw new ServiceException("当前不可文审!");
		}
		Boolean updateDocReview = instanceProcessService.updateDocReview(processId, vo.getDocReview(), vo.getDocReviewComment());
		if (!updateDocReview) {
			throw new ServiceException("更新文审状态失败!");
		}
		//文审通过的,cancel_flag为false
		if (vo.getDocReview().equals(ProjectDocReviewEnum.PASS.getCode())) {
			//更新文审状态
			projectBaseService.updateaCancelFlag(instanceProcessDto.getInstanceId(), false);
		}
		//处理集团联络人的节点,如果文审通过了.直接到 处理中 contactApprovalStatus
		instanceProcessDto.setDocReview(vo.getDocReview());
		handleContactApprovalStatus(instanceProcessDto);
		return true;

	}

	/**
	 * 处理集团联络人的节点,如果文审通过了.直接到 处理中 contactApprovalStatus
	 *
	 * @param instanceProcessDto
	 */
	private void handleContactApprovalStatus(InstanceProcessDto instanceProcessDto) {
		// instance
		Long instanceId = instanceProcessDto.getInstanceId();
		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		if (instanceDto == null) {
			log.error("流程的实例为空:instance is null,instanceId:{}", instanceId);
		}
		if (Objects.equals(instanceDto.getCurrStepName(), HEAD_CONTACT)) {
			//如果文审通过
			if (ProjectDocReviewEnum.PASS.getCode().equals(instanceProcessDto.getDocReview())) {
				//更新状态为处理中
				instanceProcessService.updateContactApprovalStatus(instanceId, HEAD_CONTACT, ContactApprovalStatusEnum.PROCESSING.getCode());
			}
		}
	}

	public InstanceCheckDataDto checkSubmit(List<InstanceCheckVo> vos) {
		InstanceCheckDataDto dto = new InstanceCheckDataDto();
		List<WrongDto> result = new ArrayList<>();

		// 校验是否存在ID
		if (CollectionUtils.isEmpty(vos)) {
			throw new ServiceException(ExceptionEnum.NOT_PARAMS.getMessage());
		}
		Map<Long, Integer> checkMap = vos.stream().collect(Collectors.toMap(InstanceCheckVo::getId, InstanceCheckVo::getCurrFlowSort));
		List<InstanceDto> list = instanceService.listByIds(checkMap.keySet().stream().collect(Collectors.toList()));
		if (CollectionUtils.isEmpty(list)) {
			throw new ServiceException(ExceptionEnum.NOT_DATA.getMessage());
		}

		for (InstanceDto instanceDto : list) {
			WrongDto wrongDto = new WrongDto();
			wrongDto.setName(instanceDto.getFlowNo());
			wrongDto.setProjectName(instanceDto.getDescription());
			//获取当前审批节点
			InstanceProcessDto curDto = instanceProcessService.queryByInstanceFlow(instanceDto.getId(), instanceDto.getCurrFlowSort());
			//如果审批节点给不相等
			if (!curDto.getFlowSort().equals(checkMap.get(instanceDto.getId()))) {
				dto.setCheckResult(false);
				wrongDto.setResult(false);
				wrongDto.setMessage(FlowExceptionEnum.REPEAT_CHECK.getMessage());
				//查询提报单位
				wrongDto.setName(curDto.getSubmitDeptName());
				result.add(wrongDto);
				continue;
			}
		}
		dto.setDataList(result);
		return dto;
	}

	public IPage<InstanceProjectAcceptPageDto> acceptApprovePage(InstancePageVo vo) {
		SzykUser user = AuthUtil.getUser();
		vo.setBusinessType(BusinessTypeEnum.ONE_PROJECT_CHECK.getCode());
		IPage<InstanceProjectAcceptPageDto> pages = null;
		//获取当前业务范围
		String deptId = AuthUtil.getDeptId();
		Dept dept = SysCache.getDept(Long.valueOf(deptId));
		//文审状态
		List<DictBiz> docReviewEnumList = dictBizService.getList(com.snszyk.zbusiness.dict.enums.DictBizEnum.DOC_REVIEW.getCode());
		Map<String, DictBiz> docReviewEnumMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(docReviewEnumList)) {
			docReviewEnumMap.putAll(docReviewEnumList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity())));
		}
		if (ObjectUtil.isNotEmpty(vo.getApplyStartTime())) {
			vo.setApplyStartTime(DateUtil.beginOfDay(vo.getApplyStartTime()));
		}
		if (ObjectUtil.isNotEmpty(vo.getApplyEndTime())) {
			vo.setApplyEndTime(DateUtil.endOfDay(vo.getApplyEndTime()));
		}
		if (vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) {
			//如果是待我审批的
			pages = instanceService.acceptApprovePage(vo, user.getUserId(), Long.valueOf(deptId));
		}
		if (vo.getApproveType().equals(ApproveTypeEnum.APPROVED.getCode())) {
			//如果是我已审批的
			pages = instanceProcessService.acceptApprovePage(vo, user.getUserId(), Long.valueOf(deptId));
		}

		if (CollectionUtil.isNotEmpty(pages.getRecords()) && pages.getRecords().size() > 0) {
			pages.getRecords().forEach(record -> {
				//如果是已发起或已办理，且状态还属于审核中或已退回的，状态拼接上节点名称
				if ((!vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) && (record.getCurStatus().equals(InstanceStatusEnum.RETURN_FLOW.getCode()) || record.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode())) && StringUtil.isNotBlank(record.getCurStepName())) {
					record.setCurStatusName(DictBizCache.getValue(DictBizEnum.REVIEW_STATUS, record.getCurStatus()) + "(" + record.getCurStepName() + ")");
				} else {
					record.setCurStatusName(DictBizCache.getValue(DictBizEnum.REVIEW_STATUS, record.getCurStatus()));
				}

				if (ObjectUtil.isNotEmpty(record.getApproveStatus())) {
					record.setApproveStatusName(ApproveStatusEnum.getByCode(record.getApproveStatus()).getMessage());
				}
				if (vo.getApproveType().equals(ApproveTypeEnum.WAIT_APPROVE.getCode())) {
					//设置审查意见标识
					if (ObjectUtil.isNotEmpty(record.getBeforeEvent()) && record.getBeforeEvent().equals(1) && record.getCurStatus().equals(InstanceStatusEnum.CHECKING.getCode())) {
						record.setCheckFlag(dept.getUnitLevel());
					}
				}
				//设置文审状态名
				String docReview = record.getDocReview();
				if (StringUtil.isNotBlank(docReview)) {
					DictBiz dictBiz = docReviewEnumMap.get(docReview);
					if (dictBiz != null) {
						record.setDocReviewName(dictBiz.getDictValue());
					}
				}
				//获取单位部门名称
				String unitDeptName = getUnitDept(record.getApplyUserDept());
				record.setApplyUserDeptName(unitDeptName);
				//获取最新组织信息
				if (Func.isNotEmpty(record.getSubmitDeptId())) {
					record.setSubmitDeptName(SysCache.getDeptName(record.getSubmitDeptId()));
				}
			});
		}

		return pages;
	}

	private String getUnitDept(Long id) {

		Dept dept = SysCache.getDept(id);
		if (dept == null) {
			return null;
		}
		String deptName = dept.getDeptName();
		if (!dept.getDeptCategory().equals(DeptCategoryEnum.UNIT.getCode())) {
			Dept parentDept = SysCache.getDept(dept.getUnitId());
			deptName = parentDept.getDeptName() + "/" + deptName;
		}
		return deptName;
		/*do {
			if (dept.getDeptCategory().equals(DeptCategoryEnum.UNIT.getCode())) {
				return deptName;
			}
			if (Objects.equals(0l, dept.getParentId())) {
				return deptName;
			}
			dept = SysCache.getDept(dept.getParentId());
			deptName = dept.getDeptName() + "/" + deptName;
		} while (true);*/
	}

	@Transactional
	public Boolean flashContractPerson() {
		//获取当前用户信息和单位信息

		Dept dept = SysCache.getDept(Long.valueOf(AuthUtil.getDeptId()));
		dept = SysCache.getDept(dept.getUnitId());
		//查询当前单位的项目联络人信息
		List<UserDept> userDepts = userDeptService.queryContactUser(dept.getId(), contractPerson);
		if (CollectionUtil.isEmpty(userDepts)) {
			throw new ServiceException(FlowExceptionEnum.NOT_CONTRACT_PERSON.getMessage());
		}
		//获取到所有联络人信息
		List<Long> userIdList = userDepts.stream().map(UserDept::getUserId).sorted().collect(Collectors.toList());
		//获取相应接收人姓名
		List<String> userNameList = userIdList.stream().map(id -> {
			return UserCache.getUser(id).getRealName();
		}).collect(Collectors.toList());
		//获取所有项目申报流程中，到当前单位的项目联络人节点的信息
		List<InstanceDto> instanceDtos = instanceService.queryProjectCurrentProcess(dept.getId(),
			BusinessTypeEnum.PROJECT_SUBMIT.getCode(), contractPerson);
		if (CollectionUtil.isEmpty(instanceDtos)) {
			//如果为空直接返回
			return true;
		}
		//如果不为空，需要逐条判断当前审批人的信息
		instanceDtos.forEach(instance -> {
			String currUserId = instance.getCurrUserId();
			//验证当前审批人和当前项目联络人
			List<Long> currentUserIds = Arrays.asList(currUserId.split(",")).stream().map(Long::valueOf).sorted().collect(Collectors.toList());
			//判断下项目联络人信息是否相同
			if (!currentUserIds.equals(userIdList)) {
				//更新项目联络人的待办和消息
				szykTaskService.updateReceiver(ToDoBusinessTypeEnum.PROJECT_REPORT, instance.getBusinessId(), instance.getCurrUserId(), instance.getCurrDeptId(), userIdList, userNameList);
				//更新流程中当前节点信息
				instanceService.updateInstanceUser(instance.getId(), userIdList);
				//更新流程节点的的信息
				instanceProcessService.updateStepUser(instance.getCurrProcessId(), userIdList, userNameList);
			}
		});
		return true;
	}

	/**
	 * 获取流程所有的名称
	 *
	 * @param businessType 业务类型
	 * @return 所有节点名称
	 */
	public List<String> getAllStepNameList(String businessType) {
		List<String> typeList = new LinkedList<>();
		typeList.add(businessType);
		if (BusinessTypeEnum.ONE_PROJECT_CHECK.getCode().equals(businessType)) {
			typeList.add(BusinessTypeEnum.TWO_PROJECT_CHECK.getCode());
		}
		List<Long> flowIdList = workflowService.getByBusinessType(typeList);
		//根据id获取节点
		if (CollectionUtil.isNotEmpty(flowIdList)) {
			List<WorkflowStepDto> steps = workflowStepService.queryByIds(flowIdList);
			if (CollectionUtil.isNotEmpty(steps)) {
				//去重
				List<String> collect = steps.stream().map(WorkflowStepDto::getStepName).distinct().collect(Collectors.toList());
				//增加发起人节点
				collect.add(FirstProcessEnum.STEP_NAME.getValue());
				return collect;
			}
		}
		return null;
	}

	/**
	 * 锁定实例
	 *
	 * @param instanceIds 需要锁定的实例ID列表
	 * @param userId      用户ID
	 * @param lockStatus  锁定状态
	 * @return 返回是否锁定成功，成功为true，失败为false
	 */
	public Boolean lockInstance(List<Long> instanceIds, Long userId, Integer lockStatus) {
		return instanceService.lockInstance(instanceIds, userId, lockStatus, LocalDateTime.now());
	}

	public Boolean verifyInstance(List<Long> instanceIds) {
		List<InstanceDto> list = instanceService.listByIds(instanceIds);
		if (CollectionUtil.isNotEmpty(list)) {
			list.forEach(dto -> {
				if (dto.getLockStatus().equals(LockStatusEnum.CLOSE.getCode())) {
					throw new ServiceException("项目正在办理中，请勿重复办理");
				}
			});
		}

		return false;
	}

	/**
	 * 获取审核流图
	 *
	 * @param instanceId
	 * @return
	 */
	public InstanceGraphDto getInstanceGraph(Long instanceId) {
		InstanceGraphDto result = new InstanceGraphDto();
		List<InstanceStepDto> instanceStepDtoList = getInstance(instanceId);
		Map<Integer, InstanceStepDto> sortMap = instanceStepDtoList.stream().collect(Collectors.toMap(e -> e.getFlowSort(), e -> e, (e1, e2) -> e2));

		//link list
		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		if (instanceDto == null) {
			return null;
		}

		List<InstanceGraphDto.LinkNodeDto> linkList = result.getLinkList();
		List<InstanceStepGraphDto> nodeList = result.getStepList();
		List<InstanceProcessDto> processList = instanceProcessService.queryByInstanceId(instanceDto.getId());
		// 1.初始化节点
		for (InstanceProcessDto curNode : processList) {
			InstanceStepGraphDto nodeDto = new InstanceStepGraphDto();
			nodeDto.setFlowSort(curNode.getFlowSort());
			nodeDto.setStepName(curNode.getStepName());
			nodeDto.setId(curNode.getFlowSort().toString());
			nodeList.add(nodeDto);
		}
		//添加终点 NodePosition
		InstanceStepGraphDto lastNode = new InstanceStepGraphDto();
		lastNode.setId("999");
		lastNode.setNodePosition("2");
		lastNode.setNodeStatus("0");
		lastNode.setStepName("结束");
		lastNode.setFlowSort(999);
		nodeList.add(lastNode);
		//2. 初始化link
		for (int i = 0; i < processList.size() - 1; i++) {
			InstanceProcessDto curNode = processList.get(i);
			InstanceProcessDto nextNode = processList.get(i + 1);
			InstanceGraphDto.LinkNodeDto linkNodeDto = new InstanceGraphDto.LinkNodeDto();
			linkNodeDto.setSource(String.valueOf(curNode.getFlowSort()));
			linkNodeDto.setTarget(String.valueOf(nextNode.getFlowSort()));
			linkList.add(linkNodeDto);
		}
		//最后的link
		InstanceGraphDto.LinkNodeDto lastLink = new InstanceGraphDto.LinkNodeDto();
		lastLink.setSource(nodeList.get(nodeList.size() - 2).getId());
		lastLink.setTarget("999");
		linkList.add(lastLink);
		//如果最后一个节点是退回的.补充一个当前节点
		if (Objects.equals(instanceStepDtoList.get(instanceStepDtoList.size() - 1).getOperateType(), ApproveStatusEnum.RETURN_FLOW.getCode())) {
			InstanceStepGraphDto backToNode = new InstanceStepGraphDto();
			backToNode.setId(instanceDto.getCurrFlowSort().toString());
			backToNode.setNodePosition("1");
			backToNode.setFlowSort(instanceDto.getCurrFlowSort());
			backToNode.setStepName((instanceDto.getCurrStepName()));
			InstanceStepDto instanceStepDto = sortMap.get(instanceDto.getCurrFlowSort());
			if (instanceStepDto != null) {
				String operatorName = instanceStepDto.getOperatorName();
				backToNode.setOperatorName(operatorName);
			}
			instanceStepDtoList.add(backToNode);
		}


		//1.标记开始节点和中间的节点NodePosition
		//2.id 节点的唯一标识
		nodeList.forEach(e -> e.setId(String.valueOf(e.getFlowSort())));
		//遍历日志节点
		List<InstanceStepGraphDto> logNodeList = com.snszyk.core.tool.utils.BeanUtil.copy(instanceStepDtoList, InstanceStepGraphDto.class);
		for (int i = 0; i < logNodeList.size(); i++) {
			InstanceStepGraphDto curNode = logNodeList.get(i);
			nodeList.add(curNode);
			curNode.setId(curNode.getFlowSort().toString());
			if ("发起人".equals(curNode.getStepName())) {
				curNode.setNodePosition("0");
			}
			if (i + 1 == logNodeList.size()) {
				if (curNode.getOperateType() != null) {
					//当前的节点
					curNode.setNodeStatus(InstanceStepGraphDto.NodeStatusEnum.PASSED.getCode());
				}
				continue;
			}
			InstanceStepGraphDto nextNode = logNodeList.get(i + 1);
			InstanceGraphDto.LinkNodeDto linkDto = new InstanceGraphDto.LinkNodeDto();
			linkDto.setSource(curNode.getFlowSort().toString());
			linkDto.setTarget(nextNode.getFlowSort().toString());
			linkList.add(linkDto);
			if (curNode.getOperateType() != null && nextNode.getOperateType() == null) {
				//当前的节点
				curNode.setNodeStatus(InstanceStepGraphDto.NodeStatusEnum.PASSED.getCode());
				linkDto.setLinkStatus(InstanceGraphDto.LinkStatusEnum.PASSED.getCode());
				nextNode.setIsCurNode(true);
				nextNode.setNodeStatus(InstanceStepGraphDto.NodeStatusEnum.CURRENT_NODE.getCode());
			} else {
				if (curNode.getOperateType() != null) {
					curNode.setNodeStatus(InstanceStepGraphDto.NodeStatusEnum.PASSED.getCode());
					linkDto.setLinkStatus(InstanceGraphDto.LinkStatusEnum.PASSED.getCode());
				}
//				if (nextNode.getOperateType() != null) {
//					nextNode.setNodeStatus(InstanceStepGraphDto.NodeStatusEnum.PASSED.getCode());
//					linkDto.setLinkStatus(InstanceGraphDto.LinkStatusEnum.PASSED.getCode());
//				}
			}
		}
		//节点和link 去重,只保留最新的
		Map<String, InstanceStepGraphDto> stepMap = nodeList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (e1, e2) -> e2));
		ArrayList<InstanceStepGraphDto> instanceStepGraphDtos = new ArrayList<>(stepMap.values());
		instanceStepGraphDtos.sort(Comparator.comparing(InstanceStepGraphDto::getId));
		result.setStepList(instanceStepGraphDtos);
		Map<String, InstanceGraphDto.LinkNodeDto> linkMap = linkList.stream().collect(Collectors.toMap(e -> e.getSource() + e.getTarget(), e -> e, (e1, e2) -> e2));
		result.setLinkList(new ArrayList<>(linkMap.values()));
		//只有状态为申报结束的才能到结束节点
		//找到对应的projectbase
		ProjectBaseDto projectBaseDto = projectBaseService.fetchById(instanceDto.getBusinessId());
		Optional.ofNullable(projectBaseDto).ifPresent(dto -> {
			if (Objects.equals(dto.getReviewStatus(), ReviewStatusEnum.PASS.getCode())) {
				result.getStepList().forEach(e -> {
					if (e.getId().equals("999")) {
						e.setNodeStatus("1");
					}
				});
				result.getLinkList().forEach(e -> {
					if (e.getTarget().equals("999")) {
						e.setLinkStatus("2");
					}
				});
			}
		});
		return result;
	}

	public Boolean dealLogData() {
		List<InstanceDto> instanceDtoList = instanceService.list(new InstanceVo());
		instanceDtoList.forEach(dto -> {
			List<InstanceLogDto> logs = instanceLogService.queryByInstanceId(dto.getId());
			for (int i = 0; i < logs.size(); i++) {
				logs.get(i).setCrossLevel(CommonConstant.ZERO);
				if (i + 1 < logs.size()) {
					InstanceLogDto nextProcessDto = logs.get(i + 1);
					logs.get(i).setTargetDeptId(nextProcessDto.getOperatorDeptId());
					//判断当前的单位层级和下一个单位层级是否是同级
					Dept dept = SysCache.getDept(logs.get(i).getOperatorDeptId());
					Dept nextDept = SysCache.getDept(nextProcessDto.getOperatorDeptId());
					if (!dept.getUnitLevel().equals(nextDept.getUnitLevel())) {
						//如果不同级,则流程跨级
						logs.get(i).setCrossLevel(CommonConstant.ONE);
					}
				}
				instanceLogService.save(BeanUtil.copyProperties(logs.get(i), InstanceLogVo.class));//
			}
		});
		return true;
	}

	public Boolean updateLogDocReview() {
		//有文审记录的process
		List<InstanceProcessDto> instanceProcessDtos = instanceProcessService.listDocProcess();
		//更新instanceLog的文审
		for (InstanceProcessDto instanceProcessDto : instanceProcessDtos) {
			if (instanceProcessDto.getDocReview() != null) {
				List<InstanceLogDto> processDtos = instanceLogService.queryByProcessId(instanceProcessDto.getId());
				processDtos.forEach(dto -> {
					dto.setDocReview(instanceProcessDto.getDocReview());
					dto.setDocReviewComment(instanceProcessDto.getDocReviewComment());
					instanceLogService.save(BeanUtil.copyProperties(dto, InstanceLogVo.class));
				});

			}
		}
		return true;
	}

	/**
	 * 下载最新的附件内容
	 *
	 * @param vo
	 * @param request
	 * @param response
	 */
	public void downloadFile(InstancePageVo vo, HttpServletRequest request, HttpServletResponse response) throws IOException {
		vo.setSize(-1);
		vo.setCurrent(-1);
		IPage<InstancePageDto> pages = approvePage(vo);
		List<InstancePageDto> records = pages.getRecords();
		if (Func.isEmpty(records)) {
			throw new ServiceException("没有找到对应的流程");
		}
		ExecutorService executor = ExecutorBuilder.create()
			.setCorePoolSize(20)
			.setMaxPoolSize(50)
			.setKeepAliveTime(1000)
			.setWorkQueue(new LinkedBlockingQueue<>(10000))
			.build();
		//项目的ids
		List<Long> projectIds = records.stream().map(InstancePageDto::getBusinessId).collect(Collectors.toList());
		//查询项目关联的附件
		List<ProjectFileDto> projectFileDtos = projectFilesService.listLatestByProjects(projectIds, ProjectFileEnum.PROJECT_FILE.getCode());
		//根据项目的id分组
		Map<Long, List<ProjectFileDto>> projectFileMap = projectFileDtos.stream()
			.filter(e -> e.getBusinessId() != null)
			.collect(Collectors.groupingBy(ProjectFileDto::getBusinessId));

		//下载附件
		String tempPath = fileConfig.getTempPath();
		String formatDate = DateUtil.format(new Date(), "yyyy-MM-dd");
		//压缩文件
		File zipFile = new File(tempPath + formatDate + File.separator + IdUtil.fastSimpleUUID() + ".zip");
		String uuid = IdUtil.fastSimpleUUID();
		for (InstancePageDto record : records) {
			Long businessId = record.getBusinessId();
			List<ProjectFileDto> oneProjectFiles = projectFileMap.get(businessId);
			if (Func.isEmpty(oneProjectFiles)) {
				continue;
			}
			for (ProjectFileDto taskFileDto : oneProjectFiles) {
				executor.submit(() -> handleDownloadOneFile(taskFileDto, tempPath, formatDate, uuid));
			}
		}
		executor.shutdown();
		try {
			executor.awaitTermination(10, TimeUnit.MINUTES);
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
		// 压缩多个文件
		File srcFile = new File(tempPath + formatDate + File.separator + uuid);
		if (!srcFile.exists()) {
			throw new ServiceException("暂无可下载内容!");
		}
		log.info("开始文件压缩");
		ZipUtil.zip(response.getOutputStream(), Charset.defaultCharset(), false, null, srcFile);
		log.info("文件压缩完成");
		//下在后会将压缩文件删除
//		FileUtils.downloadZip(zipFile, response);
		log.info("文件写回response");
		log.info("删除临时文件");
		cn.hutool.core.io.FileUtil.del(srcFile);
	}

	private static void handleDownloadOneFile(ProjectFileDto taskFileDto, String tempPath, String formatDate, String uuid) {
		String filePath = tempPath + formatDate + File.separator + uuid + File.separator
			+ taskFileDto.getCompanyCode() + "_"
			+ taskFileDto.getCompanyName()
			+ File.separator
			+ taskFileDto.getOrgCode() + "_"
			+ taskFileDto.getOrgName() + "_"
			+ taskFileDto.getProjectName() + "_"
			+ taskFileDto.getOriginalName();
		//v1.5域名改为ip
		String link = taskFileDto.getDomain() + "/" + taskFileDto.getName();

		log.info("下载附件:{}", link);
		HttpUtil.downloadFile(link, filePath);
		log.info("附件下载完成:{}", link);
	}

	/**
	 * 更新集团联络人的办理状态
	 *
	 * @param vo
	 * @return
	 */
	public Boolean updateContactApprovalStatus(ContactApprovalStatusVo vo) {
		//当前是否在集团联络人的节点
		if (Func.isEmpty(vo.getProcessId())) {
			return false;
		}
		InstanceProcessDto instanceProcessDto = instanceProcessService.fetchById(vo.getProcessId());
		if (instanceProcessDto == null) {
			return false;
		}
		Long instanceId = instanceProcessDto.getInstanceId();
		InstanceDto instanceDto = instanceService.fetchById(instanceId);
		if (instanceDto == null) {
			return false;
		}
		//如果是从办理中到待办理,更新项目编号为null
		if (Objects.equals(vo.getContactApprovalStatus(), ContactApprovalStatusEnum.TODO.getCode())) {
			List<ProjectBaseDto> projectBaseDtos = projectBaseService.listByInstanceIds(Collections.singletonList(instanceId));
			if (Func.isNotEmpty(projectBaseDtos)) {
				ProjectBaseDto projectBaseDto = projectBaseDtos.get(0);
				if (Func.isNotEmpty(projectBaseDto.getProjectNo())) {
					projectBaseService.clearProjectNo(projectBaseDto.getId());
					plBaseService.clearProjectNo(projectBaseDto.getId());
				}
			}
		}
		//当前的节点是否为集团联络人的节点
		if (Objects.equals(instanceDto.getCurrStepName(), HEAD_CONTACT)) {
			return instanceProcessService.updateContactApprovalStatus(instanceId, HEAD_CONTACT, vo.getContactApprovalStatus());
		}

		return false;
	}
}

