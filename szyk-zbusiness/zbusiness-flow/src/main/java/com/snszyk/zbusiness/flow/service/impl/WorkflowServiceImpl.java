/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.zbusiness.flow.entity.Workflow;
import com.snszyk.zbusiness.flow.dto.WorkflowDto;
import com.snszyk.zbusiness.flow.vo.WorkflowVo;
import com.snszyk.zbusiness.flow.mapper.WorkflowMapper;
import com.snszyk.zbusiness.flow.service.IWorkflowService;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作流 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@AllArgsConstructor
@Service
public class WorkflowServiceImpl extends BaseCrudServiceImpl<WorkflowMapper, Workflow, WorkflowDto, WorkflowVo> implements IWorkflowService {


	@Override
	public List<WorkflowDto> queryByBusinessType(String businessType) {
		LambdaQueryWrapper<Workflow> wrapper = Wrappers.lambdaQuery(Workflow.class).eq(Workflow::getBusinessType, businessType);
		List<Workflow> list = this.list(wrapper);
		return list.stream().map(entity -> BeanUtil.copyProperties(entity,WorkflowDto.class)).collect(Collectors.toList());
	}

	@Override
	public WorkflowDto saveOrUpdate(WorkflowVo v) {
		Workflow entity = null;
		if(ObjectUtil.isNotEmpty(v.getId())){
			//更新
			entity = this.getById(v.getId());
			BeanUtil.copyProperties(v, entity, CopyOptions.create().setIgnoreNullValue(false));
		}
		if(ObjectUtil.isEmpty(v.getId())){
			//新增
			entity = BeanUtil.copyProperties(v,Workflow.class);
		}
		this.saveOrUpdate(entity);
		return BeanUtil.copyProperties(entity,WorkflowDto.class);
	}

	@Override
	public List<Long> getByBusinessType(List<String> typeList) {
		LambdaQueryWrapper<Workflow> wrapper = Wrappers.lambdaQuery(Workflow.class).in(Workflow::getBusinessType, typeList);
		List<Workflow> list = this.list(wrapper);
		if(CollectionUtil.isNotEmpty(list)){
			return list.stream().map(Workflow::getId).collect(Collectors.toList());
		}
		return null;
	}
}
