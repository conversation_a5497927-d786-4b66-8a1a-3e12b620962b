/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.flow.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程文档表实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BpmInstanceFile extends BaseCrudEntity {

	/**
	* 附件id
	*/
	private Long attachId;
	/**
	* 业务id
	*/
	private Long businessId;
	/**
	* 业务类型
	*/
	private String businessType;


}
