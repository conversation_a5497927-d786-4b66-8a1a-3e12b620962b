<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.flow.mapper.InstanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="instanceResultMap" type="com.snszyk.zbusiness.flow.entity.Instance">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="flow_id" property="flowId"/>
        <result column="flow_no" property="flowNo"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="curr_process_id" property="currProcessId"/>
        <result column="curr_user_id" property="currUserId"/>
        <result column="curr_flow_sort" property="currFlowSort"/>
        <result column="cur_status" property="curStatus"/>
        <result column="flow_status" property="flowStatus"/>
        <result column="apply_time" property="applyTime"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_user_dept" property="applyUserDept"/>
    </resultMap>


    <select id="selectInstancePage" resultMap="instanceResultMap">
        select *
        from bpm_instance
        where is_deleted = 0
    </select>

    <select id="instancePage" resultType="com.snszyk.zbusiness.flow.dto.InstancePageDto">
        SELECT
        bi.id as id,
        bi.flow_id as flowId,
        bi.flow_name AS flowName,
        bi.business_id AS businessId,
        bi.business_type AS businessType,
        bp.project_name as description,
        bi.flow_no AS flowNo,
        bi.curr_flow_sort AS currFlowSort,
        bi.cur_status AS curStatus,
        bi.init_level as initLevel,
        bi.apply_time AS applyTime,
        bi.apply_user_name AS applyUserName,
        bi.apply_user_dept_name AS applyUserDeptName,
        bi.curr_step_name as curStepName,
       case when bi.next_step_name='--' then '/' else bi.next_step_name end  as nextStepName,
        bp.year as year,
        bp.sec_approve_remark as secApproveRemark,
        bp.corp_approve_result as corpApproveResult,
        bp.corp_approve_remark as corpApproveRemark,
        bip.before_event as beforeEvent,
        bip.submit_user_id as submitUserId,
        bip.submit_user_name as submitUserName,
        bip.submit_dept_id as submitDeptId,
        bip.submit_dept_name as submitDeptName,
        bip.submit_time as submitTime,
        bip.back_logic as backLogic,
        bi.cur_receive as curReceive,
        bip.submit_time as submitTime,
        bip.doc_review,
        bp.expert_review,
        bp.batch_name,
        bp.construction_unit_id,
        bp. specialty_classification ,
        bp.project_classification ,
        bp.distribution_channel,
        cydept.dept_name as company_name,
        bp.company_id,
        bp.project_no,
        IF(cdept.dept_category in(1,3), cdept.dept_name, concat(udept.dept_name,'/',cdept.dept_name)) as
        constructionUnitName,
        case when bip.contact_approval_status = 0 then '待办理'
        when bip.contact_approval_status = 1 then '办理中'
        else '' end as contactApprovalStatusName,
        bip.contact_approval_status,
        bip.id as processId
        FROM
        bpm_instance bi
        left join bpm_instance_process bip on bi.id = bip.instance_id and bi.curr_flow_sort = bip.flow_sort and
        bip.is_deleted = 0
        LEFT JOIN project_base bp ON bi.id = bp.instance_id and bp.is_deleted = 0
        left join szyk_dept cdept on bp.construction_unit_id = cdept.id
        left join szyk_dept cydept on bp.company_id = cydept.id
        left join szyk_dept udept on cdept.unit_id = udept.id
        WHERE
        bi.is_deleted = 0 AND bi.flow_status = 0 and bi.curr_flow_sort <![CDATA[!=]]> 0
        and bi.cur_operator_channel = 0
        <if test="page.batchName !=null and page.batchName !=''">
            and bp.batch_name like concat('%',#{page.batchName},'%')
        </if>
        <if test="page.flowName != null and page.flowName !=''">
            AND bi.flow_name LIKE concat('%',#{page.flowName},'%')
        </if>
        <if test="page.applyUserName != null and page.applyUserName !=''">
            AND bip.submit_user_name LIKE concat('%',#{page.applyUserName},'%')
        </if>
        <if test="page.businessType != null and page.businessType !=''">
            <choose>
                <when test="page.businessType == 3">
                    AND (bi.business_type = '3' or bi.business_type = '4')
                </when>
                <otherwise>
                    AND bi.business_type = #{page.businessType}
                </otherwise>
            </choose>
        </if>

        <if test="page.applyStartTime != null ">
            AND bip.submit_time <![CDATA[>=]]> #{page.applyStartTime}
        </if>
        <if test="page.applyEndTime != null ">
            AND bip.submit_time <![CDATA[<=]]> #{page.applyEndTime}
        </if>
        <if test="userId != null ">
            AND bi.curr_user_id like concat('%',#{userId},'%')
        </if>
        <if test="page.curStatus != null ">
            AND bi.cur_status = #{page.curStatus}
        </if>
        <if test="deptId != null ">
            AND bi.curr_dept_id = #{deptId}
        </if>
        <if test="page.projectName != null and page.projectName !=''">
            AND bi.description LIKE concat('%',#{page.projectName},'%')
        </if>
        <if test="page.deptId != null ">
            AND bip.submit_dept_id = #{page.deptId}
        </if>
        <if test="page.nextStepName != null and page.nextStepName !=''">
            AND bi.next_step_name = #{page.nextStepName}
        </if>
        <if test="page.docReview != null and page.docReview !=''">
            AND bip.doc_review = #{page.docReview}
        </if>
        <if test="page.curStepName != null and page.curStepName !=''">
            AND bi.curr_step_name=#{page.curStepName}
        </if>
        <if test="page.contactApprovalStatus != null and page.contactApprovalStatus !=''">
            AND bip.contact_approval_status=#{page.contactApprovalStatus}
        </if>
        <if test="page.year != null ">
            AND bp.year=#{page.year}
        </if>
        <if test="page.ids != null and page.ids.size() > 0">
            AND bi.id in
            <foreach collection="page.ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY bp.year desc ,cydept.sort asc,cdept.sort asc ,bp.submit_time asc ,bp.id asc

    </select>

    <select id="acceptApprovePage" resultType="com.snszyk.zbusiness.flow.dto.InstanceProjectAcceptPageDto">
        SELECT
        bi.id as id,
        bi.flow_id as flowId,
        bi.flow_name AS flowName,
        bi.business_id AS businessId,
        bi.business_type AS businessType,
        bi.description as description,
        bi.flow_no AS flowNo,
        bi.curr_flow_sort AS currFlowSort,
        bi.cur_status AS curStatus,
        bi.init_level as initLevel,
        bi.apply_time AS applyTime,
        bi.apply_user_name AS applyUserName,
        bi.apply_user_dept_name AS applyUserDeptName,
        bi.apply_user_dept AS applyUserDept,
        bi.curr_step_name as curStepName,
        bi.next_step_name as nextStepName,
        bp.year as year,
        bip.before_event as beforeEvent,
        bip.submit_user_id as submitUserId,
        bip.submit_user_name as submitUserName,
        bip.submit_dept_id as submitDeptId,
        bip.submit_dept_name as submitDeptName,
        bip.submit_time as submitTime,
        bip.back_logic as backLogic,
        bi.cur_receive as curReceive,
        bip.submit_time as submitTime,
        bip.doc_review
        FROM
        bpm_instance bi
        left join bpm_instance_process bip on bi.id = bip.instance_id and bi.curr_flow_sort = bip.flow_sort and
        bip.is_deleted = 0
        LEFT JOIN project_progress pp ON bi.id = pp.instance_id and pp.is_deleted = 0
        left join pl_base bp on pp.project_id = bp.id and bp.is_deleted = 0
        left join szyk_dept cdept on pp.construction_unit_id = cdept.id
        left join szyk_dept udept on cdept.unit_id = udept.id
        WHERE
        bi.is_deleted = 0 AND bi.flow_status = 0 and bi.curr_flow_sort <![CDATA[!=]]> 0
        and bi.cur_operator_channel = 0
        <if test="page.flowName != null and page.flowName !=''">
            AND bi.flow_name LIKE concat('%',#{page.flowName},'%')
        </if>
        <if test="page.applyUserName != null and page.applyUserName !=''">
            AND bip.submit_user_name LIKE concat('%',#{page.applyUserName},'%')
        </if>
        <if test="page.businessType != null and page.businessType !=''">
            <choose>
                <when test="page.businessType == 3">
                    AND (bi.business_type = '3' or bi.business_type = '4')
                </when>
                <otherwise>
                    AND bi.business_type = #{page.businessType}
                </otherwise>
            </choose>
        </if>

        <if test="page.applyStartTime != null ">
            AND bi.apply_time <![CDATA[>=]]> #{page.applyStartTime}
        </if>
        <if test="page.applyEndTime != null ">
            AND bi.apply_time <![CDATA[<=]]> #{page.applyEndTime}
        </if>
        <if test="userId != null ">
            AND bi.curr_user_id like concat('%',#{userId},'%')
        </if>
        <if test="page.curStatus != null ">
            AND bi.cur_status = #{page.curStatus}
        </if>
        <if test="deptId != null ">
            AND bi.curr_dept_id = #{deptId}
        </if>
        <if test="page.projectName != null and page.projectName !=''">
            AND bi.description LIKE concat('%',#{page.projectName},'%')
        </if>
        <if test="page.deptId != null ">
            AND bip.submit_dept_id = #{page.deptId}
        </if>
        <if test="page.nextStepName != null and page.nextStepName !=''">
            AND bi.next_step_name = #{page.nextStepName}
        </if>
        <if test="page.docReview != null and page.docReview !=''">
            AND bip.doc_review = #{page.docReview}
        </if>
        ORDER BY bip.doc_review asc ,udept.sort asc, cdept.sort asc , cdept.unit_level asc, cdept.id,bip.submit_time DESC,bi.id desc
    </select>

    <select id="queryInstanceBySort" resultType="java.lang.Long">
        SELECT
        bi.business_id
        FROM
        bpm_instance bi
        left join bpm_instance_process bip on bi.id = bip.instance_id and bi.curr_flow_sort = bip.flow_sort and
        bip.is_deleted = 0
        LEFT JOIN project_base bp ON bi.id = bp.instance_id and bp.is_deleted = 0
        left join szyk_dept cdept on bp.construction_unit_id = cdept.id
        left join szyk_dept udept on cdept.unit_id = udept.id
        WHERE
        bi.id in
        <foreach item="id" collection="ids" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY bip.doc_review asc , udept.sort asc,cdept.sort asc ,cdept.unit_level asc, cdept.id,bip.submit_time DESC,bi.id desc
    </select>

    <select id="queryProjectCurrentProcess" resultType="com.snszyk.zbusiness.flow.entity.Instance">
        select bi.id              as id,
               bi.flow_id         as flowId,
               bi.flow_name       AS flowName,
               bi.business_id     AS businessId,
               bi.business_type   AS businessType,
               bi.description     as description,
               bi.flow_no         AS flowNo,
               bi.curr_process_id AS currProcessId,
               bi.curr_user_id    AS currUserId,
               bi.curr_dept_id    AS currDeptId
        FROM bpm_instance bi
                 left join bpm_instance_process bip on bi.curr_process_id = bip.id
        where bi.is_deleted = 0
          AND bi.flow_status = 0
          and bi.cur_operator_channel = 0
          and bi.curr_dept_id = #{deptId}
          and bip.operator_id = #{contractPerson}
          and bi.business_type = #{businessType}
    </select>


</mapper>
