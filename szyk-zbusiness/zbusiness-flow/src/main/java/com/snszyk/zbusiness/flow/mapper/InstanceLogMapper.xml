<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.flow.mapper.InstanceLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="instanceLogResultMap" type="com.snszyk.zbusiness.flow.entity.InstanceLog">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="bpm_instance_id" property="bpmInstanceId"/>
        <result column="instance_process_id" property="instanceProcessId"/>
        <result column="flow_sort" property="flowSort"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_dept_id" property="operatorDeptId"/>
        <result column="operator_dept_name" property="operatorDeptName"/>
        <result column="operate_time" property="operateTime"/>
        <result column="operate_type" property="operateType"/>
        <result column="operate_remark" property="operateRemark"/>
    </resultMap>


    <select id="selectInstanceLogPage" resultMap="instanceLogResultMap">
        select *
        from bpm_instance_log
        where is_deleted = 0
    </select>

    <select id="logPage" resultType="com.snszyk.zbusiness.flow.dto.InstancePageDto">
        SELECT
        blg.operate_time AS operateTime,
        bi.flow_name AS flowName,
        bi.business_id AS businessId,
        bi.description as description,
        bi.flow_no AS flowNo,
        bi.curr_flow_sort AS currFlowSort,
        bi.cur_status AS curStatus,
        blg.operate_type AS approveStatus,
        bi.apply_time AS applyTime,
        bi.apply_user_name AS applyUserName,
        bi.apply_user_dept_name AS applyUserDeptName
        FROM
        (
            SELECT
            t1.*
            FROM
            bpm_instance_log t1
            WHERE
            t1.id IN (
                SELECT
                    substring_index( GROUP_CONCAT( blg.id ORDER BY blg.operate_time DESC ), ',', 1 )
                FROM
                    bpm_instance_log blg
                WHERE
                    blg.is_deleted = 0
                    AND blg.flow_sort != 0
                    <if test="userId != null ">
                    AND blg.operator_id = #{userId}
                    </if>
                    GROUP BY
                    blg.bpm_instance_id
            )
        ) blg
        LEFT JOIN bpm_instance bi ON blg.bpm_instance_id = bi.id
        WHERE
        bi.is_deleted = 0
        <if test="page.flowName != null and page.flowName !=''">
            AND bi.flow_name LIKE concat('%',#{page.flowName},'%')
        </if>
        <if test="page.applyUserName != null and page.applyUserName !=''">
            AND bi.apply_user_name LIKE concat('%',#{page.applyUserName},'%')
        </if>
        <if test="page.businessType != null and page.businessType !=''">
            AND bi.business_type = #{page.businessType}
        </if>

        <if test="page.applyStartTime != null ">
            AND bi.apply_time <![CDATA[>=]]> #{page.applyStartTime}
        </if>
        <if test="page.applyEndTime != null ">
            AND bi.apply_time <![CDATA[<=]]> #{page.applyEndTime}
        </if>
        ORDER BY
        blg.operate_time DESC
    </select>

</mapper>
