/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.knowledge.dto.BaseDto;
import com.snszyk.zbusiness.knowledge.dto.BasePageDto;
import com.snszyk.zbusiness.knowledge.entity.Base;
import com.snszyk.zbusiness.knowledge.mapper.KnowledgeBaseMapper;
import com.snszyk.zbusiness.knowledge.service.IBaseService;
import com.snszyk.zbusiness.knowledge.vo.BaseVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 知识库 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@AllArgsConstructor
@Service
public class BaseServiceImpl extends BaseCrudServiceImpl<KnowledgeBaseMapper, Base, BaseDto, BaseVo> implements IBaseService {

	private KnowledgeBaseMapper knowledgeBaseMapper;


	@Override
	protected Wrapper<Base> beforePage(BaseVo v) {
		if (v == null) {
			return Wrappers.emptyWrapper();
		}
		return Wrappers.lambdaQuery(Base.class)
			.eq(ObjectUtil.isNotEmpty(v.getClassifyId()), Base::getClassifyId, v.getClassifyId())
			.eq(ObjectUtil.isNotEmpty(v.getCollectType()), Base::getCollectType, v.getCollectType())
			.orderByDesc(Base::getCreateTime);

	}

	@Override
	public Boolean removeByIds(List<Long> idList) {
		return super.removeByIds(idList);
	}

	@Override
	public List<BaseDto> listByIds(List<Long> idList) {
		List<Base> list = super.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(idList), Base::getId, idList)
			.list();
		if (ObjectUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(list, BaseDto.class);
	}

	@Override
	public IPage<BasePageDto> pageList(IPage<?> page, String fileName, String collectType, Long classifyId, Long deptId, Long createUser, Long createDept) {
		return knowledgeBaseMapper.pageList(page, fileName, collectType, classifyId, deptId, createUser, createDept);
	}

	@Override
	public List<BaseDto> saveBatch(List<BaseVo> saveList) {
		List<Base> baseList = BeanUtil.copy(saveList, Base.class);
		super.saveBatch(baseList);
		return BeanUtil.copy(baseList, BaseDto.class);
	}

}
