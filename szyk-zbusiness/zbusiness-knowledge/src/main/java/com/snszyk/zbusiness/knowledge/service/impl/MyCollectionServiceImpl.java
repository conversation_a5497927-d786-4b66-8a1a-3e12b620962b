/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.knowledge.dto.MyCollectionDto;
import com.snszyk.zbusiness.knowledge.dto.MyCollectionPageDto;
import com.snszyk.zbusiness.knowledge.entity.MyCollection;
import com.snszyk.zbusiness.knowledge.mapper.MyCollectionMapper;
import com.snszyk.zbusiness.knowledge.service.IMyCollectionService;
import com.snszyk.zbusiness.knowledge.vo.MyCollectionVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 我的收藏 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@AllArgsConstructor
@Service
public class MyCollectionServiceImpl extends BaseCrudServiceImpl<MyCollectionMapper, MyCollection, MyCollectionDto, MyCollectionVo> implements IMyCollectionService {

	private MyCollectionMapper myCollectionMapper;

	@Override
	public Boolean saveOrUpdateBatch(List<MyCollectionVo> collectionVoList) {
		return super.saveOrUpdateBatch(BeanUtil.copy(collectionVoList, MyCollection.class));
	}

	@Override
	public List<MyCollectionDto> listByCondition(List<Long> kIdList, Long userId) {

		List<MyCollection> list = super.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(kIdList), MyCollection::getKId, kIdList)
			.eq(ObjectUtil.isNotEmpty(userId), MyCollection::getUserId, userId)
			.list();
		if (ObjectUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(list, MyCollectionDto.class);
	}

	@Override
	public Boolean removeByCondition(List<Long> kIdList, Long userId) {
		return super.lambdaUpdate()
			.in(MyCollection::getKId, kIdList)
			.eq(MyCollection::getUserId, userId)
			.remove();
	}

	@Override
	public IPage<MyCollectionPageDto> pageList(Page<Object> page, String collectType, String classifyName, Long userId) {
		return myCollectionMapper.pageList(page, collectType, classifyName, userId);
	}


}
