<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.knowledge.mapper.BasePermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="basePermissionResultMap" type="com.snszyk.zbusiness.knowledge.entity.BasePermission">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="k_id" property="kId"/>
        <result column="object_id" property="objectId"/>
        <result column="object_name" property="objectName"/>
    </resultMap>


    <select id="selectBasePermissionPage" resultMap="basePermissionResultMap">
        select * from k_base_permission where is_deleted = 0
    </select>

</mapper>
