/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.zbusiness.knowledge.dto.MyCollectionPageDto;
import com.snszyk.zbusiness.knowledge.entity.MyCollection;
import com.snszyk.zbusiness.knowledge.vo.MyCollectionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 我的收藏 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface MyCollectionMapper extends BaseMapper<MyCollection> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param myCollection
	 * @return
	 */
	List<MyCollectionVo> selectMyCollectionPage(IPage page, MyCollectionVo myCollection);

	/**
	 * 分页查询
	 *
	 * @param page
	 * @param collectType
	 * @param classifyName
	 * @return
	 */
	IPage<MyCollectionPageDto> pageList(Page<Object> page, @Param("collectType") String collectType, @Param("classifyName") String classifyName, @Param("userId") Long userId);
}
