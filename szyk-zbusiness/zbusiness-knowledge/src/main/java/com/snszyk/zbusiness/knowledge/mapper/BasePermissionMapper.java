/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.mapper;

import com.snszyk.zbusiness.knowledge.entity.BasePermission;
import com.snszyk.zbusiness.knowledge.vo.BasePermissionVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 知识库权限表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface BasePermissionMapper extends BaseMapper<BasePermission> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param basePermission
	 * @return
	 */
	List<BasePermissionVo> selectBasePermissionPage(IPage page, BasePermissionVo basePermission);

}
