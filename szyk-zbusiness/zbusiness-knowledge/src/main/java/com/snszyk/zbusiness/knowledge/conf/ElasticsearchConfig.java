package com.snszyk.zbusiness.knowledge.conf;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
public class ElasticsearchConfig {
	@Value("${spring.elasticsearch.rest.url}")
	private String edUrl;
	@Value("${spring.elasticsearch.rest.username}")
	private String userName;
	@Value("${spring.elasticsearch.rest.password}")
	private String password;

	@Bean
	public RestHighLevelClient restHighLevelClient() {
		//设置连接的用户名密码
		final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
		credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, password));
		RestHighLevelClient client =  new RestHighLevelClient(RestClient.builder(
			new HttpHost(edUrl, 9200,"http"))
			.setHttpClientConfigCallback(httpClientBuilder -> {
				httpClientBuilder.disableAuthCaching();
				//保持连接池处于链接状态，该bug曾导致es一段时间没使用，第一次连接访问超时
				httpClientBuilder.setKeepAliveStrategy(((response, context) -> Duration.ofMinutes(5).toMillis()));
				return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
			})
		);
		return client;
	}
}
