package com.snszyk.zbusiness.knowledge.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Setter
@Getter
@Document(indexName = "fileinfo",createIndex = false)
public class FileInfo {
	/**
	 * 主键
	 */
	@Id
	private Long id;

	/**
	 * 文件名称
	 */
	@Field(name = "fileName", type = FieldType.Text,analyzer = "jieba_index",searchAnalyzer = "jieba_index")
	private String fileName;

	/**
	 * 文件类型
	 */
	@Field(name = "fileType",  type = FieldType.Keyword)
	private String fileType;

	/**
	 * 内容类型
	 */
	@Field(name = "contentType", type = FieldType.Text)
	private String contentType;

	/**
	 * 附件内容
	 */
	@Field(name = "attachment.content", type = FieldType.Text,analyzer = "jieba_index",searchAnalyzer = "jieba_index")
	@TableField(exist = false)
	private String content;

	/**
	 * 文件地址
	 */
	@Field(name = "fileUrl", type = FieldType.Text)
	private String fileUrl;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;
}
