package com.snszyk.zbusiness.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.knowledge.dto.BasePageDto;
import com.snszyk.zbusiness.knowledge.dto.SearchInfoDto;
import com.snszyk.zbusiness.knowledge.entity.FileInfo;
import com.snszyk.zbusiness.knowledge.service.ElasticsearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * es搜索引擎
 *
 * <AUTHOR>
 */
@Api(value = "全文搜索引擎", tags = "全文搜索引擎")
@RestController
@RequestMapping("es")
public class ElasticsearchController extends BaseCrudController {
	@Resource
	private ElasticsearchService elasticsearchService;


	/**
	 * 高亮分词分页查询
	 *
	 * @param searchInfoDto
	 * @return
	 */
	@ApiOperation("高亮分词分页查询")
	@PostMapping("queryHighLightWordDoc")
	public R<IPage<BasePageDto>> queryHighLightWordDoc(@RequestBody SearchInfoDto searchInfoDto, HttpServletRequest request) {
		IPage<BasePageDto> warningInfoListPage = elasticsearchService.queryHighLightWordOther(searchInfoDto,request);
		return R.data(warningInfoListPage);
	}

	@ApiOperation("根据id删除")
	@PostMapping("deleteById")
	public R<Boolean> deleteById(@RequestBody Set<Long> ids) {
		Boolean result = elasticsearchService.deleteById(ids);
		return R.data(result);
	}


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
