/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.knowledge.dto.BasePageDto;
import com.snszyk.zbusiness.knowledge.entity.Base;
import com.snszyk.zbusiness.knowledge.vo.BaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识库 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface KnowledgeBaseMapper extends BaseMapper<Base> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param base
	 * @return
	 */
	List<BaseVo> selectBasePage(IPage page, BaseVo base);

	/**
	 * @param page
	 * @param collectType
	 * @param classifyId
	 * @return
	 */
	IPage<BasePageDto> pageList(IPage<?> page,@Param("fileName") String fileName,
								@Param("collectType") String collectType,
								@Param("classifyId") Long classifyId, @Param("deptId") Long deptId,
								@Param("createUser")Long createUser, @Param("createDept")Long createDept);
}
