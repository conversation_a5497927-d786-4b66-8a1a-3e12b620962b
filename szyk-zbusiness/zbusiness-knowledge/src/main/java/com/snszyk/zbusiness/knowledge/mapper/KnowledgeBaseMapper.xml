<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.knowledge.mapper.KnowledgeBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.snszyk.zbusiness.knowledge.entity.Base">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="collect_type" property="collectType"/>
        <result column="file_name" property="fileName"/>
        <result column="classify_id" property="classifyId"/>
        <result column="source_id" property="sourceId"/>
        <result column="source_no" property="sourceNo"/>
        <result column="uploader_id" property="uploaderId"/>
        <result column="uploader_name" property="uploaderName"/>
        <result column="upload_date" property="uploadDateTime"/>
        <result column="remark" property="remark"/>
        <result column="permission_type" property="permissionType"/>
        <result column="attach_id" property="attachId"/>
        <result column="attach_url" property="attachUrl"/>
    </resultMap>


    <select id="selectBasePage" resultMap="baseResultMap">
        select * from k_base where is_deleted = 0
    </select>
    <select id="pageList" resultType="com.snszyk.zbusiness.knowledge.dto.BasePageDto">
        SELECT
        b.*,
        c.classify_name AS "classifyName",
        case when b.create_user = #{createUser} and b.create_dept = #{createDept} then true
        else false end as editFlag
        FROM
        k_base b
        LEFT JOIN k_base_permission bp ON b.id = bp.k_id AND bp.is_deleted = 0
        LEFT JOIN k_classify c ON c.id = b.classify_id AND c.is_deleted = 0
        <where>
            b.is_deleted = 0
            <if test="collectType!=null and collectType !=''">
                and b.collect_type = #{collectType}
            </if>
            <if test="classifyId!=null and classifyId !=''">
                and b.classify_id = #{classifyId}
            </if>
            <if test="fileName!=null and fileName !=''">
                and b.file_name  like concat('%',#{fileName},'%')
            </if>

            and (bp.id IS NULL or bp.object_id = #{deptId}
            )
        </where>
        group by b.id
        order by b.create_time desc,b.id desc
    </select>

</mapper>
