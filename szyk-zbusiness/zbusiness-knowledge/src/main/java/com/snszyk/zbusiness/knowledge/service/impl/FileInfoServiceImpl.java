package com.snszyk.zbusiness.knowledge.service.impl;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.R;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.zbusiness.knowledge.dto.BaseDto;
import com.snszyk.zbusiness.knowledge.entity.FileInfo;
import com.snszyk.zbusiness.knowledge.service.FileInfoService;
import com.snszyk.zbusiness.knowledge.service.IBaseService;
import com.snszyk.zbusiness.knowledge.util.MultipartFileToFile;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.util.Base64;

@Service
@Slf4j
public class FileInfoServiceImpl implements FileInfoService {


	@Autowired
	@Qualifier("restHighLevelClient")
	private RestHighLevelClient client;

	@Autowired
	private IBaseService baseService;

	@Autowired
	private IAttachService attachService;


	/**
	 * 上传文件并进行文件内容识别上传到es
	 * @return
	 */
	@Override
	@Async
	public R<FileInfo> uploadFileInfo(BaseDto baseDto) {
		try {
			// 上传文件路径
			FileInfo fileInfo = new FileInfo();
			fileInfo.setId(baseDto.getId());
			fileInfo.setFileName(baseDto.getFileName());
			fileInfo.setFileUrl(baseDto.getAttachUrl());
			fileInfo.setContentType(baseDto.getClassifyId().toString());
			Attach attach = attachService.getById(baseDto.getAttachId());
			File  files =  getFileByHttpURL(attach.getDomain() + "/" + attach.getName());

			byte[] bytes = getContent(files);
			String base64 = Base64.getEncoder().encodeToString(bytes);
			fileInfo.setContent(base64);
			IndexRequest indexRequest = new IndexRequest("fileinfo");
			//上传同时，使用attachment pipline进行提取文件
			indexRequest.source(JSON.toJSONString(fileInfo), XContentType.JSON);
			indexRequest.setPipeline("attachment");
			IndexResponse indexResponse = client.index(indexRequest, RequestOptions.DEFAULT);
			log.info("indexResponse:" + indexResponse);

			R<FileInfo> data = R.data(fileInfo);
			return data;
		} catch (Exception e) {
			throw new ServiceException(e.getMessage());
		}
	}


	/**
	 * 文件转base64
	 *
	 * @param file
	 * @return
	 * @throws IOException
	 */
	private byte[] getContent(File file) throws IOException {

		long fileSize = file.length();
		if (fileSize > Integer.MAX_VALUE) {
			log.info("file too big...");
			return null;
		}
		FileInputStream fi = new FileInputStream(file);
		byte[] buffer = new byte[(int) fileSize];
		int offset = 0;
		int numRead = 0;
		while (offset < buffer.length
			&& (numRead = fi.read(buffer, offset, buffer.length - offset)) >= 0) {
			offset += numRead;
		}
		// 确保所有数据均被读取
		if (offset != buffer.length) {
			throw new ServiceException("Could not completely read file "
				+ file.getName());
		}
		fi.close();
		return buffer;
	}

	/**
	 * 根据URL地址获取文件
	 * @param path URL网络地址
	 * @return File
	 */
	private static File getFileByHttpURL(String path){
		String newUrl = path.split("[?]")[0];
		String[] suffix = newUrl.split("/");
		//得到最后一个分隔符后的名字
		String fileName = suffix[suffix.length - 1];
		File file = null;
		InputStream inputStream = null;
		OutputStream outputStream = null;
		try{
			file = File.createTempFile("report",fileName);//创建临时文件
			URL urlFile = new URL(newUrl);
			inputStream = urlFile.openStream();
			outputStream = new FileOutputStream(file);

			int bytesRead = 0;
			byte[] buffer = new byte[8192];
			while ((bytesRead=inputStream.read(buffer,0,8192))!=-1) {
				outputStream.write(buffer, 0, bytesRead);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			try {
				if (null != outputStream) {
					outputStream.close();
				}
				if (null != inputStream) {
					inputStream.close();
				}

			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return file;
	}

}
