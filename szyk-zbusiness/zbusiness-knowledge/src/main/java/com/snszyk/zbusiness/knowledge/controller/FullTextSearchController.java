/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.knowledge.controller;

import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.zbusiness.knowledge.service.logic.BaseLogicService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 知识库权限表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("zbusiness-knowledge/fulltextsearch")
@Api(value = "全文检索", tags = "全文检索")
public class FullTextSearchController extends BaseCrudController {

	// private final IBasePermissionService basePermissionService;

	private final BaseLogicService baseLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return baseLogicService;
	}


}
