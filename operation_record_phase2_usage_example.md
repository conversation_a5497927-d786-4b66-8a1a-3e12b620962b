# 操作记录系统第二阶段改进 - 使用示例

## 概述

第二阶段改进实现了字段语义分离，提供了配置化、可扩展的字段处理体系。本文档展示如何使用新的语义处理功能。

## 核心功能使用

### 1. 字段语义处理

#### 1.1 获取字段语义名称
```java
@Autowired
private FieldSemanticProcessor fieldSemanticProcessor;

// 获取字段的中文显示名称
String semanticName = fieldSemanticProcessor.getFieldSemanticName(
    BusinessTypeEnum.INTERNET_ASSET, "systemName");
// 结果: "系统名称"

String statusName = fieldSemanticProcessor.getFieldSemanticName(
    BusinessTypeEnum.INTERNET_ASSET, "resourceStatus");
// 结果: "资源状态"
```

#### 1.2 字段值转换
```java
// 布尔值转换
String displayValue = fieldSemanticProcessor.convertFieldValueToDisplay(
    BusinessTypeEnum.INTERNET_ASSET, "isPublicCloud", "true");
// 结果: "是"

// 等保级别转换
String levelValue = fieldSemanticProcessor.convertFieldValueToDisplay(
    BusinessTypeEnum.INTERNET_ASSET, "securityLevel", "3");
// 结果: "三级"

// 状态转换
String statusValue = fieldSemanticProcessor.convertFieldValueToDisplay(
    BusinessTypeEnum.INTERNET_ASSET, "resourceStatus", "0");
// 结果: "在用"
```

#### 1.3 批量处理变更详情
```java
// 创建变更详情列表
List<RsOperationRecordDetailDto> detailList = Arrays.asList(
    createDetail("systemName", "旧系统名", "新系统名"),
    createDetail("isPublicCloud", "false", "true"),
    createDetail("securityLevel", "2", "3")
);

// 一次性处理所有字段的语义转换
fieldSemanticProcessor.processChangeDetailsDisplay(
    BusinessTypeEnum.INTERNET_ASSET, detailList);

// 处理后的结果:
// detailList.get(0): fieldLabel="系统名称", oldDisplayValue="旧系统名", newDisplayValue="新系统名"
// detailList.get(1): fieldLabel="是否公有云", oldDisplayValue="否", newDisplayValue="是"
// detailList.get(2): fieldLabel="等保级别", oldDisplayValue="二级", newDisplayValue="三级"
```

### 2. 字段分组和敏感字段处理

#### 2.1 字段分组
```java
// 获取字段所属分组
String group = fieldSemanticProcessor.getFieldGroup(
    BusinessTypeEnum.INTERNET_ASSET, "systemName");
// 结果: "基本信息"

String contactGroup = fieldSemanticProcessor.getFieldGroup(
    BusinessTypeEnum.INTERNET_ASSET, "contactPerson");
// 结果: "联系信息"

String securityGroup = fieldSemanticProcessor.getFieldGroup(
    BusinessTypeEnum.INTERNET_ASSET, "securityLevel");
// 结果: "安全信息"
```

#### 2.2 敏感字段识别
```java
// 检查是否为敏感字段
boolean isSensitive = fieldSemanticProcessor.isSensitiveField(
    BusinessTypeEnum.INTERNET_ASSET, "contactPhone");
// 结果: true

boolean isNormal = fieldSemanticProcessor.isSensitiveField(
    BusinessTypeEnum.INTERNET_ASSET, "systemName");
// 结果: false
```

## 扩展新业务类型

### 1. 添加业务类型枚举
```java
// 在 BusinessTypeEnum 中添加新类型
EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产", "equipmentName", "orgId") {
    @Override
    public String getFieldConfigClassName() {
        return "equipmentAssetFieldConfig";
    }
}
```

### 2. 创建字段配置类
```java
@Component("equipmentAssetFieldConfig")
public class EquipmentAssetFieldConfig implements BusinessFieldConfig {

    @Override
    public Map<String, String> getFieldLabelMap() {
        Map<String, String> labelMap = new HashMap<>();
        labelMap.put("equipmentName", "设备名称");
        labelMap.put("equipmentType", "设备类型");
        labelMap.put("manufacturer", "制造商");
        labelMap.put("model", "型号");
        labelMap.put("serialNumber", "序列号");
        labelMap.put("purchaseDate", "采购日期");
        labelMap.put("warrantyPeriod", "保修期");
        labelMap.put("equipmentStatus", "设备状态");
        return Collections.unmodifiableMap(labelMap);
    }

    @Override
    public Map<String, String> getFieldTypeMap() {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("equipmentName", "STRING");
        typeMap.put("equipmentType", "DICT");
        typeMap.put("purchaseDate", "DATE");
        typeMap.put("warrantyPeriod", "INTEGER");
        typeMap.put("equipmentStatus", "DICT");
        return Collections.unmodifiableMap(typeMap);
    }

    @Override
    public Map<String, String> getFieldGroupMap() {
        Map<String, String> groupMap = new HashMap<>();
        groupMap.put("equipmentName", "基本信息");
        groupMap.put("equipmentType", "基本信息");
        groupMap.put("manufacturer", "基本信息");
        groupMap.put("model", "基本信息");
        groupMap.put("serialNumber", "标识信息");
        groupMap.put("purchaseDate", "采购信息");
        groupMap.put("warrantyPeriod", "采购信息");
        groupMap.put("equipmentStatus", "状态信息");
        return Collections.unmodifiableMap(groupMap);
    }

    @Override
    public List<String> getSensitiveFields() {
        return Arrays.asList("serialNumber", "purchasePrice");
    }

    @Override
    public Map<String, String> getDictFieldMap() {
        Map<String, String> dictMap = new HashMap<>();
        dictMap.put("equipmentType", "equipment_type");
        dictMap.put("equipmentStatus", "equipment_status");
        return Collections.unmodifiableMap(dictMap);
    }

    // 实现其他必需方法...
}
```

### 3. 创建专用字段转换器（可选）
```java
@Component
public class EquipmentFieldValueConverter implements FieldValueConverter {

    @Override
    public String convert(String fieldName, String fieldValue) {
        if ("warrantyPeriod".equals(fieldName)) {
            // 将月数转换为年月显示
            int months = Integer.parseInt(fieldValue);
            int years = months / 12;
            int remainingMonths = months % 12;
            if (years > 0 && remainingMonths > 0) {
                return years + "年" + remainingMonths + "个月";
            } else if (years > 0) {
                return years + "年";
            } else {
                return remainingMonths + "个月";
            }
        }
        return fieldValue;
    }

    @Override
    public boolean supports(String fieldName) {
        return "warrantyPeriod".equals(fieldName);
    }

    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }
}
```

## 在业务代码中使用

### 1. 在Service层使用
```java
@Service
@AllArgsConstructor
public class EquipmentAssetLogicService {

    private final FieldSemanticProcessor fieldSemanticProcessor;
    private final EquipmentAssetService equipmentAssetService;

    public IPage<EquipmentAssetDto> page(EquipmentAssetVo vo) {
        IPage<EquipmentAssetDto> pageResult = equipmentAssetService.pageList(vo);
        
        // 如果需要显示操作记录，可以使用语义处理器
        enhanceWithSemanticInfo(pageResult.getRecords());
        
        return pageResult;
    }

    private void enhanceWithSemanticInfo(List<EquipmentAssetDto> records) {
        for (EquipmentAssetDto record : records) {
            // 处理敏感字段
            if (fieldSemanticProcessor.isSensitiveField(
                BusinessTypeEnum.EQUIPMENT_ASSET, "serialNumber")) {
                record.setSerialNumber(maskSensitiveData(record.getSerialNumber()));
            }
        }
    }
}
```

### 2. 在Controller层使用
```java
@RestController
@AllArgsConstructor
public class EquipmentAssetController {

    private final FieldSemanticProcessor fieldSemanticProcessor;

    @GetMapping("/field-info/{fieldName}")
    public R<Map<String, Object>> getFieldInfo(@PathVariable String fieldName) {
        Map<String, Object> fieldInfo = new HashMap<>();
        
        // 获取字段语义信息
        String semanticName = fieldSemanticProcessor.getFieldSemanticName(
            BusinessTypeEnum.EQUIPMENT_ASSET, fieldName);
        String fieldGroup = fieldSemanticProcessor.getFieldGroup(
            BusinessTypeEnum.EQUIPMENT_ASSET, fieldName);
        boolean isSensitive = fieldSemanticProcessor.isSensitiveField(
            BusinessTypeEnum.EQUIPMENT_ASSET, fieldName);
        
        fieldInfo.put("semanticName", semanticName);
        fieldInfo.put("fieldGroup", fieldGroup);
        fieldInfo.put("isSensitive", isSensitive);
        
        return R.data(fieldInfo);
    }
}
```

## 配置示例

### 1. 字典配置示例
```java
// 在字典服务中配置设备类型
Map<String, String> equipmentTypeDict = new HashMap<>();
equipmentTypeDict.put("SERVER", "服务器");
equipmentTypeDict.put("NETWORK", "网络设备");
equipmentTypeDict.put("STORAGE", "存储设备");
equipmentTypeDict.put("SECURITY", "安全设备");

// 配置设备状态
Map<String, String> equipmentStatusDict = new HashMap<>();
equipmentStatusDict.put("NORMAL", "正常");
equipmentStatusDict.put("MAINTENANCE", "维护中");
equipmentStatusDict.put("FAULT", "故障");
equipmentStatusDict.put("RETIRED", "已退役");
```

### 2. 转换器优先级配置
```java
// 转换器优先级说明:
// 100: 布尔值转换器 (BooleanFieldValueConverter)
// 90:  状态字段转换器 (StatusFieldValueConverter)
// 80:  等保级别转换器 (SecurityLevelFieldValueConverter)
// 70:  通用字典转换器 (DictFieldValueConverter)
// 60:  业务感知字典转换器 (BusinessAwareDictFieldValueConverter)
// 50:  自定义业务转换器 (EquipmentFieldValueConverter)

// 数字越小优先级越高，会优先使用
```

## 最佳实践

### 1. 字段命名规范
- 布尔字段使用 `is`、`has`、`can` 前缀
- 状态字段使用 `status`、`state` 后缀
- 等级字段使用 `level`、`grade` 后缀
- 字典字段使用 `type`、`category` 后缀

### 2. 配置组织
- 将相关字段按业务逻辑分组
- 敏感字段单独标识和处理
- 字典映射关系清晰明确

### 3. 扩展建议
- 新业务类型优先复用现有转换器
- 特殊转换需求创建专用转换器
- 保持转换器的单一职责原则

### 4. 性能考虑
- 转换器按优先级排序，避免不必要的检查
- 使用ThreadLocal传递业务上下文，避免参数传递
- 配置信息适当缓存，减少重复计算

## 总结

第二阶段的语义处理体系提供了强大而灵活的字段处理能力，通过配置化的方式消除了硬编码，提高了系统的可维护性和扩展性。开发者可以轻松添加新的业务类型和字段转换逻辑，同时保持代码的整洁和一致性。
