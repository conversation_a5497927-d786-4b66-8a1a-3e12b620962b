# SZYK 操作记录系统使用指南

## 概述

SZYK 操作记录系统是一个高度可扩展、高性能的操作记录跟踪系统，支持多业务模块的操作记录管理。系统采用混合存储架构，结合了结构化元数据、JSON 灵活存储和专用索引，提供了完整的插件化扩展能力。

## 核心特性

### 1. 混合存储架构
- **结构化元数据**: 字段定义、验证规则、分组配置
- **JSON 灵活存储**: 动态扩展字段数据
- **专用索引**: 高性能查询优化
- **模板系统**: 业务特定配置管理

### 2. 插件化扩展
- **业务模块插件**: 支持自定义业务逻辑
- **字段值转换**: 自定义显示格式化
- **数据验证**: 业务特定验证规则
- **预处理/后处理**: 完整的生命周期钩子

### 3. 高性能设计
- **多级缓存**: 字段定义、映射、分组缓存
- **异步处理**: 非阻塞操作记录保存
- **索引优化**: 动态字段查询性能优化

## 快速开始

### 1. 基础操作记录创建

```java
@Autowired
private RsOperationRecordLogicService operationRecordLogicService;

// 创建操作记录
RsOperationRecordVo vo = new RsOperationRecordVo();
vo.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
vo.setBusinessId(1L);
vo.setBusinessName("测试系统");
vo.setOperationType(OperationTypeEnum.UPDATE.getCode());
vo.setOperationDescription("更新系统配置");

// 设置变更数据
Map<String, Object> oldData = Map.of("systemName", "旧系统名称");
Map<String, Object> newData = Map.of("systemName", "新系统名称");
vo.setOldData(JSON.toJSONString(oldData));
vo.setNewData(JSON.toJSONString(newData));
vo.setChangeFields("systemName");

// 保存操作记录
boolean success = operationRecordLogicService.createOperationRecord(vo);
```

### 2. 扩展字段使用

```java
// 在 newData 中添加扩展字段（以 _ext_ 开头）
Map<String, Object> newData = new HashMap<>();
newData.put("systemName", "新系统名称");
newData.put("_ext_customField1", "自定义值1");
newData.put("_ext_customField2", 123);
newData.put("_ext_customField3", true);

vo.setNewData(JSON.toJSONString(newData));
```

### 3. 获取增强详情

```java
// 获取包含扩展数据的详情
RsOperationRecordDto detail = operationRecordLogicService.getEnhancedDetail(recordId);

// 访问扩展数据
Map<String, Object> extensionData = detail.getExtensionData();
Map<String, String> displayValues = detail.getExtensionDisplayValues();
```

## 高级功能

### 1. 自定义业务模块插件

```java
@Component
public class CustomModulePlugin extends AbstractBusinessModulePlugin {

    @Override
    public BusinessTypeEnum getBusinessType() {
        return BusinessTypeEnum.CUSTOM_BUSINESS;
    }

    @Override
    public int getPriority() {
        return 100;
    }

    @Override
    public Map<String, Object> preprocess(Long businessId, String operationType, 
                                         Map<String, Object> oldData, Map<String, Object> newData) {
        // 自定义预处理逻辑
        Map<String, Object> result = new HashMap<>(newData);
        result.put("processedAt", System.currentTimeMillis());
        return result;
    }

    @Override
    public Map<String, String> validateBusinessData(Long businessId, String operationType, 
                                                   Map<String, Object> data) {
        Map<String, String> errors = new HashMap<>();
        
        // 自定义验证逻辑
        if (!data.containsKey("requiredField")) {
            errors.put("requiredField", "必填字段不能为空");
        }
        
        return errors;
    }

    @Override
    public String convertFieldValueToDisplay(String fieldName, Object fieldValue) {
        // 自定义字段值显示转换
        if ("status".equals(fieldName) && fieldValue != null) {
            return convertStatusToDisplay(fieldValue.toString());
        }
        return null; // 返回 null 使用默认转换
    }

    private String convertStatusToDisplay(String status) {
        switch (status) {
            case "ACTIVE": return "激活";
            case "INACTIVE": return "停用";
            default: return status;
        }
    }
}
```

### 2. 自定义操作记录模板

```java
@Autowired
private OperationRecordTemplateService templateService;

// 创建自定义模板
RsOperationRecordTemplate template = new RsOperationRecordTemplate();
template.setBusinessType(BusinessTypeEnum.INTERNET_ASSET.getCode());
template.setTemplateName("互联网资产标准模板");
template.setDescription("互联网资产操作记录标准模板");

// 配置模板
Map<String, Object> templateConfig = new HashMap<>();
List<Map<String, Object>> fields = new ArrayList<>();

// 添加字段配置
Map<String, Object> fieldConfig = new HashMap<>();
fieldConfig.put("fieldName", "systemName");
fieldConfig.put("required", true);
fieldConfig.put("displayName", "系统名称");
fieldConfig.put("fieldType", "STRING");
fieldConfig.put("maxLength", 100);
fields.add(fieldConfig);

templateConfig.put("fields", fields);
templateConfig.put("version", "1.0");
templateConfig.put("author", "系统管理员");

template.setTemplateConfig(JSON.toJSONString(templateConfig));
template.setIsDefault(true);

// 保存模板
boolean created = templateService.createTemplate(template);
```

### 3. 动态字段配置

```java
@Autowired
private DynamicFieldConfigService dynamicFieldConfigService;

// 获取字段定义
List<RsBusinessFieldDefinition> fieldDefinitions = 
    dynamicFieldConfigService.getFieldDefinitions(BusinessTypeEnum.INTERNET_ASSET);

// 获取分组字段
Map<String, List<RsBusinessFieldDefinition>> groupedFields = 
    dynamicFieldConfigService.getGroupedFieldDefinitions(BusinessTypeEnum.INTERNET_ASSET);

// 获取字段映射
Map<String, String> fieldMapping = 
    dynamicFieldConfigService.getFieldMapping(BusinessTypeEnum.INTERNET_ASSET);
```

### 4. 扩展字段存储和查询

```java
@Autowired
private ExtensionFieldStorageService extensionFieldStorageService;

// 保存扩展数据
Map<String, Object> extensionData = Map.of(
    "customField1", "值1",
    "customField2", 123,
    "customField3", true
);

Map<String, Object> metadata = Map.of(
    "operationType", OperationTypeEnum.CREATE.getCode(),
    "businessType", BusinessTypeEnum.INTERNET_ASSET.getCode()
);

boolean saved = extensionFieldStorageService.saveExtensionData(
    recordId, BusinessTypeEnum.INTERNET_ASSET, extensionData, metadata
);

// 查询扩展数据
Map<String, Object> queryConditions = Map.of("customField1", "值1");
List<Long> recordIds = extensionFieldStorageService.queryRecordIds(
    BusinessTypeEnum.INTERNET_ASSET, queryConditions
);

// 格式化显示值
Map<String, Object> data = extensionFieldStorageService.getExtensionData(
    recordId, BusinessTypeEnum.INTERNET_ASSET
);
Map<String, String> displayValues = extensionFieldStorageService.formatDisplayValues(
    BusinessTypeEnum.INTERNET_ASSET, data
);
```

## 配置说明

### 1. 缓存配置

系统使用多级缓存提高性能，主要缓存区域：

- `fieldDefinitions`: 字段定义缓存
- `fieldMappings`: 字段映射缓存  
- `fieldGroups`: 字段分组缓存
- `defaultTemplate`: 默认模板缓存
- `businessTypeTemplates`: 业务类型模板缓存

### 2. 数据库配置

确保执行了 `operation_record_extension_table.sql` 脚本，创建必要的数据库表：

- `rs_operation_record_extension`: 扩展字段存储
- `rs_business_field_definition`: 字段定义
- `rs_operation_record_detail_ext`: 详情扩展
- `rs_operation_record_index`: 性能索引
- `rs_operation_record_template`: 模板配置

### 3. 插件配置

插件会自动注册到 `BusinessModulePluginManager`，支持：

- 优先级排序
- 多插件执行
- 异常隔离
- 性能监控

## 性能优化建议

### 1. 缓存策略
- 合理设置缓存过期时间
- 使用缓存预热
- 监控缓存命中率

### 2. 索引优化
- 为常用查询字段创建索引
- 定期维护索引统计信息
- 监控慢查询

### 3. 异步处理
- 使用异步保存操作记录
- 批量处理扩展数据
- 合理设置线程池大小

## 故障排除

### 1. 常见问题

**Q: 扩展字段数据保存失败**
A: 检查字段定义配置，确保验证规则正确

**Q: 插件不生效**
A: 确认插件类添加了 `@Component` 注解，并实现了正确的接口

**Q: 模板应用失败**
A: 检查模板配置 JSON 格式是否正确

### 2. 日志配置

```yaml
logging:
  level:
    com.snszyk.zbusiness.resource.service: DEBUG
    com.snszyk.zbusiness.resource.plugin: DEBUG
```

### 3. 监控指标

- 操作记录创建成功率
- 扩展字段存储性能
- 插件执行时间
- 缓存命中率

## 最佳实践

1. **字段命名**: 使用有意义的字段名称，避免特殊字符
2. **扩展字段**: 合理使用扩展字段，避免过度使用
3. **插件开发**: 保持插件逻辑简单，避免复杂业务逻辑
4. **模板设计**: 设计通用性强的模板，减少维护成本
5. **性能监控**: 定期监控系统性能，及时优化

## 版本历史

- **v1.0.0**: 基础操作记录功能
- **v2.0.0**: 添加扩展字段支持
- **v3.0.0**: 插件系统和模板功能
- **v4.0.0**: 性能优化和缓存策略

## 技术支持

如有问题，请联系开发团队或查看相关文档。
