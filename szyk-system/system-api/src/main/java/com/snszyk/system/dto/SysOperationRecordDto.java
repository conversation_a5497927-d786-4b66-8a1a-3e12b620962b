/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作记录DTO
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作记录DTO", description = "操作记录数据传输对象")
public class SysOperationRecordDto extends BaseCrudDto {

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 业务模块名称
     */
    @ApiModelProperty(value = "业务模块名称")
    private String businessModuleName;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 业务数据ID
     */
    @ApiModelProperty(value = "业务数据ID")
    private Long businessId;

    /**
     * 业务数据名称
     */
    @ApiModelProperty(value = "业务数据名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作类型名称
     */
    @ApiModelProperty(value = "操作类型名称")
    private String operationTypeName;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作人部门ID
     */
    @ApiModelProperty(value = "操作人部门ID")
    private Long operatorDeptId;

    /**
     * 操作人部门名称
     */
    @ApiModelProperty(value = "操作人部门名称")
    private String operatorDeptName;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 请求IP
     */
    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    /**
     * 请求URI
     */
    @ApiModelProperty(value = "请求URI")
    private String requestUri;

    /**
     * 请求方法
     */
    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    /**
     * 操作前数据
     */
    @ApiModelProperty(value = "操作前数据")
    private String oldData;

    /**
     * 操作后数据
     */
    @ApiModelProperty(value = "操作后数据")
    private String newData;

    /**
     * 字段变更详情列表
     */
    @ApiModelProperty(value = "字段变更详情列表")
    private List<FieldChangeDto> fieldChanges;
}
