/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * xjob任务表实体类
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SzykXjobVo对象", description = "xjob任务表")
public class SzykXjobVo extends BaseCrudVo {

	/**
	* 业务类型
	*/
		@ApiModelProperty(value = "业务类型")
		private String businessType;
	/**
	* 业务id
	*/
		@ApiModelProperty(value = "业务id")
		private Long businessId;
	/**
	* xjob定时任务id
	*/
		@ApiModelProperty(value = "xjob定时任务id")
		private Integer jobId;


}
