/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
public class UserDeptDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "员工名称")
	private String employeeName;

	@ApiModelProperty(value = "组织名称")
	private String orgName;

	@ApiModelProperty(value = "部门名称")
	private String deptName;

	@ApiModelProperty(value = "人员头像")
	private String avatar;


	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 用户ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "用户ID")
	private Long userId;

	/**
	 * 部门ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部门ID")
	private Long deptId;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部门的祖籍列表")
	private String ancestorName;

	@ApiModelProperty(value = "默认部门：0否1是")
	private  Integer defaultDept;

	/**
	 * 业务范围
	 */
	@ApiModelProperty(value = "角色")
	private List<UserRoleDTO> roleList;

	@ApiModelProperty(value = "联络人,多个逗号隔开")
	private String contactPerson;

	@ApiModelProperty(value = "联络人名称,多个逗号隔开")
	private String contactPersonName;
	@ApiModelProperty(value = "部门类型")
	private Integer deptCategory;

	@ApiModelProperty(value = "部门类型名称,只区分了单位和部门")
	private String deptCategoryName;

}
