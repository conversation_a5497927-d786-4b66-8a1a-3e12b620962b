package com.snszyk.system.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志查询VO
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "操作日志查询VO", description = "操作日志查询对象")
public class OperationLogVo extends BaseCrudVo {

    /**
     * 业务模块
     */
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作结果
     */
    @ApiModelProperty(value = "操作结果")
    private String operationResult;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 操作人部门名称
     */
    @ApiModelProperty(value = "操作人部门名称")
    private String operatorDeptName;

    /**
     * 开始操作时间
     */
    @ApiModelProperty(value = "开始操作时间")
    private LocalDateTime startOperationTime;

    /**
     * 结束操作时间
     */
    @ApiModelProperty(value = "结束操作时间")
    private LocalDateTime endOperationTime;

    /**
     * 客户端IP
     */
    @ApiModelProperty(value = "客户端IP")
    private String clientIp;

    /**
     * 主管单位ID
     */
    @ApiModelProperty(value = "主管单位ID")
    private Long orgId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称")
    private String orgName;

    /**
     * 系统名称（互联网资产专用）
     */
    @ApiModelProperty(value = "系统名称")
    private String systemName;
}
