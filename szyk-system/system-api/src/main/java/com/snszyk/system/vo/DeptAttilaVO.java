/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.node.INode;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "DeptAttilaVO对象", description = "DeptAttilaVO对象")
public class DeptAttilaVO implements INode<DeptAttilaVO> {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long parentId;

	@JsonSerialize(using = ToStringSerializer.class)
	private String title;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long key;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long value;

	@JsonSerialize(using = ToStringSerializer.class)
	private Integer dataType;

	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<DeptAttilaVO> children;

}
