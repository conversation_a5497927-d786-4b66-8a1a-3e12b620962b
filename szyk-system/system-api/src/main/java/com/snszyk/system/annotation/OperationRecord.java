/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.annotation;

import com.snszyk.system.enums.BusinessTypeEnum;
import com.snszyk.system.enums.OperationTypeEnum;

import java.lang.annotation.*;

/**
 * 操作记录注解
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationRecord {

    /**
     * 业务类型
     */
    BusinessTypeEnum businessType();

    /**
     * 操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default false;

    /**
     * 是否记录异常信息
     */
    boolean logException() default true;

    /**
     * 业务ID的SpEL表达式
     * 例如: "#id" 或 "#dto.id" 或 "#result.data.id"
     */
    String businessIdExpression() default "";

    /**
     * 业务名称的SpEL表达式
     * 例如: "#dto.systemName" 或 "#result.data.systemName"
     */
    String businessNameExpression() default "";

    /**
     * 需要比较的字段列表
     * 用于记录字段变更详情
     * 如果为空，则比较所有字段
     */
    String[] compareFields() default {};

    /**
     * 是否异步执行
     */
    boolean async() default true;

    /**
     * 是否忽略空值变更
     */
    boolean ignoreNullChanges() default true;

    /**
     * 是否记录详细的字段变更
     */
    boolean recordFieldChanges() default true;
}
