package com.snszyk.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户在线时长
 *
 * <AUTHOR>
 * @since 2023/5/8 15:31
 **/
@Data
@TableName("szyk_user_online_duration")
public class UserOnlineDuration implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 租户ID
	 */
	private String tenantId;

	/**
	 * 用户id
	 */
	private Long userId;

	private Long deptId;

	/**
	 * 用户名称
	 */
	private String userName;

	/**
	 * 时长
	 */
	private Long duration;



	/**
	 * 用户ip地址
	 */
	private String ipAddress;

	/**
	 * 浏览器
	 */
	private String browser;

	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private Date createTime;

}
