/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    /**
     * 互联网资产
     */
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产", "systemName", "orgId"),

    /**
     * 信息系统
     */
    SYSTEM_RECORD("SYSTEM_RECORD", "信息系统", "systemName", "orgId"),

    /**
     * 设备资产
     */
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产", "equipmentName", "orgId"),

    /**
     * 软件资产
     */
    SOFTWARE_ASSET("SOFTWARE_ASSET", "软件资产", "softwareName", "orgId"),

    /**
     * 机房台账
     */
    SERVER_ROOM("SERVER_ROOM", "机房台账", "roomName", "orgId"),

    /**
     * 项目管理
     */
    PROJECT_MANAGE("PROJECT_MANAGE", "项目管理", "projectName", "deptId"),

    /**
     * 人员管理
     */
    PERSON_MANAGE("PERSON_MANAGE", "人员管理", "personName", "deptId");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型名称
     */
    private final String name;

    /**
     * 默认名称字段
     */
    private final String defaultNameField;

    /**
     * 默认组织字段
     */
    private final String defaultOrgField;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BusinessTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举，如果不存在则抛出异常
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BusinessTypeEnum fromCode(String code) {
        BusinessTypeEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("未知的业务类型编码: " + code);
        }
        return result;
    }
}
