/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {

    /**
     * 新增
     */
    CREATE("CREATE", "新增"),

    /**
     * 更新
     */
    UPDATE("UPDATE", "更新"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除"),

    /**
     * 查询
     */
    QUERY("QUERY", "查询"),

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 审核
     */
    AUDIT("AUDIT", "审核"),

    /**
     * 启用
     */
    ENABLE("ENABLE", "启用"),

    /**
     * 停用
     */
    DISABLE("DISABLE", "停用"),

    /**
     * 锁定
     */
    LOCK("LOCK", "锁定"),

    /**
     * 解锁
     */
    UNLOCK("UNLOCK", "解锁");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static OperationTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取枚举，如果不存在则抛出异常
     *
     * @param code 编码
     * @return 枚举值
     */
    public static OperationTypeEnum fromCode(String code) {
        OperationTypeEnum result = getByCode(code);
        if (result == null) {
            throw new IllegalArgumentException("未知的操作类型编码: " + code);
        }
        return result;
    }
}
