/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.system.dto.UserDeptVO;
import com.snszyk.system.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserVO对象", description = "UserVO对象")
public class UserVO extends User {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 密码
	 */
	@JsonIgnore
	private String password;

	/**
	 * 租户名
	 */
	@ApiModelProperty(value = "租户名",hidden = true)
	private String tenantName;

	/**
	 * 用户平台名
	 */
	@ApiModelProperty(value = "用户平台名",hidden = true)
	private String userTypeName;

	/**
	 * 角色名
	 */
	@ApiModelProperty(value = "角色名",hidden = true)
	private String roleName;

	/**
	 * 部门名
	 */
	@ApiModelProperty(value = "部门名",hidden = true)
	private String deptName;



	/**
	 * 单位名
	 */
	@ApiModelProperty(value = "单位名",hidden = true)
	private String unitName;

	/**
	 * 真实的祖级部门名称
	 */
	@ApiModelProperty(value = "真实的祖级部门名称",hidden = true)
	private String ancestorName;

	/**
	 * 岗位名
	 */
	@ApiModelProperty(value = "岗位名",hidden = true)
	private String postName;

	/**
	 * 性别
	 */
	@ApiModelProperty(value = "用户平台名",hidden = true)
	private String sexName;

	@ApiModelProperty(value = "手机号")
	private String phone;

	/**
	 * 拓展信息
	 */
	@ApiModelProperty(value = "拓展信息",hidden = true)
	private String userExt;
	/**
	 * 真实的组织id
	 */
	@ApiModelProperty(value = "真实的组织id")
	private  Long actualDeptId;
	/**
	 * 业务范围
	 */
	@ApiModelProperty(value = "业务范围")
	@NotEmpty(message = "业务范围不能为空")
	private List<UserDeptVO> deptScopeList;

	@ApiModelProperty(value = "业务范围级联,多个逗号隔开")
	private String scopeAncestorsName;

	@ApiModelProperty(value = "联络人,多个逗号隔开")
	private String contactPerson;

	@ApiModelProperty(value = "业务范围单位名")
	private String scopeDeptName;

	@ApiModelProperty(value = "单位id")
	private Long unitId;

	@ApiModelProperty(value = "状态名")
	private  String statusName;
}
