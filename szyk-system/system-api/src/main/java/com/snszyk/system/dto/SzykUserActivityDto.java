/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户活跃日志表实体类
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SzykUserActivityDto对象", description = "用户活跃日志表")
public class SzykUserActivityDto extends BaseCrudDto {

	/**
	 * 用户ID
	 */
	@ApiModelProperty(value = "用户ID")
	private Long userId;
	/**
	 * 用户
	 */
	@ApiModelProperty(value = "用户")
	private String userName;
	/**
	 * 组织id
	 */
	@ApiModelProperty(value = "组织id")
	private Long deptId;
	/**
	 * 区间序号
	 */
	@ApiModelProperty(value = "区间序号")
	private Integer intervalNum;
	/**
	 * 区间开始日期
	 */
	@ApiModelProperty(value = "区间开始日期")
	private LocalDate intervalStart;
	/**
	 * 区间结束日期
	 */
	@ApiModelProperty(value = "区间结束日期")
	private LocalDate intervalEnd;
	/**
	 * 用户登录IP地址
	 */
	@ApiModelProperty(value = "用户登录IP地址")
	private String ipAddress;
	/**
	 * 用户登录时间
	 */
	@ApiModelProperty(value = "用户登录时间")
	private LocalDateTime loginTime;
	/**
	 * 用户使用浏览器
	 */
	@ApiModelProperty(value = "用户使用浏览器")
	private String browser;

	private Integer year;
	private Integer month;
	private Integer intervalCount;
	private Integer addCount;

}
