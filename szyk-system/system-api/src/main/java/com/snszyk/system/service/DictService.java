package com.snszyk.system.service;

import com.snszyk.system.entity.Dict;

import java.math.BigDecimal;
import java.util.List;

/**
 * @atuthor
 * @date 2023/2/21
 * @apiNote
 */
public interface DictService {
	/**
	 * 新增字典
	 * @param dict
	 * @return
	 */
	public Long addDict(Dict dict );

	/**
	 * 获取字典值
	 * @param code
	 * @param dictKey
	 * @return
	 */
	public String getDictValue(String code, String dictKey);

	/**
	 * 根据code获取字典值
	 * @param code
	 * @return
	 */
	public List<Dict> getDictByCode(String code);

}
