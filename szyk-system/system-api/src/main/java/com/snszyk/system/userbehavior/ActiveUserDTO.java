package com.snszyk.system.userbehavior;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活跃用户 dto
 *
 * <AUTHOR>
 * @since 2023/5/8 10:15
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActiveUserDTO{

	@ApiModelProperty(value = "用户id")
	private Long userId;

	@ApiModelProperty(value = "用户名称")
	private String userName;

	@ApiModelProperty(value = "浏览量")
	private Integer pageViewNum;
}
