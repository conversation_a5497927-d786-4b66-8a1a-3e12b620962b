/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import lombok.Data;
import com.snszyk.core.tool.node.TreeNode;

import java.io.Serializable;
import java.util.List;

/**
 * GrantTreeVO
 *
 * <AUTHOR>
 */
@Data
public class GrantTreeVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private List<TreeNode> menu;

	private List<TreeNode> dataScope;

	private List<TreeNode> apiScope;

}
