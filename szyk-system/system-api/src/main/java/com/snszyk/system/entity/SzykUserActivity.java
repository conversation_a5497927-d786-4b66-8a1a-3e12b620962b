/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户活跃日志表实体类
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SzykUserActivity extends BaseCrudEntity {

	/**
	* 用户ID
	*/
	private Long userId;
	/**
	* 用户
	*/
	private String userName;
	/**
	* 组织id
	*/
	private Long deptId;
	/**
	* 区间序号
	*/
	private Integer intervalNum;
	/**
	* 区间开始日期
	*/
	private LocalDate intervalStart;
	/**
	* 区间结束日期
	*/
	private LocalDate intervalEnd;
	/**
	* 用户登录IP地址
	*/
	private String ipAddress;
	/**
	* 用户登录时间
	*/
	private LocalDateTime loginTime;
	/**
	* 用户使用浏览器
	*/
	private String browser;

	private Integer year;
	private Integer month;
	private Integer intervalCount;
	private Integer addCount;

}
