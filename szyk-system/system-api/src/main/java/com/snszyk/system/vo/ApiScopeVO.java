/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.snszyk.system.entity.ApiScope;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ApiScopeVO对象", description = "ApiScopeVO对象")
public class ApiScopeVO extends ApiScope {
	private static final long serialVersionUID = 1L;

	/**
	 * 规则类型名
	 */
	private String scopeTypeName;
}
