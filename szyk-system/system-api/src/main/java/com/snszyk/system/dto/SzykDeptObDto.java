/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 机构表（数据湖）实体类
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SzykDeptObDto对象", description = "机构表（数据湖）")
public class SzykDeptObDto extends BaseCrudDto {

	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级")
	private String level;
	/**
	 * 组织编码
	 */
	@ApiModelProperty(value = "组织编码")
	private String code;
	/**
	 * 组织全称
	 */
	@ApiModelProperty(value = "组织全称")
	private String zzqc;
	/**
	 * 组织简称
	 */
	@ApiModelProperty(value = "组织简称")
	private String zzjc;
	/**
	 * 组织路径
	 */
	@ApiModelProperty(value = "组织路径")
	private String zzlj;
	/**
	 * 公司代码
	 */
	@ApiModelProperty(value = "公司代码")
	private String gsdm;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String gsmc;
	/**
	 * 上级组织编码
	 */
	@ApiModelProperty(value = "上级组织编码")
	private String parentcode;
	/**
	 * 上级组织编码名称
	 */
	@ApiModelProperty(value = "上级组织编码名称")
	private String parentname;
	/**
	 * 二级公司代码
	 */
	@ApiModelProperty(value = "二级公司代码")
	private String ejgsdm;
	/**
	 * 二级公司名称
	 */
	@ApiModelProperty(value = "二级公司名称")
	private String ejgsmc;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String ksrq;
	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String jsrq;
	/**
	 * 组织类型代码
	 */
	@ApiModelProperty(value = "组织类型代码")
	private String zzlxdm;
	/**
	 * 组织类型名称
	 */
	@ApiModelProperty(value = "组织类型名称")
	private String zzlxmc;
	/**
	 * 排序码
	 */
	@ApiModelProperty(value = "排序码")
	private String pxm;
	/**
	 * 调度排序码
	 */
	@ApiModelProperty(value = "调度排序码")
	private String ddpxm;
	/**
	 * 版本
	 */
	@ApiModelProperty(value = "版本")
	private String bb;
	/**
	 * 单位地址
	 */
	@ApiModelProperty(value = "单位地址")
	private String dwdz;
	/**
	 * 行业
	 */
	@ApiModelProperty(value = "行业")
	private String hy;
	/**
	 * 行业名称
	 */
	@ApiModelProperty(value = "行业名称")
	private String hymc;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private String loadNumber;


}
