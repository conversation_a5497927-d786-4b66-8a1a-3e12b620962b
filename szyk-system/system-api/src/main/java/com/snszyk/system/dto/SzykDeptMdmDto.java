/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 机构表（数据湖）实体类
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SzykDeptMdmDto对象", description = "机构表（数据湖）")
public class SzykDeptMdmDto extends BaseCrudDto {

	/**
	 * 组织编码
	 */
	@ApiModelProperty(value = "组织编码")
	private String code;
	/**
	 * 组织全称
	 */
	@ApiModelProperty(value = "组织全称")
	private String zzqc;
	/**
	 * 组织简称
	 */
	@ApiModelProperty(value = "组织简称")
	private String zzjc;
	/**
	 * 上级组织编码
	 */
	@ApiModelProperty(value = "上级组织编码")
	private String parentcode;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String ksrq;
	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String jsrq;
	/**
	 * 组织类型代码
	 */
	@ApiModelProperty(value = "组织类型代码")
	private String zzlxdm;
	/**
	 * 组织类型名称
	 */
	@ApiModelProperty(value = "组织类型名称")
	private String zzlxmc;
	/**
	 * 排序码
	 */
	@ApiModelProperty(value = "排序码")
	private String pxm;
	/**
	 * 时间戳
	 */
	@ApiModelProperty(value = "时间戳")
	private String loadNumber;


}
