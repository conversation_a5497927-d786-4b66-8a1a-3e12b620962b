/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.node.INode;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.system.entity.Dept;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeptVO对象", description = "DeptVO对象")
public class DeptVO extends Dept implements INode<DeptVO> {
	private static final long serialVersionUID = 1L;


	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 父节点ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long parentId;

	@ApiModelProperty(value = "状态名称")
	private String deptStatusName;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<DeptVO> children;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	public Boolean getHasChildren(){
		if(hasChildren!=null &&hasChildren){
			return true;
		}else{
			return CollectionUtil.isNotEmpty(this.children);
		}
	}

	@Override
	public List<DeptVO> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	/**
	 * 上级机构
	 */
	private String parentName;

	/**
	 * 机构类型名称
	 */
	private String deptCategoryName;

	private Integer deptCategory;

	private String ancestorName;

	private String title;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long key;
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long value;


}
