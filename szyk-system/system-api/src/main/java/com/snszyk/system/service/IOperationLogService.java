package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.system.entity.OperationLog;
import com.snszyk.system.vo.OperationLogVo;

import java.util.List;

/**
 * 操作日志服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface IOperationLogService extends IService<OperationLog> {

    /**
     * 异步保存操作日志
     *
     * @param operationLog 操作日志
     */
    void saveAsync(OperationLog operationLog);

    /**
     * 分页查询操作日志
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<OperationLogDto> pageList(OperationLogVo vo);

    /**
     * 根据业务ID查询操作日志
     *
     * @param businessModule 业务模块
     * @param businessId     业务ID
     * @return 操作日志列表
     */
    List<OperationLogDto> listByBusinessId(String businessModule, Long businessId);

    /**
     * 比较对象变更
     *
     * @param oldObj        旧对象
     * @param newObj        新对象
     * @param compareFields 比较字段
     * @return 变更字段列表
     */
    List<String> compareObjectChanges(Object oldObj, Object newObj, String[] compareFields);

    /**
     * 删除业务相关操作日志
     *
     * @param businessModule 业务模块
     * @param businessId     业务ID
     * @return 是否成功
     */
    boolean deleteByBusinessId(String businessModule, Long businessId);

    /**
     * 清理过期日志
     *
     * @param retentionDays 保留天数
     * @return 是否成功
     */
    boolean cleanExpiredLogs(int retentionDays);

    /**
     * 统计业务模块操作日志数量
     *
     * @param businessModule 业务模块
     * @return 数量
     */
    long countByBusinessModule(String businessModule);

    /**
     * 统计操作类型日志数量
     *
     * @param operationType 操作类型
     * @return 数量
     */
    long countByOperationType(String operationType);
}
