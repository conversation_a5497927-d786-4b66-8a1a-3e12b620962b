/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.task.dto.SzykMsgDto;
import com.snszyk.task.dto.SzykMsgGenDto;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.vo.SzykMsgVo;

import java.util.Date;
import java.util.List;

/**
 * 消息表 服务类
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface ISzykMsgService extends IBaseCrudService<SzykMsgDto, SzykMsgVo> {

	/**
	 * 生成消息提醒
	 * @param dto
	 * @return
	 */

	Integer genMsg(SzykMsgGenDto dto);

	/**
	 * 批量生成消息提醒
	 * @param list
	 * @return
	 */
	Integer genMsg(List<SzykMsgGenDto> list);

	Integer msgRead(String msgId);

	IPage<SzykMsgDto> PageList(SzykMsgVo v);

	boolean canSend(SzykMsgGenDto dto);

	Integer delByBusiness(List<ToDoBusinessTypeEnum> businessTypeEnums, Long businessId);

	List<SzykMsgDto> listByParam(Long receiverId, Long businessId, String content, Long deptId);

	String getReceiverNameByIds(String receiverIdsStr);

	List<SzykMsgDto> listByParam(Long userId, Long businessId, String content);


	/**
	 * 更改跳转的url
	 * @param msgIds
	 * @param detailUrl
	 * @return
	 */
	boolean updateUrl(List<Long> msgIds, String detailUrl);

    List<SzykMsgDto> listByBusinessIdAndContent(String businessType, Long businessId, String content);
    List<SzykMsgDto> listByBusinessIdAndType(String businessType, Long businessId);

	boolean updateById(SzykMsgVo copy);

	boolean updateConentById(Long id, String content, Integer zero, Date date);
}
