/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息通知下一步操作枚举
 * 1 查看详情 2 去处理 3 去审批 4 去提交 5去上传 6 去评价 7 去打分)")
 */
@Getter
@AllArgsConstructor
public enum NoticeNextOperationEnum {


	VIEW_DETAIL(1, "查看详情"),
	TO_HANDLE(2, "去处理"),
	TO_EXAMINE(3, "去审批"),
	TO_SUBMIT(4, "去提交"),
	TO_UPLOAD(5, "去上传"),
	TO_EVALUATE(6, "去评价"),
	TO_SCORE(7, "去打分"),
	TO_SELF_EVALUATION(8, "去自评"),
	TO_SEND(9, "去指派"),
	;

	final Integer code;
	final String desc;

	// TODO: 2023/5/10  ceshi
	public static NoticeNextOperationEnum getByCode(Integer code) {
		if (code == null) {
			return null;
		}
		for (NoticeNextOperationEnum operationEnum : NoticeNextOperationEnum.values()) {
			if (operationEnum.getCode().equals(code)) {
				return operationEnum;
			}
		}
		return null;
	}

}
