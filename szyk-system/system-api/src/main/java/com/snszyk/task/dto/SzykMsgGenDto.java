/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 消息提醒生成对象
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "消息提醒生成对象", description = "任务表")
@Accessors(chain = true)
public class SzykMsgGenDto extends BaseCrudDto {

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;

	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 跳转链接
	 */
	@ApiModelProperty(value = "跳转链接")
	private String url;

	/**
	 * 接收人id
	 */
	@ApiModelProperty(value = "接收人id")
	private Long receiverId;


	@ApiModelProperty(value = "标题")
	private String title;

	@ApiModelProperty(value = "下一步操作(1 查看详情 2 去处理 3 去审批 4 去提交 5去上传 6 去评价 7 去打分)")
	private Integer nextOperation;


	@ApiModelProperty(value = "业务范围id")
	private Long deptId;

	private String ext;

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SzykMsgGenDto that = (SzykMsgGenDto) o;
		return Objects.equals(receiverId, that.receiverId) &&
			Objects.equals(deptId, that.deptId) &&
			Objects.equals(content, that.content);
	}

	@Override
	public int hashCode() {
		return Objects.hash(receiverId, deptId, content);
	}
}
