/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 任务表实体类
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SzykTaskDto对象", description = "任务表")
public class SzykTaskDto extends BaseCrudDto {

	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String businessType;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型名称")
	private String businessTypeName;
	/**
	 * 业务id
	 */
	@ApiModelProperty(value = "业务id")
	private Long businessId;
	/**
	 * 跳转链接
	 */
	@ApiModelProperty(value = "跳转链接")
	private String url;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

	/**
	 * 任务处理状态：0待办1已办
	 */
	@ApiModelProperty(value = "任务处理状态：0待办1已办")
	private Integer taskStatus;
	/**
	 * 接收人id
	 */
	@ApiModelProperty(value = "接收人id")
	private String receiverId;
	/**
	 * 接收人姓名
	 */
	@ApiModelProperty(value = "接收人姓名")
	private String receiverName;
	/**
	 * 钉钉工作通知id
	 */
	@ApiModelProperty(value = "钉钉工作通知id")
	private String ddTaskId;

	@ApiModelProperty(value = "标题")
	private String title;

	@ApiModelProperty(value = "下一步操作(1 查看详情 2 去处理 3 去审批 4 去提交 5去上传 6 去评价 7 去打分 8去自评)")
	private Integer nextOperation;

	@ApiModelProperty(value = "下一步操作(1 查看详情 2 去处理 3 去审批 4 去提交 5去上传 6 去评价 7 去打分  8去自评)")
	private String nextOperationName;

	@ApiModelProperty(value = "已办后跳转的链接")
	private String detailUrl;

	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;

	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "业务范围id")
	private Long deptId;
}
