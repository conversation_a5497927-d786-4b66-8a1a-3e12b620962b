<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.snszyk</groupId>
    <artifactId>szyk-system</artifactId>
    <version>2.0.0.RELEASE</version>
  </parent>
  <groupId>com.snszyk</groupId>
  <artifactId>system-api</artifactId>
  <version>2.0.0.RELEASE</version>
  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
</project>
