package com.snszyk.annotation.aspect;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.service.IOperationLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Aspect
@Component
@Order(1)
@AllArgsConstructor
public class OperationLogAspect {

    private final IOperationLogService operationLogService;
    private final ExpressionParser parser = new SpelExpressionParser();

    @Pointcut("@annotation(com.snszyk.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            try {
                // 记录操作日志
                recordOperationLog(joinPoint, result, exception, startTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(ProceedingJoinPoint joinPoint, Object result,
                                   Throwable exception, long startTime) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            com.snszyk.annotation.OperationLog annotation = method.getAnnotation(com.snszyk.annotation.OperationLog.class);

            if (annotation == null) {
                return;
            }

            // 构建操作日志
            com.snszyk.system.entity.OperationLog logEntity = buildOperationLog(joinPoint, annotation, result, exception, startTime);

            // 异步保存日志
            operationLogService.saveAsync(logEntity);

        } catch (Exception e) {
            log.error("构建操作日志失败", e);
        }
    }

    /**
     * 构建操作日志对象
     */
    private com.snszyk.system.entity.OperationLog buildOperationLog(ProceedingJoinPoint joinPoint, com.snszyk.annotation.OperationLog annotation,
                                               Object result, Throwable exception, long startTime) {
        com.snszyk.system.entity.OperationLog logEntity = new com.snszyk.system.entity.OperationLog();

        // 基础信息
        logEntity.setBusinessModule(annotation.businessModule().getCode());
        logEntity.setOperationType(annotation.operationType().getCode());
        logEntity.setOperationDescription(annotation.description());
        logEntity.setOperationTime(LocalDateTime.now());

        // 执行时间
        long executionTime = System.currentTimeMillis() - startTime;
        logEntity.setExecutionTime(executionTime);

        // 操作结果
        if (exception != null) {
            logEntity.setOperationResult("FAILURE");
            if (annotation.logException()) {
                logEntity.setExceptionInfo(getExceptionInfo(exception));
            }
        } else {
            logEntity.setOperationResult("SUCCESS");
        }

        // 操作人信息
        setOperatorInfo(logEntity);

        // 请求信息
        setRequestInfo(logEntity);

        // 业务信息
        setBusinessInfo(logEntity, joinPoint, annotation, result);

        // 参数和结果
        setParamsAndResult(logEntity, joinPoint, annotation, result);

        return logEntity;
    }

    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(com.snszyk.system.entity.OperationLog logEntity) {
        try {
            if (AuthUtil.getUser() != null) {
                logEntity.setOperatorId(AuthUtil.getUserId());
                logEntity.setOperatorName(AuthUtil.getUserName());
                logEntity.setOperatorDeptId(Long.valueOf(AuthUtil.getDeptId()));
                logEntity.setOperatorDeptName(AuthUtil.getDeptName());
            }
        } catch (Exception e) {
            log.warn("获取操作人信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(com.snszyk.system.entity.OperationLog logEntity) {
        try {
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                logEntity.setClientIp(WebUtil.getIP(request));
                logEntity.setUserAgent(request.getHeader("User-Agent"));
                logEntity.setRequestUri(request.getRequestURI());
                logEntity.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
    }

    /**
     * 设置业务信息
     */
    private void setBusinessInfo(com.snszyk.system.entity.OperationLog logEntity, ProceedingJoinPoint joinPoint,
                               com.snszyk.annotation.OperationLog annotation, Object result) {
        try {
            // 解析业务ID
            if (!annotation.businessIdExpression().isEmpty()) {
                Object businessId = parseSpelExpression(annotation.businessIdExpression(),
                    joinPoint, result);
                if (businessId instanceof Number) {
                    logEntity.setBusinessId(((Number) businessId).longValue());
                }
            }

            // 解析业务名称
            if (!annotation.businessNameExpression().isEmpty()) {
                Object businessName = parseSpelExpression(annotation.businessNameExpression(),
                    joinPoint, result);
                if (businessName != null) {
                    logEntity.setBusinessName(businessName.toString());
                }
            }
        } catch (Exception e) {
            log.warn("解析业务信息失败", e);
        }
    }

    /**
     * 设置参数和结果
     */
    private void setParamsAndResult(com.snszyk.system.entity.OperationLog logEntity, ProceedingJoinPoint joinPoint,
                                  com.snszyk.annotation.OperationLog annotation, Object result) {
        try {
            // 记录请求参数
            if (annotation.logParams()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    Map<String, Object> params = new HashMap<>();
                    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                    String[] paramNames = signature.getParameterNames();

                    for (int i = 0; i < args.length && i < paramNames.length; i++) {
                        if (args[i] != null && !isIgnoredParam(args[i])) {
                            params.put(paramNames[i], args[i]);
                        }
                    }

                    if (!params.isEmpty()) {
                        logEntity.setNewData(JSON.toJSONString(params));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("记录参数和结果失败", e);
        }
    }

    /**
     * 解析SpEL表达式
     */
    private Object parseSpelExpression(String expression, ProceedingJoinPoint joinPoint, Object result) {
        try {
            EvaluationContext context = new StandardEvaluationContext();

            // 设置方法参数
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();

            for (int i = 0; i < paramNames.length && i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }

            // 设置返回结果
            if (result != null) {
                context.setVariable("result", result);
            }

            Expression exp = parser.parseExpression(expression);
            return exp.getValue(context);
        } catch (Exception e) {
            log.warn("解析SpEL表达式失败: {}", expression, e);
            return null;
        }
    }

    /**
     * 获取异常信息
     */
    private String getExceptionInfo(Throwable exception) {
        if (exception == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(exception.getClass().getSimpleName()).append(": ");
        sb.append(exception.getMessage());

        // 限制异常信息长度
        String exceptionInfo = sb.toString();
        if (exceptionInfo.length() > 2000) {
            exceptionInfo = exceptionInfo.substring(0, 2000) + "...";
        }

        return exceptionInfo;
    }

    /**
     * 判断是否为需要忽略的参数类型
     */
    private boolean isIgnoredParam(Object param) {
        if (param == null) {
            return true;
        }

        String className = param.getClass().getName();

        // 忽略Servlet相关对象
        return className.startsWith("javax.servlet") ||
               className.startsWith("org.springframework.web") ||
               className.startsWith("org.springframework.ui");
    }
}
