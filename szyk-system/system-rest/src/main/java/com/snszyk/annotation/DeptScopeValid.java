package com.snszyk.annotation;

import com.snszyk.annotation.enums.DeptScopeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @atuthor <PERSON>yu
 * @date 2024-02-18
 * @apiNote
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface DeptScopeValid {
	DeptScopeEnum type() default DeptScopeEnum.SELF;

	Class<?> interfaceClass() default Object.class;

	String methodName() default "";

	String paramName() default "createDept";
}
