package com.snszyk.annotation;

import com.snszyk.system.enums.BusinessModuleEnum;
import com.snszyk.system.enums.OperationTypeEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 业务模块
     */
    BusinessModuleEnum businessModule();

    /**
     * 操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 业务ID表达式（SpEL）
     */
    String businessIdExpression() default "";

    /**
     * 业务名称表达式（SpEL）
     */
    String businessNameExpression() default "";

    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default false;

    /**
     * 是否记录异常信息
     */
    boolean logException() default true;
}
