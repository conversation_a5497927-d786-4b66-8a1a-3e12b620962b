/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.builder.oss;

import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.SneakyThrows;
import com.snszyk.core.oss.OssTemplate;
import com.snszyk.core.oss.QiniuTemplate;
import com.snszyk.core.oss.props.OssProperties;
import com.snszyk.core.oss.rule.OssRule;
import com.snszyk.resource.entity.Oss;

/**
 * 七牛云存储构建类
 *
 * <AUTHOR>
 */
public class QiniuOssBuilder {

	@SneakyThrows
	public static OssTemplate template(Oss oss, OssRule ossRule) {
		OssProperties ossProperties = new OssProperties();
		ossProperties.setEndpoint(oss.getEndpoint());
		ossProperties.setAccessKey(oss.getAccessKey());
		ossProperties.setSecretKey(oss.getSecretKey());
		ossProperties.setBucketName(oss.getBucketName());
		Configuration cfg = new Configuration(Region.autoRegion());
		Auth auth = Auth.create(oss.getAccessKey(), oss.getSecretKey());
		UploadManager uploadManager = new UploadManager(cfg);
		BucketManager bucketManager = new BucketManager(auth, cfg);
		return new QiniuTemplate(auth, uploadManager, bucketManager, ossProperties, ossRule);
	}

}
