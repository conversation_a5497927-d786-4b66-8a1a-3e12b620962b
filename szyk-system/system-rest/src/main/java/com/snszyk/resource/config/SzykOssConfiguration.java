/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.config;

import com.snszyk.resource.builder.oss.OssBuilder;
import com.snszyk.resource.service.IOssService;
import lombok.AllArgsConstructor;
import com.snszyk.core.oss.props.OssProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Oss配置类
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class SzykOssConfiguration {

	private final OssProperties ossProperties;

	private final IOssService ossService;

	@Bean
	public OssBuilder ossBuilder() {
		return new OssBuilder(ossProperties, ossService);
	}

}
