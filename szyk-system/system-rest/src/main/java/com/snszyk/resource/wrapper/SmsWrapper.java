/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.wrapper;

import com.snszyk.system.cache.DictCache;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.resource.entity.Sms;
import com.snszyk.resource.vo.SmsVO;

import java.util.Objects;

/**
 * 短信配置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class SmsWrapper extends BaseEntityWrapper<Sms, SmsVO> {

	public static SmsWrapper build() {
		return new SmsWrapper();
	}

	@Override
	public SmsVO entityVO(Sms sms) {
		SmsVO smsVO = Objects.requireNonNull(BeanUtil.copy(sms, SmsVO.class));
		String categoryName = DictCache.getValue(DictEnum.SMS, sms.getCategory());
		String statusName = DictCache.getValue(DictEnum.YES_NO, sms.getStatus());
		smsVO.setCategoryName(categoryName);
		smsVO.setStatusName(statusName);
		return smsVO;
	}

}
