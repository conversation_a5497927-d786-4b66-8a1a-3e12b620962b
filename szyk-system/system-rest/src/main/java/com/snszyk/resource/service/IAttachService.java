/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.service;

import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.vo.AttachVO;
import com.snszyk.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.project.dto.SzykAttachDto;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 附件表 服务类
 *
 * <AUTHOR>
 */
public interface IAttachService extends BaseService<Attach> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attach
	 * @return
	 */
	IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachVO attach);

	/**
	 * 下载文件
	 * @param attachId 附件id
	 * @param request req
	 * @param response res
	 */
    void download(String attachId, HttpServletRequest request, HttpServletResponse response);

	Attach getByFileId(Long id);

	List<Attach> listByFileIds(List<Long> ids);
	List<SzykAttachDto> listDetailByFileIds(List<Long> ids);
}
