/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.wrapper;

import com.snszyk.system.cache.DictCache;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.resource.entity.Oss;
import com.snszyk.resource.vo.OssVO;

import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class OssWrapper extends BaseEntityWrapper<Oss, OssVO> {

	public static OssWrapper build() {
		return new OssWrapper();
	}

	@Override
	public OssVO entityVO(Oss oss) {
		OssVO ossVO = Objects.requireNonNull(BeanUtil.copy(oss, OssVO.class));
		String categoryName = DictCache.getValue(DictEnum.OSS, oss.getCategory());
		String statusName = DictCache.getValue(DictEnum.YES_NO, oss.getStatus());
		ossVO.setCategoryName(categoryName);
		ossVO.setStatusName(statusName);
		return ossVO;
	}

}
