/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.auth.granter;

import com.snszyk.auth.provider.ITokenGranter;
import com.snszyk.auth.provider.TokenParameter;
import com.snszyk.auth.utils.TokenUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.UserInfo;
import com.snszyk.system.service.*;
import lombok.AllArgsConstructor;
import com.snszyk.common.cache.CacheNames;
import com.snszyk.system.cache.ParamCache;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.auth.enums.UserEnum;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.List;

/**
 * 验证码TokenGranter
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CaptchaTokenGranter implements ITokenGranter {

	public static final String GRANT_TYPE = "captcha";
	public static final Integer FAIL_COUNT = 5;
	public static final String FAIL_COUNT_VALUE = "account.failCount";

	private final IUserService userService;
	private final IRoleService roleService;
	private final ITenantService tenantService;
	private final SzykRedis szykRedis;
	private final IUserDeptService userDeptService;
	private final IUserRoleService userRoleService;

	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		HttpServletRequest request = WebUtil.getRequest();

		// 获取用户绑定ID
		String headerDept = request.getHeader(TokenUtil.DEPT_HEADER_KEY);
		String headerRole = request.getHeader(TokenUtil.ROLE_HEADER_KEY);
		// 获取验证码信息
		String key = request.getHeader(TokenUtil.CAPTCHA_HEADER_KEY);
		String code = request.getHeader(TokenUtil.CAPTCHA_HEADER_CODE);
		// 获取验证码
		String redisCode = szykRedis.get(CacheNames.CAPTCHA_KEY + key);
		// 判断验证码
		if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
			throw new ServiceException(TokenUtil.CAPTCHA_NOT_CORRECT);
		}

		String tenantId = tokenParameter.getArgs().getStr("tenantId");
		String username = tokenParameter.getArgs().getStr("username");
		String password = tokenParameter.getArgs().getStr("password");

		// 判断登录是否锁定
		int cnt = Func.toInt(szykRedis.get(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username)), 0);
		int failCount = Func.toInt(ParamCache.getValue(FAIL_COUNT_VALUE), FAIL_COUNT);
		if (cnt >= failCount) {
			throw new ServiceException(TokenUtil.USER_HAS_TOO_MANY_FAILS);
		}

		UserInfo userInfo = null;
		if (Func.isNoneBlank(username, password)) {
			// 获取租户信息
			Tenant tenant = tenantService.getByTenantId(tenantId);
			if (TokenUtil.judgeTenant(tenant)) {
				throw new ServiceException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
			}
			// 获取用户类型
			String userType = tokenParameter.getArgs().getStr("userType");
			// 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
			if (userType.equals(UserEnum.WEB.getName())) {
				userInfo = userService.userInfo(tenantId, username, DigestUtil.hex(password), UserEnum.WEB);
			} else if (userType.equals(UserEnum.APP.getName())) {
				userInfo = userService.userInfo(tenantId, username, DigestUtil.hex(password), UserEnum.APP);
			} else {
				userInfo = userService.userInfo(tenantId, username, DigestUtil.hex(password), UserEnum.OTHER);
			}
		}
		// 错误次数锁定
		if (userInfo == null || userInfo.getUser() == null) {
			szykRedis.setEx(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username), cnt + 1, Duration.ofMinutes(30));
		}

		// 多部门情况下获取默认部门
		if(Func.isNotEmpty(userInfo) && Func.isNotEmpty(userInfo.getUser())){
			Long defaultDeptId = userDeptService.getDefaultDept(userInfo.getUser().getId());
			if(Func.isEmpty(defaultDeptId)){
				//业务范围为空则抛出异常
				throw new ServiceException(TokenUtil.USER_NOT_DEPT_SCOPE);
			}
			userInfo.getUser().setDeptId(String.valueOf(defaultDeptId));
			//根据业务范围查询角色
			List<Long> roleList = userRoleService.queryRoleByDeptId(userInfo.getUser().getId(),defaultDeptId);
			if(CollectionUtil.isNotEmpty(roleList)) {
				List<String> roleAliases = roleService.getRoleAliasesById(roleList);
				String roles = StringUtil.join(roleList);
				userInfo.getUser().setRoleId(roles);
				userInfo.setRoles(roleAliases);
			}
		}
		// 成功则清除登录错误次数
		szykRedis.del(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username));
		return userInfo;
	}

}
