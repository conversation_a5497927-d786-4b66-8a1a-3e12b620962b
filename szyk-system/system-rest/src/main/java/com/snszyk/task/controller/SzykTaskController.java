/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.tool.api.R;
import com.snszyk.task.dto.SzykTaskDto;
import com.snszyk.task.service.logic.SzykTaskLogicService;
import com.snszyk.task.vo.SzykTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 任务表 控制器
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME +"/szyk-task")
@Api(value = "待办任务通知", tags = "待办任务通知")
public class SzykTaskController extends BaseCrudController {


    private final SzykTaskLogicService szykTaskLogicService;

    @Override
    protected BaseCrudLogicService fetchBaseLogicService() {
        return szykTaskLogicService;
    }

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "SzykTaskVo")
    public R<IPage<SzykTaskDto>> page(SzykTaskVo v) {
        IPage<SzykTaskDto> pageQueryResult = szykTaskLogicService.pageList(v);
        return R.data(pageQueryResult);
    }

}
