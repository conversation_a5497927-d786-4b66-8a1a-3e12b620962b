/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.dingding.DingtalkService;
import com.snszyk.message.socket.ISocketService;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.service.IUserService;
import com.snszyk.task.dto.SzykMsgDto;
import com.snszyk.task.dto.SzykMsgGenDto;
import com.snszyk.task.entity.SzykMsg;
import com.snszyk.task.enums.DingTalkTypeEnum;
import com.snszyk.task.enums.NoticeNextOperationEnum;
import com.snszyk.task.enums.ToDoBusinessTypeEnum;
import com.snszyk.task.mapper.SzykMsgMapper;
import com.snszyk.task.service.ISzykMsgService;
import com.snszyk.task.vo.SzykMsgVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 消息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@AllArgsConstructor
@Service
@Slf4j
public class SzykMsgServiceImpl extends BaseCrudServiceImpl<SzykMsgMapper, SzykMsg, SzykMsgDto, SzykMsgVo> implements ISzykMsgService {


	private final IDictBizService dictBizService;

	private final DingtalkService dingtalkService;
	private final ISocketService socketService;

	private final IUserService userService;

	/**
	 * 生成消息提醒并发送
	 *
	 * ******跳转的url必须一致*******
	 *
	 * @param list
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer genMsg(List<SzykMsgGenDto> list) {
		if (CollectionUtil.isEmpty(list)) {
			return CommonConstant.ZERO;
		}
		SzykMsgGenDto msgGenDto = list.get(0);
		//1保存db
		List<SzykMsg> szykMsgList = BeanUtil.copy(list, SzykMsg.class);
		for (SzykMsg szykMsg : szykMsgList) {
			String receiverNames = getReceiverNameByIds(String.valueOf(szykMsg.getReceiverId()));
			szykMsg.setReceiverName(receiverNames);
		}
		boolean saved = saveBatch(szykMsgList);
		if (!saved) {
			log.error("保存消息提醒失败!" + JSON.toJSONString(list));
			return CommonConstant.ZERO;
		}
		//2.发送钉钉消息
		//2.发送钉钉消息
		List<Long> userIds = list.stream().map(e -> e.getReceiverId()).collect(Collectors.toList());
		//2 获取钉钉userid
		List<String> dingtalkUserIdsByUserIds = dingtalkService.getDingtalkUserIdsByUserIds(userIds);
		if (CollectionUtil.isEmpty(dingtalkUserIdsByUserIds)) {
			return CommonConstant.ZERO;
		}
		//3.发送钉钉消息
		Long dingtalkId = dingtalkService.asyncsendV2(dingtalkUserIdsByUserIds, DingTalkTypeEnum.MESSAGE, msgGenDto.getContent(), msgGenDto.getUrl(), NoticeNextOperationEnum.getByCode(msgGenDto.getNextOperation()));
		//4.修改ddTaskId
		for (SzykMsg szykMsg : szykMsgList) {
			if (dingtalkId != null) {
				szykMsg.setDdTaskId(String.valueOf(dingtalkId));
			}
		}
		boolean updateBatchById = updateBatchById(szykMsgList);
		if (!updateBatchById) {
			return CommonConstant.ZERO;
		}
		//5.发送socket通知
		return sendSocketMessage(BeanUtil.copy(szykMsgList, SzykMsgDto.class));
	}

	@Override
	public IPage<SzykMsgDto> PageList(SzykMsgVo v) {
		v.setReceiverId(AuthUtil.getUserId());
//		LambdaQueryWrapper<SzykMsg> wrapper = Wrappers.lambdaQuery(SzykMsg.class)
//			.eq(v.getReceiverId() != null, SzykMsg::getReceiverId, v.getReceiverId())
//			//v1.4 业务范围
//			.eq(v.getDeptId()!=null,SzykMsg::getDeptId,v.getDeptId())
//			.orderByDesc(SzykMsg::getCreateTime).orderByDesc(SzykMsg::getId);
//		Page<SzykMsg> page = page(new Page<>(v.getCurrent(), v.getSize()), wrapper);
		IPage<SzykMsgDto> szykTaskVoIPage = this.baseMapper.selectSzykMsgPage(new Page(v.getCurrent(), v.getSize()), v);
		String pattern = "<[^>]*>"; // 正则表达式匹配HTML标签
		Pattern r = Pattern.compile(pattern);
		List<SzykMsgDto> records = szykTaskVoIPage.getRecords();
		if (CollectionUtil.isEmpty(records)) {
			return szykTaskVoIPage;
		}
		List<DictBiz> todoBusinessTypeList = dictBizService.getList("todo_business_type");
		if (CollectionUtil.isEmpty(todoBusinessTypeList)) {
			return szykTaskVoIPage;
		}
		//业务类型字典
		Map<String, String> typeMap = todoBusinessTypeList.stream().collect(Collectors.toMap(e -> e.getDictKey(), e -> e.getDictValue(), (e1, e2) -> e2));
		for (SzykMsgDto record : records) {
			String businessType = record.getBusinessType();
			if (StringUtil.isBlank(businessType)) {
				continue;
			}
			String businessTypeName = typeMap.get(businessType);
			record.setBusinessTypeName(businessTypeName);
		}
		//content 分割
		for (SzykMsgDto record : records) {
			String content = record.getContent();
			if (StringUtil.isBlank(content) || !content.contains("：")) {
				continue;
			}
			String[] split = content.split("：", 2);
			record.setContent(split[1]);
			record.setContentTitle(split[0]);
			//去掉html的标签
			Matcher m = r.matcher(record.getContent());
			content = m.replaceAll("");// 将匹配到的标签替换为空字符串
			record.setContent(content);
		}
		return szykTaskVoIPage;
	}

	/**
	 * 消息已读
	 *
	 * @param msgId
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer msgRead(String msgId) {
		boolean update = this.lambdaUpdate().eq(SzykMsg::getId, msgId)
			.set(true, SzykMsg::getMsgStatus, CommonConstant.ONE).update();
		return update ? CommonConstant.ONE : CommonConstant.ZERO;
	}

	/**
	 * 生成消息提醒
	 *
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Integer genMsg(SzykMsgGenDto dto) {
		//1保存db
		SzykMsgVo msgVo = BeanUtil.copy(dto, SzykMsgVo.class);
		String receiverNames = getReceiverNameByIds(String.valueOf(msgVo.getReceiverId()));
		msgVo.setReceiverName(receiverNames);
		SzykMsgDto szykMsgDto = save(msgVo);
		if (szykMsgDto == null) {
			log.error("保存消息提醒失败!" + JSON.toJSONString(dto));
			return CommonConstant.ZERO;
		}
		//2 获取钉钉userid
		List<String> dingtalkUserIdsByUserIds = dingtalkService.getDingtalkUserIdsByUserIds(Arrays.asList(dto.getReceiverId()));
		if (CollectionUtil.isEmpty(dingtalkUserIdsByUserIds)) {
			return CommonConstant.ZERO;
		}
		//3.发送钉钉消息
		Long dingtalkId = dingtalkService.asyncsendV2(dingtalkUserIdsByUserIds, DingTalkTypeEnum.MESSAGE, szykMsgDto.getContent(), szykMsgDto.getUrl(), NoticeNextOperationEnum.VIEW_DETAIL);
		//4.修改ddTaskId
		boolean update = this.lambdaUpdate().eq(SzykMsg::getId, szykMsgDto.getId())
			.set(SzykMsg::getDdTaskId, dingtalkId).update();
		if (!update) {
			return CommonConstant.ZERO;
		}
		sendSocketMessage(Arrays.asList(szykMsgDto));
		return CommonConstant.ONE;
	}


	/**
	 * 发送socket消息
	 *
	 * @param list
	 * @return
	 */
	private Integer sendSocketMessage(List<SzykMsgDto> list) {

		for (SzykMsgDto taskGenDto : list) {
			String content = taskGenDto.getContent();
			if (StringUtil.isBlank(content) || !content.contains("：")) {
				continue;
			}
			String[] split = content.split("：", 2);

			taskGenDto.setContent(split[1]);
			taskGenDto.setContentTitle(split[0]);

			ArrayList<Long> userIds = new ArrayList<>();
			userIds.add(taskGenDto.getReceiverId());
			JSONObject jsonObject = new JSONObject();
			//消息提醒 2
			jsonObject.put("type", "2");
			jsonObject.put("record", taskGenDto);
			socketService.sendInfo(jsonObject.toJSONString(), userIds.toArray(new Long[userIds.size()]));
		}
		return CommonConstant.ONE;
	}

	@Override
	public Integer delByBusiness(List<ToDoBusinessTypeEnum> businessTypeEnums, Long businessId) {
		//查询待办任务
		List<String> businessTypeCodes = businessTypeEnums.stream().map(e -> e.getCode()).collect(Collectors.toList());
		List<SzykMsg> list = this.lambdaQuery().in(SzykMsg::getBusinessType, businessTypeCodes)
			.eq(SzykMsg::getBusinessId, businessId).list();
		if (CollectionUtil.isNotEmpty(list)) {
			//删除待办任务
			int result = this.baseMapper.deleteBatchIds(list.stream().map(e -> e.getId()).collect(Collectors.toList()));
			if (result <= CommonConstant.ZERO) {
				log.error("删除消息通知失败!业务类型{},业务id{}", businessTypeCodes, businessId);
				return CommonConstant.ZERO;
			}

		}
		return CommonConstant.ONE;
	}

	@Override

	public boolean canSend(SzykMsgGenDto dto) {
		SzykMsgVo msgVo = BeanUtil.copy(dto, SzykMsgVo.class);
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime start = now.minusMinutes(10);


		List<SzykMsg> list = this.lambdaQuery().eq(SzykMsg::getReceiverId, msgVo.getReceiverId())
			.eq(SzykMsg::getContent, msgVo.getContent())
			.eq(SzykMsg::getBusinessType, msgVo.getBusinessType()).eq(SzykMsg::getBusinessId, msgVo.getBusinessId())
			.between(SzykMsg::getCreateTime, start, now).list();
		if (CollectionUtil.isNotEmpty(list)) {
			return false;
		}
		return true;

	}

	@Override
	public List<SzykMsgDto> listByBusinessIdAndContent(String businessType, Long businessId, String content) {
		if (StringUtil.isBlank(businessType) || businessId == null || StringUtil.isBlank(content)) {
			return new ArrayList<>();
		}
		return BeanUtil.copy(this.lambdaQuery().eq(SzykMsg::getBusinessType, businessType)
			.eq(SzykMsg::getBusinessId, businessId)
			.eq(SzykMsg::getContent, content).list(), SzykMsgDto.class);
	}

	@Override
	public List<SzykMsgDto> listByBusinessIdAndType(String businessType, Long businessId) {
		if (StringUtil.isBlank(businessType) || businessId == null) {
			return new ArrayList<>();
		}
		return BeanUtil.copy(this.lambdaQuery().eq(SzykMsg::getBusinessType, businessType)
			.eq(SzykMsg::getBusinessId, businessId)
			.list(), SzykMsgDto.class);
	}

	@Override
	public boolean updateById(SzykMsgVo copy) {
        return updateById(BeanUtil.copy(copy, SzykMsg.class));
	}

	@Override
	public boolean updateConentById(Long id, String content, Integer zero, Date date) {
		return this.lambdaUpdate().eq(SzykMsg::getId, id).set(SzykMsg::getContent, content)
			.set(BaseCrudEntity::getUpdateTime,date)
			.set(SzykMsg::getMsgStatus, zero).update();
	}

	/**
	 * 更改跳转的url
	 * @param msgIds
	 * @param detailUrl
	 * @return
	 */
	@Override
	public boolean updateUrl(List<Long> msgIds, String detailUrl) {
		if (CollectionUtil.isEmpty(msgIds) || StringUtil.isBlank(detailUrl)) {
			return false;
		}
		boolean update = this.lambdaUpdate().set(true, SzykMsg::getUrl, detailUrl)
			.in(SzykMsg::getId, msgIds).update();
		return update;
	}

	/**
	 * 根据条件查询
	 * @param receiverId
	 * @param businessId
	 * @param content
	 * @return
	 */
	@Override
	public List<SzykMsgDto> listByParam(Long receiverId, Long businessId, String content) {
		List<SzykMsg> list = this.lambdaQuery().eq(receiverId != null, SzykMsg::getReceiverId, receiverId)
			.eq(businessId != null, SzykMsg::getBusinessId, businessId)
			.eq(StringUtil.isNotBlank(content), SzykMsg::getContent, content).list();
		if (CollectionUtil.isEmpty(list)) {
			return new ArrayList<>();
		}
		return BeanUtil.copy(list, SzykMsgDto.class);
	}

	/**
	 * 根据条件查询
	 * @param receiverId
	 * @param businessId
	 * @param content
	 * @return
	 */
	@Override
	public List<SzykMsgDto> listByParam(Long receiverId, Long businessId, String content, Long deptId) {
		List<SzykMsg> list = this.lambdaQuery().eq(receiverId != null, SzykMsg::getReceiverId, receiverId)
			.eq(businessId != null, SzykMsg::getBusinessId, businessId)
			.eq(SzykMsg::getDeptId, deptId)
			.eq(StringUtil.isNotBlank(content), SzykMsg::getContent, content).list();
		if (CollectionUtil.isEmpty(list)) {
			return new ArrayList<>();
		}
		return BeanUtil.copy(list, SzykMsgDto.class);
	}

	@Override
	public String getReceiverNameByIds(String receiverIdsStr) {
		String result = null;
		if (StringUtil.isBlank(receiverIdsStr)) {
			return result;
		}
		String[] split = receiverIdsStr.split(",");
		List<String> idList = Arrays.asList(split);

		List<User> users = userService.listByIds(idList);
		if (CollectionUtil.isEmpty(users)) {
			return StringPool.EMPTY;
		}
		Map<String, String> userMap = users.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), e -> e.getRealName()));
		StringBuilder stringBuilder = new StringBuilder();
		for (String s : idList) {
			stringBuilder.append(userMap.get(s));
			stringBuilder.append(",");
		}
		String string = stringBuilder.toString();
		if (string.length() >= 1) {
			result = string.substring(0, string.length() - 1);
		}
		return result;

	}

	public static void main(String[] args) {
		String content = "1:2:3";
		String[] split = content.split(":", 2);
		for (String s : split) {
			System.out.println(s);

		}
	}
}
