/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.entity;

import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息表实体类
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SzykMsg extends BaseCrudEntity {

	/**
	* 业务类型
	*/
	private String businessType;
	/**
	* 内容
	*/
	private String content;
	/**
	* 消息阅读状态：0未读1已读
	*/
	private Integer msgStatus;
	/**
	* 接收人id
	*/
	private Long receiverId;
	/**
	* 接收人姓名
	*/
	private String receiverName;
	/**
	* 钉钉工作通知id
	*/
	private String ddTaskId;
	/**
	 * 业务id
	 */
	private Long businessId;
	/**
	 * 跳转链接
	 */
	private String url;

	private Long deptId;


}
