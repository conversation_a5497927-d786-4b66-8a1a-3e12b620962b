/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.task.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.task.dto.SzykMsgDto;
import com.snszyk.task.entity.SzykMsg;
import com.snszyk.task.vo.SzykMsgVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface SzykMsgMapper extends BaseMapper<SzykMsg> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param szykMsg
	 * @return
	 */
	IPage<SzykMsgDto> selectSzykMsgPage( IPage page, @Param("vo") SzykMsgVo szykMsg);

}
