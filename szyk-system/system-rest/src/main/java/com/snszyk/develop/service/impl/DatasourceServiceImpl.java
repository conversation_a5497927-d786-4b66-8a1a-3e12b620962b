/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.develop.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.develop.entity.Datasource;
import com.snszyk.develop.mapper.DatasourceMapper;
import com.snszyk.develop.service.IDatasourceService;
import org.springframework.stereotype.Service;

/**
 * 数据源配置表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DatasourceServiceImpl extends BaseServiceImpl<DatasourceMapper, Datasource> implements IDatasourceService {

}
