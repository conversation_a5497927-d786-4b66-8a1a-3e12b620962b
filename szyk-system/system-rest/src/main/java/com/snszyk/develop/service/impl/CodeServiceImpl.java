/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.develop.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.develop.entity.Code;
import com.snszyk.develop.mapper.CodeMapper;
import com.snszyk.develop.service.ICodeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class CodeServiceImpl extends ServiceImpl<CodeMapper, Code> implements ICodeService {

	@Override
	public boolean submit(Code code) {
		code.setIsDeleted(SzykConstant.DB_NOT_DELETED);
		return saveOrUpdate(code);
	}

}
