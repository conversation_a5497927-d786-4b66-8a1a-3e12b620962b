package com.snszyk.dingding;

/**
 * 钉钉常量
 */
public interface DingtalkConstant {
	/**
	 * 获取token
	 */
	String TOKEN_URL = "https://oapi.dingtalk.com/gettoken";
	/**
	 * 发送通知
	 */
	String SEND_MESSAGE_URL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";
	/**
	 * 获取用户id
	 */
	String GET_USER_ID_URL = "https://oapi.dingtalk.com/topapi/v2/user/getbymobile";
	/**
	 * 撤回消息通知,只能撤回24小时之内的
	 */
	String RE_CALL = "https://oapi.dingtalk.com/topapi/message/corpconversation/recall";

}
