/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.wrapper;

import com.snszyk.system.cache.DictCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.system.entity.Menu;
import com.snszyk.system.vo.MenuVO;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class MenuWrapper extends BaseEntityWrapper<Menu, MenuVO> {

	public static MenuWrapper build() {
		return new MenuWrapper();
	}

	@Override
	public MenuVO entityVO(Menu menu) {
		MenuVO menuVO = Objects.requireNonNull(BeanUtil.copy(menu, MenuVO.class));
		if (Func.equals(menu.getParentId(), SzykConstant.TOP_PARENT_ID)) {
			menuVO.setParentName(SzykConstant.TOP_PARENT_NAME);
		} else {
			Menu parent = SysCache.getMenu(menu.getParentId());
			if (parent != null) {
				menuVO.setParentName(parent.getName());
			}
		}
		String category = DictCache.getValue(DictEnum.MENU_CATEGORY, Func.toInt(menuVO.getCategory()));
		String action = DictCache.getValue(DictEnum.BUTTON_FUNC, Func.toInt(menuVO.getAction()));
		String open = DictCache.getValue(DictEnum.YES_NO, Func.toInt(menuVO.getIsOpen()));
		menuVO.setCategoryName(category);
		menuVO.setActionName(action);
		menuVO.setIsOpenName(open);
		return menuVO;
	}

	public List<MenuVO> listNodeVO(List<Menu> list) {
		List<MenuVO> collect = list.stream().map(menu -> BeanUtil.copy(menu, MenuVO.class)).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

	public List<MenuVO> listNodeLazyVO(List<MenuVO> list) {
		return ForestNodeMerger.merge(list);
	}

}
