<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.system.mapper.SysOperationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sysOperationRecordResultMap" type="com.snszyk.system.entity.SysOperationRecord">
        <id column="id" property="id"/>
        <result column="business_module" property="businessModule"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="business_name" property="businessName"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_desc" property="operationDesc"/>
        <result column="operation_time" property="operationTime"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_dept_id" property="operatorDeptId"/>
        <result column="operator_dept_name" property="operatorDeptName"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="request_ip" property="requestIp"/>
        <result column="request_uri" property="requestUri"/>
        <result column="request_method" property="requestMethod"/>
        <result column="old_data" property="oldData"/>
        <result column="new_data" property="newData"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, business_module, business_type, business_id, business_name, operation_type, operation_desc,
        operation_time, operator_id, operator_name, operator_dept_id, operator_dept_name, org_id, org_name,
        request_ip, request_uri, request_method, old_data, new_data, create_user, create_dept, create_time,
        update_user, update_time, status, is_deleted
    </sql>

    <!-- 分页查询操作记录 -->
    <select id="selectPageList" resultMap="sysOperationRecordResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM sys_operation_record
        <where>
            is_deleted = 0
            <if test="vo.orgId != null">
                AND org_id = #{vo.orgId}
            </if>
            <if test="vo.systemName != null and vo.systemName != ''">
                AND business_name LIKE CONCAT('%', #{vo.systemName}, '%')
            </if>
            <if test="vo.startUpdateTime != null">
                AND operation_time &gt;= #{vo.startUpdateTime}
            </if>
            <if test="vo.endUpdateTime != null">
                AND operation_time &lt;= #{vo.endUpdateTime}
            </if>
            <if test="vo.operationType != null and vo.operationType != ''">
                AND operation_type = #{vo.operationType}
            </if>
            <if test="vo.businessModule != null and vo.businessModule != ''">
                AND business_module = #{vo.businessModule}
            </if>
            <if test="vo.businessType != null and vo.businessType != ''">
                AND business_type = #{vo.businessType}
            </if>
            <if test="vo.operatorId != null">
                AND operator_id = #{vo.operatorId}
            </if>
            <if test="vo.operatorName != null and vo.operatorName != ''">
                AND operator_name LIKE CONCAT('%', #{vo.operatorName}, '%')
            </if>
            <if test="vo.operatorDeptId != null">
                AND operator_dept_id = #{vo.operatorDeptId}
            </if>
        </where>
        ORDER BY operation_time DESC
    </select>

</mapper>
