/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.system.entity.SysOperationRecord;
import com.snszyk.system.vo.SysOperationRecordPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 操作记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Mapper
public interface SysOperationRecordMapper extends BaseMapper<SysOperationRecord> {

    /**
     * 分页查询操作记录
     *
     * @param page 分页对象
     * @param vo   查询条件
     * @return 分页结果
     */
    IPage<SysOperationRecord> selectPageList(Page<SysOperationRecord> page, @Param("vo") SysOperationRecordPageVo vo);
}
