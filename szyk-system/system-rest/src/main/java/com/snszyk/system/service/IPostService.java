/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.entity.Post;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.PostAttilaVO;
import com.snszyk.system.vo.PostTreeVO;
import com.snszyk.system.vo.PostVO;

import java.util.List;

/**
 * 岗位表 服务类
 *
 * <AUTHOR>
 */
public interface IPostService extends BaseService<Post> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param post
	 * @return
	 */
	IPage<PostVO> selectPostPage(IPage<PostVO> page, PostVO post);

	/**
	 * 获取岗位ID
	 *
	 * @param tenantId
	 * @param postNames
	 * @return
	 */
	String getPostIds(String tenantId, String postNames);

	/**
	 * 获取岗位ID
	 *
	 * @param tenantId
	 * @param postNames
	 * @return
	 */
	String getPostIdsByFuzzy(String tenantId, String postNames);

	/**
	 * 获取岗位名
	 *
	 * @param postIds
	 * @return
	 */
	List<String> getPostNames(String postIds);

	/**
	 * 校验并删除岗位
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemovePost(List<Long> ids);

	/**
	 * 岗位列表
	 *
	 * @param tenantId
	 * @return
	 */
	List<PostAttilaVO> attilaList(String tenantId);

	/**
	 * 查询岗位和岗位下的人员
	 *
	 * @return
	 */
	List<PostTreeVO> selectUserTree(Long orgId, String tenantId);

	List<PostVO> selectPostListByUserId(Long userId);
}
