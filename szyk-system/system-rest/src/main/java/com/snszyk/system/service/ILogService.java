/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.snszyk.core.log.model.LogApi;
import com.snszyk.core.log.model.LogError;
import com.snszyk.core.log.model.LogUsual;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface ILogService {

	/**
	 * 保存通用日志
	 *
	 * @param log
	 * @return
	 */
	Boolean saveUsualLog(LogUsual log);

	/**
	 * 保存操作日志
	 *
	 * @param log
	 * @return
	 */
	Boolean saveApiLog(LogApi log);

	/**
	 * 保存错误日志
	 *
	 * @param log
	 * @return
	 */
	Boolean saveErrorLog(LogError log);

}
