package com.snszyk.system.enums;


public enum MenuEnum {

//	SPECIALIZED_CHECK_SCORE("specialized-check-score", "专项检查-打分"),
	SELF_EVALUATION_START("self_evaluation_start", "专项检查-自评编辑"),
	PROJECT_DISPATCHING_EDIT("project-dispatching-edit", "项目调度-编辑"),
	SELF_REPORT_EDIT("self-report-edit", "上传自评报告-编辑"),



	;

	private String code;
	private String name;

	MenuEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public String getCode() {
		return code;
	}
}
