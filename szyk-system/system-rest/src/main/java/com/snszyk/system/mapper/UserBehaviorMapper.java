package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.entity.UserBehavior;
import com.snszyk.system.entity.UserOnlineDuration;
import com.snszyk.system.userbehavior.ActiveUserDTO;
import com.snszyk.system.userbehavior.PageViewAnalyseDTO;
import com.snszyk.system.userbehavior.VisitorTrendDTO;
import com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto;
import com.snszyk.zbusiness.stat.dto.LoginLogDto;
import com.snszyk.zbusiness.stat.vo.LoginLogQVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface UserBehaviorMapper extends BaseMapper<UserBehavior> {

	/**
	 * 今日浏览量
	 * @return
	 */
	Integer countCurrentFlow();

	/**
	 * 今日访客数
	 * @return
	 */
	Integer countCurrentVisitor();

	/**
	 * 今日IP数
	 * @return
	 */
	Integer countCurrentIpAddress();

	/**
	 * 今日浏览量趋势
	 * @return
	 */
	List<String> currentPageView();

	/**
	 * 近7日访客趋势
	 * @param date
	 * @return
	 */
	List<VisitorTrendDTO> visitorTrend(@Param("date") LocalDate date);

	/**
	 * 浏览器占比分析
	 * @param date
	 * @return
	 */
	List<String> browserRate(@Param("date") LocalDate date);

	/**
	 * 活跃用户
	 * @param date
	 * @return
	 */
	List<ActiveUserDTO> countUserFlow(@Param("date") LocalDate date);

	/**
	 * 菜单访问分析
	 * @param date
	 * @return
	 */
	List<PageViewAnalyseDTO> pageViewMenu(@Param("date") LocalDate date);

	/**
	 * 页面访客数
	 * @return
	 */
	Integer countPageVisitor(@Param("date") LocalDate date,@Param("menuId") Long menuId);

	/**
	 * 根据日期查询list
	 * @param date
	 * @return
	 */
    List<DeptDTO> listByDate(@Param("date") LocalDate date);

	/**
	 * 提取登录的用户
	 * @param startOfMonth
	 * @param endOfMonth
	 * @return
	 */
    List<IndicatorLogExtractDto> indicatorCountUserLoginByOrg(@Param("startOfMonth") LocalDateTime startOfMonth, @Param("endOfMonth") LocalDateTime endOfMonth);

	/**
	 * 提取活跃的用户
	 * @return
	 */
	List<IndicatorLogExtractDto> indicatorActiveUserByOrg(@Param("paramTime") LocalDateTime paramTime, @Param("year") Integer year, @Param("month") Integer month);

	UserOnlineDuration isLogin(@Param("userId") Long id, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

	IPage<LoginLogDto> loginLog(@Param("v") LoginLogQVo v);
}
