/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.service.ISzykDeptMdmService;
import com.snszyk.system.vo.SzykDeptMdmTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 机构表（数据湖） 控制器
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("szyk-system/szykdeptmdm")
@Api(value = "机构表（数据湖）", tags = "机构表（数据湖）接口")
public class SzykDeptMdmController extends BaseCrudController {

    private final ISzykDeptMdmService szykDeptMdmLogicService;

	/**
	 * 懒加载获取部门树形结构
	 */
	@GetMapping("/lazyTreeByParent")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<SzykDeptMdmTreeVo>> lazyTreeByParent(String tenantId, String parentCode, SzykUser szykUser) {
		List<SzykDeptMdmTreeVo> tree = szykDeptMdmLogicService.lazyTreeByParent(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()), parentCode);
		return R.data(tree);
	}


	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return null;
	}
}
