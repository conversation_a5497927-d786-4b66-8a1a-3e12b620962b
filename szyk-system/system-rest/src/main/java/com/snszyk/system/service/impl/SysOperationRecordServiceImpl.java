/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.dto.FieldChangeDto;
import com.snszyk.system.dto.SysOperationRecordDto;
import com.snszyk.system.entity.SysOperationRecord;
import com.snszyk.system.entity.SysOperationRecordExtension;
import com.snszyk.system.enums.BusinessTypeEnum;
import com.snszyk.system.enums.OperationTypeEnum;
import com.snszyk.system.mapper.SysOperationRecordExtensionMapper;
import com.snszyk.system.mapper.SysOperationRecordMapper;
import com.snszyk.system.service.ISysOperationRecordService;
import com.snszyk.system.vo.SysOperationRecordPageVo;
import com.snszyk.system.vo.SysOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 操作记录服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysOperationRecordServiceImpl extends BaseCrudServiceImpl<SysOperationRecordMapper, SysOperationRecord, SysOperationRecordDto, SysOperationRecordVo> 
        implements ISysOperationRecordService {

    private final SysOperationRecordMapper operationRecordMapper;
    private final SysOperationRecordExtensionMapper extensionMapper;

    @Override
    public IPage<SysOperationRecordDto> pageList(SysOperationRecordPageVo vo) {
        Page<SysOperationRecord> page = new Page<>(vo.getCurrent(), vo.getSize());
        IPage<SysOperationRecord> pages = operationRecordMapper.selectPageList(page, vo);
        
        // 转换为DTO并添加枚举名称
        return pages.convert(record -> {
            SysOperationRecordDto dto = BeanUtil.copy(record, SysOperationRecordDto.class);
            
            // 设置业务模块名称
            dto.setBusinessModuleName(record.getBusinessModule());
            
            // 设置业务类型名称
            BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(record.getBusinessType());
            if (businessType != null) {
                dto.setBusinessTypeName(businessType.getName());
            }
            
            // 设置操作类型名称
            OperationTypeEnum operationType = OperationTypeEnum.getByCode(record.getOperationType());
            if (operationType != null) {
                dto.setOperationTypeName(operationType.getName());
            }
            
            return dto;
        });
    }

    @Override
    @Async
    public void saveAsync(SysOperationRecordVo vo) {
        try {
            SysOperationRecord record = BeanUtil.copy(vo, SysOperationRecord.class);
            record.setOperationTime(LocalDateTime.now());
            operationRecordMapper.insert(record);
            log.debug("异步保存操作记录成功: {}", record.getId());
        } catch (Exception e) {
            log.error("异步保存操作记录失败", e);
        }
    }

    @Override
    public Object getBusinessData(String businessType, Long businessId) {
        // 根据业务类型获取对应的业务数据
        // 这里需要根据具体的业务类型来实现
        // 暂时返回null，具体实现需要根据业务需求来完成
        return null;
    }

    @Override
    public SysOperationRecordDto getDetailWithChanges(Long recordId) {
        SysOperationRecord record = operationRecordMapper.selectById(recordId);
        if (record == null) {
            return null;
        }
        
        SysOperationRecordDto dto = BeanUtil.copy(record, SysOperationRecordDto.class);
        
        // 获取字段变更详情
        List<FieldChangeDto> fieldChanges = getFieldChangesByRecordId(recordId);
        dto.setFieldChanges(fieldChanges);
        
        return dto;
    }

    @Override
    public List<FieldChangeDto> compareFields(String businessType, Object oldData, Object newData) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        if (oldData == null && newData == null) {
            return changes;
        }
        
        if (oldData == null) {
            // 新增操作
            return extractFieldsFromObject(newData, true);
        }
        
        if (newData == null) {
            // 删除操作
            return extractFieldsFromObject(oldData, false);
        }
        
        // 更新操作，比较字段差异
        Class<?> clazz = oldData.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object oldValue = field.get(oldData);
                Object newValue = field.get(newData);
                
                if (!Objects.equals(oldValue, newValue)) {
                    String fieldName = field.getName();
                    String fieldLabel = getFieldLabel(field);
                    String oldValueStr = oldValue != null ? oldValue.toString() : "";
                    String newValueStr = newValue != null ? newValue.toString() : "";
                    
                    FieldChangeDto change = new FieldChangeDto(fieldName, fieldLabel, oldValueStr, newValueStr);
                    changes.add(change);
                }
            } catch (IllegalAccessException e) {
                log.warn("无法访问字段: {}", field.getName(), e);
            }
        }
        
        return changes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFieldChanges(Long recordId, String businessType, List<FieldChangeDto> fieldChanges) {
        if (ObjectUtil.isEmpty(fieldChanges)) {
            return;
        }
        
        for (FieldChangeDto change : fieldChanges) {
            if (change.getHasChanged()) {
                SysOperationRecordExtension extension = new SysOperationRecordExtension();
                extension.setRecordId(recordId);
                extension.setBusinessType(businessType);
                extension.setFieldName(change.getFieldName());
                extension.setFieldLabel(change.getFieldLabel());
                extension.setOldValue(change.getOldValue());
                extension.setNewValue(change.getNewValue());
                extension.setOldDisplayValue(change.getOldDisplayValue());
                extension.setNewDisplayValue(change.getNewDisplayValue());
                extension.setFieldType(change.getFieldType());
                
                extensionMapper.insert(extension);
            }
        }
    }

    @Override
    public List<FieldChangeDto> getFieldChangesByRecordId(Long recordId) {
        LambdaQueryWrapper<SysOperationRecordExtension> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOperationRecordExtension::getRecordId, recordId);
        queryWrapper.orderByAsc(SysOperationRecordExtension::getId);
        
        List<SysOperationRecordExtension> extensions = extensionMapper.selectList(queryWrapper);
        
        return extensions.stream()
                .map(ext -> new FieldChangeDto(
                        ext.getFieldName(),
                        ext.getFieldLabel(),
                        ext.getOldValue(),
                        ext.getNewValue(),
                        ext.getOldDisplayValue(),
                        ext.getNewDisplayValue()
                ))
                .toList();
    }

    /**
     * 从对象中提取字段信息
     */
    private List<FieldChangeDto> extractFieldsFromObject(Object obj, boolean isNew) {
        List<FieldChangeDto> changes = new ArrayList<>();
        
        if (obj == null) {
            return changes;
        }
        
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);
                
                if (value != null) {
                    String fieldName = field.getName();
                    String fieldLabel = getFieldLabel(field);
                    String valueStr = value.toString();
                    
                    FieldChangeDto change = isNew 
                            ? new FieldChangeDto(fieldName, fieldLabel, "", valueStr)
                            : new FieldChangeDto(fieldName, fieldLabel, valueStr, "");
                    changes.add(change);
                }
            } catch (IllegalAccessException e) {
                log.warn("无法访问字段: {}", field.getName(), e);
            }
        }
        
        return changes;
    }

    /**
     * 获取字段标签
     */
    private String getFieldLabel(Field field) {
        // 这里可以通过注解或其他方式获取字段的中文标签
        // 暂时返回字段名
        return field.getName();
    }
}
