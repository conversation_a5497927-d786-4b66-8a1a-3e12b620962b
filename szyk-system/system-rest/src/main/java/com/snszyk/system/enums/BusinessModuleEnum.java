package com.snszyk.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务模块枚举
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Getter
@AllArgsConstructor
public enum BusinessModuleEnum {

    /**
     * 互联网资产
     */
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产"),

    /**
     * 设备资产
     */
    EQUIPMENT_ASSET("EQUIPMENT_ASSET", "设备资产"),

    /**
     * 信息系统
     */
    INFORMATION_SYSTEM("INFORMATION_SYSTEM", "信息系统"),

    /**
     * 项目管理
     */
    PROJECT_MANAGEMENT("PROJECT_MANAGEMENT", "项目管理"),

    /**
     * 人员管理
     */
    PERSONNEL_MANAGEMENT("PERSONNEL_MANAGEMENT", "人员管理");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;
}
