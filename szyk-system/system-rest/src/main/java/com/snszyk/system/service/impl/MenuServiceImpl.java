/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.node.TreeNode;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.dto.MenuDTO;
import com.snszyk.system.entity.*;
import com.snszyk.system.mapper.MenuMapper;
import com.snszyk.system.service.IMenuService;
import com.snszyk.system.service.IRoleMenuService;
import com.snszyk.system.service.IRoleScopeService;
import com.snszyk.system.service.ITopMenuSettingService;
import com.snszyk.system.vo.MenuVO;
import com.snszyk.system.wrapper.MenuWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.snszyk.core.cache.constant.CacheConstant.MENU_CACHE;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {

	private final IRoleMenuService roleMenuService;
	private final IRoleScopeService roleScopeService;
	private final ITopMenuSettingService topMenuSettingService;
	private final static String PARENT_ID = "parentId";
	private final static Integer MENU_CATEGORY = 1;

	@Override
	public List<MenuVO> lazyList(Long parentId, Map<String, Object> param, String type) {
		if (Func.isEmpty(Func.toStr(param.get(PARENT_ID)))) {
			parentId = null;
		}
		List<MenuVO> lazyList = baseMapper.lazyList(parentId, param, type);

		//过滤搜索时（name、code、alias） - 查询符合搜索条件的菜单
		if (CollectionUtil.isNotEmpty(lazyList)
			&& CollectionUtil.isNotEmpty(param)
			&& (param.containsKey("name") || param.containsKey("code") || param.containsKey("alias"))) {
			log.debug("递归查询子节点，param = {}", param);
			List<MenuVO> childrenList = new ArrayList<>();
			lazyList.forEach(menuVO -> getChildrenRecursive(menuVO, childrenList, type));
			List<MenuVO> distinctChildrenList = childrenList.stream().distinct().collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(distinctChildrenList)) {
				distinctChildrenList.forEach(menuVO -> {
					if (!lazyList.contains(menuVO)) {
						lazyList.add(menuVO);
					}
				});
			}
		}

		return lazyList;
	}

	/**
	 * 递归获取指定菜单的子节点列表
	 */
	private void getChildrenRecursive(MenuVO menuVO, List<MenuVO> childrenList, String type) {
		if (menuVO.getHasChildren().equals(Boolean.TRUE)) {
			List<MenuVO> children = baseMapper.lazyList(menuVO.getId(), new HashMap<>(16), type);
			childrenList.addAll(children);
			children.forEach(menuVO1 -> getChildrenRecursive(menuVO1, childrenList, type));
			log.debug("递归查询子节点，当前节点为：{}", menuVO.getName());
		} else {
			log.debug("当前节点 {} 没子节点了，结束递归。", menuVO.getName());
		}
	}

	@Override
	public List<MenuVO> lazyMenuList(Long parentId, Map<String, Object> param, String type) {
		if (Func.isEmpty(Func.toStr(param.get(PARENT_ID)))) {
			parentId = null;
		}
		return baseMapper.lazyMenuList(parentId, param, type);
	}

	@Override
	public List<MenuVO> routes(String roleId, Long topMenuId, String type) {
		if (StringUtil.isBlank(roleId)) {
			return null;
		}
		List<Menu> allMenus = baseMapper.allMenu(type);
		List<Menu> roleMenus;
		// 超级管理员并且不是顶部菜单请求则返回全部菜单
		if (AuthUtil.isAdministrator() && Func.isEmpty(topMenuId)) {
			roleMenus = allMenus;
		}
		// 非超级管理员并且不是顶部菜单请求则返回对应角色权限菜单
		else if (!AuthUtil.isAdministrator() && Func.isEmpty(topMenuId)) {
			roleMenus = tenantPackageMenu(baseMapper.roleMenuByRoleId(Func.toLongList(roleId), type));
		}
		// 顶部菜单请求返回对应角色权限菜单
		else {
			// 角色配置对应菜单
			List<Menu> roleIdMenus = baseMapper.roleMenuByRoleId(Func.toLongList(roleId), type);
			// 反向递归角色菜单所有父级
			List<Menu> routes = new LinkedList<>(roleIdMenus);
			roleIdMenus.forEach(roleMenu -> recursion(allMenus, routes, roleMenu));
			// 顶部配置对应菜单
			List<Menu> topIdMenus = baseMapper.roleMenuByTopMenuId(topMenuId, type);
			// 筛选匹配角色对应的权限菜单
			roleMenus = topIdMenus.stream().filter(x ->
				routes.stream().anyMatch(route -> route.getId().longValue() == x.getId().longValue())
			).collect(Collectors.toList());
		}
		return buildRoutes(allMenus, roleMenus);
	}

	@Override
	public List<MenuVO> routesExt(String roleId, Long topMenuId) {
		if (StringUtil.isBlank(roleId)) {
			return null;
		}
		List<Menu> allMenus = baseMapper.allMenuExt();
		List<Menu> roleMenus = baseMapper.roleMenuExt(Func.toLongList(roleId), topMenuId);
		return buildRoutes(allMenus, roleMenus);
	}

	private List<MenuVO> buildRoutes(List<Menu> allMenus, List<Menu> roleMenus) {
		List<Menu> routes = new LinkedList<>(roleMenus);
		roleMenus.forEach(roleMenu -> recursion(allMenus, routes, roleMenu));
		routes.sort(Comparator.comparing(Menu::getSort));
		MenuWrapper menuWrapper = new MenuWrapper();
		List<Menu> collect = routes.stream().filter(x -> Func.equals(x.getCategory(), 1)).collect(Collectors.toList());
		return menuWrapper.listNodeVO(collect);
	}

	private void recursion(List<Menu> allMenus, List<Menu> routes, Menu roleMenu) {
		Optional<Menu> menu = allMenus.stream().filter(x -> Func.equals(x.getId(), roleMenu.getParentId())).findFirst();
		if (menu.isPresent() && !routes.contains(menu.get())) {
			routes.add(menu.get());
			recursion(allMenus, routes, menu.get());
		}
	}

	@Override
	public List<MenuVO> buttons(String roleId) {
		List<Menu> buttons = (AuthUtil.isAdministrator()) ? baseMapper.allButtons() : baseMapper.buttons(Func.toLongList(roleId));
		MenuWrapper menuWrapper = new MenuWrapper();
		return menuWrapper.listNodeVO(buttons);
	}

	@Override
	public List<TreeNode> tree(String type) {
		return ForestNodeMerger.merge(baseMapper.tree(type));
	}

	@Override
	public List<TreeNode> grantTree(SzykUser user) {
		List<TreeNode> menuTree = user.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? baseMapper.grantTree() : baseMapper.grantTreeByRole(Func.toLongList(user.getRoleId()));
		return ForestNodeMerger.merge(tenantPackageTree(menuTree, user.getTenantId()));
	}

	@Override
	public List<TreeNode> grantTopTree(SzykUser user, String type) {
		List<TreeNode> menuTree = user.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ?
			baseMapper.grantTopTree(type) : baseMapper.grantTopTreeByRole(Func.toLongList(user.getRoleId()), type);
		return ForestNodeMerger.merge(tenantPackageTree(menuTree, user.getTenantId()));
	}

	/**
	 * 租户菜单权限自定义筛选
	 */
	private List<TreeNode> tenantPackageTree(List<TreeNode> menuTree, String tenantId) {
		TenantPackage tenantPackage = SysCache.getTenantPackage(tenantId);
		if (!AuthUtil.isAdministrator() && Func.isNotEmpty(tenantPackage) && tenantPackage.getId() > 0L) {
			List<Long> menuIds = Func.toLongList(tenantPackage.getMenuId());
			menuTree = menuTree.stream().filter(x -> menuIds.contains(x.getId())).collect(Collectors.toList());
		}
		return menuTree;
	}

	/**
	 * 租户菜单权限自定义筛选
	 */
	private List<Menu> tenantPackageMenu(List<Menu> menu) {
		TenantPackage tenantPackage = SysCache.getTenantPackage(AuthUtil.getTenantId());
		if (Func.isNotEmpty(tenantPackage) && tenantPackage.getId() > 0L) {
			List<Long> menuIds = Func.toLongList(tenantPackage.getMenuId());
			menu = menu.stream().filter(x -> menuIds.contains(x.getId())).collect(Collectors.toList());
		}
		return menu;
	}

	@Override
	public List<TreeNode> grantDataScopeTree(SzykUser user) {
		return ForestNodeMerger.merge(user.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? baseMapper.grantDataScopeTree() : baseMapper.grantDataScopeTreeByRole(Func.toLongList(user.getRoleId())));
	}

	@Override
	public List<TreeNode> grantApiScopeTree(SzykUser user) {
		return ForestNodeMerger.merge(user.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? baseMapper.grantApiScopeTree() : baseMapper.grantApiScopeTreeByRole(Func.toLongList(user.getRoleId())));
	}

	@Override
	public List<String> roleTreeKeys(String roleIds) {
		List<RoleMenu> roleMenus = roleMenuService.list(Wrappers.<RoleMenu>query().lambda().in(RoleMenu::getRoleId, Func.toLongList(roleIds)));
		return roleMenus.stream().map(roleMenu -> Func.toStr(roleMenu.getMenuId())).collect(Collectors.toList());
	}

	@Override
	public List<String> topTreeKeys(String topMenuIds) {
		List<TopMenuSetting> settings = topMenuSettingService.list(Wrappers.<TopMenuSetting>query().lambda().in(TopMenuSetting::getTopMenuId, Func.toLongList(topMenuIds)));
		return settings.stream().map(setting -> Func.toStr(setting.getMenuId())).collect(Collectors.toList());
	}

	@Override
	public List<String> dataScopeTreeKeys(String roleIds) {
		List<RoleScope> roleScopes = roleScopeService.list(Wrappers.<RoleScope>query().lambda().eq(RoleScope::getScopeCategory, CommonConstant.DATA_SCOPE_CATEGORY).in(RoleScope::getRoleId, Func.toLongList(roleIds)));
		return roleScopes.stream().map(roleScope -> Func.toStr(roleScope.getScopeId())).collect(Collectors.toList());
	}

	@Override
	public List<String> apiScopeTreeKeys(String roleIds) {
		List<RoleScope> roleScopes = roleScopeService.list(Wrappers.<RoleScope>query().lambda().eq(RoleScope::getScopeCategory, CommonConstant.API_SCOPE_CATEGORY).in(RoleScope::getRoleId, Func.toLongList(roleIds)));
		return roleScopes.stream().map(roleScope -> Func.toStr(roleScope.getScopeId())).collect(Collectors.toList());
	}

	@Override
	@Cacheable(cacheNames = MENU_CACHE, key = "'auth:routes:' + #user.roleId")
	public List<Kv> authRoutes(SzykUser user) {
		List<MenuDTO> routes = baseMapper.authRoutes(Func.toLongList(user.getRoleId()));
		List<Kv> list = new ArrayList<>();
		routes.forEach(route -> list.add(Kv.create().set(route.getPath(), Kv.create().set("authority", Func.toStrArray(route.getAlias())))));
		return list;
	}

	/**
	 * 批量删除菜单及子菜单（逻辑删除）
	 * @param ids 预删除菜单ids
	 * @return
	 */
	@Override
	public boolean removeMenu(String ids) {
		List<Long> idList = Func.toLongList(ids);
		//递归获取子菜单id list
		List<Long> childMenuIdList = listChildMenusRecursively(idList);
		//删除子菜单
		removeByIds(childMenuIdList);
		//删除菜单
		return removeByIds(idList);
	}

	/**
	 * 递归查询子菜单
	 * @param parentIds 父级菜单id列表
	 * @return
	 */
	@Override
	public List<Long> listChildMenusRecursively(List<Long> parentIds) {
		List<Menu> menuList = baseMapper.selectList(Wrappers.<Menu>query().lambda().in(Menu::getParentId, parentIds));
		if (CollectionUtil.isEmpty(menuList)) {
			return new ArrayList<Long>(0);
		}
		List<Long> menuIdList = menuList.stream().map(Menu::getId).collect(Collectors.toList());
		List<Long> childMenuIdList = listChildMenusRecursively(menuIdList);
		menuIdList.addAll(childMenuIdList);
		return menuIdList;
	}

	@Override
	public boolean submit(Menu menu) {
		LambdaQueryWrapper<Menu> menuQueryWrapper = Wrappers.lambdaQuery();
		LambdaQueryWrapper<Menu> menuComponentNameQueryWrapper = Wrappers.lambdaQuery();
		if (menu.getId() == null) {
			menuQueryWrapper.eq(Menu::getCode, menu.getCode()).or(
				wrapper -> wrapper.eq(Menu::getName, menu.getName()).eq(Menu::getCategory, MENU_CATEGORY)
			);
			menuComponentNameQueryWrapper.eq(Menu::getComponentName, menu.getComponentName())
				.eq(Menu::getCategory, MENU_CATEGORY);
		} else {
			menuQueryWrapper.ne(Menu::getId, menu.getId()).and(
				wrapper -> wrapper.eq(Menu::getCode, menu.getCode()).or(
					o -> o.eq(Menu::getName, menu.getName()).eq(Menu::getCategory, MENU_CATEGORY)
				)
			);
			menuComponentNameQueryWrapper.ne(Menu::getId, menu.getId()).and(
				wrapper -> wrapper.eq(Menu::getComponentName, menu.getComponentName()).eq(Menu::getCategory, MENU_CATEGORY)
			);
		}
		Integer cnt = baseMapper.selectCount(menuQueryWrapper);
		if (cnt > 0) {
			throw new ServiceException("菜单名或编号已存在!");
		}
		if (StringUtil.isNotBlank(menu.getComponentName())) {
			Integer menuComponentNameCount = baseMapper.selectCount(menuComponentNameQueryWrapper);
			if (menuComponentNameCount > 0) {
				throw new ServiceException("组件名称已存在!");
			}
		}
		if (menu.getParentId() == null && menu.getId() == null) {
			menu.setParentId(SzykConstant.TOP_PARENT_ID);
		}
		if (menu.getParentId() != null && menu.getId() == null) {
			Menu parentMenu = baseMapper.selectById(menu.getParentId());
			if (parentMenu != null && parentMenu.getCategory() != 1) {
				throw new ServiceException("父节点只可选择菜单类型!");
			}
		}
		menu.setIsDeleted(SzykConstant.DB_NOT_DELETED);
		return saveOrUpdate(menu);
	}

}
