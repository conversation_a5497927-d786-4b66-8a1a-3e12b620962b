/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.annotation.OperationRecord;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.dto.FieldChangeDto;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.BusinessModuleEnum;
import com.snszyk.system.service.ISysOperationRecordService;
import com.snszyk.system.utils.DeptScopeUtil;
import com.snszyk.system.vo.SysOperationRecordVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 操作记录切面
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class OperationRecordAspect {

    private final ISysOperationRecordService operationRecordService;
    private final ObjectMapper objectMapper;

    @Pointcut("@annotation(com.snszyk.system.annotation.OperationRecord)")
    public void operationRecordPointcut() {
    }

    @Around("operationRecordPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationRecord annotation = method.getAnnotation(OperationRecord.class);

        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String[] paramNames = signature.getParameterNames();

        // 创建SpEL上下文
        EvaluationContext context = new StandardEvaluationContext();
        ExpressionParser parser = new SpelExpressionParser();

        // 设置参数到上下文
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        // 获取操作前的业务数据
        Object oldData = null;
        Long businessId = null;
        String businessName = null;
        Long orgId = null;
        String orgName = null;

        try {
            // 尝试从参数中提取业务ID和组织信息
            if (StringUtil.isNotBlank(annotation.businessIdExpression())) {
                Expression idExpression = parser.parseExpression(annotation.businessIdExpression());
                Object idValue = idExpression.getValue(context);
                if (idValue != null) {
                    businessId = Long.valueOf(idValue.toString());
                    // 获取操作前数据
                    oldData = operationRecordService.getBusinessData(annotation.businessType().getCode(), businessId);
                }
            }

            // 提取业务名称和组织信息
            extractBusinessInfo(args, annotation, context, parser);

        } catch (Exception e) {
            log.warn("提取业务信息失败", e);
        }

        // 执行目标方法
        Object result = joinPoint.proceed();

        // 异步记录操作日志
        try {
            recordOperation(annotation, method, args, result, oldData, businessId, businessName, orgId, orgName, context, parser);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }

        return result;
    }

    /**
     * 提取业务信息
     */
    private void extractBusinessInfo(Object[] args, OperationRecord annotation, EvaluationContext context, ExpressionParser parser) {
        // 从参数中提取组织信息
        for (Object arg : args) {
            if (arg != null) {
                try {
                    // 尝试通过反射获取orgId和orgName
                    Class<?> clazz = arg.getClass();
                    try {
                        java.lang.reflect.Field orgIdField = clazz.getDeclaredField("orgId");
                        orgIdField.setAccessible(true);
                        Object orgIdValue = orgIdField.get(arg);
                        if (orgIdValue != null) {
                            context.setVariable("orgId", orgIdValue);
                        }
                    } catch (NoSuchFieldException ignored) {
                        // 字段不存在，忽略
                    }

                    try {
                        java.lang.reflect.Field orgNameField = clazz.getDeclaredField("orgName");
                        orgNameField.setAccessible(true);
                        Object orgNameValue = orgNameField.get(arg);
                        if (orgNameValue != null) {
                            context.setVariable("orgName", orgNameValue);
                        }
                    } catch (NoSuchFieldException ignored) {
                        // 字段不存在，忽略
                    }
                } catch (Exception e) {
                    log.debug("提取组织信息失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 记录操作
     */
    private void recordOperation(OperationRecord annotation, Method method, Object[] args, Object result,
                                Object oldData, Long businessId, String businessName, Long orgId, String orgName,
                                EvaluationContext context, ExpressionParser parser) {

        SysOperationRecordVo recordVo = new SysOperationRecordVo();

        // 设置基本信息
        recordVo.setBusinessModule(BusinessModuleEnum.INTERNET_ASSET.getCode());
        recordVo.setBusinessType(annotation.businessType().getCode());
        recordVo.setOperationType(annotation.operationType().getCode());
        recordVo.setOperationDesc(annotation.description());
        recordVo.setOperationTime(LocalDateTime.now());

        // 设置业务信息
        try {
            // 提取业务ID
            if (businessId == null && StringUtil.isNotBlank(annotation.businessIdExpression())) {
                context.setVariable("result", result);
                Expression idExpression = parser.parseExpression(annotation.businessIdExpression());
                Object idValue = idExpression.getValue(context);
                if (idValue != null) {
                    businessId = Long.valueOf(idValue.toString());
                }
            }
            recordVo.setBusinessId(businessId);

            // 提取业务名称
            if (StringUtil.isNotBlank(annotation.businessNameExpression())) {
                context.setVariable("result", result);
                Expression nameExpression = parser.parseExpression(annotation.businessNameExpression());
                Object nameValue = nameExpression.getValue(context);
                if (nameValue != null) {
                    businessName = nameValue.toString();
                }
            }
            recordVo.setBusinessName(businessName);

            // 提取组织信息
            Object orgIdValue = context.lookupVariable("orgId");
            Object orgNameValue = context.lookupVariable("orgName");
            if (orgIdValue != null) {
                recordVo.setOrgId(Long.valueOf(orgIdValue.toString()));
            }
            if (orgNameValue != null) {
                recordVo.setOrgName(orgNameValue.toString());
            }

        } catch (Exception e) {
            log.warn("提取业务信息失败", e);
        }

        // 设置操作人信息
        setOperatorInfo(recordVo);

        // 设置请求信息
        setRequestInfo(recordVo);

        // 设置操作数据
        if (annotation.logParams()) {
            try {
                recordVo.setOldData(oldData != null ? objectMapper.writeValueAsString(oldData) : null);
            } catch (Exception e) {
                log.warn("序列化操作前数据失败", e);
            }
        }

        if (annotation.logResult()) {
            try {
                recordVo.setNewData(result != null ? objectMapper.writeValueAsString(result) : null);
            } catch (Exception e) {
                log.warn("序列化操作后数据失败", e);
            }
        }

        // 异步保存操作记录
        operationRecordService.saveAsync(recordVo);

        // 如果需要记录字段变更详情
        if (annotation.recordFieldChanges() && oldData != null && result != null) {
            try {
                List<FieldChangeDto> fieldChanges = operationRecordService.compareFields(
                        annotation.businessType().getCode(), oldData, result);
                if (!fieldChanges.isEmpty()) {
                    // 这里需要在保存记录后获取记录ID，然后保存字段变更
                    // 由于是异步操作，这里暂时跳过字段变更的保存
                    log.debug("检测到 {} 个字段变更", fieldChanges.size());
                }
            } catch (Exception e) {
                log.warn("比较字段变更失败", e);
            }
        }
    }

    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(SysOperationRecordVo recordVo) {
        try {
            SzykUser user = AuthUtil.getUser();
            if (user != null) {
                recordVo.setOperatorId(user.getUserId());
                recordVo.setOperatorName(user.getUserName());

                // 获取用户部门信息
                Dept loginDept = DeptScopeUtil.getLoginDept();
                if (loginDept != null) {
                    recordVo.setOperatorDeptId(loginDept.getId());
                    recordVo.setOperatorDeptName(loginDept.getDeptName());
                }
            }
        } catch (Exception e) {
            log.warn("获取操作人信息失败", e);
        }
    }

    /**
     * 设置请求信息
     */
    private void setRequestInfo(SysOperationRecordVo recordVo) {
        try {
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                recordVo.setRequestIp(WebUtil.getIP(request));
                recordVo.setRequestUri(request.getRequestURI());
                recordVo.setRequestMethod(request.getMethod());
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
    }
}
