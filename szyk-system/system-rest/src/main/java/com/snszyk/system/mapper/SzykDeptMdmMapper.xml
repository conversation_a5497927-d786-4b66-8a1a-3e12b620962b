<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.system.mapper.SzykDeptMdmMapper">


    <select id="lazyTreeByParent" resultType="com.snszyk.system.vo.SzykDeptMdmTreeVo">
        SELECT dept.code,
               dept.parentcode,
               dept.zzqc,
               dept.zzjc,
               dept.code              AS "value",
               dept.code              AS "key",
               dept.zzlxdm,
               dept.zzlxmc,
               (SELECT CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM szyk_dept_mdm
                WHERE parentcode = dept.code
                  ) AS "has_children"
        FROM szyk_dept_mdm dept
        WHERE dept.parentcode = #{parentCode}
        ORDER BY dept.pxm
    </select>



</mapper>
