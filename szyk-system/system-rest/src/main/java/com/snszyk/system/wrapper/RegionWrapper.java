/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.wrapper;

import com.snszyk.system.cache.RegionCache;
import com.snszyk.system.entity.Region;
import com.snszyk.system.vo.RegionVO;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.BeanUtil;

import java.util.List;
import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class RegionWrapper extends BaseEntityWrapper<Region, RegionVO> {

	public static RegionWrapper build() {
		return new RegionWrapper();
	}

	@Override
	public RegionVO entityVO(Region region) {
		RegionVO regionVO = Objects.requireNonNull(BeanUtil.copy(region, RegionVO.class));
		Region parentRegion = RegionCache.getByCode(region.getParentCode());
		regionVO.setParentName(parentRegion.getName());
		return regionVO;
	}

	public List<RegionVO> listNodeLazyVO(List<RegionVO> list) {
		return ForestNodeMerger.merge(list);
	}

}
