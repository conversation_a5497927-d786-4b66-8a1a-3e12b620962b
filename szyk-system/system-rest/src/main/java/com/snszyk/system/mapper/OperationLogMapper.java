package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.system.entity.OperationLog;
import com.snszyk.system.vo.OperationLogVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志Mapper接口
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Repository
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param vo   查询条件
     * @return 分页结果
     */
    IPage<OperationLogDto> pageList(Page<OperationLogDto> page, @Param("vo") OperationLogVo vo);

    /**
     * 根据业务ID查询操作日志
     *
     * @param businessModule 业务模块
     * @param businessId     业务ID
     * @return 操作日志列表
     */
    List<OperationLogDto> listByBusinessId(@Param("businessModule") String businessModule,
                                          @Param("businessId") Long businessId);
}
