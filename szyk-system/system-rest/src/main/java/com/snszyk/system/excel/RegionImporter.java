/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.excel;

import lombok.RequiredArgsConstructor;
import com.snszyk.core.excel.support.ExcelImporter;
import com.snszyk.system.service.IRegionService;

import java.util.List;

/**
 * 行政区划数据导入类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class RegionImporter implements ExcelImporter<RegionExcel> {

	private final IRegionService service;
	private final Boolean isCovered;

	@Override
	public void save(List<RegionExcel> data) {
		service.importRegion(data, isCovered);
	}
}
