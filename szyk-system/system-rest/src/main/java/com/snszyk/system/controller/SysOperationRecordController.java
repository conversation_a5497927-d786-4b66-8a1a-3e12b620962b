/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.tool.api.R;
import com.snszyk.system.dto.SysOperationRecordDto;
import com.snszyk.system.service.ISysOperationRecordService;
import com.snszyk.system.vo.SysOperationRecordPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 操作记录控制器
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/system/operation-record")
@Api(value = "操作记录管理", tags = "操作记录管理接口")
public class SysOperationRecordController extends BaseCrudController<ISysOperationRecordService> {

    @PostMapping("/page")
    @ApiOperation(value = "分页查询操作记录", notes = "传入查询条件")
    @ApiOperationSupport(order = 1)
    public R<IPage<SysOperationRecordDto>> page(@RequestBody SysOperationRecordPageVo vo) {
        IPage<SysOperationRecordDto> pages = baseService.pageList(vo);
        return R.data(pages);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "获取操作记录详情", notes = "传入操作记录ID")
    @ApiOperationSupport(order = 2)
    public R<SysOperationRecordDto> detail(@ApiParam(value = "操作记录ID", required = true) @PathVariable Long id) {
        SysOperationRecordDto detail = baseService.getDetailWithChanges(id);
        return R.data(detail);
    }
}
