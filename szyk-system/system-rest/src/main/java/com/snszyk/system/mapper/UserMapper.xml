<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userResultMap" type="com.snszyk.system.entity.User">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="code" property="code"/>
        <result column="user_type" property="userType"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="real_name" property="realName"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="birthday" property="birthday"/>
        <result column="sex" property="sex"/>
        <result column="role_id" property="roleId"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
    </resultMap>

    <select id="selectUserPage" resultType="com.snszyk.system.vo.UserVO">
        SELECT
        t.id,
        t.tenant_id,
        t.CODE,
        t.user_type,
        t.account,
        t.PASSWORD,
        t.NAME,
        t.real_name,
        t.avatar,
        t.phone,
        t.role_id,
        t.dept_id,
        t.post_id,
        t.STATUS,
        t1.ancestors,
        t1.ancestor_name,
        t1.dept_name,
        t.actual_dept_id ,
        if(t.status=1,'启用','停用') as statusName,
        group_concat( t3.ancestor_name,'-',t3.dept_name ) as scopeAncestorsName
        FROM
        szyk_user t
        LEFT JOIN szyk_dept t1 ON ( t.actual_dept_id = t1.id AND t1.is_deleted = 0 AND t1.hide = 0 )

        left JOIN szyk_user_dept t2 on t .id=t2.user_id
        left join szyk_dept t3 on( t2.dept_id=t3.id and t3.is_deleted = 0 AND t3.hide = 0)
        left join szyk_dept udept on t1 .unit_id = udept.id
        WHERE
        t.is_deleted = 0
        <if test="containAdmin !=null and containAdmin == false ">
            and t.account  !='SZYKxm'
        </if>
        <if test="user.status !=null">
            and t.status  =#{user.status}
        </if>

        <if test="tenantId!=null and tenantId != ''">
            and t.tenant_id = #{tenantId}
        </if>
        <if test="user.tenantId!=null and user.tenantId != ''">
            and t.tenant_id = #{user.tenantId}
        </if>
        <if test="user.account!=null and user.account != ''">
            and t.account like concat('%',#{user.account},'%')
        </if>
        <if test="user.realName!=null and user.realName != ''">
            and t.real_name like concat('%',#{user.realName},'%')
        </if>
        <if test="user.userType!=null and user.userType != ''">
            and t.user_type = #{user.userType}
        </if>

        <if test="user.roleId!=null and user.roleId != ''">
            and t.role_id like concat('%',#{user.roleId},'%')
        </if>
        <if test="deptIdList!=null and deptIdList.size>0">
            and t.id in (
            SELECT
            user_id
            FROM
            szyk_user_dept ud
            WHERE
            ud.dept_id IN
            <foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="departContact !=null and departContact !='' ">
                and ud.contact_person  like concat('%',#{departContact},'%')
            </if>
            )
        </if>
        <if test="userIdlList!=null and userIdlList.size>0">
            and t.id in
            <foreach collection="userIdlList" index="index" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        GROUP BY t.id, t1.sort ,t1.id
        ORDER BY t1.sort asc, t1.id asc,t.id desc

    </select>

    <select id="expertUserPage" resultType="com.snszyk.system.vo.UserVO">
        SELECT
        t.id,
        t.tenant_id,
        t.CODE,
        t.user_type,
        t.account,
        t.PASSWORD,
        t.NAME,
        t.real_name,
        t.avatar,
        t.phone,
        t.role_id,
        t.dept_id,
        t.post_id,
        t.STATUS,
        t1.ancestors,
        t1.ancestor_name,
        t1.dept_name,
        t.actual_dept_id ,
        if(t.status=1,'启用','停用') as statusName

        FROM
        szyk_user t
        LEFT JOIN szyk_dept t1 ON ( t.actual_dept_id = t1.id AND t1.is_deleted = 0 AND t1.hide = 0 )
        left join szyk_dept udept on t1 .unit_id = udept.id
        WHERE
        t.is_deleted = 0
        <if test="containAdmin !=null and containAdmin == false ">
            and t.account  !='SZYKxm'
        </if>
        <if test="user.status !=null">
            and t.status  =#{user.status}
        </if>
        <if test="tenantId!=null and tenantId != ''">
            and t.tenant_id = #{tenantId}
        </if>
        <if test="user.tenantId!=null and user.tenantId != ''">
            and t.tenant_id = #{user.tenantId}
        </if>
        <if test="user.account!=null and user.account != ''">
            and t.account like concat('%',#{user.account},'%')
        </if>
        <if test="user.realName!=null and user.realName != ''">
            and t.real_name like concat('%',#{user.realName},'%')
        </if>
        <if test="user.userType!=null and user.userType != ''">
            and t.user_type = #{user.userType}
        </if>

        <if test="user.roleId!=null and user.roleId != ''">
            and t.role_id like concat('%',#{user.roleId},'%')
        </if>
        <if test="deptIdList!=null and deptIdList.size>0">
            and t.actual_dept_id in
            <foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="userIdlList!=null and userIdlList.size>0">
            and t.id in
            <foreach collection="userIdlList" index="index" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        GROUP BY t.id, t1.sort ,t1.id
        ORDER BY t1.sort asc, t1.id asc,t.id desc

    </select>
    <select id="getUser" resultMap="userResultMap">
        SELECT
            *
        FROM
            szyk_user
        WHERE
            tenant_id = #{param1} and account = #{param2} and password = #{param3} and is_deleted = 0 and status=1
    </select>

    <select id="exportUser" resultType="com.snszyk.system.excel.UserExcel">
        SELECT id,
               tenant_id,
               user_type,
               account,
               name,
               real_name,
               email,
               phone,
               birthday,
               role_id,
               dept_id,
               post_id
        FROM szyk_user ${ew.customSqlSegment}
    </select>

    <select id="selectUserPageByNameAndDept" resultType="com.snszyk.system.vo.UserVO">
        SELECT su.id, su.real_name, sd.id AS dept_id, concat(sd.ancestor_name,'-',sd.dept_name) as deptName
        FROM szyk_user_dept sud
        INNER JOIN szyk_user su ON su.id = sud.user_id
        INNER JOIN szyk_dept sd ON sd.id = sud.dept_id
        WHERE su.is_deleted = 0 AND sd.is_deleted = 0 and su.status=1 and sd.dept_status=1
        <if test="tenantId != null and tenantId !=''">
            AND su.tenant_id = #{tenantId} AND sd.tenant_id = #{tenantId}
        </if>
        <if test="userName != null and userName !=''">
            AND su.real_name LIKE concat('%',#{userName},'%')
        </if>
        <if test="unitRange ==1">
            and (sd.unit_id=#{deptId} or sd.id=#{deptId})
        </if>
        <if test="unitRange ==2">
            and (sd.ancestors like concat('%',#{deptId},'%') or sd.id=#{deptId})
        </if>
        order by sd.sort asc, sd.id asc, su.id desc
    </select>
<!--    <select id="getContactPersonListByDeptRole" resultType="com.snszyk.system.dto.UserDTO">-->
<!--        SELECT t1.id,-->
<!--        t1.CODE,-->
<!--        t1.user_type,-->
<!--        t1.account,-->
<!--        t1.NAME,-->
<!--        t1.real_name,-->
<!--        t1.phone,-->
<!--        t1.sex,-->
<!--        t.role_id,-->
<!--        t.dept_id-->
<!--        FROM szyk_user_role t-->
<!--        LEFT JOIN szyk_user t1 ON t.user_id = t1.id-->
<!--        LEFT JOIN szyk_role t2 ON t.role_id = t2.id-->

<!--        <where>-->
<!--             t1.is_deleted=0 and t1.status=1-->
<!--            <if test="deptId!=null">-->
<!--                and t.dept_id=#{deptId}-->
<!--            </if>-->
<!--            <if test="roleNameList !=null and roleNameList.size>0">-->
<!--                and t2.role_name in-->
<!--                <foreach collection="roleNameList" item="item" open="(" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->


<!--    </select>-->

<!--    <select id="isDartContactByCode" resultType="com.snszyk.system.entity.UserRole">-->
<!--        select t2.*-->
<!--        from szyk_user t-->
<!--                 left join szyk_user_role t1 on t.id = t1.user_id-->
<!--                 left join szyk_role t2 on t2.id = t1.role_id-->
<!--        where t.is_deleted = 0 and t.status=1-->
<!--          and t.account = #{jobNo}-->
<!--          and t2.is_deleted = 0-->
<!--          and t2.role_alias = '联络员'-->
<!--    </select>-->

    <update id="deleteLindIamUser">
        update iam_user set is_deleted=1 where
        account_no in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>


    </update>

    <!--    根据业务范围查询用户-->
    <select id="listTaskUserByDeptScope" resultType="com.snszyk.system.vo.UserVO">

        select t.id,
        t.tenant_id,
        t.code,
        t.user_type,
        t.account,
        t.password,
        t.name,
        t.real_name,
        t.avatar,
        t.email,
        t.phone,
        t.birthday,
        t.sex,
        t.role_id,
        t.actual_dept_id,
        t1.dept_id,
        t2.dept_name,
        t.post_id,
        t.dingtalk_user_id,
        t.create_user,
        t.create_dept,
        t.create_time,
        t.update_user,
        t.update_time,
        t.status,
        t.is_deleted,
        t.update_flag,
        t1.contact_person,
        t3.dept_name as unitName,
        t2.dept_name as scopeDeptName,
         concat( t2.ancestor_name,'/',t2.dept_name ) as scopeAncestorsName
        from szyk_user t
        left join szyk_user_dept t1 on
        t.id=t1.user_id
        left join szyk_dept t2
        on (t2.id=t1.dept_id and t2.hide=0 and t2.is_deleted=0 )
        left join szyk_dept t3
        on t2.unit_id=t3.id
        <where>
            <if test="realName!=null and realName!=''">
                and t.real_Name like concat('%',#{realName},'%')
            </if>

            <if test="deptIds!=null and deptIds.size > 0">
                and (t1.dept_id in
                <foreach collection="deptIds" close=")" index="idex" item="item" open="(" separator=",">
                    #{item}
                </foreach>

                or t1.dept_id in(

                    select id from szyk_dept t4 where t4.unit_id in
                <foreach collection="deptIds" close=")" index="idex" item="item" open="(" separator=",">
                    #{item}
                </foreach>))
            </if>
            <if test="neUserId!=null ">
                and t.id != #{neUserId}
            </if>
            <if test="contact!=null and contact!=''">
                and t1.contact_person like concat('%',#{contact},'%')
            </if>
            and t.is_deleted=0
               and t.status=1
            and t2.is_deleted=0
            and t.account !='SZYKxm'
        </where>
        ORDER BY t2.sort,t.id
    </select>
    <select id="getMemberPage" resultType="com.snszyk.system.vo.UserVO">

        select t.id,
        t.tenant_id,
        t.code,
        t.user_type,
        t.account,
        t.password,
        t.name,
        t.real_name,
        t.avatar,
        t.email,
        t.phone,
        t.birthday,
        t.sex,
        t.role_id,
        t.actual_dept_id,
        t1.dept_id,
        t2.dept_name,
        t.post_id,
        t.dingtalk_user_id,
        t.create_user,
        t.create_dept,
        t.create_time,
        t.update_user,
        t.update_time,
        t.status,
        t.is_deleted,
        t.update_flag,
        t.contact_person,
        t3.dept_name as unitName,
        t3.id as unitId,
        t2.dept_name as scopeDeptName,
        concat( t2.ancestor_name,'/',t2.dept_name ) as scopeAncestorsName
        from szyk_user t
        left join szyk_user_dept t1 on
        t.id=t1.user_id
        left join szyk_dept t2
        on (t2.id=t1.dept_id and t2.hide=0 and t2.is_deleted=0 )
        left join szyk_dept t3
        on t2.unit_id=t3.id
        <where>
            <if test="realName!=null and realName!=''">
                and t.real_Name like concat('%',#{realName},'%')
            </if>

            <if test="deptIdList!=null and deptIdList.size > 0">
                and (t1.dept_id in
                <foreach collection="deptIdList" close=")" index="idex" item="item" open="(" separator=",">
                    #{item}
                </foreach>)
            </if>
            and t.is_deleted=0
               and t.status=1
            and t2.is_deleted=0
            and t.account !='SZYKxm'
        </where>
        ORDER BY t.id

    </select>
    <select id="getUserByContactAndDept" resultType="com.snszyk.system.vo.UserVO">
        select t.id,
        t.tenant_id,
        t.code,
        t.user_type,
        t.account,
        t.password,
        t.name,
        t.real_name,
        t.avatar,
        t.email,
        t.phone,
        t.birthday,
        t.sex,
        t.role_id,
        t.actual_dept_id,
        t1.dept_id,
        t2.dept_name,
        t.post_id,
        t.dingtalk_user_id,
        t.create_user,
        t.create_dept,
        t.create_time,
        t.update_user,
        t.update_time,
        t.status,
        t.is_deleted,
        t.update_flag,
        t.contact_person,
        t2.dept_name as scopeDeptName,
        concat( t2.ancestor_name,'/',t2.dept_name ) as scopeAncestorsName
        from szyk_user t
        left join szyk_user_dept t1 on
        t.id=t1.user_id
        left join szyk_dept t2
        on (t2.id=t1.dept_id and t2.hide=0 and t2.is_deleted=0 )
        <where>
            <if test="deptId!=null">
                and t1.dept_id =#{deptId}
            </if>
            <if test="contactEnum!=null and contactEnum!=''">
                and t1.contact_person like concat('%',#{contactEnum},'%')
            </if>
            and t.is_deleted=0   and t.status=1
            and t2.is_deleted=0
            and t.account !='SZYKxm'
        </where>
        ORDER BY t.id
        </select>
    <select id="indicatorCountUserByOrg" resultType="com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto">
        select count(distinct t2.id) as count,
               t.dept_code           as orgCode,
               t.dept_name,
               t.id
        from szyk_dept t
                 left join szyk_dept t1 on (FIND_IN_SET(t.id, t1.ancestors) or t.id = t1.id) and t1.is_deleted = 0 and
                                           t1.hide = 0 and t1.dept_status = 1
                 left join szyk_user_dept t2 on t2.dept_id = t1.id
                 left join szyk_user t3 on t3.id = t2.user_id
        where t2.id is not null
          and t.is_deleted = 0
          and t.hide = 0
          and t.dept_status = 1
          and t.dept_category = 1
          and t3.is_deleted = 0 and t3.status=1
          and t3.account !='SZYKxm'
        GROUP BY t.dept_code


    </select>
    <select id="listAll" resultType="com.snszyk.system.dto.UserDTO">
     select  t.*from szyk_user t
     where t.is_deleted=0 and t.status=1

    </select>

</mapper>
