package com.snszyk.system.utils;

import com.snszyk.common.constant.CommonConstant;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.zbusiness.dict.enums.DictBizEnum;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 字典工具类
 *
 * <AUTHOR>
 */
public class DictUtil {

	/**
	 * 根据字典key 获取字典map
	 *
	 * @param dictBizEnum
	 * @return
	 */
	public static Map<String, String> getMap(DictBizEnum dictBizEnum) {
		IDictBizService dictBizService = SpringUtil.getBean(IDictBizService.class);
		List<DictBiz> list = dictBizService.getList(dictBizEnum.getCode());
		Map<String, String> dictMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(list)) {
			dictMap.putAll(list.stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue)));
		}
		return dictMap;
	}

	/**
	 * 根据字典key 获取字典map
	 *
	 * @param integer
	 * @return
	 */
	public static String convertToStr(Integer integer) {
		if (integer == null) {
			return "";
		}
		if (Objects.equals(integer, CommonConstant.ONE)) {
			return "是";
		}
		if (Objects.equals(integer, CommonConstant.ZERO)) {
			return "否";
		}
		return "";
	}
	/**
	 * 根据字典key 获取字典map
	 *
	 * @param b
	 * @return
	 */
	public static String convertToStr(Boolean b) {
		if (b == null) {
			return "";
		}
		if (b) {
			return "是";
		}
        return "否";
    }
	public static Integer convertToInteger(String str) {
		if (StringUtil.isBlank(str)) {
			return null;
		}
		if (Objects.equals(str,"是")) {
			return CommonConstant.ONE;
		}
		if (Objects.equals(str,"否")) {
			return CommonConstant.ZERO;
		}
		return null;
	}
	public static Boolean convertToBoolean(String str) {
		if (StringUtil.isBlank(str)) {
			return null;
		}
		if (Objects.equals(str,"是")) {
			return true;
		}
		if (Objects.equals(str,"否")) {
			return false;
		}
		return null;
	}
}
