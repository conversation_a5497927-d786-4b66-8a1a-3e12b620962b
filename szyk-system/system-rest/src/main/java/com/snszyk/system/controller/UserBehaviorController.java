package com.snszyk.system.controller;

import com.snszyk.common.enums.AnalyseDimensionEnum;
import com.snszyk.common.utils.CommonUtil;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.system.cache.UserBehaviorCache;
import com.snszyk.system.entity.Menu;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserBehavior;
import com.snszyk.system.entity.UserOnlineDuration;
import com.snszyk.system.service.*;
import com.snszyk.system.userbehavior.ActiveUserDTO;
import com.snszyk.system.userbehavior.BrowserRateDTO;
import com.snszyk.system.userbehavior.PageViewAnalyseDTO;
import com.snszyk.system.userbehavior.UserBehaviorDTO;
import com.snszyk.system.vo.UserBehaviorVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户行为分析 控制器
 *
 * <AUTHOR>
 * @since 2023/5/5 18:59
 **/
@NonDS
@Api(value = "用户行为分析", tags = "用户行为分析")
@RestController
@RequestMapping(AppConstant.APPLICATION_USER_NAME + "/user-behavior")
@AllArgsConstructor
@Slf4j
public class UserBehaviorController {

	private final IUserBehaviorService userBehaviorService;
	private final IUserOnlineDurationService userOnlineDurationService;
	private final IUserService userService;
	private final IMenuService menuService;
	private final ISzykUserActivityService szykUserActivityService;


	@Async
	@ApiOperation("记录")
	@PostMapping("write")
	public void write(HttpServletRequest request, @RequestBody List<UserBehaviorVO> behaviors) {
		List<UserBehavior> userBehaviorList = new ArrayList<>();
		behaviors.stream().forEach(b -> {
			try {
				User user = userService.getById(b.getUserId());
				Menu menu = menuService.getById(b.getMenuId());
				UserBehavior behavior = new UserBehavior();
				behavior.setUserId(user.getId());
				behavior.setUserName(user.getRealName());
				behavior.setDeptId(b.getDeptId());
				behavior.setMenuId(menu.getId());
				behavior.setMenuName(menu.getName());
				behavior.setMenuRoute(menu.getComponentPath());
				behavior.setTenantId(SzykConstant.ADMIN_TENANT_ID);
				behavior.setIpAddress(WebUtil.getIP(request));
				behavior.setBrowser(CommonUtil.getBrowserInfo(request));
				behavior.setCreateTime(b.getCreateTime());
				userBehaviorList.add(behavior);
			} catch (Exception e) {
				e.printStackTrace();
			}
		});
		userBehaviorService.saveBatch(userBehaviorList);
		Long userId = userBehaviorList.get(0).getUserId();
		Long deptId = userBehaviorList.get(0).getDeptId();
		LocalDate now = LocalDate.now();
		LocalDateTime startTime = LocalDateTime.of(now, LocalTime.MIN);
		LocalDateTime endTime = LocalDateTime.of(now, LocalTime.MAX);
		Integer countByTime = userOnlineDurationService.countByTime(userId, startTime, endTime,deptId);
		if (countByTime == 0) {
			szykUserActivityService.recordActiveLog(request, userId, deptId);
		}
		//记录在线时长
		if (CollectionUtil.isNotEmpty(userBehaviorList)) {
			UserBehaviorVO b = behaviors.get(0);
			User user = userService.getById(b.getUserId());
			UserOnlineDuration onlineDuration = new UserOnlineDuration();
			onlineDuration.setUserId(user.getId());
			onlineDuration.setUserName(user.getRealName());
			onlineDuration.setDeptId(b.getDeptId());
			onlineDuration.setDuration(120L);
			onlineDuration.setTenantId(SzykConstant.ADMIN_TENANT_ID);
			onlineDuration.setIpAddress(WebUtil.getIP(request));
			onlineDuration.setBrowser(CommonUtil.getBrowserInfo(request));
			onlineDuration.setCreateTime(b.getCreateTime());
			userOnlineDurationService.save(onlineDuration);
		}
	}

	@ApiOperation("处理指标活跃率的历史数据")
	@PostMapping("testActiveLog")
	public void testActiveLog() {
		szykUserActivityService.testActiveLog("2023-10-01");
		szykUserActivityService.testActiveLog("2023-11-01");
		szykUserActivityService.testActiveLog("2023-12-01");
		szykUserActivityService.testActiveLog("2024-01-01");
		log.warn("1月log处理完成");
		szykUserActivityService.testActiveLog("2024-02-01");
		log.warn("2月log处理完成");
		szykUserActivityService.testActiveLog("2024-03-01");
		log.warn("3月log处理完成");
	}

	@ApiOperation("用户行为分析首页")
	@GetMapping("user-behavior")
	public R<UserBehaviorDTO> userBehavior() {
		return R.data(userBehaviorService.userBehavior());
	}

	@ApiOperation("访问分析")
	@GetMapping("page-view-list")
	public R<List<PageViewAnalyseDTO>> pageViewList(@ApiParam("分析维度") @RequestParam(value = "dim") AnalyseDimensionEnum dim) {
		//今日统计
		List<PageViewAnalyseDTO> currentPageViews = userBehaviorService.pageView(AnalyseDimensionEnum.CURRENT);
		if (AnalyseDimensionEnum.CURRENT == dim) {
			return R.data(currentPageViews);
		}
		List<PageViewAnalyseDTO> list = UserBehaviorCache.getPageView(dim);
		Map<Long, PageViewAnalyseDTO> viewAnalyseMap = list.stream().collect(Collectors.toMap(PageViewAnalyseDTO::getMenuId, Function.identity()));
		if (CollectionUtil.isNotEmpty(currentPageViews)) {
			Map<Long, PageViewAnalyseDTO> currentMap = currentPageViews.stream().collect(Collectors.toMap(PageViewAnalyseDTO::getMenuId, Function.identity()));
			currentMap.keySet().stream().forEach(menuId -> {
				if (!viewAnalyseMap.containsKey(menuId)) {
					viewAnalyseMap.put(menuId, currentMap.get(menuId));
				} else {
					PageViewAnalyseDTO dto = viewAnalyseMap.get(menuId);
					dto.setPageViewNum(Integer.sum(dto.getPageViewNum(), currentMap.get(menuId).getPageViewNum()));
					dto.setVisitorNum(Integer.sum(dto.getVisitorNum(), currentMap.get(menuId).getVisitorNum()));
				}
			});
		}
		return R.data(viewAnalyseMap.values().stream().collect(Collectors.toList()));
	}

	@ApiOperation("浏览器占比分析")
	@GetMapping("browser-rate")
	public R<List<BrowserRateDTO>> browserRate(@ApiParam("分析维度") @RequestParam(value = "dim") AnalyseDimensionEnum dim) {
		if (AnalyseDimensionEnum.CURRENT == dim) {
			return R.data(userBehaviorService.browserRate(AnalyseDimensionEnum.CURRENT));
		}
		Map<String, BigDecimal> currentMap = userBehaviorService.countByBrowser(AnalyseDimensionEnum.CURRENT);
		Map<String, BigDecimal> preMap = UserBehaviorCache.getBrowserRate(dim);
		if (CollectionUtil.isNotEmpty(currentMap)) {
			currentMap.keySet().forEach(browser -> {
				if (!preMap.containsKey(browser)) {
					preMap.put(browser, currentMap.get(browser));
				} else {
					BigDecimal num = preMap.get(browser);
					preMap.put(browser, num.add(currentMap.get(browser)));
				}
			});
		}
		BigDecimal total = preMap.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
		List<BrowserRateDTO> list = preMap.keySet().stream().map(browser -> {
			return new BrowserRateDTO(browser, preMap.getOrDefault(browser, BigDecimal.ZERO).divide(total, 2, RoundingMode.HALF_UP));
		}).collect(Collectors.toList());
		return R.data(list);
	}

	@ApiOperation("活跃用户")
	@GetMapping("active-user")
	public R<List<ActiveUserDTO>> activeUser(@ApiParam("分析维度") @RequestParam(value = "dim") AnalyseDimensionEnum dim) {
		return R.data(userBehaviorService.activeUser(dim));
	}

	/**
	 * 获取本日开始时间
	 * @return 开始时间
	 */
	public static String getStartTime() {
		LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
		return startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
	}

	/**
	 * 获取本日结束时间
	 * @return
	 */
	public static String getEndTime() {
		LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
		return endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
	}

}
