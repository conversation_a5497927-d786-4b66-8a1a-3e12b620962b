package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.system.entity.UserOnlineDuration;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface UserOnlineDurationMapper extends BaseMapper<UserOnlineDuration> {

	UserOnlineDuration isLogin(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
