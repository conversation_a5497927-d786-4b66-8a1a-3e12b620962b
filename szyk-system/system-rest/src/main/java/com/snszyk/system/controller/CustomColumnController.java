package com.snszyk.system.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.system.cache.CustomColumnCache;
import com.snszyk.system.vo.CustomColumnChildVo;
import com.snszyk.system.vo.CustomColumnVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-12-12-17:03
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/customColumn")
@Api(value = "自定义列", tags = "自定义列")
@Validated
public class CustomColumnController {

	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "保存")
	public R<Boolean> save(@Validated @RequestBody CustomColumnVo columnVo) {
		CacheUtil.put(CustomColumnCache.CUSTOM_COLUMN_CACHE, CustomColumnCache.getPrefix(columnVo.getUserId()), CustomColumnCache.getKey(columnVo.getMenuId()), columnVo.getColumnList(), false);
		return R.data(Boolean.TRUE);
	}

	@GetMapping("/listColumn")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "获取列信息", notes = "获取列信息")
	public R<List<CustomColumnChildVo>> listColumn(@RequestParam("userId") @NotNull Long userId, @NotNull @RequestParam("menuId") String menuId) {
		return R.data(CacheUtil.get(CustomColumnCache.CUSTOM_COLUMN_CACHE, CustomColumnCache.getPrefix(userId), CustomColumnCache.getKey(menuId), List.class, false));
	}

	@DeleteMapping("/delete")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "删除", notes = "删除")
	public R<Boolean> delete() {
		CacheUtil.clear(CustomColumnCache.CUSTOM_COLUMN_CACHE, false);
		return R.data(Boolean.TRUE);
	}
}
