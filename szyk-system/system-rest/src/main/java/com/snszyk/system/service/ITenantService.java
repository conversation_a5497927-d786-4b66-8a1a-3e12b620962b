/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.system.entity.Tenant;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.vo.DelResultVO;

import java.util.Date;
import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface ITenantService extends BaseService<Tenant> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tenant
	 * @return
	 */
	IPage<Tenant> selectTenantPage(IPage<Tenant> page, Tenant tenant);

	/**
	 * 根据租户编号获取实体
	 *
	 * @param tenantId
	 * @return
	 */
	Tenant getByTenantId(String tenantId);

	/**
	 * 新增
	 *
	 * @param tenant
	 * @return
	 */
	boolean submitTenant(Tenant tenant);

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	boolean removeTenant(List<Long> ids);

	/**
	 * 删除校验
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveTenant(List<Long> ids);

	/**
	 * 配置租户授权
	 *
	 * @param accountNumber
	 * @param expireTime
	 * @param ids
	 * @return
	 */
	boolean setting(Integer accountNumber, Date expireTime, String ids);

	/**
	 * 更新并刷新租户数据源
	 * @param tenantId
	 * @param datasourceId
	 * @return
	 */
	boolean updateAndRefresh(String tenantId, Long datasourceId);

}
