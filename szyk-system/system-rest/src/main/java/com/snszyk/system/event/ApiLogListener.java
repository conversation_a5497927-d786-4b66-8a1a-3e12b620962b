/*
 *      Copyright (c) 2018-2028
 */

package com.snszyk.system.event;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.snszyk.core.launch.props.SzykProperties;
import com.snszyk.core.launch.server.ServerInfo;
import com.snszyk.core.log.constant.EventConstant;
import com.snszyk.core.log.event.ApiLogEvent;
import com.snszyk.core.log.model.LogApi;
import com.snszyk.core.log.utils.LogAbstractUtil;
import com.snszyk.system.service.ILogService;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;

import java.util.Map;


/**
 * 异步监听日志事件
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class ApiLogListener {

	private final ILogService logService;
	private final ServerInfo serverInfo;
	private final SzykProperties szykProperties;


	@Async
	@Order
	@EventListener(ApiLogEvent.class)
	public void saveApiLog(ApiLogEvent event) {
		Map<String, Object> source = (Map<String, Object>) event.getSource();
		LogApi logApi = (LogApi) source.get(EventConstant.EVENT_LOG);
		LogAbstractUtil.addOtherInfoToLog(logApi, szykProperties, serverInfo);
		logService.saveApiLog(logApi);
	}

}
