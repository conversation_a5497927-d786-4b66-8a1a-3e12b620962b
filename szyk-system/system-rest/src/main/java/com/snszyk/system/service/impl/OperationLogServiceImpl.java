package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.system.entity.OperationLog;
import com.snszyk.system.enums.BusinessModuleEnum;
import com.snszyk.system.enums.OperationTypeEnum;
import com.snszyk.system.mapper.OperationLogMapper;
import com.snszyk.system.service.IOperationLogService;
import com.snszyk.system.vo.OperationLogVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 操作日志服务实现
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

    private final OperationLogMapper operationLogMapper;

    @Override
    @Async
    public void saveAsync(OperationLog operationLog) {
        try {
            save(operationLog);
        } catch (Exception e) {
            log.error("异步保存操作日志失败", e);
        }
    }

    @Override
    public IPage<OperationLogDto> pageList(OperationLogVo vo) {
        Page<OperationLogDto> page = new Page<>(vo.getCurrent(), vo.getSize());
        IPage<OperationLogDto> result = operationLogMapper.pageList(page, vo);

        // 转换枚举显示名称
        result.getRecords().forEach(this::convertEnumNames);

        return result;
    }

    @Override
    public List<OperationLogDto> listByBusinessId(String businessModule, Long businessId) {
        List<OperationLogDto> list = operationLogMapper.listByBusinessId(businessModule, businessId);

        // 转换枚举显示名称
        list.forEach(this::convertEnumNames);

        return list;
    }

    @Override
    public List<String> compareObjectChanges(Object oldObj, Object newObj, String[] compareFields) {
        List<String> changedFields = new ArrayList<>();

        if (oldObj == null || newObj == null) {
            return changedFields;
        }

        try {
            Class<?> clazz = oldObj.getClass();

            for (String fieldName : compareFields) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);

                    Object oldValue = field.get(oldObj);
                    Object newValue = field.get(newObj);

                    if (!Objects.equals(oldValue, newValue)) {
                        changedFields.add(fieldName);
                    }
                } catch (NoSuchFieldException e) {
                    log.warn("字段不存在: {}", fieldName);
                } catch (IllegalAccessException e) {
                    log.warn("无法访问字段: {}", fieldName);
                }
            }
        } catch (Exception e) {
            log.error("比较对象变更失败", e);
        }

        return changedFields;
    }

    @Override
    public boolean deleteByBusinessId(String businessModule, Long businessId) {
        try {
            LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OperationLog::getBusinessModule, businessModule)
                       .eq(OperationLog::getBusinessId, businessId);
            return remove(queryWrapper);
        } catch (Exception e) {
            log.error("删除业务操作日志失败", e);
            return false;
        }
    }

    @Override
    public boolean cleanExpiredLogs(int retentionDays) {
        try {
            // 这里可以实现清理过期日志的逻辑
            // 例如删除超过指定天数的日志记录
            log.info("清理{}天前的操作日志", retentionDays);
            return true;
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return false;
        }
    }

    @Override
    public long countByBusinessModule(String businessModule) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationLog::getBusinessModule, businessModule);
        return count(queryWrapper);
    }

    @Override
    public long countByOperationType(String operationType) {
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationLog::getOperationType, operationType);
        return count(queryWrapper);
    }

    /**
     * 转换枚举显示名称
     */
    private void convertEnumNames(OperationLogDto dto) {
        // 转换业务模块名称
        if (dto.getBusinessModule() != null) {
            for (BusinessModuleEnum moduleEnum : BusinessModuleEnum.values()) {
                if (moduleEnum.getCode().equals(dto.getBusinessModule())) {
                    dto.setBusinessModuleName(moduleEnum.getName());
                    break;
                }
            }
        }

        // 转换操作类型名称
        if (dto.getOperationType() != null) {
            for (OperationTypeEnum typeEnum : OperationTypeEnum.values()) {
                if (typeEnum.getCode().equals(dto.getOperationType())) {
                    dto.setOperationTypeName(typeEnum.getName());
                    break;
                }
            }
        }
    }
}
