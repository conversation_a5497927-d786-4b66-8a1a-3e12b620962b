package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.common.enums.AnalyseDimensionEnum;
import com.snszyk.system.dto.DeptDTO;
import com.snszyk.system.entity.UserBehavior;
import com.snszyk.system.entity.UserOnlineDuration;
import com.snszyk.system.userbehavior.*;
import com.snszyk.zbusiness.stat.dto.IndicatorLogExtractDto;
import com.snszyk.zbusiness.stat.dto.LoginLogDto;
import com.snszyk.zbusiness.stat.vo.LoginLogQVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IUserBehaviorService extends IService<UserBehavior> {

	/**
	 * 首页统计
	 * @return
	 */
	UserBehaviorDTO userBehavior();

	/**
	 * 访客趋势
	 * @param dim
	 * @return
	 */
	List<VisitorTrendDTO> visitorTrend(AnalyseDimensionEnum dim);

	/**
	 * 浏览器占比
	 * @param dim 统计维度
	 * @return List<BrowserRateDTO>
	 */
	List<BrowserRateDTO> browserRate(AnalyseDimensionEnum dim);

	/**
	 * 浏览器占比
	 * @param dim 统计维度
	 * @return Map<String, BigDecimal>
	 */
	Map<String, BigDecimal> countByBrowser(AnalyseDimensionEnum dim);

	/**
	 * 活跃用户
	 * @param dim 统计维度
	 * @return List<ActiveUserDTO>
	 */
	List<ActiveUserDTO> activeUser(AnalyseDimensionEnum dim);

	/**
	 * 访问分析
	 * @param dim 统计维度
	 * @return List<PageViewAnalyseDTO>
	 */
	List<PageViewAnalyseDTO> pageView(AnalyseDimensionEnum dim);

	/**
	 * 根据日期查询list
	 * @param date
	 * @return
	 */
	List<DeptDTO> listByDate(LocalDate date);

	/**
	 * 提取登录的用户
	 * @param startOfMonth
	 * @param endOfMonth
	 * @return
	 */
    List<IndicatorLogExtractDto> indicatorCountUserLoginByOrg(LocalDateTime startOfMonth, LocalDateTime endOfMonth);

	/**
	 * 提取活跃的用户
	 *
	 * @param
	 * @param paramTime
	 * @return
	 */
	List<IndicatorLogExtractDto> indicatorActiveUserByOrg(LocalDateTime paramTime, Integer year, Integer month);

    UserOnlineDuration isLogin(Long id, LocalDateTime startTime, LocalDateTime endTime);

	IPage<LoginLogDto> loginLog(LoginLogQVo v);
}
