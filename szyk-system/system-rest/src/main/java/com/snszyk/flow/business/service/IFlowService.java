/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.business.service;

import com.snszyk.flow.core.entity.SzykFlow;

import java.util.Map;

/**
 * 工作流调用接口.
 *
 * <AUTHOR>
 */
public interface IFlowService {

	/**
	 * 开启流程
	 *
	 * @param processDefinitionId 流程id
	 * @param businessKey         业务key
	 * @param variables           参数
	 * @return SzykFlow
	 */
	SzykFlow startProcessInstanceById(String processDefinitionId, String businessKey, Map<String, Object> variables);

	/**
	 * 开启流程
	 *
	 * @param processDefinitionKey 流程标识
	 * @param businessKey          业务key
	 * @param variables            参数
	 * @return SzykFlow
	 */
	SzykFlow startProcessInstanceByKey(String processDefinitionKey, String businessKey, Map<String, Object> variables);

	/**
	 * 完成任务
	 *
	 * @param taskId            任务id
	 * @param processInstanceId 流程实例id
	 * @param comment           评论
	 * @param variables         参数
	 * @return R
	 */
	boolean completeTask(String taskId, String processInstanceId, String comment, Map<String, Object> variables);

	/**
	 * 获取流程变量
	 *
	 * @param taskId       任务id
	 * @param variableName 变量名
	 * @return R
	 */
	Object taskVariable(String taskId, String variableName);

	/**
	 * 获取流程变量集合
	 *
	 * @param taskId 任务id
	 * @return R
	 */
	Map<String, Object> taskVariables(String taskId);
}
