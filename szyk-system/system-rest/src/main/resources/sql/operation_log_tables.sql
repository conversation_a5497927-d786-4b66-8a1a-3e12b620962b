-- 操作日志表
CREATE TABLE `sys_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_module` varchar(50) NOT NULL COMMENT '业务模块',
  `business_id` bigint DEFAULT NULL COMMENT '业务ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务名称',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型',
  `operation_description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operation_result` varchar(20) NOT NULL COMMENT '操作结果',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operator_dept_id` bigint DEFAULT NULL COMMENT '操作人部门ID',
  `operator_dept_name` varchar(200) DEFAULT NULL COMMENT '操作人部门名称',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_uri` varchar(500) DEFAULT NULL COMMENT '请求URI',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `execution_time` bigint DEFAULT NULL COMMENT '执行时间(毫秒)',
  `old_data` longtext COMMENT '变更前数据',
  `new_data` longtext COMMENT '变更后数据',
  `change_fields` varchar(1000) DEFAULT NULL COMMENT '变更字段',
  `exception_info` text COMMENT '异常信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_module`,`business_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入示例数据
INSERT INTO `sys_operation_log` (
  `business_module`, 
  `business_id`, 
  `business_name`, 
  `operation_type`, 
  `operation_description`, 
  `operation_time`, 
  `operation_result`, 
  `operator_id`, 
  `operator_name`, 
  `operator_dept_id`, 
  `operator_dept_name`, 
  `client_ip`, 
  `request_uri`, 
  `request_method`, 
  `execution_time`
) VALUES (
  'INTERNET_ASSET', 
  1, 
  '测试互联网资产', 
  'CREATE', 
  '新增互联网资产台账', 
  NOW(), 
  'SUCCESS', 
  1, 
  '系统管理员', 
  1, 
  '信息技术部', 
  '127.0.0.1', 
  '/zbusiness/internet/save', 
  'POST', 
  150
);
