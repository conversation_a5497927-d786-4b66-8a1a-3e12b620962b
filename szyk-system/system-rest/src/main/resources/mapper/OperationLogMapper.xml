<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.system.mapper.OperationLogMapper">

    <!-- 结果映射 -->
    <resultMap id="OperationLogDtoMap" type="com.snszyk.system.dto.OperationLogDto">
        <id column="id" property="id"/>
        <result column="business_module" property="businessModule"/>
        <result column="business_id" property="businessId"/>
        <result column="business_name" property="businessName"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_description" property="operationDescription"/>
        <result column="operation_time" property="operationTime"/>
        <result column="operation_result" property="operationResult"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_dept_id" property="operatorDeptId"/>
        <result column="operator_dept_name" property="operatorDeptName"/>
        <result column="client_ip" property="clientIp"/>
        <result column="user_agent" property="userAgent"/>
        <result column="request_uri" property="requestUri"/>
        <result column="request_method" property="requestMethod"/>
        <result column="execution_time" property="executionTime"/>
        <result column="old_data" property="oldData"/>
        <result column="new_data" property="newData"/>
        <result column="change_fields" property="changeFields"/>
        <result column="exception_info" property="exceptionInfo"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询操作日志 -->
    <select id="pageList" resultMap="OperationLogDtoMap">
        SELECT
            id,
            business_module,
            business_id,
            business_name,
            operation_type,
            operation_description,
            operation_time,
            operation_result,
            operator_id,
            operator_name,
            operator_dept_id,
            operator_dept_name,
            client_ip,
            user_agent,
            request_uri,
            request_method,
            execution_time,
            old_data,
            new_data,
            change_fields,
            exception_info,
            create_time,
            update_time
        FROM sys_operation_log
        <where>
            <if test="vo.businessModule != null and vo.businessModule != ''">
                AND business_module = #{vo.businessModule}
            </if>
            <if test="vo.businessId != null">
                AND business_id = #{vo.businessId}
            </if>
            <if test="vo.businessName != null and vo.businessName != ''">
                AND business_name LIKE CONCAT('%', #{vo.businessName}, '%')
            </if>
            <if test="vo.operationType != null and vo.operationType != ''">
                AND operation_type = #{vo.operationType}
            </if>
            <if test="vo.operationResult != null and vo.operationResult != ''">
                AND operation_result = #{vo.operationResult}
            </if>
            <if test="vo.operatorName != null and vo.operatorName != ''">
                AND operator_name LIKE CONCAT('%', #{vo.operatorName}, '%')
            </if>
            <if test="vo.operatorDeptName != null and vo.operatorDeptName != ''">
                AND operator_dept_name LIKE CONCAT('%', #{vo.operatorDeptName}, '%')
            </if>
            <if test="vo.startOperationTime != null">
                AND operation_time >= #{vo.startOperationTime}
            </if>
            <if test="vo.endOperationTime != null">
                AND operation_time &lt;= #{vo.endOperationTime}
            </if>
            <if test="vo.clientIp != null and vo.clientIp != ''">
                AND client_ip = #{vo.clientIp}
            </if>
        </where>
        ORDER BY operation_time DESC
    </select>

    <!-- 根据业务ID查询操作日志 -->
    <select id="listByBusinessId" resultMap="OperationLogDtoMap">
        SELECT
            id,
            business_module,
            business_id,
            business_name,
            operation_type,
            operation_description,
            operation_time,
            operation_result,
            operator_id,
            operator_name,
            operator_dept_id,
            operator_dept_name,
            client_ip,
            user_agent,
            request_uri,
            request_method,
            execution_time,
            old_data,
            new_data,
            change_fields,
            exception_info,
            create_time,
            update_time
        FROM sys_operation_log
        WHERE business_module = #{businessModule}
          AND business_id = #{businessId}
        ORDER BY operation_time DESC
    </select>

</mapper>
