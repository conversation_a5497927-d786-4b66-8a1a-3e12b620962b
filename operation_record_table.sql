-- 操作记录表
CREATE TABLE `rs_operation_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型（如：INTERNET_ASSET）',
  `business_id` bigint(20) NOT NULL COMMENT '业务数据ID',
  `business_name` varchar(200) DEFAULT NULL COMMENT '业务数据名称（如：系统名称）',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型（CREATE/UPDATE/DELETE）',
  `operation_desc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `org_id` bigint(20) DEFAULT NULL COMMENT '主管单位ID',
  `org_name` varchar(200) DEFAULT NULL COMMENT '主管单位名称',
  `full_org_id` varchar(1000) DEFAULT NULL COMMENT '组织全路径ID',
  `full_org_name` varchar(1000) DEFAULT NULL COMMENT '组织全路径名称',
  `old_data` longtext COMMENT '变更前数据（JSON格式）',
  `new_data` longtext COMMENT '变更后数据（JSON格式）',
  `change_fields` text COMMENT '变更字段列表（JSON格式）',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录表';

-- 字段变更详情表（用于存储具体的字段变更信息）
CREATE TABLE `rs_operation_record_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` bigint(20) NOT NULL COMMENT '操作记录ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(200) DEFAULT NULL COMMENT '字段中文名称',
  `old_value` text COMMENT '变更前值',
  `new_value` text COMMENT '变更后值',
  `field_type` varchar(50) DEFAULT NULL COMMENT '字段类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  CONSTRAINT `fk_operation_record_detail_record_id` FOREIGN KEY (`record_id`) REFERENCES `rs_operation_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录详情表';
