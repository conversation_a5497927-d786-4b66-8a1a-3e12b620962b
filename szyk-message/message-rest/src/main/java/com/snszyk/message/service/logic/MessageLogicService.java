package com.snszyk.message.service.logic;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.constant.RedisConstant;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.kafka.producer.KafkaProducer;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.dingding.DingtalkService;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.dto.MessagePushDto;
import com.snszyk.message.dto.SzykMessageFileDto;
import com.snszyk.message.dto.UnreadMessageCountDto;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.*;
import com.snszyk.message.vo.MessagePushVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.dto.UserDeptDTO;
import com.snszyk.system.dto.UserRoleDTO;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.entity.UserRole;
import com.snszyk.system.service.IUserDeptService;
import com.snszyk.system.service.IUserRoleService;
import com.snszyk.system.service.IUserService;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息logic
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
@Slf4j
public class MessageLogicService extends BaseCrudLogicService<MessageDto, MessageVo> implements IMessageLogicService {

	private final IMessageService messageService;

	private final IMessagePushService messagePushService;

	private final KafkaProducer kafkaProducer;

	private final IMessageAppService messageAppService;

	private final IUserDeptService userDeptService;

	private final IUserRoleService userRoleService;

	private final DingtalkService dingtalkService;
	private final IUserService userService;
	private final SzykRedis redisService;
	private final ISzykMessageFileService szykMessageFileService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.messageService;
	}

	/**
	 * 消息推送
	 *
	 * @param messageVo
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R commitMessage(MessageVo messageVo) {
		if (StringUtil.isBlank(messageVo.getTitle())) {
			return R.fail("消息标题不能为空");
		}
		if (StringUtil.isBlank(messageVo.getContent())) {
			return R.fail("消息内容不能为空");
		}
		if (StringUtil.isBlank(messageVo.getType())) {
			return R.fail("消息类型不能为空");
		}
		if (StringUtil.isBlank(messageVo.getSender())) {
			return R.fail("发送人不能为空");
		}

		//保存消息
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			messageVo.setScheduleTime(new Date());
		}
		messageVo.setReceiverInfo(JSON.toJSONString(messageVo.getReceiverInfoVo()));
		messageVo.setAppKey("aaa");
		MessageDto messageDto = messageService.save(messageVo);

		//立即发送 - 根据用户类型获取消息接收者
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			String receiverType = messageVo.getReceiverType();
			ReceiverInfoVo receiverInfoVo = messageVo.getReceiverInfoVo();
			if (ReceiverTypeEnum.USER.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.UserVo> userList = receiverInfoVo.getUserList();
				messageVo.setTo(userList.stream()
					.map(ReceiverInfoVo.UserVo::getId)
					.distinct().toArray(Long[]::new));
			} else if (ReceiverTypeEnum.DEPT.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.DeptVo> deptList = receiverInfoVo.getDeptList();
				QueryWrapper<UserDept> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().in(UserDept::getDeptId,
					deptList.stream().map(ReceiverInfoVo.DeptVo::getDeptId).collect(Collectors.toList()));
				List<UserDept> userDeptList = userDeptService.list(queryWrapper);
				messageVo.setTo(userDeptList.stream()
					.map(UserDept::getUserId)
					.distinct().toArray(Long[]::new));
			} else if (ReceiverTypeEnum.ROLE.getCode().equals(receiverType)) {
				List<ReceiverInfoVo.RoleVo> roleList = receiverInfoVo.getRoleList();
				QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().in(UserRole::getRoleId,
					roleList.stream().map(ReceiverInfoVo.RoleVo::getRoleId).collect(Collectors.toList()));
				List<UserRole> userRoleList = userRoleService.list(queryWrapper);
				messageVo.setTo(userRoleList.stream()
					.map(UserRole::getUserId)
					.distinct().toArray(Long[]::new));
			} else {
				throw new ServiceException("非法的推送用户类型：" + receiverType);
			}

			//发送消息
			Long[] to = messageVo.getTo();
			if (to != null) {
				String[] typeArray = messageVo.getType().split(",");
				List<MessagePushVo> needAddPushList = new ArrayList<>();
				for (Long toLong : to) {
					for (String type : typeArray) {
						MessagePushVo messagePushVo = new MessagePushVo();
						messagePushVo.setMessageId(messageDto.getId());
						messagePushVo.setReceiver(toLong);
						messagePushVo.setType(type);
						messagePushVo.setHasRead(0);
						needAddPushList.add(messagePushVo);

					}
				}
				if (CollectionUtil.isNotEmpty(needAddPushList)) {
					messagePushService.batchInsert(needAddPushList);
				}
				//v1.4钉钉消息推送 [小程序 通知 角色] 临时处理 后期优化处理
				if (Arrays.asList(typeArray).contains("NOTICE") && Arrays.asList(messageVo.getChannel().split(",")).contains("MINI_PROGRAM")
					&& ReceiverTypeEnum.ROLE.getCode().equals(receiverType)) {
					List<String> dingtalkUserIdsByUserIds = dingtalkService.getDingtalkUserIdsByUserIds(Arrays.asList(messageVo.getTo()));
					if (CollectionUtil.isNotEmpty(dingtalkUserIdsByUserIds)) {
						dingtalkService.asyncSendNotice(dingtalkUserIdsByUserIds, messageVo.getTitle(), messageVo.getContent());
					}
				}
//				kafkaProducer.producerMessage(KafkaTopic.TOPIC1, messageVo);
				return R.success("推送成功");
			} else {
				return R.fail("消息接收人不能为空");
			}
		} else {
			return R.success("提交成功");
		}
	}

	/**
	 * 消息推送
	 *
	 * @param messageVo
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R commitMessageV2(MessageVo messageVo) {
		if (StringUtil.isBlank(messageVo.getTitle())) {
			return R.fail("消息标题不能为空");
		}
		if (StringUtil.isBlank(messageVo.getContent())) {
			return R.fail("消息内容不能为空");
		}
		//保存消息
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			messageVo.setScheduleTime(new Date());
		}
		messageVo.setReceiverInfo(JSON.toJSONString(messageVo.getReceiverInfoVo()));
		//保存接收人信息
		saveReceiveInfo(messageVo);
		MessageDto messageDto = messageService.save(messageVo);
		saveReceiveInfo(messageVo);
		//即时发送
		if (YesNoEnum.YES.getCode().equals(messageVo.getIsImmediate())) {
			messageVo.setScheduleTime(new Date());
			messageVo.setIsSend(CommonConstant.ONE);
			messageService.save(messageVo);
			//发送消息
			messageSend(messageVo);
		} else {
			//定时发送
			if (messageVo.getScheduleTime() == null) {
				throw new ServiceException("定时发送的时间不能为空!");
			}
			Duration duration = DateUtil.between(new Date(), messageVo.getScheduleTime());
			long seconds = duration.getSeconds();
			redisService.setEx(RedisConstant.MESSAGE_PUSH + messageVo.getId(), 1, seconds);
		}
		//20240301 添加消息的附件
		//删除之前的附件
		List<Long> attachIds = messageVo.getAttachIds();
		szykMessageFileService.deleteByBusiness(messageDto.getId(),CommonConstant.ONE);
		//添加新的附件
		if (CollectionUtil.isNotEmpty(attachIds)) {
			List<SzykMessageFileDto> attachList = attachIds.stream().map(id -> {
				SzykMessageFileDto fileDto = new SzykMessageFileDto();
				fileDto.setBusinessId(messageDto.getId());
				fileDto.setBusinessType(CommonConstant.ONE_STR);
				fileDto.setAttachId(id);
				return fileDto;
			}).collect(Collectors.toList());
			szykMessageFileService.saveBatch(attachList);
		}


		return R.success("OK");
	}

	@Override
	public IPage<MessageDto> indexPageSend(MessageVo vo) {
		Long userId = AuthUtil.getUserId();
		String deptId = AuthUtil.getDeptId();
		IPage<MessageDto> messageDtoIPage = messageService.indexPageSend(vo, userId, Long.valueOf(deptId));
		List<MessageDto> records = messageDtoIPage.getRecords();
		List<SzykMessageFileDto> attachDtoList=szykMessageFileService.listAttachByBusinessId(records.stream().map(MessageDto::getId).collect(Collectors.toList())
		,CommonConstant.ONE_STR);
		Map<Long, List<SzykMessageFileDto>> attachMap = attachDtoList.stream().collect(Collectors.groupingBy(SzykMessageFileDto::getBusinessId));
		for (MessageDto record : records) {
			record.setAttachDtoList(attachMap.get(record.getId()));
		}
		return messageDtoIPage;
	}

	/**
	 * 保存接收人信息
	 * @param messageVo
	 */

	private void saveReceiveInfo(MessageVo messageVo) {
		Long messageId = messageVo.getId();
		if (messageId == null) {
			log.error("消息推送,messageId不能为空");
			return;
		}
		List<MessagePushVo> pushVos = new ArrayList<>();
		String receiverType = messageVo.getReceiverType();
		ReceiverInfoVo receiverInfoVo = messageVo.getReceiverInfoVo();
		if (ReceiverTypeEnum.USER.getCode().equals(receiverType)) {

			List<ReceiverInfoVo.UserVo> userList = receiverInfoVo.getUserList();
			for (ReceiverInfoVo.UserVo userVo : userList) {
				MessagePushVo pushVo = new MessagePushVo();
				pushVo.setMessageId(messageId);
				pushVo.setReceiver(userVo.getId());
				pushVo.setDeptId(userVo.getDeptId());
				pushVos.add(pushVo);
			}
		} else if (ReceiverTypeEnum.DEPT.getCode().equals(receiverType)) {
			List<ReceiverInfoVo.DeptVo> deptList = receiverInfoVo.getDeptList();
			List<UserDeptDTO> userDeptDTOS = userDeptService.listByDeptIds(deptList.stream().map(ReceiverInfoVo.DeptVo::getDeptId).collect(Collectors.toList()));
			for (UserDeptDTO userDeptDTO : userDeptDTOS) {

				MessagePushVo pushVo = new MessagePushVo();
				pushVo.setMessageId(messageId);
				pushVo.setReceiver(userDeptDTO.getUserId());
				pushVo.setDeptId(userDeptDTO.getDeptId());
				pushVos.add(pushVo);
			}

		} else if (ReceiverTypeEnum.ROLE.getCode().equals(receiverType)) {
			List<ReceiverInfoVo.RoleVo> roleList = receiverInfoVo.getRoleList();
			List<UserRoleDTO> userRoleDTOList = userRoleService.listByRoleIds(roleList.stream().map(ReceiverInfoVo.RoleVo::getRoleId).collect(Collectors.toList()));
			for (UserRoleDTO userRoleDTO : userRoleDTOList) {
				MessagePushVo pushVo = new MessagePushVo();
				pushVo.setMessageId(messageId);
				pushVo.setReceiver(userRoleDTO.getUserId());
				pushVo.setDeptId(userRoleDTO.getDeptId());
				pushVos.add(pushVo);
			}
		}
		if (CollectionUtil.isNotEmpty(pushVos)) {
			messagePushService.batchInsert(pushVos);
		}
	}

	/**
	 * 钉钉消息发送
	 * @param messageVo
	 */
	private void messageSend(MessageVo messageVo) {
		Long messageId = messageVo.getId();
		List<MessagePushDto> messagePushDtos = messagePushService.listByMessagetId(messageId);
		List<Long> collect = messagePushDtos.stream().map(e -> e.getReceiver()).collect(Collectors.toList());
		List<String> dingtalkUserIdsByUserIds = dingtalkService.getDingtalkUserIdsByUserIds(collect);
		dingtalkService.asyncSendNotice(dingtalkUserIdsByUserIds, messageVo.getTitle(), messageVo.getContent());
	}

	/**
	 *获取所有的dingding 用户id
	 * @return
	 */
	@NotNull
	private Set<String> getAllDingUserIds() {
		Set<String> userDingSet;
		List<User> allUser = userService.list();
		//有钉钉id的用户
		userDingSet = allUser.stream().filter(e ->
			StringUtil.isNotBlank(e.getDingtalkUserId())).map(User::getDingtalkUserId).collect(Collectors.toSet());
		//没有钉钉id的用户
		Set<User> noDingUserSet = allUser.stream().filter(e ->
				StringUtil.isBlank(e.getDingtalkUserId()) && StringUtil.isNotBlank(e.getPhone()))
			.collect(Collectors.toSet());
		for (User user : noDingUserSet) {
			String dingUserId = dingtalkService.getUserIdByMobile(user.getPhone());
			if (StringUtil.isNotBlank(dingUserId)) {
				userDingSet.add(dingUserId);
				boolean b = userService.updateDingtalkUserId(user.getId(), dingUserId);
				if (!b) {
					log.error("消息推送获取用户的dingUserId失败:userId:" + user.getId());
				}
			}
		}
		return userDingSet;
	}

	/**
	 * 已读消息
	 *
	 * @param messagePushVo
	 * @return
	 */
	public MessagePushDto readMessage(MessagePushVo messagePushVo) {
		messagePushVo.setHasRead(1);
		messagePushVo.setReadTime(new Date());
		return messagePushService.save(messagePushVo);
	}

	/**
	 * 消息接收分页列表
	 *
	 * @param messagePushVo 查询条件
	 * @return
	 */
	public IPage<MessagePushDto> pageReceive(MessagePushVo messagePushVo) {
		return messagePushService.pageReceive(messagePushVo);
	}

	/**
	 * 消息全部已读
	 *
	 * @param v
	 */
	public void updateAllReadMessagePushVo(MessagePushVo v) {
		messagePushService.updateAllReadMessagePushVo(v);
	}

	/**
	 * 消息详情
	 *
	 * @param messageId 消息id
	 * @return
	 */
	public R<MessageDto> detail(String messageId) {
		MessageDto messageDetail = messageService.fetchById(Long.parseLong(messageId));
		ReceiverInfoVo receiverInfoVo = JSON.parseObject(messageDetail.getReceiverInfo(), ReceiverInfoVo.class);
		if (receiverInfoVo == null) {
			receiverInfoVo = new ReceiverInfoVo();
		}
		List<ReceiverInfoVo.UserVo> userList=new ArrayList<>();
		receiverInfoVo.setUserList(userList);
		List<MessagePushDto> messagePushDtos = messagePushService.listByMessagetId(messageDetail.getId());
		//添加接受人信息
		if(CollectionUtil.isNotEmpty(messagePushDtos)){
			for (MessagePushDto pushDto : messagePushDtos) {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(pushDto.getReceiver());
				userVo.setDeptId(pushDto.getDeptId());
				userVo.setRealName(pushDto.getRealName());
				String deptName = pushDto.getDeptName();
				if(StringUtil.isNotBlank(deptName)){
					deptName=deptName.replaceAll("顶级-","");
					deptName=deptName.replaceAll("-","/");
				}
				userVo.setDeptName(deptName);
				userList.add(userVo);
			}
		}
		messageDetail.setReceiverInfoVo(receiverInfoVo);
		List<SzykMessageFileDto> szykMessageFileDtos = szykMessageFileService.listAttachByBusinessId(Arrays.asList(messageDetail.getId()), CommonConstant.ONE_STR);
		messageDetail.setAttachDtoList(szykMessageFileDtos);
		return R.data(messageDetail);
	}

	/**
	 * 消息发送分页列表 - 管理员
	 *
	 * @param vo 查询条件
	 * @return
	 */
	public IPage<MessageDto> pageSend(MessageVo vo) {
		return messageService.pageSend(vo);
	}

	/**
	 * 删除消息
	 *
	 * @param id 消息id
	 */
	public void deleteMessage(Long id) {
		MessageDto messageDto = messageService.fetchById(id);
		if (messageDto == null) {
			throw new ServiceException("消息不存在，id = " + id);
		}
		if (YesNoEnum.YES.getCode().equals(messageDto.getIsImmediate())) {
			throw new ServiceException("消息已发送，不允许删除！");
		}
		messageService.deleteById(id);
		if (Objects.equals(CommonConstant.ZERO, messageDto.getIsSend())) {
			redisService.del(RedisConstant.MESSAGE_PUSH + id);
		}
	}

	/**
	 * 获取未读消息数
	 *
	 * @param userId 用户id
	 * @return
	 */
	public UnreadMessageCountDto countNotReadMessage(Long userId) {
		UnreadMessageCountDto dto = new UnreadMessageCountDto();
		dto.setInAppCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.IN_APP.getCode()));
		dto.setWorkToDoCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.WORK_TODO.getCode()));
		dto.setWarningCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.WARNING.getCode()));
		dto.setNoticeCount(messagePushService.queryUnreadMessageCount(userId, MessageTypeEnum.NOTICE.getCode()));
		dto.setTotalCount(dto.getInAppCount() + dto.getWorkToDoCount() + dto.getWarningCount() + dto.getNoticeCount());
		return dto;
	}

	/**
	 * redis 过期监听发送消息
	 * @param messageId
	 */
	public void sendMessageById(String messageId) {
		if (StringUtil.isBlank(messageId)) {
			return;
		}
		MessageDto messageDto = messageService.fetchById(Long.valueOf(messageId));
		//信息是否存在
		if (messageDto == null) {
			return;
		}
		Integer isSend = messageDto.getIsSend();
		//已经发送的不在重新发送
		if (CommonConstant.ONE.equals(isSend)) {
			return;
		}
		//获取系统内的所有用户
		messageSend(BeanUtil.copy(messageDto,MessageVo.class));
		MessageVo messageVo = BeanUtil.copy(messageDto, MessageVo.class);
		messageVo.setIsSend(CommonConstant.ONE);
		messageService.save(messageVo);
	}
}
