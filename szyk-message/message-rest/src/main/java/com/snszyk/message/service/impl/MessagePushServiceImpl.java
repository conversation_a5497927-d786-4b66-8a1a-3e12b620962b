/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.message.dto.MessagePushDto;
import com.snszyk.message.entity.MessagePush;
import com.snszyk.message.mapper.MessagePushMapper;
import com.snszyk.message.service.IMessagePushService;
import com.snszyk.message.vo.MessagePushVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 消息推送service
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class MessagePushServiceImpl extends BaseCrudServiceImpl<MessagePushMapper, MessagePush, MessagePushDto, MessagePushVo> implements IMessagePushService {

	@Override
	public Wrapper<MessagePush> beforePage(MessagePushVo v) {
		if (v != null) {
			QueryWrapper<MessagePush> queryWrapper = new QueryWrapper<>();
			if (v.getReceiver() != null) {
				queryWrapper.lambda().eq(MessagePush::getReceiver, v.getReceiver());
			}
			if (v.getHasRead() != null) {
				queryWrapper.lambda().eq(MessagePush::getHasRead, v.getHasRead());
			}
			queryWrapper.orderByDesc("create_time");
			return queryWrapper;
		}
		return Wrappers.emptyWrapper();
	}

	/**
	 * 消息接收分页列表
	 * @param vo vo
	 * @return
	 */
	@Override
	public IPage<MessagePushDto> pageReceive(MessagePushVo vo) {
		IPage<MessagePushDto> page = new Page<>();
		page.setCurrent(vo.getCurrent());
		page.setSize(vo.getSize());
		page.setRecords(baseMapper.pageReceive(page, vo));
		return page;
	}

	@Override
	public void updateAllReadMessagePushVo(MessagePushVo v) {
		UpdateWrapper<MessagePush> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(MessagePush::getReceiver, v.getReceiver())
			.eq(MessagePush::getType, v.getType())
			.eq(MessagePush::getHasRead, 0)
			.set(MessagePush::getHasRead, 1)
			.set(MessagePush::getReadTime, new Date());
		super.update(updateWrapper);
	}

	@Override
	public List<MessagePushDto> batchInsert(List<MessagePushVo> needAddPushList) {
		if(CollectionUtils.isEmpty(needAddPushList)){
			return new ArrayList<>();
		}
		List<MessagePush> pushList = BeanUtil.copy(needAddPushList, MessagePush.class);
		SpringUtil.getBean(MessagePushServiceImpl.class).saveBatch(pushList);
		return BeanUtil.copy(pushList, MessagePushDto.class);
	}

    @Override
    public List<MessagePushDto> listByMessagetId(Long messageId) {
		if(messageId==null){
			return new ArrayList<>();
		}
		return this.baseMapper.listByMessagetId(messageId);

	}

    /**
	 * 获取用户未读消息数
	 * @param userId 用户id
	 * @param type 消息类型
	 * @return
	 */
	@Override
	public Integer queryUnreadMessageCount(Long userId, String type) {
		return baseMapper.selectCount(new LambdaQueryWrapper<MessagePush>()
			.eq(MessagePush::getReceiver, userId)
			.eq(MessagePush::getType,type)
			.eq(MessagePush::getHasRead, 0)
			.eq(BaseCrudEntity::getIsDeleted, 0));
	}
}
