package com.snszyk.message.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;

import java.util.Date;

/**
 * 消息
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@TableName("szyk_message")
@Data
public class Message extends BaseCrudEntity {

    /**
     * appKey
     */
    private String appKey;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 发信人
     */
    private String sender;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息跳转链接
     */
    private String url;

    /**
     * 读取完消息后的回执URL
     */
    private String readCallbackUrl;

	/**
	 * 立即发送：1-否；2-是
	 */
	private Integer isImmediate;

    /**
     * 定时发送消息时间
     */
    private Date scheduleTime;

	/**
	 * 发送渠道：PC、APP、MINI_PROGRAM
	 */
	private String channel;

	/**
	 * 接收人类型：USER、DEPT、ROLE
	 */
	private String receiverType;

	/**
	 * 接收人信息：json字符串
	 */
	private String receiverInfo;

	/**
	 * 是否已经发送
	 */
	private Integer isSend;
}
