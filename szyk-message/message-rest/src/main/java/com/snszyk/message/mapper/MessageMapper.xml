<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.message.mapper.MessageMapper">

    <select id="pageSend" resultType="com.snszyk.message.dto.MessageDto">
        SELECT *
        FROM szyk_message
        <where>
            is_deleted = 0
            <if test="vo.title != null and vo.title !=''">
                AND title LIKE concat('%',#{vo.title},'%')
            </if>
            <if test="vo.content != null and vo.content !=''">
                AND content LIKE concat('%',#{vo.content},'%')
            </if>
            <if test="vo.scheduleTime != null">
                AND DATE(schedule_time) = DATE(#{vo.scheduleTime})
            </if>
            <if test="vo.isSend != null">
                AND is_send=#{vo.isSend}
            </if>
        </where>
        ORDER BY schedule_time DESC
    </select>
    <select id="indexPageSend" resultType="com.snszyk.message.dto.MessageDto">

        SELECT distinct  t.id, t.app_key, t.type, t.sender, t.title, t.content, t.url, t.read_callback_url, t.is_send,
        t.is_immediate, t.schedule_time, t.channel, t.receiver_type, t.receiver_info, t.create_user, t.create_dept,
        t.create_time, t.update_user, t.update_time, t.status, t.is_deleted
        FROM szyk_message t
        left join szyk_message_push t1 on t1.message_id = t.id
        <where>
            t.is_deleted = 0
            and t1.receiver = #{userId}
           and t1.dept_id=#{deptId}
            <if test="vo.title != null and vo.title !=''">
                AND t.title LIKE concat('%',#{vo.title},'%')
            </if>
            <if test="vo.content != null and vo.content !=''">
                AND t.content LIKE concat('%',#{vo.content},'%')
            </if>
            <if test="vo.scheduleTime != null">
                AND DATE(t.schedule_time) = DATE(#{vo.scheduleTime})
            </if>
            <if test="vo.isSend != null">
                AND t.is_send=#{vo.isSend}
            </if>
        </where>
        ORDER BY t.schedule_time DESC

    </select>

</mapper>
