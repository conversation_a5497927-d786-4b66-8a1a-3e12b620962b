package com.snszyk.message.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;

import java.util.Date;

/**
 * 消息推送
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@TableName("szyk_message_push")
@Data
public class MessagePush extends BaseCrudEntity {

    /**
     * 消息id
     */
    private Long messageId;

    /**
     * 收信人
     */
    private Long receiver;

	/**
	 * 消息类型
	 */
	private String type;

    /**
     * 是否已读
     */
    private Integer hasRead;

    /**
     * 读信时间
     */
    private Date readTime;

	/**
	 * 业务范围id
	 */
	private  Long deptId;

}
