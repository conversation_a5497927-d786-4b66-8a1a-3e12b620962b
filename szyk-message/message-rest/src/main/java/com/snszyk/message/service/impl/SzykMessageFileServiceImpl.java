/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.message.dto.SzykMessageFileDto;
import com.snszyk.message.entity.SzykMessageFile;
import com.snszyk.message.mapper.SzykMessageFileMapper;
import com.snszyk.message.service.ISzykMessageFileService;
import com.snszyk.message.vo.SzykMessageFileVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 信息附件表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@AllArgsConstructor
@Service
public class SzykMessageFileServiceImpl extends BaseCrudServiceImpl<SzykMessageFileMapper, SzykMessageFile, SzykMessageFileDto, SzykMessageFileVo> implements ISzykMessageFileService {


	@Override
	public boolean deleteByBusiness(Long businessId, Integer businessType) {
		if (businessId == null && businessType == null) {
			return false;
		}
		return this.lambdaUpdate().eq(SzykMessageFile::getBusinessId, businessId).eq(SzykMessageFile::getBusinessType, businessType).remove();
	}

	@Override
	public boolean saveBatch(List<SzykMessageFileDto> attachList) {
		if (CollectionUtil.isEmpty(attachList)) {
			return false;
		}
		return this.saveBatch(BeanUtil.copy(attachList, SzykMessageFile.class));
	}

	@Override
	public List<SzykMessageFileDto> listAttachByBusinessId(List<Long> businessIds, String businessType) {
		if (CollectionUtil.isEmpty(businessIds)) {
			return new ArrayList<>();
		}
		return baseMapper.listAttachByBusinessId(businessIds, businessType);
	}
}
