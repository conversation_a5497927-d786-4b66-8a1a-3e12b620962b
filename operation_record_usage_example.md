# 操作记录系统使用示例

## 1. 注解使用示例

### 1.1 基本使用

```java
@OperationRecord(
    businessType = "INTERNET_ASSET",
    operationType = OperationTypeEnum.UPDATE,
    businessIdField = "id",
    businessNameField = "systemName",
    orgIdField = "orgId",
    description = "保存或更新互联网资产"
)
public RsInternetDto saveOrUpdate(RsInternetVo vo) {
    // 业务逻辑
}
```

### 1.2 记录详细变更

```java
@OperationRecord(
    businessType = "INTERNET_ASSET",
    operationType = OperationTypeEnum.UPDATE,
    businessIdField = "id",
    businessNameField = "systemName",
    orgIdField = "orgId",
    description = "更新互联网资产",
    recordDetails = true,
    ignoreFields = {"createTime", "updateTime", "createUser", "updateUser"}
)
public RsInternetDto update(RsInternetVo vo) {
    // 业务逻辑
}
```

### 1.3 删除操作记录

```java
@OperationRecord(
    businessType = "INTERNET_ASSET",
    operationType = OperationTypeEnum.DELETE,
    businessIdField = "id",
    businessNameField = "systemName",
    orgIdField = "orgId",
    description = "删除互联网资产",
    recordDetails = false
)
public boolean delete(Long id) {
    // 业务逻辑
}
```

## 2. API接口使用

### 2.1 分页查询操作记录

```http
GET /operation-record/page?businessType=INTERNET_ASSET&orgName=测试单位&current=1&size=10
```

响应示例：
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "businessType": "INTERNET_ASSET",
                "businessTypeName": "互联网资产",
                "businessId": 123,
                "businessName": "测试系统",
                "operationType": "UPDATE",
                "operationTypeName": "更新",
                "operationDesc": "互联网资产更新",
                "orgName": "测试单位",
                "operatorName": "张三",
                "operationTime": "2024-07-04 10:30:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    }
}
```

### 2.2 查询操作记录详情

```http
GET /operation-record/detail/1
```

响应示例：
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "businessType": "INTERNET_ASSET",
        "businessName": "测试系统",
        "operationType": "UPDATE",
        "operationDesc": "互联网资产更新",
        "operatorName": "张三",
        "operationTime": "2024-07-04 10:30:00",
        "detailList": [
            {
                "fieldName": "systemName",
                "fieldLabel": "系统名称",
                "oldValue": "旧系统名称",
                "newValue": "新系统名称",
                "oldDisplayValue": "旧系统名称",
                "newDisplayValue": "新系统名称"
            },
            {
                "fieldName": "resourceStatus",
                "fieldLabel": "资源状态",
                "oldValue": "0",
                "newValue": "1",
                "oldDisplayValue": "在用",
                "newDisplayValue": "停用"
            }
        ]
    }
}
```

### 2.3 根据业务ID查询操作记录

```http
GET /operation-record/list-by-business?businessType=INTERNET_ASSET&businessId=123
```

## 3. 扩展其他业务模块

### 3.1 添加新的业务类型

在 `BusinessTypeEnum` 中添加新的业务类型：

```java
public enum BusinessTypeEnum {
    INTERNET_ASSET("INTERNET_ASSET", "互联网资产"),
    SYSTEM_RECORD("SYSTEM_RECORD", "信息系统台账"),
    NEW_BUSINESS("NEW_BUSINESS", "新业务模块");
    
    // ...
}
```

### 3.2 为新业务模块添加操作记录

```java
@Service
public class NewBusinessLogicService {
    
    @OperationRecord(
        businessType = "NEW_BUSINESS",
        operationType = OperationTypeEnum.CREATE,
        businessIdField = "id",
        businessNameField = "name",
        orgIdField = "orgId",
        description = "创建新业务数据"
    )
    public NewBusinessDto create(NewBusinessVo vo) {
        // 业务逻辑
    }
}
```

### 3.3 扩展业务数据查询

在 `RsOperationRecordServiceImpl.getBusinessData` 方法中添加新的业务类型支持：

```java
@Override
public Object getBusinessData(String businessType, Long businessId) {
    switch (businessType) {
        case "INTERNET_ASSET":
            return rsInternetService.fetchById(businessId);
        case "NEW_BUSINESS":
            return newBusinessService.fetchById(businessId);
        default:
            return null;
    }
}
```

## 4. 前端集成示例

### 4.1 操作记录列表页面

```javascript
// 查询操作记录
const queryOperationRecords = async (params) => {
    const response = await request.get('/operation-record/page', { params });
    return response.data;
};

// 查看详情
const viewDetail = async (id) => {
    const response = await request.get(`/operation-record/detail/${id}`);
    return response.data;
};
```

### 4.2 变更详情展示

```vue
<template>
    <div class="change-detail">
        <h3>变更详情</h3>
        <table class="change-table">
            <thead>
                <tr>
                    <th>字段名称</th>
                    <th>变更前</th>
                    <th>变更后</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="detail in detailList" :key="detail.fieldName">
                    <td>{{ detail.fieldLabel }}</td>
                    <td>{{ detail.oldDisplayValue || '--' }}</td>
                    <td>{{ detail.newDisplayValue || '--' }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
```

## 5. 配置说明

### 5.1 异步处理配置

确保Spring Boot应用启用了异步处理：

```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("operation-record-");
        executor.initialize();
        return executor;
    }
}
```

### 5.2 数据库索引优化

确保在操作记录表上创建了适当的索引：

```sql
-- 业务查询索引
CREATE INDEX idx_rs_operation_record_business ON rs_operation_record(business_type, business_id);

-- 组织查询索引
CREATE INDEX idx_rs_operation_record_org ON rs_operation_record(org_id, operation_time);

-- 操作人查询索引
CREATE INDEX idx_rs_operation_record_operator ON rs_operation_record(operator_id, operation_time);

-- 时间范围查询索引
CREATE INDEX idx_rs_operation_record_time ON rs_operation_record(operation_time);
```

## 6. 注意事项

1. **性能考虑**：操作记录采用异步保存，不会影响主业务流程的性能
2. **存储空间**：建议定期清理历史操作记录，保留最近6个月或1年的数据
3. **权限控制**：查询操作记录时会自动应用组织权限过滤
4. **字段映射**：需要在 `getFieldLabelMap` 方法中维护字段名称到中文标签的映射
5. **异常处理**：操作记录保存失败不会影响主业务流程，但会记录错误日志
