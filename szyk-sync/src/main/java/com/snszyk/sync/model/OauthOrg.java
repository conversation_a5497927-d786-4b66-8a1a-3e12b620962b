package com.snszyk.sync.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 **/
@Data
public class OauthOrg {
	/**
	 * 同企业权威数据源系统（如 HR）中组织代码
	 */
	@ApiModelProperty(value = "同企业权威数据源系统（如 HR）中组织代码")
	private String orgCode;
	/**
	 * 组织名称
	 */
	@ApiModelProperty(value = "组织名称")
	private String orgName;
	/**
	 * 上级组织代码，根组织的上级组织代码为空
	 */
	@ApiModelProperty(value = "上级组织代码，根组织的上级组织代码为空")
	private String parentOrgCode;
	/**
	 * 组织简称
	 */
	@ApiModelProperty(value = "组织简称")
	private String simplename;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String createtime;
	/**
	 * 结束日期的值
	 */
	@ApiModelProperty(value = "结束日期的值")
	private String expiredtime;
	/**
	 * 组织类型代码
	 */
	@ApiModelProperty(value = "组织类型代码")
	private String orgtype;
	/**
	 * 组织类型名称
	 */
	@ApiModelProperty(value = "组织类型名称")
	private String orgtypename;
	/**
	 * 公司代码
	 */
	@ApiModelProperty(value = "公司代码")
	private String companycode;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyname;
	/**
	 * 数据来源
	 */
	@ApiModelProperty(value = "数据来源")
	private String sourcefrom;
	/**
	 * 排序码
	 */
	@ApiModelProperty(value = "排序码")
	private String ordernum;
}
