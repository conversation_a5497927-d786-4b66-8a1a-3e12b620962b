package com.snszyk.sync.custom;

import com.snszyk.sync.enums.ResultCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
@ApiModel(
	description = "返回信息"
)
@Data
public class Result<T> implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 同步任务流水号，同入参，可直接返回入参的 requestId，用于 IT 核对接口。
	 */
	@ApiModelProperty(value = "同步任务流水号，同入参，可直接返回入参的 requestId，用于 IT 核对接口")
	private String requestId;
	/**
	 * 返回码，不可为空，0 为成功，其他为失败，失败返回码由业务系统自定义。
	 */
	@ApiModelProperty(value = "返回码，不可为空，0 为成功，其他为失败，失败返回码由业务系统自定义")
	private int returnCode;
	/**
	 * 对返回码的文本描述内容，成功可为空。
	 */
	@ApiModelProperty(value = "对返回码的文本描述内容，成功可为空")
	private String returnMessage;

	public Result(String requestId, int returnCode, String returnMessage) {
		this.requestId = requestId;
		this.returnCode = returnCode;
		this.returnMessage = returnMessage;
	}

	public Result(String requestId, int returnCode) {
		this.requestId = requestId;
		this.returnCode = returnCode;
	}

	public static <T> Result<T> success(String requestId){
		return new Result(requestId,ResultCode.SUCCESS.getCode());
	}

	public static <T> Result<T> success(String requestId,String message){
		return new Result(requestId,ResultCode.SUCCESS.getCode(),message);
	}

	public static <T> Result<T> success(String requestId,ResultCode resultCode){
		return new Result(requestId,resultCode.getCode(),resultCode.getMessage());
	}

	public static <T> Result<T> fail(String requestId, ResultCode resultCode){
		return new Result(requestId,resultCode.getCode(),resultCode.getMessage());
	}

	public static <T> Result<T> fail(String requestId,ResultCode resultCode,String message){
		return new Result(requestId,resultCode.getCode(),message);
	}

	@Override
	public String toString() {
		return "Result{" +
			"requestId='" + requestId + '\'' +
			", returnCode=" + returnCode +
			", returnMessage='" + returnMessage + '\'' +
			'}';
	}
}
