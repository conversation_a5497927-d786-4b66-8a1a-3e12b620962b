package com.snszyk.sync.util;

import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

/**
 * 统一认证的Jwt工具类
 */
public class IamJwtUtil {
	public static void main(String[] args) {
		//生成jwt
		String appid = "56c4ef2c71694206";
		String appSecret = "33613fd52b95417c82523f4960db0f19";
		String token = JWT.create().withIssuer(appid)
			.withIssuedAt(new Date())
			.withJWTId(UUID.randomUUID()
				.toString())
			.sign(Algorithm.HMAC256(appSecret));
		System.out.println("token:\n" + token);

		//是否有效
		DecodedJWT verify = JWT.require(Algorithm.HMAC256(appSecret)).build().verify(token);
		System.out.println("verify:\n" + verify);
		//发送请求
		HashMap<String, Object> param = new HashMap<>();
		param.put("apps", Arrays.asList("1906806216518794648"));
		param.put("accountNo", Arrays.asList("********"));
		param.put("actionFlag", "0");
		String result = HttpRequest.post("https://iamtest.ykjt.cc/esc-idm/api/v1/reverse/appAccount/update")
			.header("Authorization", token)//头信息，多个头信息多次调用此方法即可
			.body(JSON.toJSONString(param))
			.timeout(20000)//超时，毫秒
			.execute().body();
		Console.log(result);
	}

	public static String createToken(String appid, String appSecret) {
		String token = JWT.create().withIssuer(appid)
			.withIssuedAt(new Date())
			.withJWTId(UUID.randomUUID()
				.toString())
			.sign(Algorithm.HMAC256(appSecret));
		return token;
	}
}
