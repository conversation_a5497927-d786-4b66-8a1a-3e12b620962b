package com.snszyk.sync.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 **/
@Data
public class BaseRequest {

	/**
	 * 同步任务流水号，返回信息里面需要带回,由统一身份管理平台随机产生
	 */
	@ApiModelProperty(value = "同步任务流水号")
	private String requestId;
	/**
	 * 应用系统在统一身份管理平台注册的应用编码
	 */
	@ApiModelProperty(value = "应用系统在统一身份管理平台注册的应用编码")
	private String appID;
	/**
	 * 任务参数生成的签名（见 3.3 签名生成规则）
	 */
	@ApiModelProperty(value = "任务参数生成的签名（见 3.3 签名生成规则）")
	private String sign;
}
