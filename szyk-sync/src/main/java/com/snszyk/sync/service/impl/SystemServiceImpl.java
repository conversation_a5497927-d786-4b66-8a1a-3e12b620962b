package com.snszyk.sync.service.impl;

import com.snszyk.core.cache.constant.CacheConstant;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.sync.client.ISyncOrgClient;
import com.snszyk.sync.client.ISyncUserClient;
import com.snszyk.sync.custom.Result;
import com.snszyk.sync.entity.IamUser;
import com.snszyk.sync.enums.ActionTypeEnum;
import com.snszyk.sync.enums.ResultCode;
import com.snszyk.sync.service.ISystemService;
import com.snszyk.sync.service.IiamUserService;
import com.snszyk.sync.vo.RequestOrgVO;
import com.snszyk.sync.vo.RequestUserVO;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 *
 **/
@Service
public class SystemServiceImpl implements ISystemService {

	@Autowired(required = false)
	private ISyncUserClient syncUserClient;
	@Autowired(required = false)
	private ISyncOrgClient syncOrgClient;
	@Autowired
	private IUserService userService;
	@Autowired
	private IiamUserService iiamUserService;

	@Override
	public Result operateUser(RequestUserVO user) {
		Boolean result = false;
		if (syncUserClient == null) {
			return Result.fail(user.getRequestId(), ResultCode.NOT_ACCESS_SYNC_USER);
		}
		ActionTypeEnum actionTypeEnum = ActionTypeEnum.getByCode(user.getActionType());
		if (actionTypeEnum == ActionTypeEnum.ADD || actionTypeEnum == ActionTypeEnum.MODIFY) { //更新操作
			result = syncUserClient.saveOrUpdate(user.getData(), actionTypeEnum);
		} else if (actionTypeEnum == ActionTypeEnum.MODIFY_PASSWORD) { //修改密码
			result = syncUserClient.updatePassword(user.getData());
		} else if (actionTypeEnum == ActionTypeEnum.DELETE || actionTypeEnum == ActionTypeEnum.DISABLE) { //删除操作
			result = syncUserClient.deleteByAccount(user.getData().getAccountNo());
		} else {
			return Result.fail(user.getRequestId(), ResultCode.NOT_FUNCTION);
		}
		//清空系统缓存
		CacheUtil.clear(CacheConstant.USER_CACHE);
		if (!result) {
			return Result.fail(user.getRequestId(), ResultCode.FAILURE);
		}
		return Result.success(user.getRequestId(), ResultCode.SUCCESS);
	}

	@Override
	public Result operateOrg(RequestOrgVO org) {
		if (syncOrgClient == null) {
			return Result.fail(org.getRequestId(), ResultCode.NOT_ACCESS_SYNC_ORG);
		}
		ActionTypeEnum actionTypeEnum = ActionTypeEnum.getByCode(org.getActionType());
		if (actionTypeEnum == ActionTypeEnum.ADD || actionTypeEnum == ActionTypeEnum.MODIFY) { //更新操作
			syncOrgClient.saveOrUpdate(org.getData(), actionTypeEnum);
		} else if (actionTypeEnum == ActionTypeEnum.DELETE) { //删除操作
			syncOrgClient.deleteByCode(org.getData().getOrgCode());
		}
		//清空系统缓存
		CacheUtil.clear(CacheConstant.SYS_CACHE);
		return Result.success(org.getRequestId(), ResultCode.SUCCESS);
	}

	@Override
	public R<Boolean> syncOrg() {
		syncOrgClient.syncOrg("");
		return R.data(true);
	}


	/**
	 * 统一认证的用户的停用
	 * @param accountList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean updateIamUserStatus(List<String> accountList) {
		if (CollectionUtil.isEmpty(accountList)) {
			return false;
		}

		//本地数据库user status的更新
		boolean b = userService.stopUserList(accountList);
		if (!b) {
			throw new ServiceException("用户本地停用失败");
		}
		for (String account : accountList) {
			IamUser iamUser = iiamUserService.queryByAccount(account);
			if (iamUser != null) {
				//删除IAM组织表
				iiamUserService.removeById(iamUser.getId());
			}
			//删除用户关联的部门角色
			UserDTO userDTO = userService.queryuserByAccount(account);
			Optional.of(userDTO).ifPresent(user -> {
				userService.deleteUserLinkInfo(Collections.singletonList(user.getId()));
				//重置默认部门角色
				userService.resetDefaultDeptRole(Collections.singletonList(user.getId()));
			});
		}
		//统一认证的更新
		Boolean sync = syncUserClient.appAccountUpdate(accountList, "0");
		if (!sync) {
			throw new ServiceException("用户统一认证停用失败");
		}
		return true;
	}
}
