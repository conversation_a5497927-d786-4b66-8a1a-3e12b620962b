<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sync.mapper.IamOrgMapper">

    <select id="queryFromIamOrg" resultType="com.snszyk.sync.entity.IamOrg">
        SELECT
            *
        FROM
            iam_org
        WHERE
            org_code NOT IN ( SELECT dept_code FROM szyk_dept )
          AND orgtype IS NOT NULL
          AND orgtype != ''
        ORDER BY
            parent_org_code ASC
    </select>

</mapper>
