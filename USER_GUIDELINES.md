# SZYK 项目代码风格指南

## 1. 项目架构与模块结构

### 1.1 整体架构

SZYK项目采用微服务架构，主要模块包括：
- `szyk-admin`: 启动、配置模块
- `szyk-common`: 公共基础模块
- `szyk-quartz`: 定时任务
- `szyk-system`: 系统基础模块
- `szyk-zbusiness`: 业务模块

### 1.2 业务模块结构

`szyk-zbusiness`模块采用多模块结构，子模块包括：
- `zbusiness-api`: 公共API模块，包含DTO、接口定义
- `zbusiness-project`: 项目管理模块
- `zbusiness-dict`: 字典管理模块
- `zbusiness-person`: 人员管理模块
- `zbusiness-resource`: 资源管理模块
- `zbusiness-assess`: 评估管理模块
- `zbusiness-rpc`: RPC服务模块
- `zbusiness-knowledge`: 知识库模块
- `zbusiness-flow`: 流程管理模块
- `zbusiness-task`: 任务管理模块
- `zbusiness-stat`: 统计分析模块

每个业务子模块内部通常按以下结构组织：
```
com.snszyk.zbusiness.{module}
  ├── annotations     # 注解定义
  ├── aspect          # 切面实现
  ├── controller      # 控制器
  ├── dto             # 数据传输对象
  ├── entity          # 实体类
  ├── enums           # 枚举定义
  ├── mapper          # MyBatis映射接口
  ├── service         # 服务接口及实现
  │   └── impl        # 服务实现
  ├── service.logic   # 业务逻辑服务
  │   └── impl        # 业务逻辑实现
  ├── util            # 工具类
  └── vo              # 视图对象
```

## 2. 命名规范

### 2.1 类命名规范

- 实体类: `XxxEntity` 或直接使用领域名称如 `ProjectBase`
- 数据传输对象: `XxxDto`
- 视图对象: `XxxVo`
- 控制器: `XxxController`
- 服务接口: `IXxxService` 或 `XxxService`
- 服务实现: `XxxServiceImpl`
- 业务逻辑接口: `XxxLogicService`
- 业务逻辑实现: `XxxLogicServiceImpl`
- 工具类: `XxxUtil`
- 枚举类: `XxxEnum`
- Mapper接口: `XxxMapper`

### 2.2 方法命名规范

- 查询单个: `getXxx`, `queryXxx`, `detail`, `fetchById`
- 查询列表: `listXxx`, `queryXxxList`, `treeList`
- 分页查询: `pageXxx`, `pageList`
- 新增: `saveXxx`, `addXxx`, `createXxx`
- 修改: `updateXxx`, `edit`
- 删除: `removeXxx`, `deleteXxx`
- 校验: `checkXxx`, `validateXxx`, `saveCheck`
- 转换: `convertXxx`, `dictConvert`
- 生成编号: `genXxxNo`, `generateXxxNo`, `getNum`

## 3. 代码风格

### 3.1 注释规范

类注释模板：
```java
/**
 * 类描述
 *
 * <AUTHOR>
 * @since 创建日期
 */
```

方法注释模板：
```java
/**
 * 方法描述
 *
 * @param paramName 参数描述
 * @return 返回值描述
 */
```

### 3.2 异常处理模式

项目使用统一的异常处理机制，主要通过`ServiceException`抛出业务异常：

```java
if (Objects.isNull(projectBaseDto)) {
    throw new ServiceException("项目不存在");
}
```

全局异常处理通过`@ControllerAdvice`实现，返回统一的`R`对象。

### 3.3 常用设计模式

#### 3.3.1 工厂模式

用于创建复杂对象或根据条件创建不同实现：

```java
/**
 * 生成编号工具类
 */
@Component
public class PaddingUtil {
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 生成信息系统编码
     */
    public String genSystemNo(String companyCode) {
        Long autoId = redisTemplate.opsForValue().increment(SYSTEM_RECORD+companyCode, 1);
        return companyCode + String.format("%05d", autoId);
    }
}
```

#### 3.3.2 策略模式

通过接口定义策略，不同实现提供不同算法：

```java
/**
 * 全局获取用户id
 */
@Component
@Slf4j
public class UserIdAutoFillHandler implements IOptionByAutoFillHandler<Long> {
    @Override
    public Long getVal(Object object, Class<?> clazz, Field field) {
        SzykUser user = AuthUtil.getUser();
        if (user != null) {
            return user.getUserId();
        }
        return null;
    }
}
```

#### 3.3.3 AOP切面

用于横切关注点如权限校验、日志记录等：

```java
@Aspect
@Component
public class LockAspect {
    @Pointcut("@annotation(com.snszyk.zbusiness.resource.annotations.LockValid)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object lockValidMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        // 当前用户所属单位
        Dept realUnitDept = SysCache.getRealUnitDept();

        if (UnitLevelEnum.UNIT_LEVEL_2.getCode().intValue() < realUnitDept.getUnitLevel()) {
            throw new RuntimeException("只有集团公司、二级公司具备此功能权限！");
        }

        return joinPoint.proceed();
    }
}
```

## 4. 分层架构实现

### 4.1 控制层 (Controller)

控制器负责接收请求、参数校验和调用服务层：

```java
@RestController
@AllArgsConstructor
@RequestMapping("/project/base")
@Api(value = "项目基础信息", tags = "项目基础信息接口")
public class ProjectBaseController extends BaseCrudController<ProjectBaseLogicService> {

    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入id")
    public R<ProjectBaseDto> detail(@PathVariable("id") Long id) {
        ProjectBaseDto detail = baseLogicService.getById(id);
        return R.data(detail);
    }
    
    @PostMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入vo")
    public R<IPage<ProjectBaseDto>> page(@RequestBody ProjectBaseVo vo) {
        IPage<ProjectBaseDto> pages = baseLogicService.page(vo);
        return R.data(pages);
    }
}
```

### 4.2 业务逻辑层 (LogicService)

业务逻辑层处理复杂业务逻辑，调用多个基础服务：

```java
@Service
@AllArgsConstructor
public class ProjectBaseLogicServiceImpl implements ProjectBaseLogicService {

    private final ProjectBaseService projectBaseService;
    private final DictMapService dictMapService;
    
    @Override
    @Transactional(readOnly = true)
    public IPage<ProjectBaseDto> page(ProjectBaseVo vo) {
        String deptId = AuthUtil.getDeptId();
        Long deptLong = Long.valueOf(deptId);
        
        IPage<ProjectBaseDto> page = projectBaseService.pageList(vo, deptLong);
        // 字典值转化
        this.dictConvert(page.getRecords());
        return page;
    }
    
    private void dictConvert(List<ProjectBaseDto> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        
        Map<String, String> projectMajorDictMap = dictMapService.getProjectMajorDictMap();
        // 字典转换逻辑...
    }
}
```

### 4.3 服务层 (Service)

服务层提供基础的业务操作，通常与单个实体相关：

```java
@Service
@AllArgsConstructor
public class ProjectBaseServiceImpl extends ServiceImpl<ProjectBaseMapper, ProjectBase> implements ProjectBaseService {

    private final ProjectBaseMapper projectBaseMapper;
    
    @Override
    public IPage<ProjectBaseDto> pageList(ProjectBaseVo vo, Long deptId) {
        IPage<ProjectBase> page = new Page<>(vo.getCurrent(), vo.getSize());
        LambdaQueryWrapper<ProjectBase> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtil.isNotBlank(vo.getProjectName())) {
            queryWrapper.like(ProjectBase::getProjectName, vo.getProjectName());
        }
        
        // 数据权限过滤
        queryWrapper.eq(ProjectBase::getDeptId, deptId);
        
        // 执行查询
        IPage<ProjectBase> pages = page(page, queryWrapper);
        
        // 转换为DTO
        return pages.convert(projectBase -> BeanUtil.copy(projectBase, ProjectBaseDto.class));
    }
}
```

### 4.4 数据访问层 (Mapper)

基于MyBatis-Plus实现数据访问：

```java
@Repository
public interface ProjectBaseMapper extends BaseMapper<ProjectBase> {
    
    /**
     * 自定义查询方法
     */
    @Select("SELECT * FROM project_base WHERE dept_id = #{deptId} AND status = 1")
    List<ProjectBase> selectActiveByDeptId(@Param("deptId") Long deptId);
}
```

## 5. 依赖注入方式

项目使用Spring的依赖注入，主要通过构造器注入：

```java
@Service
@AllArgsConstructor  // Lombok自动生成全参构造器
public class ProjectBaseLogicServiceImpl implements ProjectBaseLogicService {

    private final ProjectBaseService projectBaseService;
    private final DictMapService dictMapService;
    
    // 方法实现...
}
```

## 6. 事务管理

使用Spring的声明式事务管理：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean saveProject(ProjectBaseDto dto) {
    // 业务逻辑
    ProjectBase entity = BeanUtil.copy(dto, ProjectBase.class);
    return projectBaseService.save(entity);
}

@Override
@Transactional(readOnly = true)
public IPage<ProjectBaseDto> page(ProjectBaseVo vo) {
    // 查询逻辑
}
```

## 7. 最佳实践

### 7.1 缓存使用

项目使用Redis进行缓存，常见模式：

```java
@Component
public class ProjectCodePaddingUtil {

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取项目编号
     */
    public String getNum(String yearCompany) {
        Long autoId = redisTemplate.opsForValue().increment("projectNo:"+yearCompany, 1);
        return String.format("%03d", autoId);
    }
}
```

### 7.2 权限控制

使用注解和AOP实现权限控制：

```java
private void hasAuth(Long queryUserId, UserDTO userDTO) {
    SzykUser user = AuthUtil.getUser();
    Long userId = user.getUserId();
    //看自己的
    if (Objects.equals(userId, queryUserId)) {
        return;
    }
    //管理员
    List<String> currentRole = DeptScopeUtil.getCurrentRole();
    if (currentRole.contains("admin") || currentRole.contains("administrator")) {
        return;
    }
    List<Long> longs = DeptScopeUtil.verticalDeptList();
    Optional<UserDeptDTO> any = userDTO.getDeptScopeList().stream().filter(e -> longs.contains(e.getDeptId())).findAny();
    if (!any.isPresent()) {
        throw new ServiceException("无权查看");
    }
}
```

### 7.3 数据校验

使用自定义注解和AOP进行数据校验：

```java
@Aspect
@Component
public class LockAspect {
    @Pointcut("@annotation(com.snszyk.zbusiness.resource.annotations.LockValid)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object lockValidMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        // 当前用户所属单位
        Dept realUnitDept = SysCache.getRealUnitDept();

        if (UnitLevelEnum.UNIT_LEVEL_2.getCode().intValue() < realUnitDept.getUnitLevel()) {
            throw new RuntimeException("只有集团公司、二级公司具备此功能权限！");
        }

        return joinPoint.proceed();
    }
}
```

### 7.4 编号生成

使用Redis实现自增编号：

```java
@Component
@AllArgsConstructor
public class FormNoGenerateServiceImpl implements IFormNoGenerateService {

    private SzykRedis szykRedis;

    @Override
    public String generateFormNo(FormNoTypeEnum formNoTypeEnum) {
        //获得单号前缀
        String formNoPrefix = FormNoSerialUtil.getFormNoPrefix(formNoTypeEnum);
        //获得缓存key
        String cacheKey = FormNoSerialUtil.getCacheKey(formNoPrefix);
        //获得当日自增数
        Long incrementalSerial = szykRedis.incr(cacheKey);
        //设置失效时间 3天
        szykRedis.expire(cacheKey, FormNoConstants.DEFAULT_CACHE_DAYS);
        //组合单号并补全流水号
        return FormNoSerialUtil.completionSerial(formNoPrefix, incrementalSerial, formNoTypeEnum);
    }
}
```

## 8. 常用工具类

### 8.1 认证工具类

`AuthUtil`: 获取当前用户信息

```java
@Component
@Slf4j
public class UserIdAutoFillHandler implements IOptionByAutoFillHandler<Long> {
    @Override
    public Long getVal(Object object, Class<?> clazz, Field field) {
        SzykUser user = AuthUtil.getUser();
        if (user != null) {
            return user.getUserId();
        }
        return null;
    }
}
```

### 8.2 字符串工具类

`StringUtil`: 字符串处理工具

```java
if (StringUtil.isNotBlank(vo.getProjectName())) {
    queryWrapper.like(ProjectBase::getProjectName, vo.getProjectName());
}
```

### 8.3 对象工具类

`BeanUtil`: 对象复制工具

```java
ProjectBase entity = BeanUtil.copy(dto, ProjectBase.class);
```

### 8.4 集合工具类

`CollectionUtil`: 集合操作工具

```java
if (CollectionUtil.isEmpty(list)) {
    return;
}
```

### 8.5 数字工具类

`DigitUtil`: 数字处理工具

```java
public static String numToBatchNo(Integer no){
    String noStr = String.valueOf(no);
    //年份
    String year = noStr.substring(0, 4);
    //批次
    String batchStr = noStr.substring(4);
    return year+"年第"+ DigitUtil.arabicNumToChineseNum(Integer.parseInt(batchStr))+"批";
}
```

## 9. 代码示例

### 9.1 创建新的业务模块

以下是创建新业务模块的标准结构和实现方式：

#### 9.1.1 实体类

```java
package com.snszyk.zbusiness.module.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.entity.BaseCrudEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务实体
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@TableName("module_business")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "业务实体", description = "业务实体")
public class Business extends BaseCrudEntity {

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;
    
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String businessCode;
    
    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态")
    private Integer status;
}
```

#### 9.1.2 DTO类

```java
package com.snszyk.zbusiness.module.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务数据传输对象
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "业务数据传输对象", description = "业务数据传输对象")
public class BusinessDto extends BaseCrudDto {

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;
    
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String businessCode;
    
    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态")
    private Integer status;
    
    /**
     * 业务状态名称
     */
    @ApiModelProperty(value = "业务状态名称")
    private String statusName;
}
```

#### 9.1.3 VO类

```java
package com.snszyk.zbusiness.module.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务查询对象
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "业务查询对象", description = "业务查询对象")
public class BusinessVo extends BaseCrudVo {

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;
    
    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String businessCode;
    
    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态")
    private Integer status;
}
```

#### 9.1.4 Mapper接口

```java
package com.snszyk.zbusiness.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.module.entity.Business;
import org.springframework.stereotype.Repository;

/**
 * 业务Mapper接口
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Repository
public interface BusinessMapper extends BaseMapper<Business> {
}
```

#### 9.1.5 Service接口

```java
package com.snszyk.zbusiness.module.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.module.dto.BusinessDto;
import com.snszyk.zbusiness.module.entity.Business;
import com.snszyk.zbusiness.module.vo.BusinessVo;

/**
 * 业务服务接口
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
public interface BusinessService extends IService<Business> {

    /**
     * 分页查询
     *
     * @param vo 查询条件
     * @param deptId 部门ID
     * @return 分页结果
     */
    IPage<BusinessDto> pageList(BusinessVo vo, Long deptId);
}
```

#### 9.1.6 Service实现

```java
package com.snszyk.zbusiness.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.module.dto.BusinessDto;
import com.snszyk.zbusiness.module.entity.Business;
import com.snszyk.zbusiness.module.mapper.BusinessMapper;
import com.snszyk.zbusiness.module.service.BusinessService;
import com.snszyk.zbusiness.module.vo.BusinessVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 业务服务实现
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Service
@AllArgsConstructor
public class BusinessServiceImpl extends ServiceImpl<BusinessMapper, Business> implements BusinessService {

    private final BusinessMapper businessMapper;
    
    @Override
    public IPage<BusinessDto> pageList(BusinessVo vo, Long deptId) {
        IPage<Business> page = new Page<>(vo.getCurrent(), vo.getSize());
        LambdaQueryWrapper<Business> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtil.isNotBlank(vo.getBusinessName())) {
            queryWrapper.like(Business::getBusinessName, vo.getBusinessName());
        }
        
        if (StringUtil.isNotBlank(vo.getBusinessCode())) {
            queryWrapper.eq(Business::getBusinessCode, vo.getBusinessCode());
        }
        
        if (vo.getStatus() != null) {
            queryWrapper.eq(Business::getStatus, vo.getStatus());
        }
        
        // 数据权限过滤
        queryWrapper.eq(Business::getDeptId, deptId);
        
        // 执行查询
        IPage<Business> pages = page(page, queryWrapper);
        
        // 转换为DTO
        return pages.convert(business -> BeanUtil.copy(business, BusinessDto.class));
    }
}
```

#### 9.1.7 LogicService接口

```java
package com.snszyk.zbusiness.module.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.module.dto.BusinessDto;
import com.snszyk.zbusiness.module.vo.BusinessVo;

/**
 * 业务逻辑服务接口
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
public interface BusinessLogicService {

    /**
     * 分页查询
     *
     * @param vo 查询条件
     * @return 分页结果
     */
    IPage<BusinessDto> page(BusinessVo vo);
    
    /**
     * 根据ID查询
     *
     * @param id ID
     * @return 业务DTO
     */
    BusinessDto getById(Long id);
    
    /**
     * 保存业务
     *
     * @param dto 业务DTO
     * @return 是否成功
     */
    boolean save(BusinessDto dto);
    
    /**
     * 更新业务
     *
     * @param dto 业务DTO
     * @return 是否成功
     */
    boolean update(BusinessDto dto);
    
    /**
     * 删除业务
     *
     * @param id ID
     * @return 是否成功
     */
    boolean remove(Long id);
}
```

#### 9.1.8 LogicService实现

```java
package com.snszyk.zbusiness.module.service.logic.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.service.IDictService;
import com.snszyk.zbusiness.module.dto.BusinessDto;
import com.snszyk.zbusiness.module.entity.Business;
import com.snszyk.zbusiness.module.service.BusinessService;
import com.snszyk.zbusiness.module.service.logic.BusinessLogicService;
import com.snszyk.zbusiness.module.vo.BusinessVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 业务逻辑服务实现
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Service
@AllArgsConstructor
public class BusinessLogicServiceImpl implements BusinessLogicService {

    private final BusinessService businessService;
    private final IDictService dictService;
    
    @Override
    @Transactional(readOnly = true)
    public IPage<BusinessDto> page(BusinessVo vo) {
        String deptId = AuthUtil.getDeptId();
        Long deptLong = Long.valueOf(deptId);
        
        IPage<BusinessDto> page = businessService.pageList(vo, deptLong);
        // 字典值转化
        this.dictConvert(page.getRecords());
        return page;
    }
    
    @Override
    @Transactional(readOnly = true)
    public BusinessDto getById(Long id) {
        Business business = businessService.getById(id);
        if (business == null) {
            return null;
        }
        
        BusinessDto dto = BeanUtil.copy(business, BusinessDto.class);
        // 字典转换
        this.dictConvert(List.of(dto));
        return dto;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(BusinessDto dto) {
        Business business = BeanUtil.copy(dto, Business.class);
        return businessService.save(business);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(BusinessDto dto) {
        Business business = BeanUtil.copy(dto, Business.class);
        return businessService.updateById(business);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(Long id) {
        return businessService.removeById(id);
    }
    
    /**
     * 字典转换
     */
    private void dictConvert(List<BusinessDto> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        
        // 获取状态字典
        Map<String, String> statusMap = dictService.getDict("business_status");
        
        // 转换字典值
        list.forEach(dto -> {
            if (dto.getStatus() != null) {
                dto.setStatusName(statusMap.get(dto.getStatus().toString()));
            }
        });
    }
}
```

#### 9.1.9 Controller

```java
package com.snszyk.zbusiness.module.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.module.dto.BusinessDto;
import com.snszyk.zbusiness.module.service.logic.BusinessLogicService;
import com.snszyk.zbusiness.module.vo.BusinessVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 业务控制器
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/zbusiness/business")
@Api(value = "业务管理", tags = "业务管理接口")
public class BusinessController {

    private final BusinessLogicService businessLogicService;

    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入id")
    @ApiOperationSupport(order = 1)
    public R<BusinessDto> detail(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
        Business