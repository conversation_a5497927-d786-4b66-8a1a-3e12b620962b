-- 操作记录扩展字段表（第三阶段：业务特定字段存储设计）
CREATE TABLE `rs_operation_record_extension` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` bigint(20) NOT NULL COMMENT '操作记录ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `extension_data` longtext COMMENT '扩展数据（JSON格式）',
  `metadata` text COMMENT '元数据信息（JSON格式，包含字段定义、索引信息等）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_business` (`record_id`, `business_type`),
  KEY `idx_business_type` (`business_type`),
  CONSTRAINT `fk_operation_record_extension_record_id` FOREIGN KEY (`record_id`) REFERENCES `rs_operation_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录扩展字段表';

-- 业务字段定义表（用于定义各业务类型的特定字段）
CREATE TABLE `rs_business_field_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(200) NOT NULL COMMENT '字段中文名称',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型（STRING/INTEGER/DECIMAL/DATE/BOOLEAN/JSON）',
  `field_group` varchar(100) DEFAULT NULL COMMENT '字段分组',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否敏感字段',
  `is_searchable` tinyint(1) DEFAULT '0' COMMENT '是否可搜索',
  `is_indexable` tinyint(1) DEFAULT '0' COMMENT '是否需要索引',
  `dict_code` varchar(100) DEFAULT NULL COMMENT '字典代码',
  `validation_rules` text COMMENT '验证规则（JSON格式）',
  `display_order` int(11) DEFAULT '0' COMMENT '显示顺序',
  `description` varchar(500) DEFAULT NULL COMMENT '字段描述',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态（1-启用，0-禁用）',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_field` (`business_type`, `field_name`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_field_group` (`field_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务字段定义表';

-- 操作记录详情扩展表（增强版详情表，支持更多元数据）
CREATE TABLE `rs_operation_record_detail_ext` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` bigint(20) NOT NULL COMMENT '操作记录ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(200) DEFAULT NULL COMMENT '字段中文名称',
  `field_type` varchar(50) DEFAULT NULL COMMENT '字段类型',
  `field_group` varchar(100) DEFAULT NULL COMMENT '字段分组',
  `old_value` longtext COMMENT '变更前值',
  `new_value` longtext COMMENT '变更后值',
  `old_display_value` text COMMENT '变更前显示值',
  `new_display_value` text COMMENT '变更后显示值',
  `change_type` varchar(20) DEFAULT NULL COMMENT '变更类型（ADD/UPDATE/DELETE）',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '是否敏感字段',
  `validation_result` text COMMENT '验证结果（JSON格式）',
  `metadata` text COMMENT '字段元数据（JSON格式）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_field_name` (`field_name`),
  KEY `idx_field_group` (`field_group`),
  CONSTRAINT `fk_operation_record_detail_ext_record_id` FOREIGN KEY (`record_id`) REFERENCES `rs_operation_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录详情扩展表';

-- 操作记录索引表（用于提高查询性能）
CREATE TABLE `rs_operation_record_index` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` bigint(20) NOT NULL COMMENT '操作记录ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `index_key` varchar(200) NOT NULL COMMENT '索引键',
  `index_value` varchar(500) NOT NULL COMMENT '索引值',
  `index_type` varchar(20) DEFAULT 'STRING' COMMENT '索引类型（STRING/NUMBER/DATE）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_business_index` (`business_type`, `index_key`, `index_value`),
  KEY `idx_index_key_value` (`index_key`, `index_value`),
  CONSTRAINT `fk_operation_record_index_record_id` FOREIGN KEY (`record_id`) REFERENCES `rs_operation_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录索引表';

-- 操作记录模板表（用于定义不同业务类型的记录模板）
CREATE TABLE `rs_operation_record_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `template_name` varchar(200) NOT NULL COMMENT '模板名称',
  `template_config` longtext NOT NULL COMMENT '模板配置（JSON格式）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `description` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(2) DEFAULT '1' COMMENT '状态（1-启用，0-禁用）',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_template` (`business_type`, `template_name`),
  KEY `idx_business_type` (`business_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作记录模板表';

-- 初始化业务字段定义数据
INSERT INTO `rs_business_field_definition` (`business_type`, `field_name`, `field_label`, `field_type`, `field_group`, `is_required`, `is_sensitive`, `is_searchable`, `is_indexable`, `dict_code`, `display_order`) VALUES
-- 互联网资产特定字段
('INTERNET_ASSET', 'systemName', '系统名称', 'STRING', '基本信息', 1, 0, 1, 1, NULL, 1),
('INTERNET_ASSET', 'internetAddress', '互联网地址', 'STRING', '基本信息', 1, 0, 1, 1, NULL, 2),
('INTERNET_ASSET', 'networkAddress', '内网地址', 'STRING', '基本信息', 0, 1, 1, 1, NULL, 3),
('INTERNET_ASSET', 'domainName', '域名', 'STRING', '基本信息', 0, 0, 1, 1, NULL, 4),
('INTERNET_ASSET', 'internetType', '互联网类型', 'STRING', '分类信息', 1, 0, 1, 1, 'internet_type', 5),
('INTERNET_ASSET', 'applicationType', '应用类型', 'STRING', '分类信息', 1, 0, 1, 1, 'application_type', 6),
('INTERNET_ASSET', 'contactPerson', '联系人', 'STRING', '联系信息', 0, 0, 1, 0, NULL, 7),
('INTERNET_ASSET', 'contactPhone', '联系电话', 'STRING', '联系信息', 0, 1, 0, 0, NULL, 8),
('INTERNET_ASSET', 'securityLevel', '等保级别', 'INTEGER', '安全信息', 0, 0, 1, 1, 'security_level', 9),
('INTERNET_ASSET', 'resourceStatus', '资源状态', 'INTEGER', '状态信息', 1, 0, 1, 1, 'resource_status', 10),
('INTERNET_ASSET', 'isPublicCloud', '是否公有云', 'BOOLEAN', '部署信息', 0, 0, 1, 1, NULL, 11),
('INTERNET_ASSET', 'publicCloudSupplier', '公有云供应商', 'STRING', '部署信息', 0, 0, 1, 0, 'cloud_supplier', 12),

-- 设备资产特定字段
('EQUIPMENT_ASSET', 'equipmentName', '设备名称', 'STRING', '基本信息', 1, 0, 1, 1, NULL, 1),
('EQUIPMENT_ASSET', 'equipmentType', '设备类型', 'STRING', '基本信息', 1, 0, 1, 1, 'equipment_type', 2),
('EQUIPMENT_ASSET', 'manufacturer', '制造商', 'STRING', '基本信息', 0, 0, 1, 1, NULL, 3),
('EQUIPMENT_ASSET', 'model', '型号', 'STRING', '基本信息', 0, 0, 1, 1, NULL, 4),
('EQUIPMENT_ASSET', 'serialNumber', '序列号', 'STRING', '标识信息', 0, 1, 1, 1, NULL, 5),
('EQUIPMENT_ASSET', 'purchaseDate', '采购日期', 'DATE', '采购信息', 0, 0, 1, 1, NULL, 6),
('EQUIPMENT_ASSET', 'warrantyPeriod', '保修期（月）', 'INTEGER', '采购信息', 0, 0, 1, 0, NULL, 7),
('EQUIPMENT_ASSET', 'equipmentStatus', '设备状态', 'STRING', '状态信息', 1, 0, 1, 1, 'equipment_status', 8);

-- 初始化操作记录模板数据
INSERT INTO `rs_operation_record_template` (`business_type`, `template_name`, `template_config`, `is_default`, `description`) VALUES
('INTERNET_ASSET', '互联网资产标准模板', '{"recordDetails": true, "sensitiveFieldMask": true, "indexFields": ["systemName", "internetAddress", "internetType"], "displayGroups": ["基本信息", "分类信息", "联系信息", "安全信息", "状态信息", "部署信息"]}', 1, '互联网资产的标准操作记录模板'),
('EQUIPMENT_ASSET', '设备资产标准模板', '{"recordDetails": true, "sensitiveFieldMask": true, "indexFields": ["equipmentName", "equipmentType", "serialNumber"], "displayGroups": ["基本信息", "标识信息", "采购信息", "状态信息"]}', 1, '设备资产的标准操作记录模板');
